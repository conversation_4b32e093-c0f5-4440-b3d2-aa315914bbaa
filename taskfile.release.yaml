version: '3'

vars:
  REGISTRY: "ghcr.io/trocho"
  DEFAULT_DEV_TAG: "dev"
  DEFAULT_PROD_TAG: "prod"
  DEFAULT_ALL_TAG: "latest"

tasks:
  slim:
    desc: "Slim down images"
    requires:
      vars: [ TAG ]
    cmds:
      - docker context use default
      - slim build --target {{.TAG}} --tag {{.TAG}} --http-probe=false {{.ARGS}}


  # New build tasks with CLI_ARGS support
  build:dev:
    desc: Build development images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - IMAGE_TAG={{.TAG}} REGISTRY_URL={{.REGISTRY}} docker-compose -f docker-compose.dev.yml build

  build:prod:
    desc: Build production images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_PROD_TAG}}'
    cmds:
      - IMAGE_TAG={{.TAG}} REGISTRY_URL={{.REGISTRY}} docker-compose -f docker-compose.prod.yml build

  push:dev:
    desc: Push development images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - IMAGE_TAG={{.TAG}} REGISTRY_URL={{.REGISTRY}} docker-compose -f docker-compose.dev.yml push

  push:prod:
    desc: Push production images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_PROD_TAG}}'
    cmds:
      - IMAGE_TAG={{.TAG}} REGISTRY_URL={{.REGISTRY}} docker-compose -f docker-compose.prod.yml push

  build-and-push:dev:
    desc: Build and push development images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - task: build:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: push:dev
        vars: {CLI_ARGS: '{{.TAG}}'}

  build-and-push:prod:
    desc: Build and push production images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_PROD_TAG}}'
    cmds:
      - task: build:prod
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: push:prod
        vars: {CLI_ARGS: '{{.TAG}}'}

  build:all:
    desc: Build both dev and prod images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_ALL_TAG}}'
    cmds:
      - task: build:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: build:prod
        vars: {CLI_ARGS: '{{.TAG}}'}

  push:all:
    desc: Push both dev and prod images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_ALL_TAG}}'
    cmds:
      - task: push:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: push:prod
        vars: {CLI_ARGS: '{{.TAG}}'}

  release:
    desc: Build and push both dev and prod images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_ALL_TAG}}'
    cmds:
      - task: build-and-push:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: build-and-push:prod
        vars: {CLI_ARGS: '{{.TAG}}'}
