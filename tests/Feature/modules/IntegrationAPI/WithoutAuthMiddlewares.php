<?php

declare(strict_types=1);

namespace Tests\Feature\modules\IntegrationAPI;

use <PERSON><PERSON>\Passport\Http\Middleware\CheckClientCredentials;
use Modules\IntegrationAPI\Priv\Http\Middleware\ApiAuthClientMiddleware;
use Modules\IntegrationAPI\Priv\Http\Middleware\IsIntegrationApiEnabledMiddleware;

trait WithoutAuthMiddlewares
{
    public function setUp(): void
    {
        parent::setUp();

        $this->withoutAuthMiddlewares();
    }

    protected function withoutAuthMiddlewares(): void
    {
        $this->withoutMiddleware([
            'client',
            'integration_api.client_auth',
            'integration_api.documentation_auth',
            'integration_api.is_user_from_current_instance',
            'integration_api.is_enabled',
            IsIntegrationApiEnabledMiddleware::class,
            CheckClientCredentials::class,
            ApiAuthClientMiddleware::class
        ]);
    }
}