<?php

declare(strict_types=1);

namespace Feature\modules\MyCard\Priv\Http\Controllers;

use Tests\TestCase;

class MyCardProxyControllerTest extends TestCase
{
    public function testForwarderRequestCorrectly(): void
    {
        // when
        $this->loggedUser();
        $response = $this->getJson('/api/mycard/tenants');

        // then
        $this->assertMatchesResponseSnapshot($response);
    }
}
