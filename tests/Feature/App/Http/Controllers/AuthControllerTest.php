<?php

declare(strict_types=1);

namespace Tests\Feature\App\Http\Controllers;

use Tests\TestCase;

class AuthControllerTest extends TestCase
{
    public function testStatus()
    {
        $user = $this->loggedUser();

        $response = $this->get($this->createUrl($user->instance, '/api/auth/status'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'user'
            ],
        ]);
    }
}
