<?php

declare(strict_types=1);

namespace Tests\Feature\App\Http\Controllers;

use Tests\TestCase;
use Mo<PERSON>les\DecisionMaker\Priv\Models\DecisionProcess;
use Mo<PERSON>les\DecisionMaker\Priv\Models\DecisionProcessNode;
use Modules\DecisionMaker\Priv\Models\DecisionProcessCondition;

class DecisionProcessGraphControllerTest extends TestCase
{
    public function testGetAllGraphData()
    {
        $user = $this->loggedUser();

        $response = $this->get($this->createUrl($user->instance, '/api/decision-processes/graph'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'nodes' => [
                '*' => [
                    'id',
                    'label',
                    'type',
                    'processId',
                    'processSlug',
                    'nodeId',
                    'operatorCode',
                    'decisionCode',
                    'decisionParameters',
                    'order',
                    'conditions',
                    'group',
                    'instanceId',
                    'isDefault',
                ]
            ],
            'edges' => [
                '*' => [
                    'id',
                    'source',
                    'target',
                    'type',
                    'label',
                ]
            ],
            'processGroups' => [
                '*' => [
                    'id',
                    'slug',
                    'name',
                ]
            ],
            'metadata' => [
                'totalProcesses',
                'totalNodes',
                'totalEdges',
                'currentInstanceId',
                'currentInstanceName',
                'generatedAt',
            ]
        ]);
    }

    public function testGetSpecificProcessGraphData()
    {
        $user = $this->loggedUser();
        
        // Create a test decision process if it doesn't exist
        $process = DecisionProcess::firstOrCreate([
            'slug' => 'TEST_PROCESS'
        ]);

        $response = $this->get($this->createUrl($user->instance, '/api/decision-processes/graph/TEST_PROCESS'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'nodes',
            'edges',
            'processGroups',
            'metadata'
        ]);
    }

    public function testGetNonExistentProcessReturns404()
    {
        $user = $this->loggedUser();

        $response = $this->get($this->createUrl($user->instance, '/api/decision-processes/graph/NON_EXISTENT_PROCESS'));

        $response->assertStatus(404);
        $response->assertJson([
            'error' => 'Decision process not found'
        ]);
    }

    public function testGraphDataContainsCorrectNodeStructure()
    {
        $user = $this->loggedUser();

        // Create test data
        $process = DecisionProcess::firstOrCreate([
            'slug' => 'TEST_GRAPH_PROCESS'
        ]);

        $node = DecisionProcessNode::create([
            'decision_process_id' => $process->id,
            'operator_code' => 'and',
            'decision_code' => 'TestDecision',
            'decision_parameters' => ['test' => 'value'],
            'order' => 1,
            'description' => 'Test node description'
        ]);

        DecisionProcessCondition::create([
            'decision_process_node_id' => $node->id,
            'code' => 'TestCondition',
            'parameters' => ['param' => 'value'],
            'negate' => false
        ]);

        $response = $this->get($this->createUrl($user->instance, '/api/decision-processes/graph/TEST_GRAPH_PROCESS'));

        $response->assertStatus(200);
        
        $data = $response->json();
        
        $this->assertCount(1, $data['nodes']);
        $this->assertEquals('TestDecision', $data['nodes'][0]['decisionCode']);
        $this->assertEquals('and', $data['nodes'][0]['operatorCode']);
        $this->assertEquals(['test' => 'value'], $data['nodes'][0]['decisionParameters']);
        $this->assertCount(1, $data['nodes'][0]['conditions']);
        $this->assertEquals('TestCondition', $data['nodes'][0]['conditions'][0]['code']);
    }

    public function testUnauthorizedAccessReturns401()
    {
        $response = $this->get('/api/decision-processes/graph');

        $response->assertStatus(401);
    }
}
