<?php

declare(strict_types=1);

namespace Tests\to_rewrite\api\src\Provider\Disposable;

use ApiTester;
use Codeception\Example;
use Tests\ObjectMother\Provider\Disposable\ProviderMother;

class StoreProviderCest
{
    public function it_test_creating(ApiTester $I)
    {
        //When
        $I->amLoggedAsSuperAdmin();
        //And
        $recordsNum = $I->grabNumRecords('provider_disposables');

        // When
        $I->sendPost('provider-disposables', $data = [
            'company_id' => null,
            'country_id' => 345,
            'name' => 'Supplier 1',
            'tax_id' => '*********',
            'erp_id' => 'WR434DF',
            'address' => 'Address 1',
            'city' => 'City name',
            'postcode' => '00-000',
        ]);

        //Given
        $I->seeResponseCodeIs(201);
        $I->seeNumRecords($recordsNum + 1, 'provider_disposables', $data);
        $I->canSeeRecord('provider_disposables', $data);
    }

    public function it_test_creating_when_already_exists(ApiTester $I)
    {
        //When
        $I->amLoggedAsSuperAdmin();
        //And
        $recordsNum = $I->grabNumRecords('provider_disposables');
        //And
        ProviderMother::make($I->currentInstanceId(), $companyId = null)->save();

        // When
        $I->sendPost('provider-disposables', $data = [
            'company_id' => $companyId,
            'country_id' => 345,
            'name' => 'Supplier 1',
            'tax_id' => '*********',
            'erp_id' => 'WR434DF',
            'address' => 'Address 1',
            'city' => 'City name',
            'postcode' => '00-000',
        ]);

        //Given
        $I->seeResponseCodeIs(201);
        $I->seeNumRecords($recordsNum + 1, 'provider_disposables', $data);
        $I->canSeeRecord('provider_disposables', $data);
    }

    /**
     * @dataProvider suppliers
     */
    public function it_test_creating_with_incorrect_data(ApiTester $I, Example $data)
    {
        //When
        if ($data['login'] ?? true) {
            $I->amLoggedAsSuperAdmin();
        }

        //Then
        $I->sendPost('provider-disposables', $data['data']);

        //Given
        $I->seeResponseCodeIs($data['code']);
        $I->seeValidationFailedFields($data['fields']);
    }

    protected function suppliers(): array
    {
        return [
            ['data' => [], 'code' => 422, 'login' => true, 'fields' => ['erp_id']],
            ['data' => [], 'code' => 401, 'login' => false, 'fields' => []],
        ];
    }
}
