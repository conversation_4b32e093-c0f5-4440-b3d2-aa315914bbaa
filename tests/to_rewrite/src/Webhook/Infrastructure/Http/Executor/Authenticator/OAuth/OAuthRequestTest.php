<?php

namespace Mindento\Webhook\Infrastructure\Http\Executor\Authenticator\OAuth;

use Mindento\Webhook\Domain\ConfigProvider\WebhookAuthConfig;
use PHPUnit\Framework\TestCase;

class OAuthRequestTest extends TestCase
{
    /**
     * @test
     * @dataProvider requests
     */
    public function itIsCreatedWithWebhookAuthConfig(WebhookAuthConfig $authConfig): void
    {
        $this->assertEquals(
            (new OAuthRequest("POST", $authConfig->getAuthUrl()))->withOptions(
                [
                    'form_params' => $authConfig->getFormData(),
                    'headers' => $authConfig->getHeaders(),
                ]
            ),
            OAuthRequest::fromWebhookConfig($authConfig)
        );
    }

    public function requests(): array
    {
        return [
            [
                new WebhookAuthConfig("http://auth-url"),
                new WebhookAuthConfig(
                    "http://auth-url",
                    ["some-header" => "header value"]
                ),
                new WebhookAuthConfig(
                    "http://auth-url",
                    ["some-header" => "header-value"],
                    ["form-data" => "form-data-value"]
                ),
            ]
        ];
    }
}
