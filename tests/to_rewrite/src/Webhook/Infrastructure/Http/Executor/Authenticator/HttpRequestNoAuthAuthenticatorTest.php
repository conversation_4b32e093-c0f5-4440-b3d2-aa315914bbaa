<?php

namespace <PERSON>ento\Webhook\Infrastructure\Http\Executor\Authenticator;

use Mindento\Webhook\Domain\ConfigProvider\WebhookConfig;
use Mindento\Webhook\Domain\ConfigProvider\WebhookRetryIntervals;
use Modules\Common\Http\Client\GenericRequest;
use PHPUnit\Framework\TestCase;

class HttpRequestNoAuthAuthenticatorTest extends TestCase
{
    private HttpRequestNoAuthAuthenticator $authenticator;

    protected function setUp(): void
    {
        $this->authenticator = new HttpRequestNoAuthAuthenticator();
    }

    /**
     * @test
     */
    public function itIsAuthProvider()
    {
        $this->assertInstanceOf(HttpRequestAuthenticator::class, $this->authenticator);
    }

    /**
     * @test
     */
    public function itDoesNotModifyTheRequest()
    {
        $config = new WebhookConfig("http://some-url", new WebhookRetryIntervals([]));

        $authenticatedRequest = new GenericRequest("POST", "http://some-url");
        $requestToAuthenticate = new GenericRequest("POST", "http://some-url");

        $this->authenticator->authenticate($requestToAuthenticate, $config);

        $this->assertEquals($authenticatedRequest, $requestToAuthenticate);
    }
}
