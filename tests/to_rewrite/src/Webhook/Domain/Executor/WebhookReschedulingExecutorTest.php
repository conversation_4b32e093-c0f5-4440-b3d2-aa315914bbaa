<?php

namespace <PERSON>ent<PERSON>\Webhook\Domain\Executor;

use Mindento\Webhook\Domain\Scheduler\WebhookScheduler;
use Mindento\Webhook\Domain\WebhookAttempt;
use Mindento\Webhook\Domain\WebhookAttemptRepository;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use Prophecy\Prophecy\ObjectProphecy;

class WebhookReschedulingExecutorTest extends TestCase
{
    use ProphecyTrait;

    /** @var ObjectProphecy|WebhookExecutor */
    private ObjectProphecy $innerExecutor;

    /** @var ObjectProphecy|WebhookScheduler */
    private ObjectProphecy $webhookScheduler;

    private WebhookReSchedulingExecutor $executor;

    protected function setUp(): void
    {
        $this->innerExecutor = $this->prophesize(WebhookExecutor::class);
        $this->webhookScheduler = $this->prophesize(WebhookScheduler::class);

        $this->executor = new WebhookReSchedulingExecutor(
            $this->innerExecutor->reveal(),
            $this->webhookScheduler->reveal()
        );
    }

    /**
     * @test
     */
    public function itReSchedulesWebhookBeforeExecution(): void
    {
        $webhookAttempt = $this->prophesize(WebhookAttempt::class);

        $this->webhookScheduler->reScheduleWebhook($webhookAttempt->reveal());
        $this->innerExecutor->execute($webhookAttempt->reveal());
        $this->executor->execute($webhookAttempt->reveal());

        $this->innerExecutor->execute($webhookAttempt->reveal())->shouldBeCalled();
    }
}
