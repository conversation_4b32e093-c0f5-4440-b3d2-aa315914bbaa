<?php

declare(strict_types=1);

namespace Tests\Support;

use PHPUnit\Event\Test\Failed;
use PHPUnit\Event\Test\FailedSubscriber;
use Shared\Error\ContextExceptionInterface;
use Throwable;

class ContextExceptionListener implements FailedSubscriber
{
    public function notify(Failed $event): void
    {
        echo "\n\n>>> ContextExceptionListener::notify() called <<<\n\n";

        // $throwable = $event->throwable();
        // $exception = $throwable->asThrowable();

        // if ($exception instanceof ContextExceptionInterface) {
        //     $this->printContext($exception);
        // } elseif ($exception instanceof \PHPUnit\Framework\ExpectationFailedException) {
        //     $previous = $exception->getPrevious();

        //     if ($previous !== null && $previous instanceof ContextExceptionInterface) {
        //         $this->printContext($previous);
        //     }
        // }
    }

    private function printContext(ContextExceptionInterface $exception): void
    {
        $context = $exception->getContext();

        if (!empty($context)) {
            echo "\n\nException Context:\n";
            echo "==================\n";

            foreach ($context as $key => $values) {
                echo "{$key}: ";

                if (is_array($values)) {
                    foreach ($values as $value) {
                        if (is_object($value)) {
                            echo get_class($value);
                        } else {
                            echo $value;
                        }
                        echo ", ";
                    }
                } else {
                    echo $values;
                }

                echo "\n";
            }

            echo "==================\n";
        }
    }
}
