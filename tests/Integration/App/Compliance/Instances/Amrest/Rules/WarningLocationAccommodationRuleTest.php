<?php

declare(strict_types=1);

namespace Tests\Integration\App\Compliance\Instances\Amrest\Rules;

use App\Compliance\Accommodation as ComplianceAccommodation;
use App\Compliance\Instances\Amrest\Rules\WarningLocationAccommodationRule;
use App\Currency;
use App\Instance;
use App\Repositories\RuleRepository;
use App\Rule;
use App\User;
use Tests\ObjectMother\App\AccommodationMother;
use Tests\ObjectMother\App\InstanceMother;
use Tests\ObjectMother\App\RuleMother;
use Tests\TestCase;

class WarningLocationAccommodationRuleTest extends TestCase
{
    private Instance $instance;
    private User $user;

    public function setUp(): void
    {
        parent::setUp();

        $this->instance = InstanceMother::anInstance();
        RuleMother::aRule($this->instance->id, [
            'name' => WarningLocationAccommodationRule::NAME,
            'parameters' => $this->getRuleParameters(),
            'level' => Rule::LEVEL_WARNING,
        ]);

        $this->user = new User(['grade' => 1]);
    }

    public function tearDown(): void
    {
        Rule::where('name', WarningLocationAccommodationRule::NAME)->delete();
        parent::tearDown();
    }

    public function testSuccessValidation(): void
    {
        // point: Bytom
        $accommodation = $this->createComplianceAccommodation(50.3483816, 18.9157175, 1, 100);
        $rule = new WarningLocationAccommodationRule($accommodation, $this->instance, $this->user);
        $rule->validate();

        self::assertTrue($rule->isValid());
        self::assertEmpty($rule->getMessages());
    }

    public function testFailureValidation(): void
    {
        // point: Bytom
        $accommodation = $this->createComplianceAccommodation(50.3483816, 18.9157175, 1, 500);
        $rule = new WarningLocationAccommodationRule($accommodation, $this->instance, $this->user);
        $rule->validate();

        self::assertFalse($rule->isValid());
        self::assertCount(1, $rule->getMessages());
    }

    private function createComplianceAccommodation(
        float $lat,
        float $lng,
        int $numberOfRooms,
        float $amount
    ): ComplianceAccommodation {
        $accommodation = AccommodationMother::anAccommodation(
            [
                'instance_id' => $this->instance->id,
                'number_of_rooms' => $numberOfRooms,
                'amount' => $amount,
            ],
            ['long' => $lng, 'lat' => $lat],
        );
        return ComplianceAccommodation::fromEloquentModel($accommodation);
    }

    public function getRuleParameters(): array
    {
        return require $this->getDataPath() . 'rules-config/WarningLocationAccommodationRule.config.php';
    }
}
