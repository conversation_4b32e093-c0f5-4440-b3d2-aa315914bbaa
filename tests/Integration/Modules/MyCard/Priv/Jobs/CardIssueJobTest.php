<?php

declare(strict_types=1);

namespace Tests\integration\Modules\MyCard\Priv\Jobs;

use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\MyCard\Priv\Entities\Cardholder;
use Modules\MyCard\Priv\Event\CardholderWasCreatedEvent;
use Modules\MyCard\Priv\Exceptions\CardIssueException;
use Modules\MyCard\Priv\Jobs\CardIssueJob;
use Modules\MyCard\Priv\Repositories\CardholderRepository;
use App\Repositories\UserRepository;
use ObjectMother\App\CardholderMother;
use ObjectMother\App\UserMother;
use Tests\TestCase;
use Illuminate\Support\Facades\Event;
use Modules\MyCard\API\Client;

class CardIssueJobTest extends TestCase
{
    use WithFaker;
    private UserRepository $userRepository;
    private CardholderRepository $cardholderRepository;
    private Dispatcher $dispatcher;
    protected function setUp(): void
    {
        parent::setUp();

        Event::fake();
        $this->dispatcher = resolve(Dispatcher::class);

        $this->cardholderRepository = resolve(CardholderRepository::class);
        $this->userRepository = resolve(UserRepository::class);
    }
    public function testCardIssueJobSuccess()
    {
        // given
        $APICardholderId = $this->faker()->uuid();
        $myCardApi = $this->createMock(Client::class);
        $myCardApi->method('post')->willReturn((object) ['cardholder_id' => $APICardholderId]);

        $user = UserMother::aUser();
        $cardholder = CardholderMother::aCardholder($user->id, $user->instance_id);

        // when
        $job = new CardIssueJob(
            'tenant_id',
            'tenant_provider_id',
            $cardholder->id,
            $user->id,
            1000,
            36
        );

        $job->handle(
            $this->dispatcher,
            $myCardApi,
            $this->userRepository,
            $this->cardholderRepository,
        );

        // then
        $updatedCardholder = $this->cardholderRepository->findByUserId($user->id);
        
        self::assertInstanceOf(Cardholder::class, $updatedCardholder);
        self::assertEquals($APICardholderId, $updatedCardholder->getReferenceCardholderId());

        Event::assertDispatched(CardholderWasCreatedEvent::class, function(CardholderWasCreatedEvent $event) use ($user, $APICardholderId) {
            return $event->getUserId() === $user->id && $event->getCardholderId() === $APICardholderId;
        });
    }

    public function testCardIssueJobFailure()
    {
        // given
        $myCardApi = $this->createMock(Client::class);
        $myCardApi->method('post')->willThrowException(new \Exception('API Error', 500));

        $user = UserMother::aUser();
        $cardholder = CardholderMother::aCardholder($user->id, $user->instance_id);

        $this->expectException(CardIssueException::class);

        // when
        $job = new CardIssueJob(
            'tenant_id',
            'tenant_provider_id',
            $cardholder->id,
            $user->id,
            1000,
            36
        );
        $job->handle(
            $this->dispatcher,
            $myCardApi,
            $this->userRepository,
            $this->cardholderRepository,
        );

        // then
        $updatedCardholder = $this->cardholderRepository->findByUserId($user->id);

        self::assertInstanceOf(Cardholder::class, $updatedCardholder);
        self::assertNull($updatedCardholder->getReferenceCardholderId());
        self::assertNotNull($updatedCardholder->status);

        Event::assertNotDispatched(CardholderWasCreatedEvent::class);
    }
}
