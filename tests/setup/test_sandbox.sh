#!/bin/bash

# test_sandbox.sh - Test sandbox image functionality
# Tests the sandbox stage with Docker-in-Docker, Python, and Node.js

set -e

# Load test framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/../BashTestCase.sh"

# =============================================================================
# TEST CONFIGURATION
# =============================================================================

CONTAINER_NAME="salesto-sandbox-$(generate_test_id)"
TEST_USER="allhands"
TEST_HOME="/home/<USER>"
PROJECT_IN_CONTAINER="/workspace"
SANDBOX_IMAGE="salesto:sandbox"

# =============================================================================
# DOCKER UTILITIES
# =============================================================================

# Execute command in container as test user
exec_in_container() {
    docker exec -w "$PROJECT_IN_CONTAINER" -u "$TEST_USER" "$CONTAINER_NAME" bash -c "$1"
}

# Execute command in container as root
exec_in_container_root() {
    docker exec "$CONTAINER_NAME" sh -c "$1"
}

# =============================================================================
# CLEANUP FUNCTIONS
# =============================================================================

cleanup_test_container() {
    docker_cleanup_container "$CONTAINER_NAME"
}

# =============================================================================
# TEST SETUP FUNCTIONS
# =============================================================================

build_sandbox_image() {
    print_status "INFO" "Building sandbox image..."
    
    # Check if Dockerfile exists
    check_project_file "Dockerfile"
    
    # Build the sandbox stage
    if docker build --target sandbox -t "$SANDBOX_IMAGE" "$PROJECT_ROOT"; then
        print_status "PASS" "Sandbox image built successfully"
    else
        print_status "FAIL" "Failed to build sandbox image"
        return 1
    fi
}

setup_test_container() {
    print_status "INFO" "Starting sandbox container..."

    # Start container from our sandbox image
    docker run -dt --privileged \
        --name "$CONTAINER_NAME" \
        -v "$PROJECT_ROOT:/workspace" \
        "$SANDBOX_IMAGE"

    print_status "PASS" "Sandbox container started: $CONTAINER_NAME"
}

wait_for_container_ready() {
    print_status "INFO" "Waiting for container services to be ready..."

    # Wait for Docker daemon, Python, and Node.js to be ready
    wait_for_condition \
        "docker exec '$CONTAINER_NAME' docker info && docker exec '$CONTAINER_NAME' python3 --version && docker exec '$CONTAINER_NAME' node --version" \
        120 \
        3 \
        "container services"
}

# =============================================================================
# TEST CASES
# =============================================================================

test_docker_functionality() {
    print_status "INFO" "Testing Docker functionality..."

    # Test Docker daemon
    if docker exec "$CONTAINER_NAME" docker info >/dev/null 2>&1; then
        local docker_version=$(docker exec "$CONTAINER_NAME" docker --version 2>/dev/null)
        print_status "PASS" "Docker daemon is working: $docker_version"
    else
        print_status "FAIL" "Docker daemon is not working" false
        return 1
    fi

    # Test Docker Compose
    if docker exec "$CONTAINER_NAME" docker compose version >/dev/null 2>&1; then
        local compose_version=$(docker exec "$CONTAINER_NAME" docker compose version 2>/dev/null | head -1)
        print_status "PASS" "Docker Compose is working: $compose_version"
    else
        print_status "FAIL" "Docker Compose is not working" false
        return 1
    fi

    # Test user can access Docker
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" docker ps >/dev/null 2>&1; then
        print_status "PASS" "User $TEST_USER can access Docker"
        return 0
    else
        print_status "FAIL" "User $TEST_USER cannot access Docker" false
        return 1
    fi
}

test_python_functionality() {
    print_status "INFO" "Testing Python functionality..."

    # Test Python 3
    if docker exec "$CONTAINER_NAME" python3 --version >/dev/null 2>&1; then
        local python_version=$(docker exec "$CONTAINER_NAME" python3 --version 2>/dev/null)
        print_status "PASS" "Python is working: $python_version"
    else
        print_status "FAIL" "Python is not working" false
        return 1
    fi

    # Test pip
    if docker exec "$CONTAINER_NAME" pip3 --version >/dev/null 2>&1; then
        local pip_version=$(docker exec "$CONTAINER_NAME" pip3 --version 2>/dev/null)
        print_status "PASS" "pip is working: $pip_version"
    else
        print_status "FAIL" "pip is not working" false
        return 1
    fi

    # Test pipenv
    if docker exec "$CONTAINER_NAME" pipenv --version >/dev/null 2>&1; then
        local pipenv_version=$(docker exec "$CONTAINER_NAME" pipenv --version 2>/dev/null)
        print_status "PASS" "pipenv is working: $pipenv_version"
    else
        print_status "FAIL" "pipenv is not working" false
        return 1
    fi

    # Test poetry
    if docker exec "$CONTAINER_NAME" poetry --version >/dev/null 2>&1; then
        local poetry_version=$(docker exec "$CONTAINER_NAME" poetry --version 2>/dev/null)
        print_status "PASS" "poetry is working: $poetry_version"
    else
        print_status "FAIL" "poetry is not working" false
        return 1
    fi

    # Test uv
    if docker exec "$CONTAINER_NAME" uv --version >/dev/null 2>&1; then
        local uv_version=$(docker exec "$CONTAINER_NAME" uv --version 2>/dev/null)
        print_status "PASS" "uv is working: $uv_version"
        return 0
    else
        print_status "FAIL" "uv is not working" false
        return 1
    fi
}

test_nodejs_functionality() {
    print_status "INFO" "Testing Node.js functionality..."

    # Test Node.js
    if docker exec "$CONTAINER_NAME" node --version >/dev/null 2>&1; then
        local node_version=$(docker exec "$CONTAINER_NAME" node --version 2>/dev/null)
        print_status "PASS" "Node.js is working: $node_version"
    else
        print_status "FAIL" "Node.js is not working" false
        return 1
    fi

    # Test npm
    if docker exec "$CONTAINER_NAME" npm --version >/dev/null 2>&1; then
        local npm_version=$(docker exec "$CONTAINER_NAME" npm --version 2>/dev/null)
        print_status "PASS" "npm is working: $npm_version"
    else
        print_status "FAIL" "npm is not working" false
        return 1
    fi

    # Test yarn
    if docker exec "$CONTAINER_NAME" yarn --version >/dev/null 2>&1; then
        local yarn_version=$(docker exec "$CONTAINER_NAME" yarn --version 2>/dev/null)
        print_status "PASS" "yarn is working: $yarn_version"
    else
        print_status "FAIL" "yarn is not working" false
        return 1
    fi

    # Test corepack (optional)
    if docker exec "$CONTAINER_NAME" corepack --version >/dev/null 2>&1; then
        local corepack_version=$(docker exec "$CONTAINER_NAME" corepack --version 2>/dev/null)
        print_status "PASS" "corepack is working: $corepack_version"
    else
        print_status "WARN" "corepack is not available (optional)"
    fi
    
    return 0
}

test_user_environment() {
    print_status "INFO" "Testing user environment..."

    # Test user exists
    if docker exec "$CONTAINER_NAME" id "$TEST_USER" >/dev/null 2>&1; then
        print_status "PASS" "User $TEST_USER exists"
    else
        print_status "FAIL" "User $TEST_USER does not exist" false
        return 1
    fi

    # Test user has sudo access
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" sudo -n true >/dev/null 2>&1; then
        print_status "PASS" "User $TEST_USER has sudo access"
    else
        print_status "FAIL" "User $TEST_USER does not have sudo access" false
        return 1
    fi

    # Test user can access workspace
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" ls /workspace >/dev/null 2>&1; then
        print_status "PASS" "User $TEST_USER can access workspace"
    else
        print_status "FAIL" "User $TEST_USER cannot access workspace" false
        return 1
    fi

    # Test user home directory
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" test -d "$TEST_HOME"; then
        print_status "PASS" "User home directory exists: $TEST_HOME"
        return 0
    else
        print_status "FAIL" "User home directory does not exist: $TEST_HOME" false
        return 1
    fi
}

test_package_installation() {
    print_status "INFO" "Testing package installation capabilities..."

    # Test Python package installation (using virtual environment)
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" bash -c "cd /tmp && python3 -m venv test_venv && source test_venv/bin/activate && pip install requests" >/dev/null 2>&1; then
        print_status "PASS" "Can install Python packages"
    else
        print_status "FAIL" "Cannot install Python packages" false
        return 1
    fi

    # Test Node.js package installation
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" sh -c "cd /tmp && npm init -y && npm install lodash" >/dev/null 2>&1; then
        print_status "PASS" "Can install Node.js packages"
    else
        print_status "FAIL" "Cannot install Node.js packages" false
        return 1
    fi

    # Test Docker container operations
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" docker run --rm hello-world >/dev/null 2>&1; then
        print_status "PASS" "Can run Docker containers"
        return 0
    else
        print_status "FAIL" "Cannot run Docker containers" false
        return 1
    fi
}

test_setup_script() {
    print_status "INFO" "Testing setup script execution..."

    # Run the setup script to install Task and other dependencies
    if exec_in_container "cd /workspace && ./setup.sh" >/dev/null 2>&1; then
        print_status "PASS" "Setup script executed successfully"
    else
        print_status "FAIL" "Setup script failed" false
        return 1
    fi

    return 0
}

test_task_installation() {
    print_status "INFO" "Testing Task (taskfile) installation..."

    # Test Task
    if docker exec "$CONTAINER_NAME" task --version >/dev/null 2>&1; then
        local task_version=$(docker exec "$CONTAINER_NAME" task --version 2>/dev/null)
        print_status "PASS" "Task is working: $task_version"
        return 0
    else
        print_status "FAIL" "Task is not working" false
        return 1
    fi
}

test_application_build() {
    print_status "INFO" "Testing application build with docker-compose..."

    # Build the application using Task
    print_status "INFO" "Building application containers..."
    if exec_in_container "cd /workspace && task build" >/dev/null 2>&1; then
        print_status "PASS" "Application containers built successfully"
    else
        print_status "FAIL" "Failed to build application containers" false
        return 1
    fi

    return 0
}

test_application_startup() {
    print_status "INFO" "Testing application startup with docker-compose..."

    # Start the application using Task
    print_status "INFO" "Starting application with task up..."
    if exec_in_container "cd /workspace && task up" >/dev/null 2>&1; then
        print_status "PASS" "Application started successfully"
    else
        print_status "FAIL" "Failed to start application" false
        return 1
    fi

    # Wait for services to be ready
    print_status "INFO" "Waiting for application services to be ready..."
    sleep 30

    # Check if containers are running
    if exec_in_container "cd /workspace && docker compose ps --format json" >/dev/null 2>&1; then
        local running_containers=$(exec_in_container "cd /workspace && docker compose ps --format json | jq -r '.State' | grep -c running" 2>/dev/null || echo "0")
        if [[ "$running_containers" -gt 0 ]]; then
            print_status "PASS" "Application containers are running ($running_containers containers)"
        else
            print_status "FAIL" "No application containers are running" false
            return 1
        fi
    else
        print_status "FAIL" "Failed to check container status" false
        return 1
    fi

    return 0
}

test_application_endpoints() {
    print_status "INFO" "Testing application endpoints..."

    # Test health endpoint
    if exec_in_container "curl -f http://localhost:80/up" >/dev/null 2>&1; then
        print_status "PASS" "Health endpoint is responding"
    else
        print_status "FAIL" "Health endpoint is not responding" false
        return 1
    fi

    # Test main application endpoint
    if exec_in_container "curl -f http://localhost:80" >/dev/null 2>&1; then
        print_status "PASS" "Application homepage is accessible"
    else
        print_status "FAIL" "Application homepage is not accessible" false
        return 1
    fi

    return 0
}

test_application_commands() {
    print_status "INFO" "Testing application commands through Task..."

    # Test artisan command through Task
    if exec_in_container "cd /workspace && task artisan -- --version" >/dev/null 2>&1; then
        local artisan_version=$(exec_in_container "cd /workspace && task artisan -- --version" 2>/dev/null)
        print_status "PASS" "Laravel Artisan is working through Task: $artisan_version"
    else
        print_status "FAIL" "Laravel Artisan is not working through Task" false
        return 1
    fi

    # Test composer command through Task
    if exec_in_container "cd /workspace && task composer -- --version" >/dev/null 2>&1; then
        local composer_version=$(exec_in_container "cd /workspace && task composer -- --version" 2>/dev/null)
        print_status "PASS" "Composer is working through Task: $composer_version"
    else
        print_status "FAIL" "Composer is not working through Task" false
        return 1
    fi

    return 0
}

test_application_cleanup() {
    print_status "INFO" "Testing application cleanup..."

    # Stop the application using Task
    if exec_in_container "cd /workspace && task down" >/dev/null 2>&1; then
        print_status "PASS" "Application stopped successfully"
    else
        print_status "FAIL" "Failed to stop application" false
        return 1
    fi

    return 0
}

# =============================================================================
# MAIN TEST EXECUTION
# =============================================================================

main() {
    # Initialize test suite
    init_test_suite "Sandbox Image Test Suite" "$CONTAINER_NAME"

    # Register cleanup
    register_cleanup cleanup_test_container

    # Show configuration
    log_info "Container name: $CONTAINER_NAME"
    log_info "Test user: $TEST_USER"
    log_info "Sandbox image: $SANDBOX_IMAGE"
    log_info "Project in container: $PROJECT_IN_CONTAINER"

    # Setup phase
    build_sandbox_image
    setup_test_container
    wait_for_container_ready

    # Test phase
    local failed_tests=0

    # Basic sandbox functionality tests
    run_test_case "Docker Functionality" test_docker_functionality || ((failed_tests++))
    run_test_case "Python Functionality" test_python_functionality || ((failed_tests++))
    run_test_case "Node.js Functionality" test_nodejs_functionality || ((failed_tests++))
    run_test_case "User Environment" test_user_environment || ((failed_tests++))
    run_test_case "Package Installation" test_package_installation || ((failed_tests++))
    
    # Application-specific tests
    run_test_case "Setup Script" test_setup_script || ((failed_tests++))
    run_test_case "Task Installation" test_task_installation || ((failed_tests++))
    run_test_case "Application Build" test_application_build || ((failed_tests++))
    run_test_case "Application Startup" test_application_startup || ((failed_tests++))
    run_test_case "Application Endpoints" test_application_endpoints || ((failed_tests++))
    run_test_case "Application Commands" test_application_commands || ((failed_tests++))
    run_test_case "Application Cleanup" test_application_cleanup || ((failed_tests++))

    # Complete test suite
    if [[ $failed_tests -eq 0 ]]; then
        complete_test_suite 0
    else
        log_error "$failed_tests test(s) failed"
        complete_test_suite 1
    fi
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Show usage
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    echo "Usage: $0 [--debug]"
    echo "Test sandbox image functionality and application deployment"
    echo ""
    echo "Basic Tests: Docker, Python, Node.js, User environment, Package installation"
    echo "Application Tests: Setup script, Task, Build, Startup, Endpoints, Commands, Cleanup"
    echo ""
    echo "Options:"
    echo "  --debug    Enable debug output"
    echo "  -h, --help Show this help message"
    exit 0
fi

# Enable debug mode if requested
if [[ "${1:-}" == "--debug" ]]; then
    export DEBUG=true
    print_status "DEBUG" "Debug mode enabled"
fi

# Run main function
main "$@"