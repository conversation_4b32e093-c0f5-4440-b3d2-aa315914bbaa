<?php

use App\Services\RulesService\ConfigResolver;
use Modules\Analytics\Priv\Entities\RequestAccountDimensionItem;
use Tests\ObjectMother\App\RequestMother;
use Tests\TestCase;

class ConfigResolverTest extends TestCase
{
    public function testResolveForNullableUserReturnsDefaultConfig()
    {
        $config = ['default' => 'value'];
        $resolver = new ConfigResolver($config, null);

        $this->assertSame($config, $resolver->resolve());
    }

    public function testResolveForMountedUser()
    {
        $config = [
            'grade-0' => ['key' => 'value'],
            'grade-1' => ['key' => 'otherValue']
        ];

        $request = RequestMother::aRequest();
        $user = $request->user;

        $expectedConfig = $config['grade-' . $user->grade];

        $resolver = new ConfigResolver($config, $user);
        $this->assertSame($expectedConfig, $resolver->resolve());
    }

    public function testResolveForMountedRequestAccountDimension()
    {
        $config = [
            'grade-0' => [
                'account_dimension-AD1-item-AD11' => ['auto_acceptance' => false],
                'account_dimension-AD2-item-AD122' => ['auto_acceptance' => false],
            ],
            'grade-3' => [
                'account_dimension-AD1-item-AD11' => ['auto_acceptance' => true],
                'account_dimension-AD2-item-AD122' => ['auto_acceptance' => true],
            ],
            'grade-4' => [
                'account_dimension-AD1-item-AD11' => ['auto_acceptance' => false],
                'account_dimension-AD2-item-AD122' => ['auto_acceptance' => true],
            ],
            'grade-5' => [
                'account_dimension-AD1-item-AD11' => ['auto_acceptance' => true],
                'account_dimension-AD2-item-AD122' => ['auto_acceptance' => false],
            ]
        ];

        $request = RequestMother::aRequest();
        $user = $request->user;

        $requestAccountDimensionItem = $request->accountDimensionItems->filter(
            function (RequestAccountDimensionItem $item) {
                return $item->accountDimension->code === 'AD1';
            }
        )->first();

        $expectedConfig = $config['grade-' . $user->grade][sprintf(
            'account_dimension-%s-item-%s',
            $requestAccountDimensionItem->accountDimension->code,
            $requestAccountDimensionItem->accountDimensionItem->code
        )];

        $resolver = new ConfigResolver($config, $user, $request);
        $this->assertSame($expectedConfig, $resolver->resolve());
    }

    public function testResolveForMountedRequestAccountDimensionWithoutPassing()
    {
        $config = [
            'grade-0' => [
                'account_dimension-AD3-item-AD33' => ['auto_acceptance' => false],
                'account_dimension-AD4-item-AD144' => ['auto_acceptance' => false],
            ],
            'grade-1' => [
                'account_dimension-AD3-item-AD33' => ['auto_acceptance' => true],
                'account_dimension-AD4-item-AD144' => ['auto_acceptance' => true],
            ],
            'grade-2' => [
                'account_dimension-AD3-item-AD33' => ['auto_acceptance' => false],
                'account_dimension-AD4-item-AD144' => ['auto_acceptance' => true],
            ],
            'grade-3' => [
                'account_dimension-AD3-item-AD33' => ['auto_acceptance' => true],
                'account_dimension-AD4-item-AD144' => ['auto_acceptance' => false],
            ]
        ];

        $request = RequestMother::aRequest();
        $user = $request->user;

        $resolver = new ConfigResolver($config, $user, $request);

        $this->assertSame([], $resolver->resolve());
    }
}
