<?php

declare(strict_types=1);

namespace Tests\Unit\App\Compliance\Instances\Amrest\Rules\WarningLocationAccommodationRule;

use App\Compliance\Instances\Amrest\Rules\WarningLocationAccommodationRule\Config;
use App\Vendors\PointLocation\Point;
use Tests\TestCase;

class ConfigTest extends TestCase
{
    private array $config;

    protected function setUp(): void
    {
        parent::setUp();

        $config = require $this->getDataPath() . 'rules-config/WarningLocationAccommodationRule.config.php';
        $this->config = $config['grade-1'];
    }

    /**
     * @dataProvider dataProviderKatowice
     */
    public function testAmountForPolandKatowice(int $numberOfRooms, float $expectedMaxAmount): void
    {
        $config = new Config(
            $this->config,
            'PL',
            $numberOfRooms,
            Point::create(50.061,22.049) // Katowice
        );

        self::assertEquals($expectedMaxAmount, $config->getMaxAmount());
    }

    public function testDefaultAmount(): void
    {
        $config = new Config(
            $this->config,
            'US',
            1,
            Point::create(0,0)
        );

        self::assertEquals(600, $config->getMaxAmount());
    }

    public function testDefaultAmountLocationOuterGroups(): void
    {
        $config = new Config(
            $this->config,
            'PL',
            1,
            Point::create(51.2464536,22.5684463) // Lublin
        );

        self::assertEquals(280, $config->getMaxAmount());
    }

    public function testDefaultMaxAmountForEuropeanCountry(): void
    {
        $config = new Config(
            $this->config,
            'FR',
            1,
            Point::create(0, 0)
        );

        self::assertEquals(436, $config->getMaxAmount());
    }

    public function dataProviderKatowice(): array
    {
        return [
            [1, 330.0],
            [2, 440.0],
            [5, 330.0],
        ];
    }
}
