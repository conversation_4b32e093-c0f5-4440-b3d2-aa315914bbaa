<?php

declare(strict_types=1);

namespace Tests\Unit\App\BillingAPI\Client\Services\Hotels;

use App\BillingAPI\Client\Services\Hotels\HotelRoomAllocationsCounter;
use InvalidArgumentException;
use Tests\TestCase;

class HotelRoomAllocationsCounterTest extends TestCase
{
    public function testRequiredKeys(): void
    {
        self::expectException(InvalidArgumentException::class);

        $validAllocation = new \stdClass();
        $validAllocation->guests = [new \stdClass(), new \stdClass()]; // correct example

        $invalidAllocation = new \stdClass(); // missing property guests, so should assign one guest

        new HotelRoomAllocationsCounter([$validAllocation, $invalidAllocation]);
    }

    /**
     * @dataProvider allocationProviders
     */
    public function testCountingRooms(array $allocation, int $expectedResultRooms, int $expectedResultGuests): void
    {
        $counter = new HotelRoomAllocationsCounter($allocation);

        self::assertEquals($expectedResultRooms, $counter->getCountedRooms());
        self::assertEquals($expectedResultGuests, $counter->getCountedGuests());
    }

    public function allocationProviders(): array
    {
        $oneRoomTwoGuests = [
            (object) ['guests' => [new \stdClass(), new \stdClass()]],
        ];
        $twoRoomsTwoGuests = [
            (object) ['guests' => [new \stdClass()]],
            (object) ['guests' => [new \stdClass()]],
        ];
        $twoRoomsOneGuest = [
            (object) ['guests' => [new \stdClass()]],
            (object) ['guests' => []],
        ];
        $oneRoomOneGuest = [
            (object) ['guests' => [new \stdClass()]],
        ];

        return [
            'one room, two guests' => [$oneRoomTwoGuests, 1, 2],
            'two rooms, two guests' => [$twoRoomsTwoGuests, 2, 2],
            'two rooms, one guest in one room' => [$twoRoomsOneGuest, 2, 1],
            'one room, one guest' => [$oneRoomOneGuest, 1, 1],
        ];
    }
}
