<?php

namespace Tests\Obt\Integrations\Marketplace\Extractor;

use Domain\Obt\Integrations\Marketplace\Extractor\AccommodationTimeExtractor;
use PHPUnit\Framework\TestCase;

class AccommodationTimeExtractorTest extends TestCase
{
    private AccommodationTimeExtractor $extractor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->extractor = new AccommodationTimeExtractor();
    }

    /**
     * @dataProvider remarksProvider
     */
    public function testGetCheckinTimeAndCheckoutTime(
        string $remarks,
        string $expectedCheckinTime,
        string $expectedCheckoutTime,
        string $expectedCheckInEndTime
    ): void {
        $checkinTime = $this->extractor->getCheckinTime($remarks);
        $checkinEndTime = $this->extractor->getCheckinEndTime($remarks);
        $checkoutTime = $this->extractor->getCheckoutTime($remarks);

        $this->assertEquals($expectedCheckinTime, $checkinTime);
        $this->assertEquals($expectedCheckoutTime, $checkoutTime);
        $this->assertEquals($expectedCheckInEndTime, $checkinEndTime);
    }

    public function remarksProvider(): array
    {
        return [
            '15:00 / 11:00' => [
                'Please note that check-in is from Check-in: 15:00, and check-out is until CheckOut Time: 11:00.',
                '15:00',
                '11:00',
                ''
            ],
            '14:30 / 10:00 AM' => [
                'Please be advised that the check-in time is CheckIn Time: 14:30, and check-out is at Check-out 10:00 AM.',
                '14:30',
                '10:00 AM',
                ''
            ],
            '13:00 / 10:30' => [
                'Important information: Check-in 13:00 and check-out is until CheckOut Time 10:30.',
                '13:00',
                '10:30',
                ''
            ],
            '12:00 - 15:00 / 11:00' => [
                'Make sure to arrive between Check-in 12:00 - 15:00. Check-out is at CheckOut Time 11:00.',
                '12:00 - 15:00',
                '11:00',
                ''
            ],
            '14:00 / 12:00' => [
                'Kindly remember that CheckIn Time-Begin: 14:00, CheckIn Time-End 20:00, CheckOut Time: 12:00.',
                '14:00',
                '12:00',
                '20:00'
            ],
            'withoutHours' => [
                'Please ensure to check in within the specified time frame. Breakfast is served daily from 7 a.m. until 10 a.m. Please note that pets are allowed with applicable charges.',
                '',
                '',
                ''
            ],

        ];
    }
}
