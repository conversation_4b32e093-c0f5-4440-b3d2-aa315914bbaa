<?php

declare(strict_types=1);

namespace Tests\unit\Modules\Users\Priv\Rules;

use Modules\Users\Priv\Rules\IbanRule;
use Tests\TestCase;

class IbanRuleTest extends TestCase
{
    public function testValidIbanWithCountryCode(): void
    {
        $rule = new IbanRule();

        $this->assertTrue(
            $rule->passes('iban', '****************************'),
            'Valid IBAN with country code should pass.'
        );
    }

    public function testValidIbanWithoutCountryCode(): void
    {
        $rule = new IbanRule('PL');

        $this->assertTrue(
            $rule->passes('iban', '61109010140000071219812874'),
            'Valid IBAN without country code should pass with default country.'
        );
    }

    public function testInvalidIbanWithCountryCode(): void
    {
        $rule = new IbanRule();

        $this->assertFalse(
            $rule->passes('iban', '****************************'),
            'Invalid IBAN with country code should fail.'
        );
    }

    public function testInvalidIbanWithoutCountryCode(): void
    {
        $rule = new IbanRule('PL');

        $this->assertFalse(
            $rule->passes('iban', '12345678901234567890123456'),
            'Invalid IBAN without country code should fail.'
        );
    }

    public function testCustomCountryCode(): void
    {
        $rule = new IbanRule('DE');

        $this->assertTrue(
            $rule->passes('iban', '**********************'),
            'Valid German IBAN without country code should pass with default country DE.'
        );
    }

    public function testInvalidCustomCountryCode(): void
    {
        $rule = new IbanRule('DE');

        $this->assertFalse(
            $rule->passes('iban', '12345678901234567890123456'),
            'Invalid IBAN without country code should fail for custom country code.'
        );
    }
}
