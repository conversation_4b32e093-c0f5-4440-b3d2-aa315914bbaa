<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\InvoiceOcr\Port;

use Mindento\Shared\InvoiceOcr\Port\RecognizeResult;
use Tests\TestCase;

class RecognizeResultTest extends TestCase
{
    /**
     * @dataProvider provideRecognizedData
     */
    public function test_recognize_result(array $data): void
    {
        $metadata = ['key' => 'value'];

        $result = RecognizeResult::createFromArray($data, $metadata);

        $this->assertEquals('FV123456', $result->number);
        $this->assertEquals('2022-01-01', $result->documentDate);
        $this->assertEquals('123.45', $result->totalAmount);
        $this->assertEquals('EUR', $result->currency);
        $this->assertEquals('**********', $result->supplierTaxId);
        $this->assertEquals('**********', $result->purchaserTaxId);
        $this->assertEquals(['key' => 'value'], $result->metadata);
    }

    public static function provideRecognizedData(): \Generator
    {
        yield [
            [
                'invoice_series' => 'FV',
                'invoice_number' => '123456',
                'issue_date' => '2022-01-01',
                'total_amount' => '123.45',
                'currency' => 'EUR',
                'supplier_tax_id' => '**********',
                'purchaser_tax_id' => '**********',
            ],
            ['key' => 'value'],
        ];

        yield [
            [
                'invoice_series' => 'FV',
                'invoice_number' => 'FV123456',
                'issue_date' => '2022-01-01',
                'total_amount' => '123.45',
                'currency' => 'EUR',
                'supplier_tax_id' => '**********',
                'purchaser_tax_id' => '**********',
            ],
            ['key' => 'value'],
        ];
    }
}
