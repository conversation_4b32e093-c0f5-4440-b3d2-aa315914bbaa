services:
  php:
    build:
      dockerfile: Dockerfile
      target: dev
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
      - '8383:8383'
    environment:
      XDEBUG_MODE: '${XDEBUG_MODE:-off}'
      XDEBUG_CONFIG: '${XDEBUG_CONFIG:-client_host=host.docker.internal}'
      IGNITION_LOCAL_SITES_PATH: '${PWD}'
    volumes:
      - '.:/var/www/html'
      - './docker/php/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini'

  postgres:
    image: postgres:17-alpine
    environment:
      POSTGRES_PASSWORD: salesto
      POSTGRES_DB: salesto_local
      POSTGRES_USER: salesto
    ports:
      - '${POSTGRES_PORT:-5432}:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data

  postgres-testing:
    image: postgres:17-alpine
    environment:
      POSTGRES_PASSWORD: salesto
      POSTGRES_DB: salesto_testing
      POSTGRES_USER: salesto
    ports:
      - '${POSTGRES_PORT:-5433}:5432'

  http:
    build:
      dockerfile: Dockerfile
      target: http-base
    volumes:
      - './public:/var/www/html/public'
    ports:
      - '${APP_PORT:-80}:80'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/up"]
      interval: 5s
      timeout: 10s
      start_period: 10s
      retries: 10
    depends_on:
      - php

volumes:
  postgres_data:
