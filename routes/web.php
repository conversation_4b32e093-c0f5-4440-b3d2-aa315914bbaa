<?php

Route::middleware(['web'])->get('/autologin/{user}', 'AuthController@autologin')->name('autologin');

Route::get('/storage/cache/{route}', 'File<PERSON>ontroller@thumbnail')->where('route', ".*");

Route::get('notification/test/{email}', 'NotificationController@test');
//Route::get('notification/view/{template}', 'EmailTesterController@renderMail');

Route::get('company/policy/{fileName}/token/{token}', 'CompanyController@companyPolicy');

Route::group(['middleware' => ['accept-by-token']], function () {
    // requests
    Route::get('request/{slug}/accept-by-token/{token}/{fallback_locale}', 'RequestController@acceptByToken');
    Route::post('request/{slug}/accept-by-token-save/{token}', 'RequestController@acceptByTokenSave')->name(
        'accept-by-token.save'
    );

    Route::get('request/{slug}/reject-by-token/{token}/{fallback_locale}', 'RequestController@rejectByToken');
    Route::post('request/{slug}/reject-by-token-save/{token}', 'RequestController@rejectByTokenSave')->name(
        'reject-by-token.save'
    );

    // requests slack/user token
    Route::get('request/{slug}/accept-by-user-token/{token}', 'RequestController@acceptByUserToken');
    Route::get('request/{slug}/reject-by-user-token/{token}', 'RequestController@rejectByUserToken');

    // settlements
    Route::get(
        'settlement/{slug}/accept-by-token/{token}/{fallback_locale}',
        'Request\SettlementController@acceptByToken'
    );
    Route::post(
        'settlement/{slug}/accept-by-token-save/{token}',
        'Request\SettlementController@acceptByTokenSave'
    )->name('settlement-accept-by-token.save');

    Route::get(
        'settlement/{slug}/reject-by-token/{token}/{fallback_locale}',
        'Request\SettlementController@rejectByToken'
    );
    Route::post(
        'settlement/{slug}/reject-by-token-save/{token}',
        'Request\SettlementController@rejectByTokenSave'
    )->name('settlement-reject-by-token.save');

    // settlement slack/user token
    Route::get('settlement/{slug}/accept-by-user-token/{token}', 'Request\SettlementController@acceptByUserToken');
    Route::get('settlement/{slug}/reject-by-user-token/{token}', 'Request\SettlementController@rejectByUserToken');
});
