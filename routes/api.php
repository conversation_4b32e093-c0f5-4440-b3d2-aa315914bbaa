<?php

Route::post('ocr/document-processed/{documentId}/{ocrDataMd5}', 'Ocr\DocumentProcessingController@documentProcessed');
Route::get('decision-processes/graph/{slug?}', 'DecisionProcessGraphController@getGraphData');
Route::get('decision-processes/test', 'DecisionProcessGraphController@test');
Route::get('decision-processes/graph/{slug?}', 'DecisionProcessGraphController@getGraphData');

Route::get('instances', 'InstanceController@index');

Route::get('auth/sso', 'AuthController@sso')->name('auth.sso');
Route::post('auth/sso-mobile-handle', 'AuthController@ssoMobileHandle')->name('auth.sso-handle-mobile');
Route::get('auth/sso-handle', 'AuthController@ssoHandle')->name('auth.sso-handle');

Route::group(['middleware' => ['auth.optional', 'auth.locale']], function () {
    Route::get('auth/instance', 'AuthController@instance')->name('auth.instance');
    Route::get('instance/ping', 'InstanceController@ping');
});

Route::group(['middleware' => ['auth:api', 'auth.locale', 'auth.instance', 'auth.loggedAs', 'auth.blockedUser']], function() {

    Route::group(['middleware' => ['auth.isAdminOrSuperAdmin']], function () {
        Route::get('instance/clear', 'InstanceController@clear');
    });

    Route::post('auth/reset-password', 'AuthController@resetPassword')->name('auth.resetPassword');
    Route::post('logs/error', 'LogController@error')->name('log.error');
    Route::get('auth/logout', 'AuthController@logout')->name('auth.logout');
    Route::get('auth/relogin/{slug}', 'AuthController@relogin')->name('auth.relogin');
    Route::get('auth/status', 'AuthController@status')->name('auth.status');
    Route::get('auth/login-as/{slug}/{type}', 'AuthController@loginAs')->name('auth.loginAs');
    Route::get('auth/refresh', 'AuthController@refresh')->name('auth.refresh');

    //Notifications
    Route::resource('notifications', 'NotificationController', ['only' => [
        'index'
    ]]);

    Route::resource('users', 'UserController', ['only' => [
        'index', 'update', 'destroy',
    ]]);

    Route::get('users/for-select', 'UserController@indexForSelect');

    Route::get('users/instance', 'UserController@currentInstance');

    Route::get('user/loggable-to', 'UserController@loggableTo');

    Route::resource('user-assistants', 'UserAssistantController', ['only' => [
        'index', 'store' , 'destroy'
    ]]);

    Route::resource('user-deputies', 'UserDeputyController', ['only' => [
        'index', 'store', 'destroy'
    ]]);

    Route::get('user/profile', 'AuthController@status')->name('user.profile');

    Route::get('user/{user_slug}/profile', 'UserController@profile');
    Route::post('user/{user_slug}/groups', 'UserController@groups');

    Route::post('user/profile/change-password', 'AuthController@changePassword')->name('auth.changePassword');
    Route::post('user/profile/change-pin-code', 'AuthController@changePinCode')->name('auth.changePinCode');

    Route::group(['middleware' => ['auth.loggedAsOverwrite']], function() {
        Route::get('available-languages', 'InstanceController@availableLanguages');
        Route::get('organizational-structure', 'InstanceController@organizationalStructure');

        //Instances
        Route::resource('instances', 'InstanceController', ['only' => [
            'store', 'destroy', 'update',
        ]]);

        //Users
        Route::get('users/acceptors', 'UserController@acceptors');
        Route::get('users/settlement-acceptors', 'UserController@settlementAcceptors');

        Route::post('users/{slug}/change-avatar', 'UserController@changeAvatar');
        Route::post('users/{slug}/blocking', 'UserController@userBlocking');

        Route::post('users/notifications/welcome', 'UserNotificationController@welcomeAll');
        Route::post('users/{slug}/notifications/welcome', 'UserNotificationController@welcomeSingle');

        //import users
        Route::post('users/import/parse', 'UserController@importParseUsersByFile');
        Route::post('users/import/process', 'UserController@importProcessUsers');

        //Currencies
        Route::resource('currencies', 'CurrencyController', ['only' => [
            'index',
        ]]);

        //Exchange rates
        Route::resource('{request_slug}/exchange-rates', 'ExchangeRateController', ['only' => [
            'index',
        ]]);

        //Levels
        Route::resource('levels', 'LevelController', ['only' => [
            'index',
        ]]);

        //Companies
        Route::resource('companies', 'CompanyController', ['only' => [
            'index', 'store', 'update', 'destroy',
        ]]);

        Route::resource('provider-disposables', '\Mindento\Provider\Disposable\Controller\ApiProviderController', ['only' => [
            'store', 'index'
        ]]);

        //Requests
        Route::get('requests/report', 'ReportController@getClaimsReport');
        Route::get('reports/cockpit/xls', 'ReportController@getCockpitReport');
        Route::get('requests/index-instance', 'RequestController@indexInstance');
        Route::get('requests/settlements', 'RequestController@settlements');
        Route::get('requests/settlements/export', 'RequestController@exportSettlements');
        Route::get('requests/{request_slug}/export', 'RequestController@export');
	    Route::post('requests/create-for', 'RequestController@createFor');
        Route::resource('requests', 'RequestController', ['only' => [
            'show', 'index', 'store', 'update', 'destroy'
        ]]);

        Route::post('requests/{request_slug}/account-dimension-items/{account_dimension_id}/{account_dimension_item_id}', 'RequestController@addAccountDimensionItem');
        Route::delete('requests/{request_slug}/account-dimension-items/{radi_id}', 'RequestController@removeAccountDimensionItem');

        Route::post('requests/{request_slug}/mileage-allowance/{milleage_allowance_id}/account-dimension-items/{account_dimension_id}/{account_dimension_item_id}', 'RequestMileageAllowanceController@addAccountDimensionItem');
        Route::delete('requests/{request_slug}/mileage-allowance/{milleage_allowance_id}/account-dimension-items/{rmadi_id}', 'RequestMileageAllowanceController@removeAccountDimensionItem');

        Route::get('requests/{request_slug}/account-dimensions/{visibility}', 'RequestController@accountDimensions')
            ->where('visibility', '(request_header|accounting)');

        Route::get('requests/{request_slug}/allowances/account-dimensions/{visibility}', 'RequestAccountingAllowanceController@accountDimensions')
            ->where('visibility', '(header)');

        Route::post('requests/{request_slug}/allowances/account-dimension-items/{account_dimension_id}/{account_dimension_item_id}', 'RequestAccountingAllowanceController@addAccountDimensionItem');
        Route::delete('requests/{request_slug}/allowances/account-dimension-items/{raadi_id}', 'RequestAccountingAllowanceController@removeAccountDimensionItem');

        Route::resource('requests/{request_slug}/accounting-mileage-allowance', 'RequestAccountingMileageAllowanceController', ['only' => [
            'store', 'update', 'destroy',
        ]]);
        Route::post('requests/{request_slug}/accounting-mileage-allowance/{accounting_mileage_allowance}/account-dimension-items/{account_dimension_id}/{account_dimension_item_id}', 'RequestAccountingMileageAllowanceController@addAccountDimensionItem');
        Route::delete('requests/{request_slug}/accounting-mileage-allowance/{accounting_mileage_allowance}/account-dimension-items/{rmasadi_id}', 'RequestAccountingMileageAllowanceController@removeAccountDimensionItem');


        Route::put('requests/{slug}/change-private/{private}', 'RequestController@changePrivate')
            ->middleware('feature_switcher.feature_enabled:FEATURE_PRIVATE_TRIP_REQUESTS_ENABLED');

        Route::get('requests/{slug}/comments', 'RequestController@comments');
        Route::get('requests/{slug}/accountingDocuments', 'RequestController@accountingDocuments');

        Route::get('index-dashboard/requests', 'RequestController@indexDashboard');
        Route::get('index-dashboard/counter', 'RequestController@dashboardCounter');
        Route::get('agent/requests', 'RequestController@indexAgent');

        Route::get('search', 'SearchController@search');

        Route::resource('requests/{request_slug}/border-crossings', 'RequestBorderCrossingController', ['only' => [
            'index', 'store', 'update', 'destroy'
        ]]);

        Route::put('requests/{request_slug}/change-deductions-widget-state', 'RequestBorderCrossingController@changeDeductionsWidgetState');
        Route::put('requests/{request_slug}/update-border-crossings-weights', 'RequestBorderCrossingController@updateWeights');

        Route::resource('requests/{request_slug}/meal-deductions', 'RequestMealDeductionController', ['only' => [
            'index', 'store', 'update', 'destroy',
        ]]);

        Route::resource('requests/{request_slug}/accommodation-drive-lump-sums', 'RequestAccommodationDriveLumpSumController', ['only' => [
            'index', 'store', 'update', 'destroy',
        ]]);

        Route::resource('requests/{request_slug}/accounting-travel-expenses', 'RequestAccountingTravelExpensesController', ['only' => [
            'index', 'store', 'update', 'destroy',
        ]]);

        Route::post('requests/{request_slug}/accounting-travel-expenses/{travel_expense_id}/account-dimension-items/{account_dimension_id}/{account_dimension_item_id}', 'RequestAccountingTravelExpensesController@addAccountDimensionItem');
        Route::delete('requests/{request_slug}/accounting-travel-expenses/{travel_expense_id}/account-dimension-items/{rateadi_id}', 'RequestAccountingTravelExpensesController@removeAccountDimensionItem');

        Route::put('requests/{request_slug}/re-generate-allowances-entries', 'RequestAccountingTravelExpensesController@reGenerateAllowancesEntries');

        Route::post('requests/{request_slug}/access-lump-sums', 'AccessLumpSumController@store');
        Route::put('requests/{request_slug}/access-lump-sums', 'AccessLumpSumController@update');
        Route::delete('requests/{request_slug}/access-lump-sums', 'AccessLumpSumController@destroy');

        Route::put('requests/{request_slug}/update-status', 'RequestController@updateStatus');
        Route::put('requests/{request_slug}/save-draft', 'RequestController@saveDraft');
        Route::get('requests/{request_slug}/create-similar', 'RequestController@createSimilarExpense');
        Route::get('requests/{request_slug}/create-similar/{date}', 'RequestController@createSimilarTrip');
        Route::put('requests/{request_slug}/assign-to-me', 'RequestController@assignToMe');

        Route::post('requests/{request_slug}/acceptors', 'RequestController@attachAcceptors');
        Route::delete('requests/{request_slug}/acceptors/{user_slug}', 'RequestController@detachAcceptors');

        Route::post('requests/{request_slug}/settlement-acceptors', 'RequestController@attachSettlementAcceptors');
        Route::delete('requests/{request_slug}/settlement-acceptors/{user_slug}', 'RequestController@detachSettlementAcceptors');

        Route::post('requests/{request_slug}/plane-trips', 'RequestController@createPlaneTrip');
        Route::put('requests/{request_slug}/plane-trips/{plane_trips_id}', 'RequestController@updatePlaneTrip');
        Route::delete('requests/{request_slug}/plane-trips/{plane_trips_id}', 'RequestController@deletePlaneTrip');

        Route::post('requests/{request_slug}/private-car-trips', 'RequestController@createPrivateCarTrip');
        Route::put('requests/{request_slug}/private-car-trips/{private_car_trip_id}', 'RequestController@updatePrivateCarTrip');
        Route::delete('requests/{request_slug}/private-car-trips/{private_car_trip_id}', 'RequestController@deletePrivateCarTrip');

        Route::post('requests/{request_slug}/company-car-trips', 'RequestController@createCompanyCarTrip');
        Route::put('requests/{request_slug}/company-car-trips/{company_car_trip_id}', 'RequestController@updateCompanyCarTrip');
        Route::delete('requests/{request_slug}/company-car-trips/{company_car_trip_id}', 'RequestController@deleteCompanyCarTrip');

        Route::post('requests/{request_slug}/replacement-car-trips', 'RequestController@createReplacementCarTrip');
        Route::put('requests/{request_slug}/replacement-car-trips/{replacement_car_trip_id}', 'RequestController@updateReplacementCarTrip');
        Route::delete('requests/{request_slug}/replacement-car-trips/{replacement_car_trip_id}', 'RequestController@deleteReplacementCarTrip');

        Route::post('requests/{request_slug}/passenger-car-trips', 'RequestController@createPassengerCarTrip');
        Route::put('requests/{request_slug}/passenger-car-trips/{passenger_car_trip_id}', 'RequestController@updatePassengerCarTrip');
        Route::delete('requests/{request_slug}/passenger-car-trips/{passenger_car_trip_id}', 'RequestController@deletePassengerCarTrip');

        Route::post('requests/{request_slug}/rented-car-trips', 'RequestController@createRentedCarTrip');
        Route::put('requests/{request_slug}/rented-car-trips/{rented_car_trip_id}', 'RequestController@updateRentedCarTrip');
        Route::delete('requests/{request_slug}/rented-car-trips/{rented_car_trip_id}', 'RequestController@deleteRentedCarTrip');

        Route::post('requests/{request_slug}/train-trips', 'RequestController@createTrainTrip');
        Route::put('requests/{request_slug}/train-trips/{train_trip_id}', 'RequestController@updateTrainTrip');
        Route::delete('requests/{request_slug}/train-trips/{train_trip_id}', 'RequestController@deleteTrainTrip');

        Route::post('requests/{request_slug}/ferry-boat-trips', 'RequestController@createFerryBoatTrip');
        Route::put('requests/{request_slug}/ferry-boat-trips/{ferry_boat_trip_id}', 'RequestController@updateFerryTripBoat');
        Route::delete('requests/{request_slug}/ferry-boat-trips/{ferry_boat_trip_id}', 'RequestController@deleteFerryBoatTrip');

        Route::post('requests/{request_slug}/bus-trips', 'RequestController@createBusTrip');
        Route::put('requests/{request_slug}/bus-trips/{bus_trip_id}', 'RequestController@updateBusTrip');
        Route::delete('requests/{request_slug}/bus-trips/{bus_trip_id}', 'RequestController@deleteBusTrip');

        Route::post('requests/{request_slug}/accomodations', 'RequestController@createAccomodation');
        Route::put('requests/{request_slug}/accomodations/{accomodation_id}', 'RequestController@updateAccomodation');
        Route::delete('requests/{request_slug}/accomodations/{accomodation_id}', 'RequestController@deleteAccomodation');

        Route::resource('requests/{request_slug}/provided-accommodations', 'ProvidedAccommodationController', ['only' => [
            'store', 'update', 'destroy'
        ]]);

        Route::resource('requests/{request}/private-accomodations', 'PrivateAccomodationController', ['only' => [
            'store', 'update', 'destroy'
        ]]);

        Route::get('{requestSlug}/private-accomodations/count-lump-sum/{start}/{end}/{countryCode}',
            'PrivateAccomodationController@countLumpSum');

        Route::get('requests/{request_slug}/costs', 'RequestController@indexCosts');
        Route::post('requests/{request_slug}/costs', 'RequestController@createCost');
        Route::delete('requests/{request_slug}/costs/{cost_id}', 'RequestController@deleteCost');
        Route::put('requests/{request_slug}/costs/{cost_id}', 'RequestController@updateCost');

        Route::resource('requests/{request}/target-points', 'TargetPointController', ['only' => [
            'store', 'update', 'destroy'
        ]]);

        Route::get('installments', 'InstallmentController@index');
        Route::post('installments/{id}/set-paid-date', 'InstallmentController@setPaidDate');
        Route::post('installments/{id}/set-paid-amount', 'InstallmentController@setPaidAmount');
        Route::post('installments/{id}/set-status', 'InstallmentController@setStatus');
        Route::post('installments/{id}/set-erp-id', 'InstallmentController@setErpId');

        Route::post('installments/{id}/set-accounting-date', 'InstallmentController@setAccountingDate');
        Route::post('installments/{id}/set-exchange-rate', 'InstallmentController@setExchangeRate');
        Route::post('installments/{id}/set-accounting-status', 'InstallmentController@setAccountingStatus');


        Route::resource('requests/{request}/installments', 'InstallmentController', ['only' => [
            'store', 'update', 'destroy'
        ]]);


        Route::get('requests/{request_slug}/meal-deductions-widget', 'RequestController@mealDeductionsWidget');
        Route::get('requests/{request_slug}/accommodation-drive-lump-sums-widget', 'RequestController@accommodationDriveLumpSumsWidget');

        Route::get('cost-types', 'RequestController@costTypeIndex');

        Route::get('requests/{request_slug}/combined-travel-elements', 'RequestController@combinedTravelElements');
        Route::get('requests/{request_slug}/all-request-elements', 'RequestController@allRequestElements');
        Route::get('requests/{request_slug}/request-elements-for-documents', 'RequestController@getRequestElementsForDocuments');
        Route::post('requests/send-unaccounted-reminder', 'RequestController@sendUnaccountedReminder');
        Route::put('requests/{slug}/update-timeline-weight', 'RequestController@updateTimelineWeight');

        Route::resource('requests/{request_slug}/mileage-allowances', 'RequestMileageAllowanceController', ['only' => [
            'index', 'update'
        ]]);

        //Groups
        Route::resource('groups', 'GroupController', ['only' => [
            'show', 'index', 'store', 'update', 'destroy'
        ]]);

        //Comments
        Route::resource('comments', 'CommentController', ['only' => [
            'index', 'store',
        ]]);

        //Documents
        Route::resource('documents', 'DocumentController', ['only' => [
            'index', 'show', 'store', 'update', 'destroy'
        ]]);

        Route::get('documents/{documentId}/account-dimensions/{visibility}', 'DocumentController@accountDimensions')
            ->where('visibility', '(header|accounting)');

        Route::post('/documents/{documentId}/account-dimension-items/{account_dimension_id}/{account_dimension_item_id}', 'DocumentController@addAccountDimensionItem');
        Route::delete('/documents/{documentId}/account-dimension-items/{dad_id}', 'DocumentController@removeAccountDimensionItem');

        //transactions
        Route::resource('transactions', 'TransactionController', ['only' => [
            'index'
        ]]);
        Route::get('transactions/get-types', 'TransactionController@getTypes');

        Route::put('/documents/{id}/account', 'DocumentController@account');

        Route::put('/documents/{id}/add-request-element', 'DocumentController@addRequestElement');
        Route::delete('/documents/{id}/add-request-element', 'DocumentController@removeRequestElement');

        //Countries
        Route::resource('countries', 'CountryController', ['only' => [
            'index', 'show',
        ]]);

        Route::post('notifications/read', 'NotificationController@read');

        //Document elements

        Route::resource('documents/{document}/elements', 'DocumentElementController', ['only' => [
            'index', 'store', 'update', 'destroy'
        ]]);

        Route::put('/documents/{document}/elements/{id}/account', 'DocumentElementController@account');
        Route::post('/documents/{documentId}/elements/{elementId}/account-dimension-items/{account_dimension_id}/{account_dimension_item_id}', 'DocumentElementController@addAccountDimensionItem');
        Route::delete('/documents/{documentId}/elements/{elementId}/account-dimension-items/{rdeadi_id}', 'DocumentElementController@removeAccountDimensionItem');

        Route::resource('documents/{document}/ocr-hints', 'OcrHintController', ['only' => [
            'index', 'update',
        ]]);

        Route::resource('documents/{document}/attachments', 'DocumentAttachmentController', ['only' => [
            'index', 'store',
        ]]);

        Route::resource('providers', 'ProviderController', ['only' => [
            'index', 'store', 'destroy', 'update'
        ]]);

        //LoyaltyCards
        Route::resource('users/{slug}/loyalty-cards', 'LoyaltyCardController', ['only' => [
            'index', 'store', 'update', 'destroy'
        ]]);

        //Cards
        Route::resource('cards', 'CardController', ['only' => [
            'index', 'store', 'destroy'
        ]]);

        Route::post('cards/update-cards-priority', 'CardController@updateCardsPriority');
        Route::get('cards/corporate', 'CardController@corporate');
        /** request business processes */

        // request acceptor actions
        Route::put('/request/{slug}/accept', 'RequestController@accept');
        Route::put('/request/{slug}/reject', 'RequestController@reject');
        Route::put('/request/{slug}/return-to-improvement', 'RequestController@returnToImprovement');
        //settlement acceptor actions
        Route::put('/request/{slug}/settlement-accept', 'RequestController@settlementAccept');
        Route::put('/request/{slug}/settlement-return-to-improvement', 'RequestController@settlementReturnToImprovement');

        //accountant actions
        Route::put('/request/{slug}/send-to-erp', 'RequestController@sendToERP');
        Route::put('/request/{slug}/accountant-return-to-improvement', 'RequestController@accountantReturnToImprovement');
        Route::put('/request/{slug}/accountant-return-to-decree', 'RequestController@accountantReturnToDecree');
        Route::delete('/settlement-accounting/{slug}/assignment-to-accountant', 'Request\SettlementAccountingController@assignmentToAccountant');

        //owner actions
        Route::put('/request/{slug}/cancel', 'RequestController@cancel');
        Route::put('/request/{slug}/change-status-to-settlement', 'RequestController@changeStatusToSettlement');
        Route::put('/request/{slug}/send-to-acceptance', 'RequestController@sendToAcceptance');
        Route::put('/request/{slug}/send-to-settlement-acceptance', 'RequestController@sendToSettlementAcceptance');
        Route::put('/request/{slug}/set-as-unrealized', 'RequestController@setAsUnrealized');
        Route::put('/request/{slug}/set-trip-did-not-have-place', 'RequestController@setTripDidNotHavePlace');

        Route::put('/send-multiple-to-erp', 'RequestController@sendMultipleToErp');

        //reservation and searching offers
        Route::get('train-stations-vocabulary', 'SpotsController@searchTrainSpots');
        Route::get('airports-vocabulary', 'SpotsController@searchPlaneSpots');
        Route::post('/request/{slug}/start-hotels-search', 'ReservationOffersController@startHotelsSearch');
        Route::post('research-hotels/{searchUuid}', 'ReservationOffersController@researchHotels');
        Route::get('/request/{slug}/start-flights-search', 'ReservationOffersController@startFlightsSearch');
        Route::get('/get-flights/{searchUuid}', 'ReservationOffersController@getFlights');
        Route::get('/research-flights/{searchUuid}', 'ReservationOffersController@researchFlights');
        Route::get('/get-return-flights/{searchUuid}/{offerUuid}', 'ReservationOffersController@getReturnFlights');
        Route::post('/request/{slug}/choose-offer', 'ReservationOffersController@chooseOffer');
        Route::get('/request/{slug}/get-offers', 'ReservationOffersController@getOffers');
	    Route::post('/request/{slug}/start-trains-search', 'ReservationOffersController@startTrainsSearch');
	    Route::post('/request/{slug}/book/{uuid}', 'ReservationOffersController@bookOffer');
	    Route::get('/request/{slug}/valuate/{uuid}', 'ReservationOffersController@valuate');
	    Route::post('/disable-searcher', 'ReservationOffersController@disableSearcher');
	    Route::get('/get-offers-by-search-uuid/{uuid}', 'ReservationOffersController@getOffersByUuid');
        Route::get('/request/{requestSlug}/cancel-single-booked-offer/{offerUuid}', 'ReservationOffersController@cancelSingleBookedOffer');
        Route::get('/request/{requestSlug}/get-single-booked-offer/{offerUuid}', 'ReservationOffersController@getSingleBookedOffer');
        Route::get('/rented-car/order-car-enum-values', 'RentedCarController@getEnumValues');

	    //validate accommodation lump sum
	    Route::get('requests/{request_slug}/accommodation-drive-lump-sums/{id}/validate', 'RequestAccommodationDriveLumpSumController@validate');
	    Route::get('requests/{request_slug}/border-crossings/pre-validate', 'RequestBorderCrossingController@validate');

        // Decision Process Graph API
        Route::get('decision-processes/test', 'DecisionProcessGraphController@test');
        Route::get('decision-processes/debug', 'DecisionProcessGraphController@debug');
        Route::get('decision-processes/graph/{slug?}', 'DecisionProcessGraphController@getGraphData');

        // Secure storage route
        Route::group(['prefix' => 'storage'], function () {
            Route::get('documents/{docId}/{fileType}/{width?}/{height?}', 'StorageController@getDocument');
            Route::get('avatars/{userSlug}/{fileName}/{fileType}/{width?}/{height?}', 'StorageController@getAvatar');
        });
    });
});
