<?php

declare(strict_types=1);

namespace Modules\MyCard\Pub\Facades;

use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Modules\MyCard\API\Client;

class TransactionGeneratorFacade
{
    private Client $client;

    public function __construct(Client $client)
    {
        $this->client = $client;
    }

    public function generate(string $documentSlug): ?string
    {
        try {
            $this->client->post('mycard/fake/transaction', [
                'document_reference' => $documentSlug,
                'number' => 3,
            ]);
            return null;

        } catch (ServerException | ClientException  $e) {
            return $e->getMessage();
        }
    }
}
