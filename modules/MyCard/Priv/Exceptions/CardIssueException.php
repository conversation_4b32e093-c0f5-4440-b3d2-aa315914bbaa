<?php

declare(strict_types=1);

namespace Modules\MyCard\Priv\Exceptions;

use Exception;

class CardIssueException extends Exception
{
    public static function userNotFound(int $userId): self
    {
        return new self(sprintf('User with ID (%s) not found.', $userId));
    }

    public static function cardholderNotFound(int $cardholderId): self
    {
        return new self(sprintf('Cardholder with ID (%s) not found.', $cardholderId));
    }

    public static function apiError(string $message): self
    {
        return new self(sprintf('MyCard API error: %s', $message));
    }

    public static function tenantIdDoesntExists(string $userSlug, string $companySlug): self
    {
        return new self(sprintf(
            'Can not issue card for user with slug (%s) and company (%s) because MyCardTenant was not set',
            $userSlug,
            $companySlug
        ));
    }
}
