<?php

declare(strict_types=1);

namespace Modules\MyCard\Priv\Http\Requests;

use App\Permission;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\MyCard\Priv\Enum\ValidityPeriod;

class CardIssueRequest extends FormRequest
{
    private const LIMIT_MIN = 0;
    private const LIMIT_MAX = 6000;

    public function rules(): array
    {
        return [
            'ids' => 'array|required',
            'limit' => [
                'required',
                'numeric',
                'min:' . self::LIMIT_MIN,
                'max:' . self::LIMIT_MAX
            ],
            'validity_period' => [
                'required',
                Rule::in(ValidityPeriod::toArray())
            ],
        ];
    }

    public function getUserIds(): array
    {
        return $this->input('ids', []);
    }

    public function getLimit(): int
    {
        return (int) $this->input('limit');
    }

    public function getValidityPeriod(): int
    {
        return (int) $this->input('validity_period');
    }

    public function authorize(): bool
    {
        return $this->user()->hasAbility(Permission::MYCARD_CARD_ISSUING);
    }
}
