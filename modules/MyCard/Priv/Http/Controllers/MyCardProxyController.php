<?php

declare(strict_types=1);

namespace Modules\MyCard\Priv\Http\Controllers;

use Illuminate\Http\Request;
use Modules\MyCard\API\Client;

class MyCardProxyController
{
    public function handle(Request $request, Client $myCardAPI)
    {
        // \App\Http\Middleware\DefaultInstanceParameterMiddleware
        $request->request->remove('instance_id');
        if (! $request->hasValidSignature(false)) {
//            abort(401);
        }

        $path = preg_replace('/^api\//', '', $request->path());
        $method = $request->method();

        return $myCardAPI->requestAndBuildResponse('/'.$path, $method);
    }
}
