<?php

declare(strict_types=1);

namespace Modules\MyCard\Priv\Listener;

use App\Events\Document\DocumentRemovedEvent;
use App\Repositories\DocumentRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\MyCard\Priv\Service\DocumentSyncService;

class DocumentRemovedListener implements ShouldQueue
{
    use InteractsWithQueue;

    private DocumentSyncService $documentSyncService;
    private DocumentRepository $documentRepository;

    public function __construct(DocumentSyncService $documentSyncService)
    {
        $this->documentSyncService = $documentSyncService;
    }

    public function handle(DocumentRemovedEvent $event): void
    {
        $this->documentSyncService->remove($event->getDocumentSlug(), $event->getRequestSlug());
    }

    public function tags(): array
    {
        return ['listener:DocumentRemovedListener'];
    }
}
