<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Providers\FxRates\Ecb\Dtos;

use Carbon\Carbon;
use Modules\ExchangeRate\Priv\Providers\FxRates\Shared\Dtos\TableBase;
use Modules\ExchangeRate\Priv\Providers\FxRates\Shared\Dtos\TableBaseInterface;

class EcbTable extends TableBase implements TableBaseInterface
{
    public function __construct(Carbon $effectiveDate)
    {
        parent::__construct($effectiveDate, collect());
    }
}
