<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Providers\FxRates\Mnb\Importers;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Common\Enums\SupportedCurrencyCodesEnum;
use Modules\ExchangeRate\Priv\Providers\FxRates\Mnb\Dtos\MnbRate;
use Modules\ExchangeRate\Priv\Providers\FxRates\Mnb\Dtos\MnbTable;
use Modules\ExchangeRate\Priv\Repositories\ExchangeRateMnbRepository;

class MnbImporter
{
    private const REQUEST_URL = 'http://www.mnb.hu/arfolyamok.asmx?wsdl';
    private ExchangeRateMnbRepository $exchangeRateMnbRepository;

    public function __construct(ExchangeRateMnbRepository $exchangeRateMnbRepository)
    {
        $this->exchangeRateMnbRepository = $exchangeRateMnbRepository;
    }

    public function run(Carbon $from, Carbon $to): void
    {
        $rates = $this->getExchangesRatesXML($from, $to);

        foreach ($rates->children() as $dayRates) {
            $this->saveExchangeRates(
                $dayRates,
                Carbon::parse((string)$dayRates->attributes()->date)
            );
        }
    }

    protected function getExchangesRatesXML(Carbon $from, Carbon $to): \SimpleXMLElement
    {
        $client = new \SoapClient(self::REQUEST_URL);

        $result = $client->GetExchangeRates(
            [
                'startDate' => $from->toDateString(),
                'endDate' => $to->toDateString(),
                'currencyNames' => implode(',', SupportedCurrencyCodesEnum::toArray())
            ]
        );

        return simplexml_load_string($result->GetExchangeRatesResult);
    }

    protected function saveExchangeRates(\SimpleXMLElement $xmlElement, Carbon $date): void
    {
        $rates = new Collection();

        $rates->push(
            new MnbRate(
                SupportedCurrencyCodesEnum::HUF()->getValue(),
                SupportedCurrencyCodesEnum::HUF()->getValue(),
                '1',
                '1',
            )
        );

        foreach ($xmlElement->children() as $rate) {
            $rates->push(
                new MnbRate(
                    $rate->attributes()->curr->__toString(),
                    $rate->attributes()->curr->__toString(),
                    str_replace(',', '.', $rate->__toString()),
                    $rate->attributes()->unit->__toString(),
                )
            );
        }

        $this->exchangeRateMnbRepository->saveTable(
            new MnbTable(
                $date,
                $rates
            )
        );
    }
}