<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Providers\FxRates\Shared\Dtos;

use Carbon\Carbon;
use Illuminate\Support\Collection;

interface TableBaseInterface
{
    public function getEffectiveDate(): Carbon;

    public function getRates(): Collection;

    public function getFilteredRates(Collection $codesToFilter): Collection;

    public function getCurrencies(): Collection;
}