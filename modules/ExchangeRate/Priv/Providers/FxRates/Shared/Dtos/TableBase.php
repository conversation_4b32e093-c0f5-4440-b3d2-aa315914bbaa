<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Providers\FxRates\Shared\Dtos;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\ExchangeRate\Pub\Dtos\Output\CurrencyDto;

abstract class TableBase implements TableBaseInterface
{
    protected Carbon $effectiveDate;

    protected Collection $rates;

    public function __construct(Carbon $effectiveDate, Collection $rates)
    {
        $this->effectiveDate = $effectiveDate;
        $this->rates = $rates;
    }

    public function getEffectiveDate(): Carbon
    {
        return $this->effectiveDate;
    }

    public function getRates(): Collection
    {
        return $this->rates;
    }

    public function getCurrencies(): Collection
    {
        return $this->rates
            ->map(
                fn(RateBaseInterface $rate) => new CurrencyDto(
                    $rate->getCode(),
                    $rate->getName()
                )
            )->keyBy('code');
    }

    public function getFilteredRates(Collection $codesToFilter): Collection
    {
        return $this->rates->filter(function (RateBaseInterface $rate) use ($codesToFilter) {
            return !$codesToFilter->contains($rate->getCode());
        });
    }
}