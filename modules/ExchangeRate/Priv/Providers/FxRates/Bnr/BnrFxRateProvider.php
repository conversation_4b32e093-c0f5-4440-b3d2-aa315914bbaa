<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Providers\FxRates\Bnr;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Common\Enums\SupportedCurrencyCodesEnum;
use Modules\Common\ValueObjects\CurrencyCode;
use Modules\ExchangeRate\Priv\Providers\FxRates\Bnr\Importers\BnrImporter;
use Modules\ExchangeRate\Priv\Providers\FxRates\FxRateProviderInterface;
use Modules\ExchangeRate\Priv\Repositories\ExchangeRateBnrRepository;
use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateDto;

class BnrFxRateProvider implements FxRateProviderInterface
{
    public const SLUG = 'bnr';

    private BnrImporter $bnrImporter;

    private ExchangeRateBnrRepository $exchangeRateBnrRepository;

    public function __construct(
        BnrImporter $bnrImporter,
        ExchangeRateBnrRepository $exchangeRateBnrRepository
    ) {
        $this->bnrImporter = $bnrImporter;
        $this->exchangeRateBnrRepository = $exchangeRateBnrRepository;
    }

    public function findLastAvailableExchangeRateForDate(CurrencyCode $currencyCode, Carbon $date): ?ExchangeRateDto
    {
        $exchangeRate = $this->exchangeRateBnrRepository->findLastAvailableExchangeRateForDate($currencyCode, $date);

        if ($exchangeRate === null) {
            return null;
        }

        return $exchangeRate->getExchangeRateDto();
    }

    public function findLastExchangeRate(CurrencyCode $currencyCode): ?ExchangeRateDto
    {
        $exchangeRate = $this->exchangeRateBnrRepository->getLastRateFromDB($currencyCode);

        if ($exchangeRate === null) {
            return null;
        }

        return $exchangeRate->getExchangeRateDto();
    }

    public function import(Carbon $from, Carbon $to): void
    {
        $this->bnrImporter->run($from, $to);
    }

    public function getAvailableCurrencies(): Collection
    {
        return new Collection([
            SupportedCurrencyCodesEnum::AUD(),
            SupportedCurrencyCodesEnum::BGN(),
            SupportedCurrencyCodesEnum::CAD(),
            SupportedCurrencyCodesEnum::CHF(),
            SupportedCurrencyCodesEnum::CZK(),
            SupportedCurrencyCodesEnum::DKK(),
            SupportedCurrencyCodesEnum::EGP(),
            SupportedCurrencyCodesEnum::EUR(),
            SupportedCurrencyCodesEnum::GBP(),
            SupportedCurrencyCodesEnum::HUF(),
            SupportedCurrencyCodesEnum::JPY(),
            SupportedCurrencyCodesEnum::MDL(),
            SupportedCurrencyCodesEnum::NOK(),
            SupportedCurrencyCodesEnum::PLN(),
            SupportedCurrencyCodesEnum::RUB(),
            SupportedCurrencyCodesEnum::SEK(),
            SupportedCurrencyCodesEnum::TRY(),
            SupportedCurrencyCodesEnum::USD(),
            SupportedCurrencyCodesEnum::ZAR(),
            SupportedCurrencyCodesEnum::BRL(),
            SupportedCurrencyCodesEnum::CNY(),
            SupportedCurrencyCodesEnum::INR(),
            SupportedCurrencyCodesEnum::KRW(),
            SupportedCurrencyCodesEnum::MXN(),
            SupportedCurrencyCodesEnum::NZD(),
            SupportedCurrencyCodesEnum::RSD(),
            SupportedCurrencyCodesEnum::UAH(),
            SupportedCurrencyCodesEnum::AED(),
            SupportedCurrencyCodesEnum::HRK(),
            SupportedCurrencyCodesEnum::THB(),
            SupportedCurrencyCodesEnum::XAF(),
            SupportedCurrencyCodesEnum::XDR(),
            SupportedCurrencyCodesEnum::RON(),
        ]);
    }

    /**
     * @throws \Exception
     */
    public function fetchAvailableCurrenciesFromProvider(): Collection
    {
        throw new \Exception('no need to be implemented');
    }
}