<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Services\Strategies\ForeignTravelAllowance;

use Carbon\Carbon;
use Modules\ExchangeRate\Priv\Services\Strategies\AbstractStrategy;
use Modules\ExchangeRate\Pub\Dtos\Input\ForeignTravelAllowanceDto;
use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateStrategyResultDto;

class ForeignTravelAllowanceRequestNbpCurrentRateStrategy
    extends AbstractStrategy
    implements ForeignTravelAllowanceExchangeRateStrategyInterface
{
    public const CODE = 'foreign_travel_allowance_request_nbp_current_rate_strategy';

    public function getExchangeRate(ForeignTravelAllowanceDto $foreignTravelAllowanceDto): ExchangeRateStrategyResultDto
    {
        $date = Carbon::now()->modify(sprintf('%s days', $this->getDayShiftFormatted()));
        $currencyCode = $foreignTravelAllowanceDto->getCurrencyCode();
        $exchangeRate = $this->fxRateService->findLastAvailableExchangeRateForDate($foreignTravelAllowanceDto->getRequestDto()->getInstanceDto()->getExchangeRateProviderSlug(), $currencyCode, $date);

        return ExchangeRateStrategyResultDto::createFromExchangeRateDto($exchangeRate, get_class($this), false);
    }
}
