<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Services\Strategies\ForeignTravelAllowanceEstimation;

use App\Request;
use Carbon\Carbon;
use Modules\Common\ValueObjects\RequestStatus;
use Modules\ExchangeRate\Priv\Services\Strategies\AbstractStrategy;
use Modules\ExchangeRate\Pub\Dtos\Input\ForeignTravelAllowanceEstimationDto;
use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateDto;
use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateStrategyResultDto;

class ForeignTravelAllowanceRequestEstimationNbpCurrentRateStrategy
    extends AbstractStrategy
    implements ForeignTravelAllowanceEstimationExchangeRateStrategyInterface
{
    public function getExchangeRate(ForeignTravelAllowanceEstimationDto $estimationDto): ExchangeRateStrategyResultDto
    {
        if ($estimationDto->getRequestDto()->getStatus()->isEqual(new RequestStatus(Request::STATUS_DRAFT))) {
            $date = Carbon::now();
        } else {
            $date = $estimationDto->getRequestDto()->getSendAtDate() instanceof Carbon
                ? $estimationDto->getRequestDto()->getSendAtDate()
                : $estimationDto->getRequestDto()->getApplicatedAtDate();
        }

        $date = $date->modify(sprintf('%s days', $this->getDayShiftFormatted()));

        $currencyCode = $estimationDto->getCurrencyCode();
        $exchangeRateDto = $this->fxRateService->findLastAvailableExchangeRateForDate($estimationDto->getRequestDto()->getInstanceDto()->getExchangeRateProviderSlug(), $currencyCode, $date);

        if($exchangeRateDto === nulL) {
            $exchangeRateDto = new ExchangeRateDto(
                $currencyCode,
                $date,
                "1.00000000"
            );
        }

        return ExchangeRateStrategyResultDto::createFromExchangeRateDto($exchangeRateDto, get_class($this), false);
    }
}
