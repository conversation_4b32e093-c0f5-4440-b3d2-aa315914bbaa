<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Common\ValueObjects\CurrencyCode;
use Modules\Common\ValueObjects\ExchangeRateProviderSlug;
use Modules\ExchangeRate\Priv\Factories\FxRateProviderFactory;
use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateDto;

class FxRateService
{
    protected FxRateProviderFactory $fxRateProviderFactory;

    public function __construct(FxRateProviderFactory $fxRateProviderFactory)
    {
        $this->fxRateProviderFactory = $fxRateProviderFactory;
    }

    public function findLastAvailableExchangeRateForDate(ExchangeRateProviderSlug $exchangeRateProviderSlug, CurrencyCode $currencyCode, Carbon $date): ?ExchangeRateDto
    {
        static $cache = [];
        $key = implode('-', [$exchangeRateProviderSlug, $currencyCode, $date->toDateString()]);

        if (isset($cache[$key])) {
            return $cache[$key];
        }

        return $cache[$key] = $this->fxRateProviderFactory->create($exchangeRateProviderSlug)->findLastAvailableExchangeRateForDate($currencyCode, $date);
    }

    public function findLastExchangeRate(ExchangeRateProviderSlug $exchangeRateProviderSlug, CurrencyCode $currencyCode): ?ExchangeRateDto
    {
        return $this->fxRateProviderFactory->create($exchangeRateProviderSlug)->findLastExchangeRate($currencyCode);
    }

    public function getAvailableCurrencies(ExchangeRateProviderSlug $exchangeRateProviderSlug): Collection
    {
        return $this->fxRateProviderFactory->create($exchangeRateProviderSlug)->getAvailableCurrencies();
    }

    public function fetchAvailableCurrenciesFromProvider(ExchangeRateProviderSlug $exchangeRateProviderSlug): Collection
    {
        return $this->fxRateProviderFactory->create($exchangeRateProviderSlug)->fetchAvailableCurrenciesFromProvider();
    }
}
