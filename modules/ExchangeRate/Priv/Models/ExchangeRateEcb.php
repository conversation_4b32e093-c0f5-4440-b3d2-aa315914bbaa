<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Models;

use App\Currency;
use Carbon\Carbon;

/**
 * @property Carbon effective_date
 * @property string rate
 * @property Currency currency
 */
class ExchangeRateEcb extends ExchangeRateBase implements ExchangeRateInterface
{
    protected $table = 'exchange_rates_ecb';

    protected $fillable = ['currency_id', 'rate', 'effective_date'];

    protected $hidden = [];

    public function getRateAttribute($rate): string
    {
        return $rate;
    }
}
