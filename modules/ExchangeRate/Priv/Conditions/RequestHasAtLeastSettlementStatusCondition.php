<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Conditions;

use App\Request;
use Modules\DecisionMaker\Pub\DataSets\DataSetInterface;
use Modules\DecisionMaker\Pub\Interfaces\ConditionInterface;
use Modules\ExchangeRate\Pub\Dtos\Internal\RequestDto;

class RequestHasAtLeastSettlementStatusCondition implements ConditionInterface
{
    use ExtractRequestDtoTrait;

    public const CODE = 'request_has_at_least_settlement_status';

    public function check(DataSetInterface $data): bool
    {
        /** @var RequestDto $requestDto */
        $requestDto = $this->extractRequestDto($data);

        return in_array((string)$requestDto->getStatus(), [
            Request::STATUS_SETTLEMENT,
            Request::STATUS_ACCEPTANCE_OF_SETTLEMENT,
            Request::STATUS_ACCOUNTING,
            Request::STATUS_TRANSFER_ERROR,
            Request::STATUS_TRANSFERRED,
            Request::STATUS_FINISH,
        ]);
    }
}