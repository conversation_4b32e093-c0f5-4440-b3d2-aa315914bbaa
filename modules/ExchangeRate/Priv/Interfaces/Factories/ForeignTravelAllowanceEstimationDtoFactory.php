<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Interfaces\Factories;

use App\Currency;
use App\Request;
use App\User;
use Modules\ExchangeRate\Pub\Dtos\Input\ForeignTravelAllowanceEstimationDto;

interface ForeignTravelAllowanceEstimationDtoFactory
{
    public function createFromRequestUserAndUser(
        Request $request,
        User $user,
        Currency $currency
    ): ForeignTravelAllowanceEstimationDto;
}
