<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Interfaces\Service;

use Modules\ExchangeRate\Pub\Dtos\Input\DocumentDto;
use Modules\ExchangeRate\Pub\Dtos\Input\ForeignTravelAllowanceDto;
use Modules\ExchangeRate\Pub\Dtos\Input\ForeignTravelAllowanceEstimationDto;
use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateStrategyResultDto;

interface ExchangeRateService
{
    public function getForDocument(DocumentDto $documentDto): ExchangeRateStrategyResultDto;
    public function getForForeignTravelAllowance(ForeignTravelAllowanceDto $foreignTravelAllowanceDto): ExchangeRateStrategyResultDto;
    public function getForRequestEstimation(ForeignTravelAllowanceEstimationDto $estimationDto): ExchangeRateStrategyResultDto;
}