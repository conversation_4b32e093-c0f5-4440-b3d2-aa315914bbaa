<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Decisions\ForeignTravelAllowance;

use Illuminate\Support\Facades\App;
use Modules\DecisionMaker\Pub\Interfaces\DecisionInterface;
use Modules\ExchangeRate\Priv\Decisions\AbstractExchangeRateDecision;
use Modules\ExchangeRate\Priv\Services\Strategies\ForeignTravelAllowance\ForeignTravelAllowanceRequestTripEndRateStrategy;

class ForeignTravelAllowanceRequestTripEndRateDecision
    extends AbstractExchangeRateDecision
    implements DecisionInterface, ForeignTravelAllowanceDecisionInterface
{
    public const CODE = 'ForeignTravelAllowanceRequestTripEndRateDecision';

    public function execute(array $params = [])
    {
        return App::makeWith(
            ForeignTravelAllowanceRequestTripEndRateStrategy::class,
            [
                static::DAY_SHIFT                => $this->dayShift
            ]
        );
    }
}
