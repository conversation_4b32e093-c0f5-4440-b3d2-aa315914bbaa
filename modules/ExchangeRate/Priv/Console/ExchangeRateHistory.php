<?php

namespace Modules\ExchangeRate\Priv\Console;

use Carbon\Carbon;

class ExchangeRateHistory extends CurrentExchangeRate
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exchangerate:history {--provider=} {--S|start=} {--E|end=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Pobiera kursy walut dla podanego przedziału dat';

    protected function getStartDate(): Carbon
    {
        if($this->option('start') === null) {
            return Carbon::today()->subDays(90);
        }

        return Carbon::createFromFormat('Y-m-d', $this->option('start'));
    }

    /**
     * @inheritDoc
     */
    protected function getEndDate(): Carbon
    {
        if($this->option('end') === null) {
            return Carbon::now();
        }

        return Carbon::createFromFormat('Y-m-d', $this->option('end'));
    }
}
