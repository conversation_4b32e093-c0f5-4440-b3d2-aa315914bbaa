<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Pub\Dtos\Internal\Installment;

use Carbon\Carbon;
use Modules\Common\ValueObjects\CurrencyCode;
use Modules\Common\ValueObjects\Rate;

class InstallmentDto
{
    /** @var CurrencyCode */
    protected $currencyCode;

    /** @var Rate */
    protected $rate;

    /** @var Carbon|null */
    protected $effectiveDate;

    /** @var bool */
    protected $paid;

    public function __construct(string $currencyCode, string $rate, ?Carbon $effectiveDate, bool $paid)
    {
        $this->currencyCode = new CurrencyCode($currencyCode);
        $this->rate = new Rate($rate);
        $this->effectiveDate = $effectiveDate;
        $this->paid = $paid;
    }

    public function getCurrencyCode(): CurrencyCode
    {
        return $this->currencyCode;
    }

    public function getRate(): Rate
    {
        return $this->rate;
    }

    public function getEffectiveDate(): ?Carbon
    {
        return $this->effectiveDate;
    }

    public function isPaid(): bool
    {
        return $this->paid;
    }
}
