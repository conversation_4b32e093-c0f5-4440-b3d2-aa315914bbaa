<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Pub\Dtos\Input;

use Carbon\Carbon;
use Modules\Common\ValueObjects\CurrencyCode;
use Modules\Common\ValueObjects\PaymentMethod;
use Modules\Common\ValueObjects\Slug;
use Modules\ExchangeRate\Pub\Dtos\Internal\RequestDto;
use Modules\ExchangeRate\Pub\Dtos\Internal\UserDto;
use Modules\ExchangeRate\Pub\Dtos\UniqueDtoInterface;

class DocumentDto implements UniqueDtoInterface
{
    /** @var Slug */
    protected $slug;

    /** @var PaymentMethod|null */
    protected $paymentMethod;

    /** @var CurrencyCode */
    protected $currencyCode;

    /** @var RequestDto */
    protected $requestDto;

    /** @var UserDto */
    protected $userDto;

    /** @var Carbon|null*/
    protected $createdAtDate;

    /** @var Carbon|null */
    protected $issuedAtDate;


    public function __construct(
        string $slug,
        ?string $paymentMethod,
        string $currencyCode,
        RequestDto $requestDto,
        UserDto $userDto,
        ?Carbon $createdAtDate = null,
        ?Carbon $issuedAtDate = null
    ) {
        $this->slug = new Slug($slug);
        $this->paymentMethod = is_string($paymentMethod) === true ? new PaymentMethod($paymentMethod) : null;
        $this->currencyCode = new CurrencyCode($currencyCode);
        $this->requestDto = $requestDto;
        $this->userDto = $userDto;
        $this->createdAtDate = $createdAtDate;
        $this->issuedAtDate = $issuedAtDate;
    }

    public function getSlug(): Slug
    {
        return $this->slug;
    }

    public function getPaymentMethod(): ?PaymentMethod
    {
        return $this->paymentMethod;
    }

    public function getCurrencyCode(): CurrencyCode
    {
        return $this->currencyCode;
    }

    public function getRequestDto(): RequestDto
    {
        return $this->requestDto;
    }

    public function getUserDto(): UserDto
    {
        return $this->userDto;
    }

    public function getCreatedAtDate(): ?Carbon
    {
        return $this->createdAtDate;
    }

    public function getIssuedAtDate(): ?Carbon
    {
        return $this->issuedAtDate;
    }

    public function getMd5(): string
    {
        return md5(serialize($this));
    }
}
