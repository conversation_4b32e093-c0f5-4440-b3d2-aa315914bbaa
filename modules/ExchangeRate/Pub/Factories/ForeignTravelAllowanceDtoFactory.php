<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Pub\Factories;

use App\Currency;
use App\Installment;
use App\Interfaces\ExchangeRateRequestStrategyInterface;
use App\Repositories\RequestRepository;
use App\Request;
use App\User;
use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Enums\SupportedCurrencyCodesEnum;
use Modules\Common\Exceptions\GeneralInconsistencyException;
use Modules\ExchangeRate\Pub\Dtos\Input\ForeignTravelAllowanceDto;
use Modules\ExchangeRate\Pub\Dtos\Internal\Installment\InstallmentDto;
use Modules\ExchangeRate\Pub\Dtos\Internal\Installment\InstallmentsCollectionDto;
use Modules\ExchangeRate\Pub\Dtos\Internal\InstanceDto;
use Modules\ExchangeRate\Pub\Dtos\Internal\RequestDto;
use Modules\ExchangeRate\Pub\Dtos\Internal\UserDto;
use Modules\ExchangeRate\Pub\Interfaces\Factories\ForeignTravelAllowanceDtoFactory as ForeignTravelAllowanceDtoFactoryInterface;

class ForeignTravelAllowanceDtoFactory implements ForeignTravelAllowanceDtoFactoryInterface
{
    public function createFromForeignTravelAllowanceAndUser(
        Request $request,
        string $allowanceName,
        string $currencyCode
    ): ForeignTravelAllowanceDto {

        $instanceCurrencyCode = $request->instance->currency->code;
        return new ForeignTravelAllowanceDto(
            $allowanceName,
            $currencyCode,
            new RequestDto(
                $request->slug,
                $request->status,
                $request->type,
                new InstallmentsCollectionDto(
                    $request->installments instanceof Collection
                    && $request->installments->isNotEmpty()
                        ? $request->installments->toBase()->transform(function (Installment $installment) use ($instanceCurrencyCode) {
                        $exchangeRate = $installment->currency->code === $instanceCurrencyCode ? Currency::DEFAULT_CURRENCY_EXCHANGE_RATE : $installment->exchange_rate;
                        return new InstallmentDto(
                                $installment->currency instanceof Currency
                                    ? $installment->currency->code
                                    : (string)SupportedCurrencyCodesEnum::PLN(),
                                $exchangeRate,
                                $installment->date,
                                $installment->isPaid()
                            );
                        })
                        ->toArray()
                        : []
                ),
                new InstanceDto($request->instance->slug, $request->instance->exchange_rate_provider),
                $request->trip_ends,
                $request->settled_at,
                $request->sent_at,
                $request->applicated_at
            ),
            new UserDto($request->user->slug, $request->user->isAccountant())
        );
    }
}