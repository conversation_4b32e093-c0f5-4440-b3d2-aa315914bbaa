<?php

namespace Modules\Report\Priv\Provider;

use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    protected $namespace = 'Modules\Report\Priv\Http\Controller';

    public function map(): void
    {
        $this->mapApiRoutes();
    }

    protected function mapApiRoutes(): void
    {
        Route::prefix('api/reports')
            ->middleware('api')
            ->namespace($this->namespace)
            ->group(__DIR__ . '/../Route/api.php');
    }
}
