<?php

namespace Modules\Report\Priv\Generator;

use App\Services\SlugGeneratorService;
use App\Services\Storage\StorageService;
use App\Services\Storage\StorageServiceDto;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;
use Modules\IntegrationFILE\Priv\Services\Exporters\ExcelArrayExporter;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;

class FileGenerator
{
    private const XLS_CONTENT_TYPE = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet';

    private SlugGeneratorService $slugGeneratorService;

    public function __construct(SlugGeneratorService $slugGeneratorService)
    {
        $this->slugGeneratorService = $slugGeneratorService;
    }

    public function toExcel(array $headers, array $content): StorageServiceDto
    {
        $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::XLSX());

        $filePath = sprintf('public/exports/%s', $fileName);
        $fullFilePath = sprintf('%s/storage/app/public/exports/%s', base_path(), $fileName);

        Excel::store(new ExcelArrayExporter(array_merge([$headers], $content)), $filePath);

        $storageServiceDTO = new StorageServiceDto(
            $fileName,
            sprintf(StorageService::FILE_TYPE_TEMPLATE, self::XLS_CONTENT_TYPE),
            File::get($fullFilePath),
        );

        File::delete($fullFilePath);

        return $storageServiceDTO;
    }
}