<?php

namespace Modules\Report\Priv\Factory;

use Modules\Report\Priv\Transformer\Output\OutputDataTransformerInterface;
use RuntimeException;

class OutputReportDataTransformerFactory
{
    public function make(string $transformerClass): OutputDataTransformerInterface
    {
        if (!class_exists($transformerClass)) {
            throw new RuntimeException("Transformer class {$transformerClass} does not exist.");
        }

        return app($transformerClass);
    }
}