<?php

namespace Modules\Report\Priv\Transformer\Input\Expense;

use App\DocumentElement;
use App\Instance;
use Modules\Analytics\Priv\Services\AccountDimensionService;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class DocumentElementAccountDimensionsTransformer
{
    private array $accountDimensions;

    private AccountDimensionService $accountDimensionService;

    public function __construct(AccountDimensionService $accountDimensionService) {
        $this->accountDimensionService = $accountDimensionService;
    }

    public function transform(DocumentElement $documentElement): array
    {
        $documentElementAccountDimensionItems = [];

        foreach ($documentElement->accountDimensionItems as $accountDimensionItem) {
            $documentElementAccountDimensionItems[$accountDimensionItem->accountDimension->id] = $accountDimensionItem->accountDimensionItem->name;
        }

        $transformedDocumentElement = [];

        foreach (
            $this->getAccountDimensions($documentElement->instance) as $accountDimensionId => $accountDimensionName
        ) {
            $transformedDocumentElement[$accountDimensionName] = $documentElementAccountDimensionItems[$accountDimensionId] ?? '';
        }

        return [$transformedDocumentElement];
    }

    private function getAccountDimensions(Instance $instance): array
    {
        if (!empty($this->accountDimensions[$instance->id])) {
            return $this->accountDimensions[$instance->id];
        }

        $accountDimensions = $this->accountDimensionService->findAllActiveForInstance(
            $instance,
            [AccountDimensionVisibilityEnum::ACCOUNTING(), AccountDimensionVisibilityEnum::GENERAL()],
        );

        $preparedAccountDimensions = [];
        foreach ($accountDimensions as $accountDimension) {
            $preparedAccountDimensions[$accountDimension->id] = $this->capitalizeWords($accountDimension->name);
        }

        return $this->accountDimensions[$instance->id] = $preparedAccountDimensions;
    }

    public function capitalizeWords(string $sentence): string
    {
        $delimiters = ['-', '.'];

        foreach ($delimiters as $delimiter) {
            $sentence = implode(
                ' ',
                collect(explode($delimiter, $sentence))->transform(
                    function (string $word) {
                        return ucfirst($word);
                    },
                )->toArray(),
            );
        }

        return $sentence;
    }
}