<?php

namespace Modules\Report\Priv\Transformer\Input\RequestTrip;

use App\Accomodation;
use App\Document;
use App\Request;
use Illuminate\Support\Collection;

class AccommodationCollectionTransformer
{
    public function transform(Request $request): Collection
    {
        return $request->accomodations->map(function (Accomodation $accommodation) {
            $offer = $accommodation->offer;

            if ($offer === null) {
                return null;
            }

            $fullOffer = $offer->full_offer;
            $accommodationLocation = $accommodation->location;

            if (empty($fullOffer)) {
                return null;
            }

            /** @var Document $invoice */
            $invoice = $accommodation->documents->filter(
                fn(Document $document) => $document->self_generated && $document->readonly
            )->first();

            if ($invoice === null) {
                return null;
            }

            $arrivalAndDepartureSecondsDiff = $accommodation->arrival_at->diffInSeconds($accommodation->departure_at);
            $arrivalAndDepartureDaysDiff = ceil($arrivalAndDepartureSecondsDiff / (24 * 60 * 60));
            $arrivalAndDepartureDaysDiff = $arrivalAndDepartureDaysDiff <= 0 ? 1 : $arrivalAndDepartureDaysDiff;

            return [
                'Invoice number' => $invoice->document_number,
                'Cost gross' => $invoice->gross,
                'Hotel standard' => $fullOffer[0]['attributes']['stars'] ?? '',
                'Hotel name' => $fullOffer[0]['attributes']['name'] ?? '',
                'Start Date' => $accommodation->arrival_at->format('Y-m-d'),
                'End Date' => $accommodation->departure_at->format('Y-m-d'),
                'City (hotel)' => $accommodationLocation->city,
                'Country (hotel)' => $accommodationLocation->country,
                'Number of rooms' => $accommodation->number_of_rooms,
                'Parking' => $accommodation->parking ? 'Yes' : 'No',
                'Breakfest included (yes/no)' => $accommodation->breakfast ? 'Yes' : 'No',
                'Number of nights' => $arrivalAndDepartureDaysDiff,
                'Average room night price (excluding additional services)' =>
                    $invoice->gross
                    / $arrivalAndDepartureDaysDiff
                    / ($accommodation->number_of_rooms <= 0 ? 1 : $accommodation->number_of_rooms),
                'Average price per person night (excluding additional services)' =>
                    $invoice->gross
                    / $arrivalAndDepartureDaysDiff
                    / $accommodation->request->travelers->count(),
            ];
        })->filter();
    }
}