<?php

namespace Modules\Report\Priv\Transformer\Input\RequestTrip;

use App\Document;
use App\Request;
use Illuminate\Database\Eloquent\Model;
use Modules\Report\Priv\Transformer\Input\InputDataTransformerInterface;

class RequestTripTransformer implements InputDataTransformerInterface
{
    private AccommodationCollectionTransformer $accommodationCollectionTransformer;
    private PlaneTripCollectionTransformer $planeTripCollectionTransformer;
    private TrainTripCollectionTransformer $trainTripCollectionTransformer;

    public function __construct(
        AccommodationCollectionTransformer $accommodationCollectionTransformer,
        PlaneTripCollectionTransformer $planeTripCollectionTransformer,
        TrainTripCollectionTransformer $trainTripCollectionTransformer
    ) {
        $this->accommodationCollectionTransformer = $accommodationCollectionTransformer;
        $this->planeTripCollectionTransformer = $planeTripCollectionTransformer;
        $this->trainTripCollectionTransformer = $trainTripCollectionTransformer;
    }

    /**
     * @param Request $model
     * @return array|null
     */
    public function transform(Model $model): ?array
    {
        $accommodations = $this->accommodationCollectionTransformer->transform($model)->toArray();
        $planeTrips = $this->planeTripCollectionTransformer->transform($model)->toArray();
        $trainTrips = $this->trainTripCollectionTransformer->transform($model)->toArray();

        if (empty($accommodations) && empty($planeTrips) && empty($trainTrips)) {
            return null;
        }

        $others = '';
        if (!is_null($model->unrealized_at)) {
            $others = trans('request.trip-did-not-have-place');
        } elseif ($model->isTrip() === false || $model->isDelegation() === false) {
            $others = trans('request.non-delegation-overlay-message');
        }

        return [
            'instance_id' => $model->instance_id,
            'Request created at' => $model->created_at->format('Y-m-d H:i:s'),
            'Request' => $model->slug,
            'Request number' => $model->number,
            'Company' => $model->company->name,
            'Grade' => $model->user->grade,
            'Others' => $others,
            'Name Surname' => $model->getUser()->full_name,
            'User HR ID' => $model->getUser()->hr_id,
            'Acceptors' => $model->acceptors->implode('full_name', ', '),
            'Acceptors ERP ID' => $model->acceptors->implode('erp_id', ', '),
            'acceptors_hr_id' => $model->acceptors->implode('hr_id', ', '),
            'Cost Centre' => $model->mpk ? $model->mpk->code : '',
            'accommodations' => $accommodations,
            'plane_trips' => $planeTrips,
            'train_trips' => $trainTrips,
        ];
    }

    public function isModelSupported(Model $model): bool
    {
        return $model instanceof Request && $model->isTrip() && (
                $model->accomodations->count() > 0
                || $model->planeTrips->count() > 0
                || $model->trainTrips->count() > 0
            );
    }

    public static function modelSupportedQuery(): \Closure
    {
        return function ($query) {
            $query->whereHas('accomodations')
                ->orWhereHas('planeTrips')
                ->orWhereHas('trainTrips')
                ->where('is_trip', true);
        };
    }
}

