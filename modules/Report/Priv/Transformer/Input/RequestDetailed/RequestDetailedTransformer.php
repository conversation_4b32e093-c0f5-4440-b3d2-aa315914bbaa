<?php

declare(strict_types=1);

namespace Modules\Report\Priv\Transformer\Input\RequestDetailed;

use App\Company;
use App\LumpSum;
use App\Repositories\RequestRepository;
use App\Repositories\UserDeputyRepository;
use App\Request;
use App\RequestMileageAllowance;
use App\Services\AccessLumpSum\AccessLumpSumService;
use App\Services\AccommodationLumpSum\AccommodationLumpSumService;
use App\Services\DriveLumpSum\DriveLumpSumService;
use App\Services\RequestCourse\Elements\TargetPointCollection;
use App\Services\RequestCourse\Location\LocationElement;
use App\Services\TravelExpenses\TravelExpensesService;
use App\User;
use App\UserDeputy;
use App\Vendors\Math;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Modules\Allowance\Priv\RequestTravelTypesCollection;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Pub\Facades\FeatureSwitcherFacade;
use Modules\Report\Priv\Transformer\Input\AccountDimensionCollectionTransformer;
use Modules\Report\Priv\Transformer\Input\InputDataTransformerInterface;

class RequestDetailedTransformer implements InputDataTransformerInterface
{
    private const EMPTY_VALUE_IN_DATA_EXPORT = '';
    private const EMPTY_NUMBER_VALUE_IN_DATA_EXPORT = 0;
    private const PROVEN_EMPTY_VALUE_IN_DATA_EXPORT = '-empty-';

    private ExpenseTypeCollectionTransformer $expenseTypeCollectionTransformer;
    private AccountDimensionCollectionTransformer $accountDimensionCollectionTransformer;

    public function __construct(
        ExpenseTypeCollectionTransformer $expenseTypeCollectionTransformer,
        AccountDimensionCollectionTransformer $accountDimensionCollectionTransformer
    ) {
        $this->expenseTypeCollectionTransformer = $expenseTypeCollectionTransformer;
        $this->accountDimensionCollectionTransformer = $accountDimensionCollectionTransformer;
    }

    /**
     * @param Request|Model $model
     * @return array|null
     */
    public function transform(Model $model): ?array
    {
        $user = $model->getUser();

        if ($this->isOnlyAccountedRequestsNeeded($user->company)) {
            $actionDate = $model->accounted_at;
        } else {
            $actionDate = $model->completion_date;
        }

        [$deputies, $deputiesErpIds, $deputiesHrIds] = $this
            ->getActiveUserDeputies(
                $user,
                $model->created_at,
                $actionDate,
            );

        $mileageAllowanceAmount = '0';
        $model->mileageAllowances->each(function (RequestMileageAllowance $mileageAllowance) use (&$mileageAllowanceAmount) {
            $mileageAllowanceAmount = Math::add($mileageAllowanceAmount, $mileageAllowance->amount(), 2);
        });

        //Per diems
        $travelExpensesService = (new TravelExpensesService())->init($model);

        $perDiemAmount = $travelExpensesService->calculateSettled(false)
            ->each(function (LumpSum $lumpSum) {
                $lumpSum->convertedAmount = Math::multiply(
                    $lumpSum->exchangeRate ?? '1',
                    Math::subtract($lumpSum->amount, $lumpSum->deductions),
                );
                return $lumpSum;
            })
            ->sum('convertedAmount');

        $perDiemAmount = Math::round($perDiemAmount, 2);

        $accommodationLumpSums = (new AccommodationLumpSumService($model, resolve(RequestRepository::class)))->calculate();
        $accommodationSum = Math::round($accommodationLumpSums->sum('convertedAmount'), 2);

        $accessLumpSums = (new AccessLumpSumService($model))->calculate();
        $accessSum = Math::round($accessLumpSums->sum('convertedAmount'), 2);

        $driveLumpSums = (new DriveLumpSumService($model))->calculate();
        $driveSum = Math::round($driveLumpSums->sum('convertedAmount'), 2);

        return [
            'instance_id' => $model->instance_id,
            'Request number' => $model->number,
            'Request date' => $model->created_at->format('Y-m-d H:i:s'),
            'Claim date' => $model->settled_at ? $model->settled_at->format('Y-m-d H:i:s') : null,
            'Approval date' => $model->accounted_at ? $model->accounted_at->format('Y-m-d H:i:s') : null,
            'Transfer date' => $actionDate ? $actionDate->format('Y-m-d H:i:s') : null,
            'Request' => $model->slug,
            'Request status' => $model->getTranslatedStatusInCurrentUserLanguage(),
            'Employee' => $user->full_name,
            'Employee ERP ID' => $user->erp_id,
            'Employee HR ID' => $user->hr_id,
            'Approver' => $model->acceptors->implode('full_name', ', '),
            'Approver ERP ID' => $model->acceptors->implode('erp_id', ', '),
            'Approver HR ID' => $model->acceptors->implode('hr_id', ', '),
            'Deputies' => $deputies,
            'Deputies ERP ID' => $deputiesErpIds,
            'Deputies HR ID' => $deputiesHrIds,
            'Company' => $model->company->name,
            'Cost Centre' => $model->mpk->code ?? '',
            'Trip type' => $model->type === Request::TYPE_TRIP
                ? ($model->isNationalTrip() ? 'domestic' : 'abroad')
                : self::EMPTY_VALUE_IN_DATA_EXPORT,
            'Countries' => $this->getCountriesFromTargetPoints($model),
            'Days' => $this->prepareDaysInformation($model),
            'Start date' => $model->type === Request::TYPE_TRIP ? $model->trip_starts->format('Y-m-d H:i:s') : null,
            'End date' => $model->type === Request::TYPE_TRIP ? $model->trip_ends->format('Y-m-d H:i:s') : null,
            'Trip start' => $model->type === Request::TYPE_TRIP && $model->startLocation()->get()->isNotEmpty(
            ) ? $model->startLocation()->get()[0]['formatted_address'] : self::EMPTY_VALUE_IN_DATA_EXPORT,
            'Destination' => $model->type === Request::TYPE_TRIP ? $model->target_points_text : self::EMPTY_VALUE_IN_DATA_EXPORT,
            'Trip end' => $model->type === Request::TYPE_TRIP && $model->endLocation()->get()->isNotEmpty(
            ) ? $model->endLocation()->get()[0]['formatted_address'] : self::EMPTY_VALUE_IN_DATA_EXPORT,
            'Accommodation names' => ucwords(strtolower(implode(', ', $model->getAllHotelNames()))),
            'Means of transport' => $model->type === Request::TYPE_TRIP ? $this->getMeansOfTransport($model)
                : self::EMPTY_VALUE_IN_DATA_EXPORT,
            'Mileage distance' => $model->type === Request::TYPE_TRIP ? $model->privateCarTrips()->sum(
                'distance',
            ) : self::EMPTY_VALUE_IN_DATA_EXPORT,
            'Mileage allowance' => $mileageAllowanceAmount,
            'Per diems' => $perDiemAmount,
            'Accommodation allowance' => $accommodationSum,
            'Getting to and from stations/airports travelling abroad allowance' => $accessSum,
            'Public transportation allowance' => $driveSum,
            'Others' => $this->prepareRequestInformation($model),
            'expenses' => [$this->expenseTypeCollectionTransformer->transform($model)->toArray()],
            'account_dimensions' => [$this->accountDimensionCollectionTransformer->transform($model)->toArray()],
        ];
    }

    public function isModelSupported(Model $model): bool
    {
        return $model instanceof Request
            && in_array(
                $model->status,
                [
                    Request::STATUS_ACCOUNTING,
                    Request::STATUS_FINISH,
                    Request::STATUS_TRANSFERRED,
                    Request::STATUS_TRANSFER_ERROR,
                ],
                true,
            );
    }

    public static function modelSupportedQuery(): \Closure
    {
        return function ($query) {
            $query->whereIn('status', [
                Request::STATUS_TRANSFERRED,
                Request::STATUS_TRANSFER_ERROR,
                Request::STATUS_FINISH
            ]);
        };
    }

    private function prepareRequestInformation(Request $request): string
    {
        if ($request->isUnrealized()) {
            return trans('trip-request.trip-did-not-have-place');
        }

        if (!$request->isDelegation()) {
            return trans('trip-request.trip-type-non-delegation');
        }

        return self::EMPTY_VALUE_IN_DATA_EXPORT;
    }

    private function prepareDaysInformation(Request $request): int
    {
        if (
            $request->isUnrealized() ||
            $request->type !== Request::TYPE_TRIP ||
            $request->trip_starts === null ||
            $request->trip_ends === null
        ) {
            return self::EMPTY_NUMBER_VALUE_IN_DATA_EXPORT;
        }

        return $request->trip_ends->endOfDay()->diffInDays($request->trip_starts->startOfDay()) + 1;
    }

    private function getMeansOfTransport(Request $request): string
    {
        return RequestTravelTypesCollection::makeTripsFromRequest($request)->getTranslatedAndCommaSeparated();
    }

    private function getActiveUserDeputies(?User $user, Carbon $from, ?Carbon $to): array
    {
        if (null === $to) {
            $to = $from->copy()->endOfDay();
        }

        if (null === $user) {
            return [
                self::EMPTY_VALUE_IN_DATA_EXPORT,
                self::EMPTY_VALUE_IN_DATA_EXPORT,
                self::EMPTY_VALUE_IN_DATA_EXPORT,
            ];
        }

        /**
         * @var Collection $userDeputies
         */
        $userDeputies = resolve(UserDeputyRepository::class)
            ->getAllUserDeputiesForUserByTimeInterval(
                $user,
                $from->toDateTimeString(),
                $to->toDateTimeString(),
            )->load('deputy');

        $users = $userDeputies
            ->map(fn(UserDeputy $userDeputy) => $userDeputy->deputy)
            ->filter()
            ->map(fn(User $user) => [
                'full_name' => $user->full_name,
                'erp_id' => $user->erp_id ?? self::PROVEN_EMPTY_VALUE_IN_DATA_EXPORT,
                'hr_id' => $user->hr_id ?? self::PROVEN_EMPTY_VALUE_IN_DATA_EXPORT,
            ]);

        return [
            $users->implode('full_name', '; '),
            $users->implode('erp_id', '; '),
            $users->implode('hr_id', '; '),
        ];
    }

    private function isOnlyAccountedRequestsNeeded(Company $company): bool
    {
        return resolve(FeatureSwitcherFacade::class)->isEnabledForCompany(
            FeatureEnum::FEATURE_ACCOUNTED_REQUESTS_IN_COCKPIT_REPORT(),
            $company,
        );
    }

    private function getCountriesFromTargetPoints(Request $request): ?string
    {
        $targetPointsLocations = (new TargetPointCollection($request))->unifyLocationElements()->sortAsc(
        )->getLocations();
        $targetPointsText = ($targetPointsLocations->reduce(function ($carry, LocationElement $element) {
            $location = $element->getLocation();

            return $carry . "{$location['country']} ";
        }));

        return $targetPointsText;
    }

}

