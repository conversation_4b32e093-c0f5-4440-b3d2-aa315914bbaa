<?php

namespace Modules\Report\Priv\Job;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Modules\Report\Pub\Facade\ReportFacade;

class StoreReportDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    private string $reportName;
    private array $data;

    public function __construct(string $reportName, array $data)
    {
        $this->reportName = $reportName;
        $this->data = $data;
        $this->onQueue('reports');
    }

    public function handle(ReportFacade $reportFacade): void
    {
        $reportFacade->store($this->reportName, $this->data);
    }
}