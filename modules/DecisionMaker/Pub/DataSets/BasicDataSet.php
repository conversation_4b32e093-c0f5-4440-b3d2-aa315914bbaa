<?php

declare(strict_types=1);

namespace Modules\DecisionMaker\Pub\DataSets;

use Mo<PERSON>les\DecisionMaker\Pub\Exceptions\InvalidTypedDataSetElementException;

class BasicDataSet implements DataSetInterface
{
    protected $data = [];

    /**
     * BasicDataSet constructor.
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function get(string $key, ?string $expectedClass = null)
    {
        $data = $this->data[$key] ?? null;

        if (is_string($expectedClass) === true && ($data instanceof $expectedClass) === false) {
            throw InvalidTypedDataSetElementException::create();
        }

        return $data;
    }

    public function set(string $key, $value): DataSetInterface
    {
        $this->data[$key] = $value;

        return $this;
    }
}