<?php

declare(strict_types=1);

namespace Modules\DecisionMaker\Priv\Operators\Logical;

use Mo<PERSON>les\DecisionMaker\Pub\Interfaces\ConditionInterface;
use Modules\DecisionMaker\Pub\DataSets\DataSetInterface;
use Mo<PERSON>les\DecisionMaker\Priv\Operators\OperatorInterface;

class AndOperator implements OperatorInterface
{
    public const CODE = 'and';

    public function execute($expressions, DataSetInterface $data): bool
    {
        $result = true;

        /** @var ConditionInterface $condition */
        foreach ($expressions as $condition) {
            $result = ($result && $condition->check($data));
        }

        return $result;
    }
}