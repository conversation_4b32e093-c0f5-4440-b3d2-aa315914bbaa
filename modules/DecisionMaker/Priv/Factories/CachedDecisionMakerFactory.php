<?php

declare(strict_types=1);

namespace Modules\DecisionMaker\Priv\Factories;

use Mo<PERSON>les\DecisionMaker\Priv\Conditions\ConditionPool;
use Modules\DecisionMaker\Priv\DecisionMaker;
use Modules\DecisionMaker\Priv\Decisions\DecisionPool;
use Modules\DecisionMaker\Pub\Enums\DecisionProcessEnum;
use Modules\DecisionMaker\Priv\Operators\OperatorPool;
use Modules\DecisionMaker\Priv\Repositories\DecisionProcessNodeRepository;
use Modules\DecisionMaker\Priv\Repositories\DecisionProcessRepository;
use Illuminate\Cache\CacheManager;
use Illuminate\Support\Facades\Cache;

class CachedDecisionMakerFactory extends DecisionMakerFactory
{
    /**
     * @var CacheManager
     */
    protected $cacheManager;

    public function __construct(
        CacheManager $cacheManager,
        DecisionProcessRepository $decisionProcessRepository,
        DecisionProcessNodeRepository $decisionProcessNodeRepository,
        ?ConditionPool $conditionPool = null,
        ?DecisionPool $decisionPool = null,
        ?OperatorPool $operatorPool = null
    ) {
        parent::__construct(
            $decisionProcessRepository,
            $decisionProcessNodeRepository,
            $conditionPool,
            $decisionPool,
            $operatorPool
        );

        $this->cacheManager = $cacheManager;
    }

    protected function resolveNodesForInstance(DecisionProcessEnum $decisionProcessEnum, int $instanceId)
    {
        $key = 'decision-process:' . $decisionProcessEnum . ':' . $instanceId;
        return $this->cacheManager->remember($key, 60, function () use ($decisionProcessEnum, $instanceId) {
            return parent::resolveNodesForInstance($decisionProcessEnum, $instanceId);
        });
    }
}
