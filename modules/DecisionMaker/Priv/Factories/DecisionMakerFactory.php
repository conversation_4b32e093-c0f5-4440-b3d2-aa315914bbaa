<?php

declare(strict_types=1);

namespace Modules\DecisionMaker\Priv\Factories;

use Mo<PERSON>les\DecisionMaker\Priv\Builders\ConditionBuilder;
use Modules\DecisionMaker\Priv\Conditions\ConditionPool;
use Modules\DecisionMaker\Priv\DecisionMaker;
use Modules\DecisionMaker\Priv\Decisions\DecisionPool;
use Modules\DecisionMaker\Pub\Enums\DecisionProcessEnum;
use Modules\DecisionMaker\Priv\Exceptions\DecisionProcessNodesNotFoundException;
use Modules\DecisionMaker\Priv\Exceptions\DecisionProcessNotFoundException;
use Modules\DecisionMaker\Priv\Models\DecisionProcess;
use Modules\DecisionMaker\Priv\Models\DecisionProcessCondition;
use Modules\DecisionMaker\Priv\Models\DecisionProcessNode;
use Modules\DecisionMaker\Priv\Nodes\Node;
use Modules\DecisionMaker\Priv\Operators\OperatorPool;
use Modules\DecisionMaker\Priv\Repositories\DecisionProcessNodeRepository;
use Modules\DecisionMaker\Priv\Repositories\DecisionProcessRepository;
use Illuminate\Support\Collection;
use Modules\DecisionMaker\Pub\Interfaces\DecisionMakerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class DecisionMakerFactory implements DecisionMakerFactoryInterface
{
    /**
     * @var DecisionProcessRepository
     */
    protected $decisionProcessRepository;

    /**
     * @var DecisionProcessNodeRepository
     */
    protected $decisionProcessNodeRepository;

    /**
     * @var ConditionPool|null
     */
    protected $conditionPool;

    /**
     * @var DecisionPool|null
     */
    protected $decisionPool;

    /**
     * @var OperatorPool|null
     */
    protected $operatorPool;

    /**
     * DecisionMakerFactory constructor.
     * @param DecisionProcessRepository $decisionProcessRepository
     * @param DecisionProcessNodeRepository $decisionProcessNodeRepository
     * @param ConditionPool|null $conditionPool
     * @param DecisionPool|null $decisionPool
     * @param OperatorPool|null $operatorPool
     */
    public function __construct(
        DecisionProcessRepository $decisionProcessRepository,
        DecisionProcessNodeRepository $decisionProcessNodeRepository,
        ?ConditionPool $conditionPool = null,
        ?DecisionPool $decisionPool = null,
        ?OperatorPool $operatorPool = null
    ) {
        $this->decisionProcessRepository = $decisionProcessRepository;
        $this->decisionProcessNodeRepository = $decisionProcessNodeRepository;
        $this->conditionPool = $conditionPool;
        $this->decisionPool = $decisionPool;
        $this->operatorPool = $operatorPool;
    }

    public function buildForSlug(DecisionProcessEnum $decisionProcessEnum, int $instanceId): DecisionMakerInterface
    {
        $nodes = $this->resolveNodesForInstance($decisionProcessEnum, $instanceId);

        return $this->buildFromArray($nodes);
    }

    protected function resolveNodesForInstance(DecisionProcessEnum $decisionProcessEnum, int $instanceId)
    {
        try {
            /** @var DecisionProcess $decisionProcess */
            $decisionProcess = $this->decisionProcessRepository->findBySlug($decisionProcessEnum->getValue());
        } catch (NotFoundHttpException $exception) {
            throw DecisionProcessNotFoundException::create($decisionProcessEnum);
        }

        $decisionProcessNodes = $this->decisionProcessNodeRepository->findByDecisionProcessIdAndInstanceId($decisionProcess->id, $instanceId);
        if($decisionProcessNodes->isNotEmpty()) {
            return $this->prepareDecisionProcessNodes($decisionProcessNodes);
        }

        $decisionProcessNodes = $this->decisionProcessNodeRepository->findDefaultByDecisionProcessId($decisionProcess->id);
        if($decisionProcessNodes->isNotEmpty()) {
            return $this->prepareDecisionProcessNodes($decisionProcessNodes);
        }

        throw DecisionProcessNodesNotFoundException::create($decisionProcessEnum);
    }

    protected function prepareDecisionProcessNodes(Collection $decisionProcessNodes): array
    {
        $nodes = [];
        /** @var DecisionProcessNode $decisionProcessNode */
        foreach($decisionProcessNodes as $decisionProcessNode) {
            $node = [
                Node::OPERATOR => $decisionProcessNode->getOperatorDto(),
                Node::DECISION => $decisionProcessNode->getDecisionDto(),
                Node::CONDITIONS => []
            ];

            /** @var DecisionProcessCondition $condition */
            foreach($decisionProcessNode->conditions as $condition) {
                $node[Node::CONDITIONS][] = $condition->getConditionDto();
            }

            $nodes[] = $node;
        }

        return $nodes;
    }

    public function buildFromArray(array $nodes): DecisionMakerInterface
    {
        $builder = new ConditionBuilder($this->conditionPool, $this->decisionPool, $this->operatorPool);
        $condition = $builder->build($nodes);

        return new DecisionMaker($condition);
    }
}
