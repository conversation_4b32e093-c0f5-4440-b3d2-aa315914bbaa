<?php

declare(strict_types=1);

namespace Modules\DecisionMaker\Priv\Dtos;

final class DecisionDto
{
    /**
     * @var string
     */
    protected $code;

    /**
     * @var array
     */
    protected $parameters;

    /**
     * DecisionDto constructor.
     * @param string $code
     * @param array $parameters
     */
    public function __construct(string $code, array $parameters)
    {
        $this->code = $code;
        $this->parameters = $parameters;
    }

    /**
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * @return array
     */
    public function getParameters(): array
    {
        return $this->parameters;
    }

    public static function create(string $code, array $parameters = []): DecisionDto
    {
        return new DecisionDto($code, $parameters);
    }
}