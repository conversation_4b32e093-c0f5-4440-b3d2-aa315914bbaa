<?php

declare(strict_types=1);

namespace Modules\FeatureSwitcher\Pub\Facades;

use App\Company;
use App\Instance;
use Modules\FeatureSwitcher\Pub\Dtos\FeatureEnumDto;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use App\User;
use Modules\FeatureSwitcher\Priv\Services\FeatureService;

class FeatureSwitcherFacade
{
    /**
     * @var FeatureService
     */
    protected $featureService;

    /**
     * FeatureSwitcherFacade constructor.
     * @param FeatureService $featureService
     */
    public function __construct(FeatureService $featureService)
    {
        $this->featureService = $featureService;
    }

    public function isEnabled(FeatureEnum $feature, User $user): bool
    {
        return $this->featureService->isEnabled($feature, $user->company, $user->instance);
    }

    public function isEnabledForCompany(FeatureEnum $feature, Company $company): bool
    {
        return $this->featureService->isEnabled($feature, $company, $company->instance);
    }

    public function getTextValueForCompany(FeatureEnum $feature, Company $company, Instance $instance): string
    {
        return $this->featureService->getTextValue($feature, $company, $instance);

    }

    public function getTextParametersForCompany(FeatureEnum $feature, Company $company, Instance $instance): ?array
    {
        return $this->featureService->getTextParameters($feature, $company, $instance);
    }

    public function getEnumValue(FeatureEnum $feature, Company $company): FeatureEnumDto
    {
        return $this->featureService->getEnumValue($feature, $company);

    }
}