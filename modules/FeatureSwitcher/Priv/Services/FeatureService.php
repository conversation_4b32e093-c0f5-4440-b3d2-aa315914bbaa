<?php

declare(strict_types=1);

namespace Modules\FeatureSwitcher\Priv\Services;

use App\Company;
use App\Enum\AbstractEnum;
use App\Enum\ActiveEnum;
use App\Instance;
use Modules\FeatureSwitcher\Priv\Exceptions\FeatureEnumClassNotAllowed;
use Modules\FeatureSwitcher\Priv\Exceptions\FeatureEnumClassNotFoundException;
use Modules\FeatureSwitcher\Pub\Dtos\FeatureEnumDto;
use Modules\FeatureSwitcher\Pub\Interfaces\FeatureEnumClassInterface;
use Modules\FeatureSwitcher\Priv\Dtos\FeatureSettingDto;
use Modules\FeatureSwitcher\Priv\Entities\FeatureSetting;
use Modules\FeatureSwitcher\Priv\Exceptions\FeatureSettingNotFoundException;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use Modules\FeatureSwitcher\Priv\Exceptions\FeatureNotFoundException;
use Modules\FeatureSwitcher\Priv\Exceptions\FeatureSwitchInvalidTypeException;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Priv\Repositories\FeatureRepository;
use Modules\FeatureSwitcher\Priv\Repositories\FeatureSettingRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class FeatureService
{
    /**
     * @var FeatureRepository
     */
    protected $featureRepository;

    /**
     * @var FeatureSettingRepository
     */
    protected $featureSettingRepository;

    /**
     * FeatureService constructor.
     * @param FeatureRepository $featureRepository
     * @param FeatureSettingRepository $featureSettingRepository
     */
    public function __construct(
        FeatureRepository $featureRepository,
        FeatureSettingRepository $featureSettingRepository
    ) {
        $this->featureRepository = $featureRepository->disableAuthorization();
        $this->featureSettingRepository = $featureSettingRepository->disableAuthorization();
    }

    public function getTextValue(FeatureEnum $feature, Company $company, Instance $instance): string
    {
        try {
            $feature = $this->featureRepository->findBySlugWithSettings($feature, $company, $instance);
        } catch (NotFoundHttpException $exception) {
            throw new FeatureNotFoundException();
        }

        if($feature->type->notEquals(FeatureTypeEnum::TEXT())) {
            throw new FeatureSwitchInvalidTypeException();
        }

        $setting = $this->getSetting($feature, $company);

        return $setting->getAsString();
    }

    public function getTextParameters(FeatureEnum $feature, Company $company, Instance $instance): ?array
    {
        try {
            $feature = $this->featureRepository->findBySlugWithSettings($feature, $company, $instance);
        } catch (NotFoundHttpException $exception) {
            throw new FeatureNotFoundException();
        }

        if($feature->type->notEquals(FeatureTypeEnum::TEXT())) {
            throw new FeatureSwitchInvalidTypeException();
        }

        return $this->getSetting($feature, $company)->getParameters();
    }

    public function isEnabled(FeatureEnum $feature, Company $company, Instance $instance): bool
    {
        try {
            $feature = $this->featureRepository->findBySlugWithSettings($feature, $company, $instance);
        } catch (NotFoundHttpException $exception) {
            throw new FeatureNotFoundException();
        }

        if($feature->type->notEquals(FeatureTypeEnum::SWITCH())) {
            throw new FeatureSwitchInvalidTypeException();
        }

        $setting = $this->getSetting($feature, $company);
        return $setting->getAsBoolean();
    }

    public function getEnumValue(FeatureEnum $feature, Company $company): FeatureEnumDto
    {
        try {
            $feature = $this->featureRepository->findBySlugWithSettings($feature, $company, $company->instance);
        } catch (NotFoundHttpException $exception) {
            throw new FeatureNotFoundException();
        }

        if($feature->type->notInArray([FeatureTypeEnum::ENUM(), FeatureTypeEnum::SWITCH()])) {
            throw new FeatureSwitchInvalidTypeException();
        }

        $setting = $this->getSetting($feature, $company);


        if($feature->type->equals(FeatureTypeEnum::SWITCH())) {
            $className = ActiveEnum::class;
            $enumValue = $setting->getAsNumeric();
        } else {
            $className = $feature->class;
            $enumValue = $setting->getAsString();
        }

        if(!class_exists($className)) {
            throw new FeatureEnumClassNotFoundException();
        }

        if (
            is_subclass_of($className, AbstractEnum::class) === false ||
            is_subclass_of($className,FeatureEnumClassInterface::class) === false
        ) {
            throw new FeatureEnumClassNotAllowed();
        }

        $enum = new $className($enumValue);

        return new FeatureEnumDto($enum, $setting->getParameters());
    }

    protected function getSetting(Feature $feature, Company $company): FeatureSettingDto
    {
        $setting = $feature->featureSettings->first();

        if ($setting !== null) {
            return new FeatureSettingDto($setting->setting, $setting->parameters);
        }

        return new FeatureSettingDto($feature->default, $feature->parameters);
    }

    public function changeConfiguration(Feature $feature, Instance $instance, $configuration, ?Company $company = null, array $parameters = []): void
    {
        $featureSetting = $this->findCurrentConfiguration($feature, $instance, $company);

        if($featureSetting === null) {
            $featureSetting = new FeatureSetting();
        }

        $featureSetting->instance_id = $instance->id;
        $featureSetting->feature_id = $feature->id;
        if($company !== null) {
            $featureSetting->company_id = $company->id;
        }
        $featureSetting->setting = $configuration;
        $featureSetting->parameters = $parameters;

        $this->featureSettingRepository->persist($featureSetting);

    }

    public function removeConfiguration(Feature $feature, Instance $instance, ?Company $company)
    {
        $featureSetting = $this->findCurrentConfiguration($feature, $instance, $company);
        if ($featureSetting === null) {
            throw new FeatureSettingNotFoundException();
        }
        $this->featureSettingRepository->remove($featureSetting);
    }

    public function findCurrentConfiguration(Feature $feature, Instance $instance, ?Company $company = null): ?FeatureSetting
    {
        if ($company !== null) {
            return $this->featureSettingRepository->findForFeatureAndCompany($feature, $company);
        }

        return $this->featureSettingRepository->findForFeatureAndInstance($feature, $instance);
    }
}
