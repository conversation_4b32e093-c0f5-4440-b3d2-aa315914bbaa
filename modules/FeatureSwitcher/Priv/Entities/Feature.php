<?php

declare(strict_types=1);

namespace Modules\FeatureSwitcher\Priv\Entities;

use Mo<PERSON>les\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Model;

/**
 * @property FeatureTypeEnum type
 * @property FeatureEnum slug
 * @property int id
 * @property string default
 * @property string class
 * @property array parameters
 * @property string description
 * @property-read \Illuminate\Database\Eloquent\Collection|FeatureSetting[] $featureSettings
 */
class Feature extends Model
{
    use HasTimestamps;

    protected $casts = [
        'slug' => FeatureEnum::class,
        'type' => FeatureTypeEnum::class,
        'parameters' => 'array'
    ];

    public function isForCompany(): bool
    {
        return FeatureTypeEnum::COMPANY()->equals($this->type);
    }

    public function featureSettings()
    {
        return $this->hasMany(FeatureSetting::class);
    }

    public function getTypeAttribute(): FeatureTypeEnum
    {
        return new FeatureTypeEnum($this->attributes['type']);
    }

    public function getSlugAttribute(): FeatureEnum
    {
        return new FeatureEnum($this->attributes['slug']);
    }
}
