<?php

declare(strict_types=1);

namespace Modules\Common\ValueObjects;

abstract class BaseValueObject
{
    /** @var string */
    protected $value;

    public function isEqual(BaseValueObject $abstractValueObject): bool
    {
        return $this->value === (string)$abstractValueObject;
    }

    public function __toString(): string
    {
        return $this->value;
    }

    abstract protected function isValid(string $value): bool;
}