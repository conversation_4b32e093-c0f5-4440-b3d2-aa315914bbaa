<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Rules;

use App\Repositories\RequestMileageAllowanceAccountDimensionItemRepository;
use Illuminate\Contracts\Validation\Rule;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Repositories\RequestAccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\RequestAccountingTravelExpenseDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\RequestAccountingMileageAllowanceAccountDimensionItemRepository;

class AccountDimensionItemParentCanBeChangedRule implements Rule
{
    protected AccountDimensionItem $accountDimensionItem;

    protected RequestAccountDimensionItemRepository $requestAccountDimensionItemRepository;

    protected RequestAccountingTravelExpenseDimensionItemRepository $requestAccountingTravelExpenseDimensionItemRepository;

    protected RequestMileageAllowanceAccountDimensionItemRepository $mileageAllowanceAccountDimensionItemRepository;

    protected RequestAccountingMileageAllowanceAccountDimensionItemRepository $requestMileageAllowanceSummaryAccountDimensionItemRepository;

    public function __construct(AccountDimensionItem $accountDimensionItem)
    {
        $this->accountDimensionItem = $accountDimensionItem;
        $this->requestAccountDimensionItemRepository = resolve(RequestAccountDimensionItemRepository::class);
        $this->requestAccountingTravelExpenseDimensionItemRepository = resolve(RequestAccountingTravelExpenseDimensionItemRepository::class);
        $this->mileageAllowanceAccountDimensionItemRepository = resolve(RequestMileageAllowanceAccountDimensionItemRepository::class);
        $this->requestMileageAllowanceSummaryAccountDimensionItemRepository = resolve(RequestAccountingMileageAllowanceAccountDimensionItemRepository::class);
    }

    public function passes($attribute, $value)
    {
        if($this->accountDimensionItem->accountDimension->id === $value) {
            return true;
        }

        $count = $this->requestAccountDimensionItemRepository->countByAccountDimensionItem($this->accountDimensionItem);
        if($count !== 0) {
            return false;
        }

        $count = $this->requestAccountingTravelExpenseDimensionItemRepository->countByAccountDimensionItem($this->accountDimensionItem);
        if($count !== 0) {
            return false;
        }

        $count = $this->mileageAllowanceAccountDimensionItemRepository->countByAccountDimensionItem($this->accountDimensionItem);
        if($count !== 0) {
            return false;
        }

        $count = $this->requestMileageAllowanceSummaryAccountDimensionItemRepository->countByAccountDimensionItem($this->accountDimensionItem);
        if($count !== 0) {
            return false;
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function message()
    {
        return trans('analytics::error.account-dimension-cannot-be-changed-when-account-dimension-item-has-been-already-used');
    }
}
