<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Entities;

use App\Traits\AccountDimensionItemTrait;
use App\Traits\AccountDimensionTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int id
 * @property string slug
 * @property int document_element_type_id
 * @property int account_dimension_item_id
 * @property int account_dimension_id
 */
class DocumentElementTypeAccountDimensionItem extends Model
{
    use AccountDimensionTrait;
    use AccountDimensionItemTrait;
}
