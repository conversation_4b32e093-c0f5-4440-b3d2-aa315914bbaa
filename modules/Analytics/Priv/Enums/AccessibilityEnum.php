<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Enums;

use App\Enum\AbstractEnum;

/**
 * @method static AccessibilityEnum DEFAULT
 * @method static AccessibilityEnum EDITABLE_ON_WAITING_FOR_ACCEPTANCE
 */
class AccessibilityEnum extends AbstractEnum{
    private const DEFAULT = 'default';
    private const EDITABLE_ON_WAITING_FOR_ACCEPTANCE = 'editable_on_waiting_for_acceptance';
}