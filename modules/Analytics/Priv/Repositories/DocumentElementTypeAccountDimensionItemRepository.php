<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Repositories;

use App\Database\TransactionManagerInterface;
use App\Repositories\Repository;
use App\Services\SlugGeneratorService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\DocumentElementTypeAccountDimensionItem;

class DocumentElementTypeAccountDimensionItemRepository extends Repository
{
    protected TransactionManagerInterface $transactionManager;

    protected SlugGeneratorService $slugGenerator;

    public function __construct(TransactionManagerInterface $transactionManager, SlugGeneratorService $slugGenerator)
    {
        parent::__construct();
        $this->transactionManager = $transactionManager;
        $this->slugGenerator = $slugGenerator;
    }

    static function model()
    {
        return DocumentElementTypeAccountDimensionItem::class;
    }

    public function findByDocumentElementTypeIdAndId(int $documentElementTypeId, int $documentElementTypeAccountDimensionItemId): ?DocumentElementTypeAccountDimensionItem
    {
        $this->prepare();

        return $this->builder->where([
            'document_element_type_id' => $documentElementTypeId,
            'id' => $documentElementTypeAccountDimensionItemId,
        ])->first();
    }

    public function findByDocumentElementTypeId(int $documentElementTypeId, bool $forSplitLine = false): Collection
    {
        $this->prepare();

        return $this->builder->where([
            'document_element_type_id' => $documentElementTypeId,
            'split_journal_entries_line' => $forSplitLine,
        ])->get();
    }

    public function disconnect(DocumentElementTypeAccountDimensionItem $documentElementTypeAccountDimensionItem): bool
    {
        $this->transactionManager->beginTransaction();

        try {
            $documentElementTypeAccountDimensionItem->delete();
            $this->transactionManager->commit();

            return true;
        } catch (\Throwable $exception) {
            $this->transactionManager->rollBack();
            $this->logger->error((string)$exception);

            return false;
        }
    }

    public function disconnectAll(int $documentElementTypeId): void
    {
        $this->prepare();

        $this->builder->where([
            'document_element_type_id' => $documentElementTypeId
        ])->delete();
    }

    protected function beforeCreate(Model $model, $data)
    {
        $model->slug = $this->slugGenerator->generate();

        return $model;
    }

    public function connectWithItem(int $documentElementTypeId, AccountDimensionItem $accountDimensionItem): DocumentElementTypeAccountDimensionItem
    {
        $this->transactionManager->beginTransaction();

        try {
            $this->prepare();
            $this->builder->where([
                'document_element_type_id' => $documentElementTypeId,
                'account_dimension_id' => $accountDimensionItem->account_dimension_id,
            ])->delete();

            $documentElementTypeAccountingDimensionItem = new DocumentElementTypeAccountDimensionItem();
            $documentElementTypeAccountingDimensionItem->document_element_type_id = $documentElementTypeId;
            $documentElementTypeAccountingDimensionItem->account_dimension_item_id = $accountDimensionItem->id;
            $documentElementTypeAccountingDimensionItem->account_dimension_id = $accountDimensionItem->account_dimension_id;

            $documentElementTypeAccountingDimensionItem = $this->persist($documentElementTypeAccountingDimensionItem);

            $this->transactionManager->commit();

            return $this->getFreshModel($documentElementTypeAccountingDimensionItem);

        } catch (\Throwable $exception) {
            $this->transactionManager->rollBack();

            throw $exception;
        }
    }
}
