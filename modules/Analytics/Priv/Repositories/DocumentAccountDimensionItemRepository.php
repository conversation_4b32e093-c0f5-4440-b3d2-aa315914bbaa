<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Repositories;

use App\Database\TransactionManagerInterface;
use App\Document;
use App\Repositories\Repository;
use App\Services\SlugGeneratorService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\DocumentAccountDimensionItem;
use Psr\Log\LoggerInterface;

class DocumentAccountDimensionItemRepository extends Repository
{
    protected TransactionManagerInterface $transactionManager;

    protected SlugGeneratorService $slugGenerator;

    protected LoggerInterface $logger;

    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    public function __construct(
        TransactionManagerInterface $transactionManager,
        SlugGeneratorService $slugGenerator,
        LoggerInterface $logger,
        AccountDimensionItemRepository $accountDimensionItemRepository
    ) {
        parent::__construct();
        $this->transactionManager = $transactionManager;
        $this->slugGenerator = $slugGenerator;
        $this->logger = $logger;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
    }

    public function disconnectAll(int $documentId): void
    {
        $this->prepare();

        $this->builder->where([
            'document_id' => $documentId
        ])->delete();
    }

    public function findAllByRequestIdAndAccountDimensionId(int $requestId, int $accountDimensionId): Collection
    {
        $this->prepare();

        return $this->builder->where([
            'request_id' => $requestId,
            'account_dimension_id' => $accountDimensionId,
        ])->get();
    }

    protected function beforeCreate(Model $model, $data)
    {
        $model->slug = $this->slugGenerator->generate();

        return $model;
    }

    static function model()
    {
        return DocumentAccountDimensionItem::class;
    }

    public function findByDocumentAndAccountDimensionId(int $documentId, int $accountDimensionId): ?DocumentAccountDimensionItem
    {
        $this->prepare();

        return $this->builder->where([
            'document_id' => $documentId,
            'account_dimension_id' => $accountDimensionId,
        ])->first();
    }

    public function findByDocumentAndId(int $documentId, int $documentAccountDimensionItemId): ?DocumentAccountDimensionItem
    {
        $this->prepare();

        return $this->builder->where([
            'id' => $documentAccountDimensionItemId,
            'document_id' => $documentId,
        ])->first();
    }

    public function findByDocumentElementId(int $documentId, array $visibilites = []): Collection
    {
        $this->prepare();

        $builder = $this
            ->builder
            ->select('document_account_dimension_items.*')
            ->where([
                'document_account_dimension_items.document_id' => $documentId,
            ]);

        if(!empty($visibilites)) {
            $builder
                ->join(
                    'account_dimensions',
                    'document_account_dimension_items.account_dimension_id',
                    '=',
                    'account_dimensions.id'
                )
                ->whereIn('account_dimensions.visibility', $visibilites);
        }

        return $builder->get();
    }

    public function disconnect(DocumentAccountDimensionItem $documentAccountDimensionItem): bool
    {
        $this->transactionManager->beginTransaction();

        try {
            $documentAccountDimensionItem->delete();
            $this->transactionManager->commit();

            return true;
        } catch (\Throwable $exception) {
            $this->transactionManager->rollBack();
            $this->logger->error((string)$exception);

            return false;
        }
    }

    public function connectWithItem(
        int $documentId,
        int $requestId,
        AccountDimensionItem $accountDimensionItem
    ): DocumentAccountDimensionItem {
        $this->transactionManager->beginTransaction();

        try {

            $this->prepare();
            $this->builder->where([
                'document_id' => $documentId,
                'request_id' => $requestId,
                'account_dimension_id' => $accountDimensionItem->account_dimension_id,
            ])->delete();

            $documentAccountDimensionItem = new DocumentAccountDimensionItem();
            $documentAccountDimensionItem->document_id = $documentId;
            $documentAccountDimensionItem->request_id = $requestId;
            $documentAccountDimensionItem->account_dimension_item_id = $accountDimensionItem->id;
            $documentAccountDimensionItem->account_dimension_id = $accountDimensionItem->account_dimension_id;

            $documentAccountDimensionItem = $this->persist($documentAccountDimensionItem);

            $this->transactionManager->commit();

            return $this->getFreshModel($documentAccountDimensionItem);

        } catch (\Throwable $exception) {

            $this->transactionManager->rollBack();;
            throw $exception;

        }
    }
}
