<?php

namespace Modules\Analytics\Priv\Repositories\Criterion;

use App\Repositories\Criteria\Criterion;
use App\Repositories\Repository;
use Illuminate\Database\Eloquent\Builder;
use Modules\Analytics\Priv\Repositories\KPIRepository;
use Modules\Analytics\Priv\Services\FinancialReports\KPI\BusinessTripKPICalculator;

class OnlyVisibleKPICriterion extends Criterion
{
    public const INVISIBLE_KPIS = [
        BusinessTripKPICalculator::SLUG,
    ];

    protected $invisibleKpis;

    public function __construct(array $invisibleKpis = [])
    {
        $this->invisibleKpis = empty($invisibleKpis) === false
            ? collect($invisibleKpis)
            : collect(self::INVISIBLE_KPIS);
    }

    public function supportedRepositories()
    {
        return [
            KPIRepository::class,
        ];
    }

    public function make(Builder $builder, Repository $repository)
    {
        return $builder->whereNotIn('slug', $this->invisibleKpis);
    }
}
