<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Repositories;

use App\Company;
use App\Enum\ActiveEnum;
use App\Repositories\Criteria\CompanyOrNullCriterion;
use App\Repositories\Criteria\InstanceV2Criterion;
use App\Repositories\Criteria\OrderByCriterion;
use App\Repositories\Criteria\SearchPhraseCriterion;
use App\Repositories\Pagination\Paginator;
use App\Repositories\Repository;
use App\Services\SlugGeneratorService;
use App\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Repositories\Criterion\AccountDimensionCriterion;
use Modules\Analytics\Priv\Repositories\Criterion\DimensionItemsAssignedToUserCriterion;
use Modules\Common\Dtos\PageInterface;
use Modules\Common\Repositories\Criterion\OnlyActivatedCriterion;

class AccountDimensionItemRepository extends Repository
{
    protected SlugGeneratorService $slugGenerator;

    public function __construct(SlugGeneratorService $slugGeneratorService)
    {
        parent::__construct();
        $this->slugGenerator = $slugGeneratorService;
    }

    static function model(): string
    {
        return AccountDimensionItem::class;
    }

    public function findOneByAccountDimensionIdAndAccountDimensionItemId(
        int $accountDimensionId,
        int $accountDimensionItemId,
        int $instanceId,
        ?int $companyId = null,
        array $visibilities = []
    ): ?AccountDimensionItem {
        $this->prepare();

        $criteria = [
            'account_dimensions.is_active' => ActiveEnum::ACTIVE(),
            'account_dimensions.instance_id' => $instanceId,
            'account_dimension_items.is_active' => ActiveEnum::ACTIVE(),
            'account_dimension_items.account_dimension_id' => $accountDimensionId,
            'account_dimension_items.id' => $accountDimensionItemId,
        ];

        $builder = $this->builder
            ->select('account_dimension_items.*')
            ->leftJoin(
                'account_dimensions',
                'account_dimensions.id',
                '=',
                'account_dimension_items.account_dimension_id'
            )
            ->where($criteria)
            ->where(function (Builder $builder) use ($companyId) {
                if ($companyId === null) {
                    $builder->whereNull('account_dimensions.company_id');
                } else {
                    $builder
                        ->where('account_dimensions.company_id', '=', $companyId)
                        ->orWhereNull('account_dimensions.company_id');
                }
            })
            ->where(function (Builder $builder) use ($companyId) {
                if ($companyId === null) {
                    $builder->whereNull('account_dimension_items.company_id');
                } else {
                    $builder
                        ->where('account_dimension_items.company_id', '=', $companyId)
                        ->orWhereNull('account_dimension_items.company_id');
                }
            });

        if (!empty($visibilities)) {
            $builder->whereIn('account_dimensions.visibility', $visibilities);
        }

        return $builder->first();
    }

    public function findActiveByAccountDimensionAndCode(
        AccountDimension $accountDimension,
        string $code
    ): ?AccountDimensionItem {
        $this->prepare();

        $criteria = [
            'account_dimension_items.is_active' => ActiveEnum::ACTIVE(),
            'account_dimension_items.code' => $code,
            'account_dimension_items.account_dimension_id' => $accountDimension->id,
        ];

        return $this->builder
            ->select('account_dimension_items.*')
            ->where($criteria)
            ->first();
    }

    public function findActiveByInstancesAndRequestType(
        int $instanceId,
        string $requestType
    ): Collection {
        $this->prepare();

        $criteria = [
            'account_dimension_items.is_active' => ActiveEnum::ACTIVE(),
            'account_dimension_items.instance_id' => $instanceId,
            'account_dimension_items.request_type' => $requestType,
        ];

        return $this->builder->select('account_dimension_items.*')
            ->where($criteria)
            ->get();
    }

    public function getIndex(
        PageInterface $page,
        AccountDimension $accountDimension,
        Company $company,
        User $user
    ): Paginator {
        return $this->paginateByCriteria(
            [
                new AccountDimensionCriterion([$accountDimension->id]),
                new InstanceV2Criterion($company->instance),
                // new CompanyOrNullCriterion([$company->id]), temporary disabled due MIN-5941
                new SearchPhraseCriterion(),
                new OnlyActivatedCriterion(ActiveEnum::ACTIVE()),
                new OrderByCriterion('order', 'asc'),
                new OrderByCriterion('code', 'asc'),
                new DimensionItemsAssignedToUserCriterion(
                    $accountDimension, $user, $company
                )
            ],
            function () {
                return $this->builder
                    ->distinct()
                    ->select(['account_dimension_items.*', 'account_dimensions.code as account_dimensions_code'])
                    ->join(
                        'account_dimensions',
                        'account_dimensions.id',
                        '=',
                        'account_dimension_items.account_dimension_id'
                    );
            },
            'account_dimension_items.id',
            $page
        )->transformItems(function (\StdClass $item) {
            $translationKey = strtolower(
                sprintf(
                    'account-dimension-items.%s.%s',
                    $item->account_dimensions_code,
                    $item->code
                )
            );

            $message = trans($translationKey);

            $item->message = null;

            if (is_string($message) && $message !== $translationKey) {
                $item->message = $message;
            }

            return $item;
        });
    }

    protected function beforeCreate(Model $model, $data)
    {
        $model->slug = $this->slugGenerator->generate();

        return $model;
    }

    public function findBySlugAndInstanceId(string $slug, int $instanceId): ?Model
    {
        $this->prepare();

        $element = $this->builder
            ->select('account_dimension_items.*')
            ->leftJoin(
                'account_dimensions',
                'account_dimensions.id',
                '=',
                'account_dimension_items.account_dimension_id'
            )
            ->where([
                'account_dimension_items.slug' => $slug,
                'account_dimensions.instance_id' => $instanceId
            ])
            ->first();

        abort_if($element === null, 404, trans('global.item-does-not-exists'));

        $this->authorize('view', $element);
        $this->builder = $this->model::query();
        $this->select = collect();

        return $element;
    }

    public function findByCodeAndAccountDimensionId(string $slug, int $accountDimensionId): ?AccountDimensionItem
    {
        $this->prepare();

        /** @var AccountDimensionItem $element */
        $element = $this->builder
            ->select('account_dimension_items.*')
            ->where([
                'account_dimension_items.code' => $slug,
                'account_dimension_items.account_dimension_id' => $accountDimensionId
            ])
            ->first();


        $this->builder = $this->model::query();
        $this->select = collect();

        return $element;
    }
}
