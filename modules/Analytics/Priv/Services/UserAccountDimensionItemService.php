<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Services;

use Illuminate\Support\Collection;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionItemViewObject;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\UserAccountDimensionItemRepository;
use Modules\Analytics\Pub\Dtos\UserDto;

class UserAccountDimensionItemService
{
    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    protected UserAccountDimensionItemRepository $userAccountDimensionItemRepository;

    public function __construct(
        AccountDimensionItemRepository $accountDimensionItemRepository,
        UserAccountDimensionItemRepository $userAccountDimensionItemRepository
    ) {
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
        $this->userAccountDimensionItemRepository = $userAccountDimensionItemRepository;
    }

    /**
     * @param UserDto $userDto
     * @param Collection<AccountDimensionItem> $accountDimensionItems
     * @return void
     * @throws \Throwable
     */
    public function connectCollection(UserDto $userDto, Collection $accountDimensionItems): void
    {
        $this->userAccountDimensionItemRepository->disconnectAll($userDto->getId());

        /** @var AccountDimensionItemViewObject $accountDimensionItemViewObject */
        foreach ($accountDimensionItems as $accountDimensionItemViewObject) {
            $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
                $accountDimensionItemViewObject->getAccountDimensionId(),
                $accountDimensionItemViewObject->getId(),
                $userDto->getInstanceId(),
                $userDto->getCompanyId()
            );

            if ($accountDimensionItem === null) {
                continue;
            }

            $this->userAccountDimensionItemRepository->connectWithItem(
                $userDto->getId(),
                $accountDimensionItem,
                null,
                $accountDimensionItemViewObject->getType()
            );
        }
    }
}
