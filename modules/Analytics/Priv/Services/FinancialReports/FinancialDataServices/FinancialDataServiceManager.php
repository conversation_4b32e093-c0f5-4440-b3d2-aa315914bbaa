<?php


namespace Modules\Analytics\Priv\Services\FinancialReports\FinancialDataServices;


use Modules\Analytics\Priv\Services\FinancialReports\Contracts\FinancialDataSubserviceInterface;
use Illuminate\Support\Manager;

class FinancialDataServiceManager extends Manager
{

    /**
     * Get the default driver name.
     *
     * @return string
     */
    public function getDefaultDriver()
    {
        return 'requests';
    }

    public function createAccommodationsDriver(): FinancialDataSubserviceInterface
    {
        return new AccommodationsDataService();
    }

    public function createPlaneTripsDriver(): FinancialDataSubserviceInterface
    {
        return new PlaneTripsDataService();
    }

    public function createDocumentElementsDriver(): FinancialDataSubserviceInterface
    {
        return new DocumentElementsDataService();
    }
    public function createRequestsDriver(): FinancialDataSubserviceInterface
    {
        return new RequestsDataService();
    }

}