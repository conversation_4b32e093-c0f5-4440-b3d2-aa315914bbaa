<?php


namespace Modules\Analytics\Priv\Services\FinancialReports\FinancialDataServices;


use App\DocumentElement;
use App\PlaneTrip;
use Modules\Analytics\Priv\Services\FinancialReports\FinancialDataCollection;
use Illuminate\Support\Collection;

class PlaneTripsDataService extends RequestElementsDataService
{
    public function getNationalPlaneTrips(): FinancialDataCollection
    {
        return FinancialDataCollection::fromDocumentElementCollection(
            $this->getDocumentElementsFromRequestElementsCollection(
                $this->getNationalPlaneTripsCollection(), DocumentElement::REQUEST_ELEMENT_PLANE_TRIP
            ));
    }

    public function getAbroadPlaneTrips(): FinancialDataCollection
    {
        return FinancialDataCollection::fromDocumentElementCollection(
            $this->getDocumentElementsFromRequestElementsCollection(
                $this->getAbroadPlaneTripsCollection(), DocumentElement::REQUEST_ELEMENT_PLANE_TRIP
            ));
    }

    protected function getNationalPlaneTripsCollection(): Collection
    {
        return $this->data->pluck('planeTrips')->flatten()->filter(function(PlaneTrip $planeTrip) {
            return $planeTrip->isNational();
        });
    }

    protected function getAbroadPlaneTripsCollection(): Collection
    {
        return $this->data->pluck('planeTrips')->flatten()->filter(function(PlaneTrip $planeTrip) {
            return $planeTrip->isAbroad();
        });
    }
}