<?php


namespace Modules\Analytics\Priv\Services\FinancialReports;

use App\Instance;
use App\User;
use App\Vendors\CollectionItem;
use App\Vendors\Math;

/**
 * Class FinancialDataResponseItem
 * @package Modules\Analytics\Priv\Services\FinancialReports
 * @property string value
 * @property string unit
 * @property int quantity
 * @property User|null $user
 */
class FinancialDataResponseItem extends CollectionItem
{
    protected $attributes = [
        'value' => null,
        'unit' => null,
        'quantity' => null,
        'user' => null,
    ];

    public function getFormattedValue(): string
    {
        return $this->value . ' ' . $this->unit;
    }

    public function toArray()
    {
        return array_merge(parent::toArray(), [
            'formatted' => $this->getFormattedValue()
        ]);
    }

    public static function createZeroValue(Instance $instance): FinancialDataResponseItem
    {
        return new static([
            'quantity' => 0,
            'value' => Math::round(0, 2),
            'unit' => $instance->currency->code
        ]);
    }
}