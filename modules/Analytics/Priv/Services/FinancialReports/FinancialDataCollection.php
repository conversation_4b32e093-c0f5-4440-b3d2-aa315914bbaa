<?php


namespace Modules\Analytics\Priv\Services\FinancialReports;


use App\DocumentElement;
use App\Request;
use App\Vendors\Math;
use App\Vendors\TypedCollection;
use Illuminate\Support\Collection;


class FinancialDataCollection extends TypedCollection
{
    public function getTypeClassName(): string
    {
        return FinancialDataItem::class;
    }

    /**
     * Create collection from Request models collection
     * @param Collection $requests
     * @return FinancialDataCollection
     */
    public static function fromRequestsCollection(Collection $requests): FinancialDataCollection
    {
        return new static($requests->map(function(Request $request) {
            return FinancialDataItem::fromRequest($request);
        }));
    }

    public static function fromDocumentElementCollection(Collection $documentElements): FinancialDataCollection
    {
        return new static($documentElements->map(function(DocumentElement $documentElement) {
            return FinancialDataItem::fromDocumentElement($documentElement);
        }));
    }

    /**
     * @return Collection Returns Collection with keys (year-month) and values in type FinancialDataCollection
     */
    public function groupByYearMonth(): Collection
    {
        $collection = collect();

        $this->each(function(FinancialDataItem $item) use (&$collection) {
            $date = $item->date->format('Y-m');

            (!$collection->has($date))
            ? $collection->put($date, new FinancialDataCollection([$item]))
            : $collection[$date]->push($item);
        });

        return $collection;
    }

    public function groupByUser()
    {
        $collection = collect();

        $this->each(function(FinancialDataItem $item) use (&$collection) {
            $key = $item->user->id;

            (!$collection->has($key))
                ? $collection->put($key, new FinancialDataCollection([$item]))
                : $collection[$key]->push($item);
        });

        return $collection;
    }

    public function sum($callback = null)
    {
        return parent::sum($this->setDefaultCallback($callback));
    }

    public function average($callback = null)
    {
        if($count = $this->count()) {
            return Math::round(parent::sum($this->setDefaultCallback($callback)) / $count, 2);
        }

        return Math::round(0,  2);
    }

    public function averageAmount()
    {
        return $this->average();
    }

    public function averageDuration()
    {
        return $this->average(function(FinancialDataItem $dataItem) {
           return $dataItem->duration;
        });
    }

    protected function setDefaultCallback($callback): callable
    {
        if($callback === null) {
            $callback = $this->amountCallback();
        }

        return $callback;
    }

    protected function amountCallback(): callable
    {
        return function(FinancialDataItem $item) {
            return $item->amount;
        };
    }

    public function amountToThousands()
    {
        return $this->map(function(FinancialDataItem $dataItem) {
            $dataItem->amount = Math::divide($dataItem->amount, 1000);

            return $dataItem;
        });
    }
}