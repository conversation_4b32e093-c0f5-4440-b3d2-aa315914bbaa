<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Services;

use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionItemViewObject;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\RequestAccountDimensionItem;
use Modules\Analytics\Priv\Entities\RequestAccountingTravelExpenseDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\RequestAccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\RequestAccountingTravelExpenseDimensionItemRepository;
use Modules\Analytics\Pub\Dtos\RequestAccountingTravelExpenseDto;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class RequestAccountingTravelExpenseService
{
    protected AccountDimensionService $accountDimensionService;

    protected RequestAccountDimensionItemRepository $requestAccountDimensionItemRepository;

    protected RequestAccountingTravelExpenseDimensionItemRepository $requestAccountingTravelExpenseDimensionItemRepository;

    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    public function __construct(
        AccountDimensionService $accountDimensionService,
        RequestAccountDimensionItemRepository $requestAccountDimensionItemRepository,
        RequestAccountingTravelExpenseDimensionItemRepository $requestAccountingTravelExpenseDimensionItemRepository,
        AccountDimensionItemRepository $accountDimensionItemRepository
    ) {
        $this->accountDimensionService = $accountDimensionService;
        $this->requestAccountDimensionItemRepository = $requestAccountDimensionItemRepository;
        $this->requestAccountingTravelExpenseDimensionItemRepository = $requestAccountingTravelExpenseDimensionItemRepository;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
    }

    public function getValue(RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto, int $accountDimensionId): ?RequestAccountingTravelExpenseDimensionItem
    {
        return $this->requestAccountingTravelExpenseDimensionItemRepository->findByRequestAccountingTravelExpense(
            $requestAccountingTravelExpenseDto->getId(),
            $accountDimensionId
        );
    }

    public function disconnectOne(RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto, int $rateadiId): bool
    {
        $requestAccountingTravelExpenseAccountDimensionItem = $this->requestAccountingTravelExpenseDimensionItemRepository->findByRequestAndId(
            $requestAccountingTravelExpenseDto->getId(),
            $requestAccountingTravelExpenseDto->getRequestId(),
            $rateadiId
        );

        abort_if($requestAccountingTravelExpenseAccountDimensionItem === null, Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));
        abort_if($requestAccountingTravelExpenseAccountDimensionItem->accountDimension->is_required === true, Response::HTTP_UNPROCESSABLE_ENTITY, trans('validation.filled'));

        return $this->requestAccountingTravelExpenseDimensionItemRepository->disconnect($requestAccountingTravelExpenseAccountDimensionItem);
    }

    public function connect(
        RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto,
        AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): RequestAccountingTravelExpenseDimensionItem {
        $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
            $accountDimensionItemViewObject->getAccountDimensionId(),
            $accountDimensionItemViewObject->getId(),
            $requestAccountingTravelExpenseDto->getInstanceId(),
            $requestAccountingTravelExpenseDto->getCompanyId()
        );

        return $this->requestAccountingTravelExpenseDimensionItemRepository->connect(
            $requestAccountingTravelExpenseDto->getId(),
            $requestAccountingTravelExpenseDto->getRequestId(),
            $accountDimensionItem
        );
    }

    public function getDefaults(RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto): Collection
    {
        $defaultAccountingAccountDimensionItems = $this
            ->accountDimensionService
            ->findAllActiveForCompany(
                $requestAccountingTravelExpenseDto->getInstanceId(),
                $requestAccountingTravelExpenseDto->getCompanyId(),
                [AccountDimensionVisibilityEnum::ACCOUNTING()]
            )
            ->filter(function (AccountDimension $accountDimension) {
                return $accountDimension->defaultItem !== null;
            })->map(function (AccountDimension $accountDimension) {
                return $accountDimension->defaultItem;
            })->keyBy(function(AccountDimensionItem $accountDimensionItem) {
                return $accountDimensionItem->accountDimension->id;
            });

        $requestAccountDimensionItems = $this
            ->requestAccountDimensionItemRepository
            ->findByRequestId($requestAccountingTravelExpenseDto->getRequestId())
            ->map(function (RequestAccountDimensionItem $requestAccountDimensionItem) {
                return $requestAccountDimensionItem->accountDimensionItem;
            })->keyBy(function(AccountDimensionItem $accountDimensionItem) {
                return $accountDimensionItem->accountDimension->id;
            });

        return $defaultAccountingAccountDimensionItems->merge($requestAccountDimensionItems);
    }

    public function connectCollection(
        RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto,
        Collection $accountDimensionItems
    ): void {
        /** @var AccountDimensionItem $accountDimensionItem */
        foreach ($accountDimensionItems as $accountDimensionItem) {
            $this->requestAccountingTravelExpenseDimensionItemRepository->connect(
                $requestAccountingTravelExpenseDto->getId(),
                $requestAccountingTravelExpenseDto->getRequestId(),
                $accountDimensionItem
            );
        }
    }
}
