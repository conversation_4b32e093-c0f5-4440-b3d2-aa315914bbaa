<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\FeatureSettings;

use App\Enum\AbstractEnum;
use Modules\FeatureSwitcher\Pub\Interfaces\FeatureEnumClassInterface;

/**
 * @method static AccountDimensionSettings DISABLED
 * @method static AccountDimensionSettings USER_PROJECTS
 */
class AccountDimensionSettings extends AbstractEnum implements FeatureEnumClassInterface
{
    private const DISABLED = 'DISABLED';

    private const USER_PROJECTS = 'USER_PROJECTS';
}
