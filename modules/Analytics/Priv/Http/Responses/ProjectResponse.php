<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Http\Responses;

use App\Http\Responses\Response2;
use Modules\Analytics\Priv\Entities\Project;

class ProjectResponse extends Response2
{
    protected function transform(Project $project)
    {
        return [
            'id'          => $project->id,
            'slug' => $project->slug,
            'name'        => $project->name,
            'code'        => $project->code,
            'is_active' => $project->is_active
        ];
    }
}
