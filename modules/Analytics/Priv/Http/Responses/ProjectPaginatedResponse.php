<?php

namespace Modules\Analytics\Priv\Http\Responses;

use App\Http\Responses\Response2Paginated;

class ProjectPaginatedResponse extends Response2Paginated
{
    protected function transform(\stdClass $project)
    {
        return [
            'id' => $project->id,
            'slug' => $project->slug,
            'name' => $project->name,
            'code' => $project->code,
            'is_active' => $project->is_active
        ];
    }
}
