<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Dtos;

use App\Document;
use App\Request;

class DocumentDto
{
    protected int $id;

    protected int $requestId;

    protected int $companyId;

    protected int $instanceId;

    protected bool $requestSettled;

    public function __construct(int $id, int $requestId, int $companyId, int $instanceId, bool $requestSettled)
    {
        $this->id = $id;
        $this->requestId = $requestId;
        $this->companyId = $companyId;
        $this->instanceId = $instanceId;
        $this->requestSettled = $requestSettled;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getRequestId(): int
    {
        return $this->requestId;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getInstanceId(): int
    {
        return $this->instanceId;
    }

    public function isRequestSettled(): bool
    {
        return $this->requestSettled;
    }

    public static function createFromDocument(Document $document): DocumentDto
    {
        return new static(
            $document->id,
            $document->request->id,
            $document->request->company_id,
            $document->request->instance_id,
            in_array($document->request->status, [
                Request::STATUS_ACCOUNTING,
                Request::STATUS_TRANSFER_ERROR,
                Request::STATUS_TRANSFERRED,
                Request::STATUS_FINISH
            ])
        );
    }
}
