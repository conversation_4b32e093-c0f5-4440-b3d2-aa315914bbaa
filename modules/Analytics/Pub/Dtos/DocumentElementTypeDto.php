<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Dtos;

use Modules\Accounting\Priv\Entities\DocumentElementType;

class DocumentElementTypeDto
{
    protected int $id;

    protected int $instanceId;

    public function __construct(int $id, int $instanceId)
    {
        $this->id = $id;
        $this->instanceId = $instanceId;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getInstanceId(): int
    {
        return $this->instanceId;
    }

    public static function createFromDocumentElementType(DocumentElementType $documentElementType): DocumentElementTypeDto
    {
        return new static(
            $documentElementType->id,
            $documentElementType->instance_id
        );
    }
}
