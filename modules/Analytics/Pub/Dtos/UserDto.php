<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Dtos;

use App\User;

class UserDto
{
    protected int $id;

    protected int $companyId;

    protected int $instanceId;

    public function __construct(int $id, int $companyId, int $instanceId)
    {
        $this->id = $id;
        $this->companyId = $companyId;
        $this->instanceId = $instanceId;
    }

    public static function createFromUser(User $request): UserDto
    {
        return new static(
            $request->id,
            $request->company_id,
            $request->instance_id
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getInstanceId(): int
    {
        return $this->instanceId;
    }
}