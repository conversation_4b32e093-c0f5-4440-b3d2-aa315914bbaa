<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Facades;

use App\User;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Wrappers\AccountDimensionWithActiveCompanyItemsWrapper;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionItemViewObject;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionWithItemsViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentAccountDimensionItemViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentElementAccountDimensionItemViewObject;
use Modules\Accounting\Pub\ViewObjects\RequestAccountDimensionItemViewObject;
use Modules\Accounting\Pub\ViewObjects\RequestAccountingAccountDimensionItemViewObject;
use Modules\Accounting\Pub\ViewObjects\RequestAccountingMileageAllowanceAccountDimensionItemViewObject;
use Modules\Accounting\Pub\ViewObjects\RequestAccountingTravelExpenseDimensionItemViewObject;
use Modules\Accounting\Pub\ViewObjects\UserAccountDimensionViewObject;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Services\DocumentAccountDimensionItemService;
use Modules\Analytics\Priv\Services\DocumentElementAccountDimensionItemService;
use Modules\Analytics\Priv\Services\DocumentElementTypeAccountDimensionItemService;
use Modules\Analytics\Priv\Services\RequestAccountDimensionItemService;
use Modules\Analytics\Priv\Services\RequestAccountingMileageAllowanceAccountDimensionItemService;
use Modules\Analytics\Priv\Services\RequestAccountingTravelExpenseService;
use Modules\Analytics\Priv\Services\RequestAllowanceAccountingAccountDimensionItemService;
use Modules\Analytics\Priv\Services\UserAccountDimensionItemService;
use Modules\Analytics\Priv\UseCases\AccountDimensionsItems\CreateAccountDimensionItemUseCase;
use Modules\Analytics\Priv\UseCases\AccountDimensionsItems\DeactivateAccountDimensionItemUseCase;
use Modules\Analytics\Priv\UseCases\AccountDimensionsItems\UpdateAccountDimensionItemUseCase;
use Modules\Analytics\Pub\Dtos\DocumentDto;
use Modules\Analytics\Pub\Dtos\DocumentElementDto;
use Modules\Analytics\Pub\Dtos\DocumentElementTypeDto;
use Modules\Analytics\Pub\Dtos\InstanceDto;
use Modules\Analytics\Pub\Dtos\RequestAccountingMileageAllowanceDto;
use Modules\Analytics\Pub\Dtos\RequestAccountingTravelExpenseDto;
use Modules\Analytics\Pub\Dtos\RequestDto;
use Modules\Analytics\Pub\Dtos\UserDto;
use Modules\Analytics\Pub\Interfaces\StoreAccountDimensionItemInterface;
use Modules\Analytics\Pub\Interfaces\UpdateAccountDimensionItemInterface;
use Modules\Analytics\Pub\ViewObjects\DocumentElementTypeAccountDimensionItemViewObject;

class AccountDimensionItemFacade
{
    protected CreateAccountDimensionItemUseCase $createAccountDimensionItemUseCase;

    protected UpdateAccountDimensionItemUseCase $updateAccountDimensionItemUseCase;

    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    protected DocumentElementAccountDimensionItemService $documentElementAccountDimensionItemService;

    protected DocumentAccountDimensionItemService $documentAccountDimensionItemService;

    protected RequestAccountDimensionItemService $requestAccountDimensionItemService;

    protected RequestAllowanceAccountingAccountDimensionItemService $requestAllowanceAccountingAccountDimensionItemService;

    protected RequestAccountingMileageAllowanceAccountDimensionItemService $requestAccountingMileageAllowanceSummaryService;

    protected RequestAccountingTravelExpenseService $requestAccountingTravelExpenseService;

    protected DocumentElementTypeAccountDimensionItemService $documentElementTypeAccountDimensionItemService;

    protected DeactivateAccountDimensionItemUseCase $deactivateAccountDimensionItemUseCase;

    protected UserAccountDimensionItemService $userAccountDimensionItemService;

    public function __construct(
        CreateAccountDimensionItemUseCase $createAccountDimensionItemUseCase,
        UpdateAccountDimensionItemUseCase $updateAccountDimensionItemUseCase,
        AccountDimensionItemRepository $accountDimensionItemRepository,
        DocumentElementAccountDimensionItemService $documentElementAccountDimensionItemService,
        DocumentAccountDimensionItemService $documentAccountDimensionItemService,
        RequestAccountDimensionItemService $requestAccountDimensionItemService,
        RequestAllowanceAccountingAccountDimensionItemService $requestAllowanceAccountingAccountDimensionItemService,
        RequestAccountingMileageAllowanceAccountDimensionItemService $requestAccountingMileageAllowanceSummaryService,
        RequestAccountingTravelExpenseService $requestAccountingTravelExpenseService,
        DocumentElementTypeAccountDimensionItemService $documentElementTypeAccountDimensionItemService,
        DeactivateAccountDimensionItemUseCase $deactivateAccountDimensionItemUseCase,
        UserAccountDimensionItemService $userAccountDimensionItemService
    ) {
        $this->createAccountDimensionItemUseCase = $createAccountDimensionItemUseCase;
        $this->updateAccountDimensionItemUseCase = $updateAccountDimensionItemUseCase;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
        $this->documentElementAccountDimensionItemService = $documentElementAccountDimensionItemService;
        $this->documentAccountDimensionItemService = $documentAccountDimensionItemService;
        $this->requestAccountDimensionItemService = $requestAccountDimensionItemService;
        $this->requestAllowanceAccountingAccountDimensionItemService = $requestAllowanceAccountingAccountDimensionItemService;
        $this->requestAccountingMileageAllowanceSummaryService = $requestAccountingMileageAllowanceSummaryService;
        $this->requestAccountingTravelExpenseService = $requestAccountingTravelExpenseService;
        $this->documentElementTypeAccountDimensionItemService = $documentElementTypeAccountDimensionItemService;
        $this->deactivateAccountDimensionItemUseCase = $deactivateAccountDimensionItemUseCase;
        $this->userAccountDimensionItemService = $userAccountDimensionItemService;
    }

    public function create(StoreAccountDimensionItemInterface $request): AccountDimensionItemViewObject
    {
        return $this->createAccountDimensionItemUseCase->run($request);
    }

    public function update(UpdateAccountDimensionItemInterface $request): AccountDimensionItemViewObject
    {
        return $this->updateAccountDimensionItemUseCase->run($request);
    }

    public function findAccountDimensionItem(
        int $accountDimensionId,
        int $accountDimensionItemId,
        int $instanceId,
        int $companyId = null,
        array $visibilities = []
    ): ?AccountDimensionItemViewObject {
        $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
            $accountDimensionId,
            $accountDimensionItemId,
            $instanceId,
            $companyId,
            $visibilities,
        );

        if ($accountDimensionItem === null) {
            return null;
        }

        return AccountDimensionItemViewObject::createFromAccountDimensionItem($accountDimensionItem);
    }

    public function findAccountDimensionItemCode(
        int $accountDimensionId,
        string $accountDimensionItemCode
    ): ?AccountDimensionItem {
        return $this->accountDimensionItemRepository->findByCodeAndAccountDimensionId(
            $accountDimensionItemCode,
            $accountDimensionId,
        );
    }

    public function setDefaultsForRequest(RequestDto $requestDto): void
    {
        $defaults = $this->requestAccountDimensionItemService->getDefaults($requestDto);

        $this->requestAccountDimensionItemService->connectCollection($requestDto, $defaults);
    }

    public function connectAllWithUser(UserDto $userDto, Collection $accountDimensionItems): void
    {
        $this->userAccountDimensionItemService->connectCollection($userDto, $accountDimensionItems);
    }

    public function disconnectAllFromDocument(DocumentDto $documentDto): void
    {
        $this->documentAccountDimensionItemService->disconnectAll($documentDto);
    }

    public function setDefaultsForDocument(DocumentDto $documentDto): void
    {
        $defaults = $this->documentAccountDimensionItemService->getDefaults($documentDto);

        $this->documentAccountDimensionItemService->connectCollection($documentDto, $defaults);
    }

    public function setDefaultsForDocumentAccounting(DocumentDto $documentDto): void
    {
        $defaults = $this->documentAccountDimensionItemService->getAccountingDefaults($documentDto);

        $this->documentAccountDimensionItemService->connectCollection($documentDto, $defaults);
    }

    public function setDefaultsForDocumentElement(DocumentElementDto $documentElementDto): void
    {
        $defaults = $this->documentElementAccountDimensionItemService->getDefaults($documentElementDto);

        $this->documentElementAccountDimensionItemService->connectCollection($documentElementDto, $defaults);
    }

    public function setDefaultsForAccountingMileageAllowanceSummary(
        RequestAccountingMileageAllowanceDto $accountingMileageAllowanceDto
    ): void {
        $defaults = $this->requestAccountingMileageAllowanceSummaryService->getDefaults($accountingMileageAllowanceDto);

        $this->requestAccountingMileageAllowanceSummaryService->connectCollection(
            $accountingMileageAllowanceDto,
            $defaults,
        );
    }

    public function setDefaultsForAccountingTravelExpenses(
        RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto
    ): void {
        $defaults = $this->requestAccountingTravelExpenseService->getDefaults($requestAccountingTravelExpenseDto);

        $this->requestAccountingTravelExpenseService->connectCollection($requestAccountingTravelExpenseDto, $defaults);
    }

    public function setDefaultsForRequestAllowanceAccounting(RequestDto $requestDto): void
    {
        $defaults = $this->requestAllowanceAccountingAccountDimensionItemService->getAccountingDefaults($requestDto);

        $this->requestAllowanceAccountingAccountDimensionItemService->connectCollection($requestDto, $defaults);
    }

    public function connectWithRequest(
        RequestDto $requestDto,
        Collection $documentDtoCollection,
        AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): RequestAccountDimensionItemViewObject {
        return RequestAccountDimensionItemViewObject::createFromRequestAccountDimensionItem(
            $this->requestAccountDimensionItemService->connect(
                $requestDto,
                $documentDtoCollection,
                $accountDimensionItemViewObject,
            ),
        );
    }

    public function connectWithRequestAccountingAllowances(
        RequestDto $requestDto,
        ?AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): RequestAccountingAccountDimensionItemViewObject {
        return RequestAccountingAccountDimensionItemViewObject::createFromRequestAccountingAccountDimensionItem(
            $this->requestAllowanceAccountingAccountDimensionItemService->connect(
                $requestDto,
                $accountDimensionItemViewObject,
            ),
        );
    }

    public function connectWithDocument(
        DocumentDto $documentDto,
        AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): DocumentAccountDimensionItemViewObject {
        return DocumentAccountDimensionItemViewObject::createFromDocumentAccountDimensionItem(
            $this->documentAccountDimensionItemService->connect($documentDto, $accountDimensionItemViewObject),
        );
    }

    public function connectWithDocumentElement(
        DocumentElementDto $documentElementDto,
        AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): DocumentElementAccountDimensionItemViewObject {
        return DocumentElementAccountDimensionItemViewObject::createFromDocumentElementAccountDimensionItem(
            $this->documentElementAccountDimensionItemService->connect(
                $documentElementDto,
                $accountDimensionItemViewObject,
            ),
        );
    }

    public function connectWithRequestMileageAllowanceSummary(
        RequestAccountingMileageAllowanceDto $accountingMileageAllowanceDto,
        AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): RequestAccountingMileageAllowanceAccountDimensionItemViewObject {
        return RequestAccountingMileageAllowanceAccountDimensionItemViewObject::createFromRequestMileageAllowanceSummaryAccountDimensionItem(
            $this->requestAccountingMileageAllowanceSummaryService->connect(
                $accountingMileageAllowanceDto,
                $accountDimensionItemViewObject,
            ),
        );
    }

    public function connectWithDocumentElementType(
        DocumentElementTypeDto $documentElementTypeDto,
        AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): DocumentElementTypeAccountDimensionItemViewObject {
        $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
            $accountDimensionItemViewObject->getAccountDimensionId(),
            $accountDimensionItemViewObject->getId(),
            $documentElementTypeDto->getInstanceId(),
        );

        return DocumentElementTypeAccountDimensionItemViewObject::createFromDocumentElementTypeAccountDimensionItem(
            $this->documentElementTypeAccountDimensionItemService->connect(
                $documentElementTypeDto->getId(),
                $accountDimensionItem,
            ),
        );
    }

    public function connectWithRequestAccountingTravelExpenses(
        RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto,
        ?AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): RequestAccountingTravelExpenseDimensionItemViewObject {
        return RequestAccountingTravelExpenseDimensionItemViewObject::createFromRequestAccountingTravelExpenseDimensionItem(
            $this->requestAccountingTravelExpenseService->connect(
                $requestAccountingTravelExpenseDto,
                $accountDimensionItemViewObject,
            ),
        );
    }

    public function disconnectOneFromDocument(DocumentDto $documentDto, int $dadId): bool
    {
        return $this->documentAccountDimensionItemService->disconnectOne($documentDto, $dadId);
    }

    public function disconnectOneFromRequest(RequestDto $requestDto, int $radiId): bool
    {
        return $this->requestAccountDimensionItemService->disconnectOne($requestDto, $radiId);
    }

    public function disconnectOneFromRequestAllowanceAccounting(RequestDto $requestDto, int $raadiId): bool
    {
        return $this->requestAllowanceAccountingAccountDimensionItemService->disconnectOne($requestDto, $raadiId);
    }

    public function disconnectOneFromDocumentElement(DocumentElementDto $documentElementDto, int $rdeadiId): bool
    {
        return $this->documentElementAccountDimensionItemService->disconnectOne($documentElementDto, $rdeadiId);
    }

    public function disconnectOneFromRequestMileageAllowanceSummary(
        RequestAccountingMileageAllowanceDto $accountingMileageAllowanceDto,
        int $rmasadiId
    ): bool {
        return $this->requestAccountingMileageAllowanceSummaryService->disconnectOne(
            $accountingMileageAllowanceDto,
            $rmasadiId,
        );
    }

    public function disconnectOneFromDocumentElementType(
        DocumentElementTypeDto $documentElementTypeDto,
        int $detadiId
    ): bool {
        return $this->documentElementTypeAccountDimensionItemService->disconnectOne(
            $documentElementTypeDto->getId(),
            $detadiId,
        );
    }

    public function disconnectOneFromRequestAccountingTravelExpense(
        RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto,
        int $rateadiId
    ): bool {
        return $this->requestAccountingTravelExpenseService->disconnectOne(
            $requestAccountingTravelExpenseDto,
            $rateadiId,
        );
    }

    public function getAvailableForDocumentElementType(InstanceDto $instanceDto): Collection
    {
        return $this->documentElementTypeAccountDimensionItemService
            ->getAvailableForAssignment($instanceDto->getId())
            ->map(function (AccountDimensionWithActiveCompanyItemsWrapper $wrapper) {
                return AccountDimensionWithItemsViewObject::createOptionalFromAccountDimensionWrapper($wrapper);
            });
    }

    /**
     * @param RequestDto $requestDto
     * @return Collection<AccountDimensionWithItemsViewObject>
     */
    public function getAvailableForRequestAccounting(RequestDto $requestDto): Collection
    {
        return $this->requestAccountDimensionItemService
            ->getAvailableForAccounting($requestDto)
            ->map(function (AccountDimensionWithActiveCompanyItemsWrapper $wrapper) {
                return AccountDimensionWithItemsViewObject::createFromAccountDimensionWrapper($wrapper);
            });
    }

    public function getAvailableForRequestAccountingHeader(RequestDto $requestDto): Collection
    {
        return $this->requestAllowanceAccountingAccountDimensionItemService
            ->getAvailableForAccountingHeader($requestDto)
            ->map(function (AccountDimensionWithActiveCompanyItemsWrapper $wrapper) {
                return AccountDimensionWithItemsViewObject::createFromAccountDimensionWrapper($wrapper);
            });
    }

    /**
     * @param RequestDto $requestDto
     * @return Collection<AccountDimensionWithItemsViewObject>
     */
    public function getAvailableForRequestHeader(RequestDto $requestDto): Collection
    {
        return $this->requestAccountDimensionItemService
            ->getAvailableForHeader($requestDto)
            ->map(function (AccountDimensionWithActiveCompanyItemsWrapper $wrapper) {
                return AccountDimensionWithItemsViewObject::createFromAccountDimensionWrapper($wrapper);
            });
    }

    public function getAvailableForDocumentHeader(DocumentDto $documentDto): Collection
    {
        return $this->documentAccountDimensionItemService
            ->getAvailableForHeader($documentDto)
            ->map(function (AccountDimensionWithActiveCompanyItemsWrapper $wrapper) {
                return AccountDimensionWithItemsViewObject::createFromAccountDimensionWrapper($wrapper);
            });
    }

    public function getAvailableForDocumentAccounting(DocumentDto $documentDto): Collection
    {
        return $this->documentAccountDimensionItemService
            ->getAvailableForAccounting($documentDto)
            ->map(function (AccountDimensionWithActiveCompanyItemsWrapper $wrapper) {
                return AccountDimensionWithItemsViewObject::createFromAccountDimensionWrapper($wrapper);
            });
    }

    public function getValueForRequest(
        RequestDto $requestDto,
        int $accountDimensionId
    ): ?RequestAccountDimensionItemViewObject {
        $requestAccountDimensionItem = $this->requestAccountDimensionItemService->getValue(
            $requestDto,
            $accountDimensionId,
        );

        if ($requestAccountDimensionItem === null) {
            return null;
        }

        return RequestAccountDimensionItemViewObject::createFromRequestAccountDimensionItem(
            $requestAccountDimensionItem,
        );
    }

    public function getValueForRequestAllowanceAccounting(
        RequestDto $requestDto,
        int $accountDimensionId
    ): ?RequestAccountingAccountDimensionItemViewObject {
        $requestAllowanceAccountingAccountDimensionItem = $this->requestAllowanceAccountingAccountDimensionItemService->getValue(
            $requestDto,
            $accountDimensionId,
        );

        if ($requestAllowanceAccountingAccountDimensionItem === null) {
            return null;
        }

        return RequestAccountingAccountDimensionItemViewObject::createFromRequestAccountingAccountDimensionItem(
            $requestAllowanceAccountingAccountDimensionItem,
        );
    }

    public function getValueForMileageAllowanceSummary(
        RequestAccountingMileageAllowanceDto $accountingMileageAllowanceDto,
        int $accountDimensionId
    ): ?RequestAccountingMileageAllowanceAccountDimensionItemViewObject {
        $requestMileageAllowanceSummaryAccountDimensionItem = $this->requestAccountingMileageAllowanceSummaryService->getValue(
            $accountingMileageAllowanceDto,
            $accountDimensionId,
        );

        if ($requestMileageAllowanceSummaryAccountDimensionItem === null) {
            return null;
        }

        return RequestAccountingMileageAllowanceAccountDimensionItemViewObject::createFromRequestMileageAllowanceSummaryAccountDimensionItem(
            $requestMileageAllowanceSummaryAccountDimensionItem,
        );
    }

    public function getValueForAccountingTravelExpense(
        RequestAccountingTravelExpenseDto $requestAccountingTravelExpenseDto,
        int $accountDimensionId
    ): ?RequestAccountingTravelExpenseDimensionItemViewObject {
        $requestTravelExpenseAccountDimensionItem = $this->requestAccountingTravelExpenseService->getValue(
            $requestAccountingTravelExpenseDto,
            $accountDimensionId,
        );

        if ($requestTravelExpenseAccountDimensionItem === null) {
            return null;
        }

        return RequestAccountingTravelExpenseDimensionItemViewObject::createFromRequestAccountingTravelExpenseDimensionItem(
            $requestTravelExpenseAccountDimensionItem,
        );
    }

    public function getValueForDocumentHeader(
        DocumentDto $documentDto,
        int $accountDimensionId
    ): ?DocumentAccountDimensionItemViewObject {
        $documentAccountDimensionItem = $this->documentAccountDimensionItemService->getValue(
            $documentDto,
            $accountDimensionId,
        );
        if ($documentAccountDimensionItem === null) {
            return null;
        }

        return DocumentAccountDimensionItemViewObject::createFromDocumentAccountDimensionItem(
            $documentAccountDimensionItem,
        );
    }

    public function getValueForDocumentElement(
        DocumentElementDto $documentElementDto,
        int $accountDimensionId
    ): ?DocumentElementAccountDimensionItemViewObject {
        $documentElementAccountDimensionItem = $this->documentElementAccountDimensionItemService->getValue(
            $documentElementDto,
            $accountDimensionId,
        );
        if ($documentElementAccountDimensionItem === null) {
            return null;
        }

        return DocumentElementAccountDimensionItemViewObject::createFromDocumentElementAccountDimensionItem(
            $documentElementAccountDimensionItem,
        );
    }

    public function disconnectAllFromDocumentElementType(DocumentElementTypeDto $documentElementTypeDto): void
    {
        $this->documentElementTypeAccountDimensionItemService->disconnectAll($documentElementTypeDto->getId());
    }

    public function deactivate(string $slug, User $actor): bool
    {
        $accountDimensionItemId = $this->accountDimensionItemRepository->findBySlugAndInstanceId(
            $slug,
            $actor->instance_id,
        );

        return $this->deactivateAccountDimensionItemUseCase->run($accountDimensionItemId);
    }

    public function getById(int $id, int $instanceId): AccountDimensionItemViewObject
    {
        $accountDimensionItem = $this->accountDimensionItemRepository->findByIdAndInstanceId($id, $instanceId);

        return AccountDimensionItemViewObject::createFromAccountDimensionItem($accountDimensionItem);
    }

    public function getByViewObject(UserAccountDimensionViewObject $accountDimensionViewObject, int $instanceId): AccountDimensionItemViewObject
    {
        $accountDimensionItem = $this->accountDimensionItemRepository->findByIdAndInstanceId($accountDimensionViewObject->accountDimensionItemId, $instanceId);

        return AccountDimensionItemViewObject::createFromAccountDimensionItem(
            $accountDimensionItem,
            $accountDimensionViewObject->type
        );
    }

    public function getBySlug(string $slug, int $instanceId): AccountDimensionItemViewObject
    {
        $accountDimensionItem = $this->accountDimensionItemRepository->findBySlugAndInstanceId($slug, $instanceId);

        return AccountDimensionItemViewObject::createFromAccountDimensionItem($accountDimensionItem);
    }
}
