<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Factories;

use App\Repositories\CompanyRepository;
use Modules\IntegrationFILE\Priv\Interfaces\CreateExportInterface;
use Modules\IntegrationFILE\Pub\Dtos\InitializeExportDto;
use Modules\IntegrationFILE\Pub\Enums\ImportStatusEnum;

class InitializeExportDtoFactory
{
    protected CompanyRepository $companyRepository;

    public function __construct(CompanyRepository $companyRepository)
    {
        $this->companyRepository = $companyRepository;
    }

    public function create(CreateExportInterface $request): InitializeExportDto
    {
        $user = $request->getUser();
        return new InitializeExportDto(
            $request->getType(),
            $request->getSubType(),
            (string)ImportStatusEnum::PENDING(),
            $user->slug,
            $request->getCompanyId() ? $this->companyRepository->findById($request->getCompanyId())->slug : null,
            $user->instance->slug,
        );
    }
}