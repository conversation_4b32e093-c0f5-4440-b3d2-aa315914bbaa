<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use App\Document;
use App\Enum\AvailableLanguagesEnum;
use App\FileSystem\Filesystem;
use App\Instance;
use App\Repositories\CompanyRepository;
use App\Services\SlugGeneratorService;
use App\Vendors\Math;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\Accounting\Pub\ViewObjects\AllowanceLineViewObject;
use Modules\Accounting\Pub\ViewObjects\AllowanceViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentElementViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\SelectedAccountDimensionViewObject;
use Modules\Accounting\Pub\ViewObjects\SettlementSummaryViewObject;
use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Priv\Services\Exporters\Traits\DotsToCommaTrait;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;
use Excel;

class TmhExporter implements SingleFileExporterInterface
{
    use DotsToCommaTrait;

    private const FILE_DATE_FORMAT = 'Ymd';
    private const DATE_FORMAT = 'Y-m-d';

    private const COLUMN_DEBIT = 0;
    private const COLUMN_ACCOUNT = 3;
    private const COLUMN_VAT_CODE = 10;
    private const COLUMN_TEXT = 11;
    private const COLUMN_NAME = 13;
    private const ACCOUNT_PROVIDER = '2440';
    private const ACCOUNT_EMPLOYEE = '2822';
    private const PAYMENT_FORM_CASH = 'gotówka';
    private const PAYMENT_FORM_CARD = 'karta';
    private const ACCOUNT_DIMENSION_PRODUCT = 'PRODUCT_ID';
    private const ACCOUNT_DIMENSION_BUS_PART = 'BUS_PART';

    private const PAYMENT_CONDITION_EMPLOYEE = '002';
    private const PAYMENT_CONDITION_PROVIDER = 'NET';

    private const PAYMENT_METHOD_EMPLOYEE = 'PL1';
    private const PAYMENT_METHOD_PROVIDER = 'MAN';

    private const ACCOUNT_DIMENSION_BRAND = 'BRAND';
    private const ACCOUNT_DIMENSION_STATUS = 'STATUS';

    protected const TMP_DIR_PATH = 'tmp/ase381';

    protected Filesystem $filesystem;

    protected SlugGeneratorService $slugGeneratorService;

    private array $fileNames;
    private Instance $instance;

    public function __construct(Filesystem $filesystem, SlugGeneratorService $slugGeneratorService)
    {
        $this->filesystem = $filesystem;
        $this->slugGeneratorService = $slugGeneratorService;
        $this->fileNames = [];
    }

    public function processCollection(ClaimSummaryCollection $claimSummaryCollection): ExportFileDtoCollection
    {
        $exportFileDtoCollection = new ExportFileDtoCollection();

        $company = resolve(CompanyRepository::class)
            ->findBySlug($claimSummaryCollection->first()->getCompany()->getSlug());
        $this->instance = $company->instance;

        $content = $this->getHeaders();

        /** @var SettlementSummaryViewObject $settlementSummaryViewObject */
        foreach ($claimSummaryCollection as $settlementSummaryViewObject) {
            $content = array_merge($content, $this->processClaimSummary($settlementSummaryViewObject));
        }

        /**
         * @var SettlementSummaryViewObject $claimSummary
         */
        $claimSummary = $claimSummaryCollection->first();

        $relativeTmpPath = $this->persist($content);

        $exportFileDtoCollection->push(
            new ExportFileDto(
                sprintf(
                    '%s_%s',
                    $claimSummary->getCompany()->getCode(),
                    $claimSummary->getSettledAt()->format('d-m-Y')
                ),
                ExportFileTypeEnum::XLSX(),
                $relativeTmpPath
            )
        );

        return $exportFileDtoCollection;
    }

    protected function persist(array $content): string
    {
        $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::XLSX());

        $filePath = sprintf(self::TMP_DIR_PATH . '/%s', $fileName);
        Excel::store(new ExcelArrayExporter($content), $filePath);

        return $filePath;
    }

    protected function processClaimSummary(SettlementSummaryViewObject $settlement): array
    {
        $documentLines = [];

        /**
         * @var DocumentSummaryViewObject $documentSummary
         */
        foreach ($settlement->getDocuments() as $documentSummary) {
            $singleDocumentLines = [];
            /**
             * @var DocumentElementViewObject $documentElement
             */
            foreach ($documentSummary->getDocumentElements() as $documentElement) {
                $documentLine = $this->prepareDocumentLine($settlement, $documentSummary, $documentElement);
                $singleDocumentLines[] = $documentLine;

                if (Math::isGreaterThanZero($documentElement->getInstanceCurrencyVatAmount())) {
                    $vatLine = $documentLine;
                    $vatLine[self::COLUMN_DEBIT] = $documentElement->getInstanceCurrencyVatAmount();
                    $vatLine[self::COLUMN_ACCOUNT] = $documentElement
                        ->getVatNumber()
                        ->getAccountingAccount()
                        ->getCode();
                    $singleDocumentLines[] = $vatLine;
                }
            }

            $firstDebitLine = Arr::first($singleDocumentLines);
            $summaryLine = $this->prepareDocumentSummaryLine($settlement, $documentSummary);
            $summaryLine[self::COLUMN_VAT_CODE] = $firstDebitLine[self::COLUMN_VAT_CODE];
            $summaryLine[self::COLUMN_TEXT] = $firstDebitLine[self::COLUMN_TEXT];

            $singleDocumentLines[] = $summaryLine;

            $documentLines = array_merge($documentLines, $singleDocumentLines);
        }

        $allowanceAmount = '0';
        /** @var AllowanceViewObject $allowance */
        foreach ($settlement->getAllowances() as $allowance) {
            /** @var AllowanceLineViewObject $line */
            foreach ($allowance->getAllowanceLines() as $line) {
                $documentLines[] = $this->prepareAllowanceLine($settlement, $allowance, $line);
                $allowanceAmount = Math::add($allowanceAmount, $line->getAmount());
            }


            $documentLines[] = $this->prepareAllowanceSummaryLine(
                $settlement,
                $allowance,
                Arr::last($documentLines)[self::COLUMN_NAME],
                $allowanceAmount
            );
        }

        return $documentLines;
    }

    private function prepareAllowanceSummaryLine(
        SettlementSummaryViewObject $settlement,
        AllowanceViewObject $allowance,
        string $description,
        string $amount
    ): array {
        $providerErpId = $settlement->getEmployee()->getErpId();
        $documentNumber = $settlement->getNumber();

        return [
            '',
            $amount,
            $settlement->getCompany()->getInstance()->getCurrency()->getCode(),
            '2822',
            '',
            '',
            '',
            '',
            '',
            '',
            0,
            sprintf('%s %s %s', $providerErpId, $documentNumber, $description),
            $providerErpId,
            $description,
            $settlement->getNumber(),
            $settlement->getCompletionDate()
                ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
                : Carbon::now()->format(self::DATE_FORMAT),
            $settlement->getCompletionDate()
                ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
                : Carbon::now()->format(self::DATE_FORMAT),
            $settlement->getNumber(),
            'n/a',
            self::PAYMENT_CONDITION_EMPLOYEE,
            self::PAYMENT_METHOD_EMPLOYEE,
            $settlement->getCompletionDate()
                ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
                : Carbon::now()->format(self::DATE_FORMAT),
            $providerErpId,
            $amount,
            '',
            $amount,
            $allowance->getCurrency()->getCode(),
            $allowance->getCurrency()->getExchangeRate(),
            'n/a',
            'n/a',
            $settlement->getEmployee()->getFullName(),
            ''

        ];
    }

    private function prepareAllowanceLine(
        SettlementSummaryViewObject $settlement,
        AllowanceViewObject $allowance,
        AllowanceLineViewObject $line
    ): array {
        $providerErpId = $settlement->getEmployee()->getErpId();
        $documentNumber = $settlement->getNumber();
        $description = trans($line->getDescriptionSlug(), [], (string)AvailableLanguagesEnum::PL());

        return [
            $line->getAmount(),
            '',
            $settlement->getCompany()->getInstance()->getCurrency()->getCode(),
            $line->getAccountingAccount()->getCode(),
            $line->getMpk()->getCode(),
            '',
            '',
            '',
            '',
            '',
            0,
            sprintf('%s %s %s', $providerErpId, $documentNumber, $description),
            $providerErpId,
            $description,
            $settlement->getNumber(),
            $settlement->getCompletionDate()
                ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
                : Carbon::now()->format(self::DATE_FORMAT),
            $settlement->getCompletionDate()
                ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
                : Carbon::now()->format(self::DATE_FORMAT),
            $settlement->getNumber(),
            'n/a',
            self::PAYMENT_CONDITION_EMPLOYEE,
            self::PAYMENT_METHOD_EMPLOYEE,
            $settlement->getCompletionDate()
                ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
                : Carbon::now()->format(self::DATE_FORMAT),
            $providerErpId,
            '',
            '',
            '',
            $allowance->getCurrency()->getCode(),
            $allowance->getCurrency()->getExchangeRate(),
            'n/a',
            'n/a',
            $settlement->getEmployee()->getFullName(),
            trans($line->getDescriptionSlug())
        ];
    }

    private function prepareDocumentLine(
        SettlementSummaryViewObject $settlement,
        DocumentSummaryViewObject $document,
        DocumentElementViewObject $documentElement
    ): array {
        $elementDimensions = $documentElement->getAccountingDimensions();
        $providerErpId = $document->getProvider()->getErpId();
        $documentNumber = $document->getNumber();
        $expenseName = trans($documentElement->getType()->getShortName(), [], (string)AvailableLanguagesEnum::PL());

        $providerIsEmployee = null !== $document->getProvider()->getEmployee();

        return [
            $documentElement->getInstanceCurrencyNetAmount(),
            '',
            $settlement->getCompany()->getInstance()->getCurrency()->getCode(),
            $documentElement->getAccountingAccount()->getCode(),
            $documentElement->getMpk()->getCode(),
            $this->getAccountingAccountValue($elementDimensions, self::ACCOUNT_DIMENSION_PRODUCT),
            $this->getAccountingAccountValue($elementDimensions, self::ACCOUNT_DIMENSION_BUS_PART),
            $this->getAccountingAccountValue($elementDimensions, self::ACCOUNT_DIMENSION_BRAND),
            $this->getAccountingAccountValue($elementDimensions, self::ACCOUNT_DIMENSION_STATUS),
            $documentElement->getProject() ? $documentElement->getProject()->getCode() : '',
            $documentElement->getVatNumber()->getCode(),
            sprintf('%s %s %s', $providerErpId, $documentNumber, $expenseName),
            $providerErpId,
            $expenseName,
            $documentNumber,
            $document->getIssueDate()->format(self::DATE_FORMAT),
            $document->getIssueDate()->format(self::DATE_FORMAT),
            $settlement->getNumber(),
            $this->getPaymentMethod($document->getPaymentMethod()),
            $providerIsEmployee ? self::PAYMENT_CONDITION_EMPLOYEE : self::PAYMENT_CONDITION_PROVIDER,
            $providerIsEmployee ? self::PAYMENT_METHOD_EMPLOYEE : self::PAYMENT_METHOD_PROVIDER,
            $settlement->getErpAccountedAt()
                ? $settlement->getErpAccountedAt()->format(self::DATE_FORMAT)
                : Carbon::now()->format(self::DATE_FORMAT),
            $settlement->getEmployee()->getErpId(),
            $document->isInForeignCurrency() ? $documentElement->getForeignCurrencyNetAmount() : '',
            '',
            $document->isInForeignCurrency() ? $documentElement->getForeignCurrencyNetAmount() : '',
            $document->getCurrency()->getCode(),
            $document->isInForeignCurrency() ? $document->getCurrency()->getExchangeRate() : 1,
            $document->getProvider()->getName(),
            $document->getProvider()->getTaxReferenceNo(),
            $settlement->getEmployee()->getFullName(),
            $documentElement->getDescription()
        ];
    }

    private function prepareDocumentSummaryLine(
        SettlementSummaryViewObject $settlement,
        DocumentSummaryViewObject $document
    ): array {
        $providerErpId = $document->getProvider()->getErpId();
        $documentNumber = $document->getNumber();
        $providerIsEmployee = null !== $document->getProvider()->getEmployee();

        return [
            '',
            $document->getInstanceCurrencyGrossAmount(),
            $settlement->getCompany()->getInstance()->getCurrency()->getCode(),
            $providerIsEmployee ? self::ACCOUNT_EMPLOYEE : self::ACCOUNT_PROVIDER,
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            sprintf('%s %s', $providerErpId, $documentNumber),
            $providerErpId,
            '',
            $documentNumber,
            $document->getIssueDate()->format(self::DATE_FORMAT),
            $document->getIssueDate()->format(self::DATE_FORMAT),
            $settlement->getNumber(),
            $this->getPaymentMethod($document->getPaymentMethod()),
            $providerIsEmployee ? self::PAYMENT_CONDITION_EMPLOYEE : self::PAYMENT_CONDITION_PROVIDER,
            $providerIsEmployee ? self::PAYMENT_METHOD_EMPLOYEE : self::PAYMENT_METHOD_PROVIDER,
            $settlement->getErpAccountedAt()
                ? $settlement->getErpAccountedAt()->format(self::DATE_FORMAT)
                : Carbon::now()->format(self::DATE_FORMAT),
            $settlement->getEmployee()->getErpId(),
            $document->getForeignCurrencyNetAmount(),
            $document->getForeignCurrencyVatAmount(),
            $document->getForeignCurrencyGrossAmount(),
            $document->getCurrency()->getCode(),
            $document->isInForeignCurrency() ? $document->getCurrency()->getExchangeRate() : 1,
            $document->getProvider()->getName(),
            $document->getProvider()->getTaxReferenceNo(),
            $settlement->getEmployee()->getFullName(),
            ''
        ];
    }

    private function getPaymentMethod(string $paymentMethod): string
    {
        return $paymentMethod === Document::PAYMENT_TYPE_OWN
            ? self::PAYMENT_FORM_CASH
            : self::PAYMENT_FORM_CARD;
    }

    private function getAccountingAccountValue(Collection $accountDimensions, string $accountDimensionCode): ?string
    {
        $accountDimensionValue = null;

        /**
         * @var SelectedAccountDimensionViewObject $accountDimension
         * @description Account dimension Kasa oddziału has selected value
         */
        $accountDimension = $accountDimensions
            ->filter(
                fn(SelectedAccountDimensionViewObject $accountDimensionItem) => $accountDimensionItem
                        ->getAccountDimensionViewObject()
                        ->getCode() === $accountDimensionCode
            )
            ->first();

        if ($accountDimension !== null) {
            $accountDimensionValue = $accountDimension
                ->getAccountDimensionItemViewObject()
                ->getCode();
        }

        return $accountDimensionValue;
    }

    private function getHeaders(): array
    {
        return [
            [
                'Debit',
                'Credit',
                'Waluta',
                'Account',
                'Cost center',
                'Prod. ID', // account dim: PRODUCT_ID
                'Bus Part', // account dim: BUS_PART
                'Brand/Var', // account dim: BRAND
                'Status', // account dim: STATUS
                'Project/div', // account dim: PROJ_VAR
                'VAT code',
                'Text',
                'ERP_ID', // provider
                'Nazwa', // expense type
                'Numer faktury',
                'Data faktury',
                'Data ob. VAT',
                'Numer rozliczenia',
                'Forma płatności',
                'War płatności',
                'Metoda płatności',
                'Data rozliczenia',
                'ERP_ID pracownika',
                'Kwota netto',
                'Kwota VAT',
                'Kwota brutto',
                'Waluta',
                'Kurs',
                'Nazwa dostawcy',
                'NIP dostawcy z faktury',
                'Imię nazwisko pracownika',
                'Opis linii',
            ],
        ];
    }
}
