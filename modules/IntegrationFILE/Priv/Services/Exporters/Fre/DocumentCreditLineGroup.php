<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters\Fre;

class DocumentCreditLineGroup
{
    private string $groupAmount;
    private DocumentCreditLines $group;
    private string $accountNumber;

    public function __construct(string $groupAmount, DocumentCreditLines $group, string $accountNumber)
    {
        $this->groupAmount = $groupAmount;
        $this->group = $group;
        $this->accountNumber = $accountNumber;
    }

    public function getGroupAmount(): string
    {
        return $this->groupAmount;
    }

    public function getGroup(): DocumentCreditLines
    {
        return $this->group;
    }

    public function getAccountNumber(): string
    {
        return $this->accountNumber;
    }
}
