<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use App\Document;
use App\Enum\AvailableLanguagesEnum;
use App\FileSystem\Filesystem;
use App\Request;
use App\Services\SlugGeneratorService;
use App\Vendors\Math;
use Modules\Accounting\Pub\ViewObjects\AllowanceLineViewObject;
use Modules\Accounting\Pub\ViewObjects\AllowanceViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentElementViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\SelectedAccountDimensionViewObject;
use Modules\Accounting\Pub\ViewObjects\SettlementSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\VatSummaryViewObject;
use Modules\Common\ValueObjects\RequestType;
use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Priv\Services\Exporters\Traits\DotsToCommaTrait;
use Modules\IntegrationFILE\Priv\Services\Exporters\Traits\UtfToAnsiTrait;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;
use Excel;

class StcExporter implements SingleFileExporterInterface
{
    use DotsToCommaTrait, UtfToAnsiTrait;

    private const DATE_FORMAT = 'd.m.Y';
    protected const TMP_DIR_PATH = 'tmp/pir';
    private const DEFAULT_CURRENCY_CODE = 'PLN';
    private const DEFAULT_PAYMENT_TERMS = 'PNAT';
    private const DEFAULT_PAYMENT_FORM = 'P';
    private const PURCHASE_ACCOUNT_CODE = '**********';
    private const LINE_ASSIGNMENT_PAYMENT_TYPE_OWN = 'ZALICZKA';
    private const LINE_ASSIGNMENT_PAYMENT_TYPE_SERVICE_CARD = 'INNE';
    private const LINE_DIM_TAX_GROUP_TEXT_CODE_DEFAULT = 'KBP';
    private const LINE_DIM_TAX_DIFFERENCE_KIND_CODE_DEFAULT = 'B';
    private const LINE_ACCOUNT_DIM_TAX_DIFFERENCE_SUBJECT_EMPTY_CODE = 'BRAK';

    protected const COLUMN_DOCUMENT_DATE = 0;
    protected const COLUMN_DOCUMENT_TYPE = 1;
    protected const COLUMN_UNIT = 2;
    protected const COLUMN_ACCOUNTING_DATE = 4;
    protected const COLUMN_DOCUMENT_CURRENCY = 6;
    protected const COLUMN_REFERENCE = 7;
    protected const COLUMN_HEADER_TEXT = 8;
    protected const COLUMN_ACCOUNTING_CODE = 9;
    protected const COLUMN_ACCOUNT = 10;
    protected const COLUMN_AMOUNT = 11;
    protected const COLUMN_CURRENCY_CODE = 12;
    protected const COLUMN_DESCRIPTION = 13;
    protected const COLUMN_MPK = 14;
    protected const COLUMN_TAX_CODE = 15;
    protected const COLUMN_ASSIGNMENT = 16;
    protected const COLUMN_PAYMENT_DATE = 18;
    protected const COLUMN_PAYMENT_TERMS = 19;
    protected const COLUMN_PAYMENT_FORM = 20;
    protected const COLUMN_TAX_GROUP = 21;
    protected const COLUMN_TAX_DIFFERENCE_KIND = 22;
    protected const COLUMN_TAX_DIFFERENCE_SUBJECT = 23;
    protected const COLUMN_TAX_DATE = 24;
    protected const COLUMN_REFERENCE_KEY = 25;

    private const LINE_ACCOUNTING_CODE_CREDIT = 31;
    private const LINE_ACCOUNTING_CODE_DEBIT = 40;
    private const LINE_ACCOUNTING_CODE_CORR_CREDIT = 21;
    private const LINE_ACCOUNTING_CODE_CORR_DEBIT = 50;

    private const ACCOUNT_DIM_PURCHASE_REGISTER_TYPE_CODE = 'RODZAJ_REJESTRU_ZAKUPU';
    private const ACCOUNT_DIM_DOCUMENT_HEADER_TEXT_CODE = 'TND';
    private const ACCOUNT_DIM_TAX_GROUP_TEXT_CODE = 'KWALIFIKACJA_PODATKOWA';
    private const ACCOUNT_DIM_TAX_DIFFERENCE_KIND_CODE = 'RODZAJ_ROZNICY_PODATKOWEJ';
    private const ACCOUNT_DIM_TAX_DIFFERENCE_SUBJECT_CODE = 'TYTUL_ROZNICY_PODATKOWEJ';

    protected Filesystem $filesystem;

    protected SlugGeneratorService $slugGeneratorService;

    private int $decretNumber;

    public function __construct(Filesystem $filesystem, SlugGeneratorService $slugGeneratorService)
    {
        $this->filesystem = $filesystem;
        $this->slugGeneratorService = $slugGeneratorService;
        $this->decretNumber = 0;
    }

    public function processCollection(ClaimSummaryCollection $claimSummaryCollection): ExportFileDtoCollection
    {
        $exportFileDtoCollection = new ExportFileDtoCollection();

        $content = $this->getHeaders();

        /** @var SettlementSummaryViewObject $settlementSummaryViewObject */
        foreach ($claimSummaryCollection as $settlementSummaryViewObject) {
            $content = array_merge($content, $this->processClaimSummary($settlementSummaryViewObject));
        }
        /**
         * @var SettlementSummaryViewObject $claimSummary
         */
        $claimSummary = $claimSummaryCollection->first();

        $relativeTmpPath = $this->persist($content);

        $exportFileDtoCollection->push(
            new ExportFileDto(
                sprintf(
                    '%s_%s',
                    $claimSummary->getEmployee()->getInitials(),
                    $claimSummary->getSettledAt()->format('d-m-Y')
                ),
                ExportFileTypeEnum::XLSX(),
                $relativeTmpPath
            )
        );
        return $exportFileDtoCollection;
    }

    protected function persist(array $content): string
    {
        $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::XLSX());

        $filePath = sprintf(self::TMP_DIR_PATH . '/%s', $fileName);
        Excel::store(new ExcelArrayExporter($content), $filePath);

        return $filePath;
    }

    protected function processClaimSummary(SettlementSummaryViewObject $settlement): array
    {
        $lines = $this->preparePostingOnVendorDocumentLines($settlement);
        $lines = array_merge($lines, $this->preparePostingOnEmployeeDocumentLinesWithAllowances($settlement));

        return array_merge($lines, $this->prepareAllowances($settlement));
    }

    private function prepareAllowances(SettlementSummaryViewObject $settlement): array
    {
        if ($settlement->getType()->isEqual(new RequestType(Request::TYPE_EXPENSE))) {
            return [];
        }

        $summaryLine = $this->prepareBaseDocumentLineRow();

        $summaryLine[self::COLUMN_DOCUMENT_DATE] = $settlement->getSettledAt() ? $settlement->getSettledAt()->format(
            self::DATE_FORMAT
        ) : '';

        $summaryLine[self::COLUMN_DOCUMENT_TYPE] = 'DL';
        $summaryLine[self::COLUMN_UNIT] = $settlement->getCompany()->getCode();
        $summaryLine[self::COLUMN_ACCOUNTING_DATE] = $settlement->getCompletionDate()
            ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
            : '';

        $summaryLine[self::COLUMN_DOCUMENT_CURRENCY] = self::DEFAULT_CURRENCY_CODE;
        $summaryLine[self::COLUMN_REFERENCE] = $settlement->getNumber();
        $summaryLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_CREDIT;
        $summaryLine[self::COLUMN_ACCOUNT] = $settlement->getEmployee()->getErpId();
        $summaryLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
        $summaryLine[self::COLUMN_ASSIGNMENT] = self::LINE_ASSIGNMENT_PAYMENT_TYPE_OWN;
        $summaryLine[self::COLUMN_PAYMENT_DATE] = $summaryLine[self::COLUMN_DOCUMENT_DATE];
        $summaryLine[self::COLUMN_PAYMENT_TERMS] = self::DEFAULT_PAYMENT_TERMS;
        $summaryLine[self::COLUMN_PAYMENT_FORM] = self::DEFAULT_PAYMENT_FORM;
        $summaryLine[self::COLUMN_TAX_DATE] = $settlement->getCompletionDate()
            ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
            : '';
        $summaryLine[self::COLUMN_REFERENCE_KEY] = $settlement->getNumber();

        $totalAmount = '0';
        $description = [];
        $allowanceLines = [];
        /**
         * @var AllowanceViewObject $allowance
         */
        foreach ($settlement->getAllowances() as $allowance) {
            /**
             * @var AllowanceLineViewObject $allowanceLine
             */
            foreach ($allowance->getAllowanceLines() as $allowanceLine) {
                $totalAmount = Math::add($totalAmount, $allowanceLine->getAmount());
                if (!isset($description[$allowanceLine->getDescriptionSlug()])) {
                    $description[$allowanceLine->getDescriptionSlug()] = trans(
                        $allowanceLine->getDescriptionSlug(),
                        [],
                        (string)AvailableLanguagesEnum::PL()
                    );
                }

                $preparedLine = $this->prepareBaseDocumentLineRow();
                $preparedLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_DEBIT;
                $preparedLine[self::COLUMN_ACCOUNT] = $allowanceLine->getAccountingAccount()->getCode();
                $preparedLine[self::COLUMN_AMOUNT] = $allowanceLine->getAmount();
                $preparedLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
                $preparedLine[self::COLUMN_MPK] = $allowanceLine->getMpk()->getCode();
                $preparedLine[self::COLUMN_ASSIGNMENT] = $settlement->getNumber();
                $preparedLine[self::COLUMN_TAX_GROUP] = self::LINE_DIM_TAX_GROUP_TEXT_CODE_DEFAULT;
                $preparedLine[self::COLUMN_TAX_DIFFERENCE_KIND] = self::LINE_DIM_TAX_DIFFERENCE_KIND_CODE_DEFAULT;

                $preparedLine[self::COLUMN_REFERENCE_KEY] = $summaryLine[self::COLUMN_REFERENCE_KEY];
                $allowanceLines[] = $preparedLine;
            }
        }

        if (!Math::isGreaterThanZero($totalAmount)) {
            return [];
        }

        $summaryLine[self::COLUMN_AMOUNT] = $totalAmount;
        $summaryLine[self::COLUMN_DESCRIPTION] = sprintf(
            '%s %s',
            implode(', ', $description),
            $settlement->getNumber()
        );

        $debitLines = [];
        /**
         * @var AllowanceViewObject $allowance
         */
        foreach ($settlement->getAllowances() as $allowance) {
            $debitLines = $this->prepareAllowanceDebitLines(
                $settlement,
                $totalAmount,
                $summaryLine[self::COLUMN_DESCRIPTION]
            );
        }

        foreach ($allowanceLines as $allowanceLine) {
            $allowanceLine[self::COLUMN_DESCRIPTION] = $summaryLine[self::COLUMN_DESCRIPTION];
        }

        return array_merge([$summaryLine], $debitLines, $allowanceLines);
    }

    private function prepareAllowanceDebitLines(
        SettlementSummaryViewObject $settlement,
        string $totalAmount,
        string $description
    ): array {
        $preparedLine = $this->prepareBaseDocumentLineRow();
        $preparedLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_DEBIT;
        $preparedLine[self::COLUMN_ACCOUNT] = self::PURCHASE_ACCOUNT_CODE;
        $preparedLine[self::COLUMN_AMOUNT] = $totalAmount;
        $preparedLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
        $preparedLine[self::COLUMN_DESCRIPTION] = $description;
        $preparedLine[self::COLUMN_TAX_CODE] = 'XV';
        $preparedLine[self::COLUMN_ASSIGNMENT] = $settlement->getNumber();
        $preparedLine[self::COLUMN_REFERENCE_KEY] = $settlement->getNumber();

        $secondLine = $preparedLine;
        $secondLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_CORR_DEBIT;

        return [$preparedLine, $secondLine];
    }

    protected function preparePostingOnVendorDocumentLines(SettlementSummaryViewObject $settlement): array
    {
        $documentLines = [];
        /**
         * @var $document DocumentSummaryViewObject
         */
        foreach ($settlement->getDocuments() as $document) {
            if ($document->getProvider() && $document->getProvider()->getEmployee() !== null) {
                continue;
            }

            $documentLine = $this->prepareDocumentSummaryLine($document, $settlement);
            $documentLines[] = $documentLine;

            /** @var DocumentElementViewObject $line */
            $documentLines = $this->getPostingLines($document, $documentLines, $settlement, $documentLine);

            if ($this->needTransferOnEmployee($document)) {
                $vendorLine = $this->prepareBaseDocumentLineRow();
                $vendorLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_CORR_CREDIT;
                $vendorLine[self::COLUMN_ACCOUNT] = $document->getProvider()
                    ? $document->getProvider()->getErpId()
                    : '';
                $vendorLine[self::COLUMN_AMOUNT] = $document->getInstanceCurrencyGrossAmount();
                $vendorLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
                $vendorLine[self::COLUMN_ASSIGNMENT] = $documentLine[self::COLUMN_REFERENCE];
                $vendorLine[self::COLUMN_PAYMENT_DATE] = $document->getReceivedDate()
                    ? $document->getReceivedDate()->format(self::DATE_FORMAT)
                    : '';
                $vendorLine[self::COLUMN_REFERENCE_KEY] = $settlement->getNumber();


                $employeeLine = $vendorLine;
                $employeeLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_CREDIT;
                $employeeLine[self::COLUMN_ACCOUNT] = $settlement->getEmployee()->getErpId();
                $employeeLine[self::COLUMN_DESCRIPTION] = $documentLine[self::COLUMN_DESCRIPTION];
                $employeeLine[self::COLUMN_ASSIGNMENT] = $documentLine[self::COLUMN_ASSIGNMENT];
                $employeeLine[self::COLUMN_PAYMENT_TERMS] = self::DEFAULT_PAYMENT_TERMS;
                $employeeLine[self::COLUMN_PAYMENT_FORM] = self::DEFAULT_PAYMENT_FORM;

                $documentLines[] = $vendorLine;
                $documentLines[] = $employeeLine;
            }

            $documentLines = array_merge($documentLines, $this->prepareDocumentVatLines($document, $settlement));
        }

        return $documentLines;
    }

    private function preparePostingOnEmployeeDocumentLinesWithAllowances(SettlementSummaryViewObject $settlement): array
    {
        $documentLines = [];
        /**
         * @var $document DocumentSummaryViewObject
         */
        foreach ($settlement->getDocuments() as $document) {
            if ($document->getProvider() && $document->getProvider()->getEmployee() === null) {
                continue;
            }

            $documentLine = $this->preparePostingOnEmployeeSummaryLine($document, $settlement);
            $documentLines[] = $documentLine;

            /** @var DocumentElementViewObject $line */
            $documentLines = $this->getPostingLines($document, $documentLines, $settlement, $documentLine);

            $documentLines = array_merge($documentLines, $this->prepareDocumentVatLines($document, $settlement));
        }

        return $documentLines;
    }

    private function prepareDebitLines(
        DocumentElementViewObject $line,
        DocumentSummaryViewObject $document,
        SettlementSummaryViewObject $settlement
    ): array {
        $preparedLine = $this->prepareBaseDocumentLineRow();
        $preparedLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_DEBIT;

        $preparedLine[self::COLUMN_TAX_CODE] = $line->getVatNumber()->getCode();
        $preparedLine[self::COLUMN_ACCOUNT] = self::PURCHASE_ACCOUNT_CODE;
        $preparedLine[self::COLUMN_AMOUNT] = $line->getInstanceCurrencyNetAmount();
        $preparedLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
        $preparedLine[self::COLUMN_DESCRIPTION] = $document->getDescription();
        $preparedLine[self::COLUMN_ASSIGNMENT] = $document->getNumber();
        $preparedLine[self::COLUMN_REFERENCE_KEY] = $settlement->getNumber();

        $secondLine = $preparedLine;
        $secondLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_CORR_DEBIT;

        if (Math::isGreaterThanZero($line->getVatNumber()->getValue())) {
            $secondLine[self::COLUMN_TAX_CODE] = 'XV';
        }

        return [$preparedLine, $secondLine];
    }

    private function preparePostingOnEmployeeSummaryLine(
        DocumentSummaryViewObject $document,
        SettlementSummaryViewObject $settlement
    ): array {
        $preparedLine = $this->prepareBaseDocumentLineRow();

        $preparedLine[self::COLUMN_DOCUMENT_DATE] = $document->getIssueDate() ? $document->getIssueDate()->format(
            self::DATE_FORMAT
        ) : '';

        /**
         * @var SelectedAccountDimensionViewObject $accountDimensionPurchaseRegisterType
         * @description Account dimension Kasa oddziału has selected value
         */
        $accountDimensionPurchaseRegisterType = $document
            ->getAccountingDimensions()
            ->filter(
                fn(SelectedAccountDimensionViewObject $accountDimension) => $accountDimension
                        ->getAccountDimensionViewObject()
                        ->getCode() === self::ACCOUNT_DIM_PURCHASE_REGISTER_TYPE_CODE
            )
            ->first();

        if ($accountDimensionPurchaseRegisterType !== null) {
            $preparedLine[self::COLUMN_DOCUMENT_TYPE] = $accountDimensionPurchaseRegisterType
                ->getAccountDimensionItemViewObject()
                ->getCode();
        }

        $preparedLine[self::COLUMN_UNIT] = $settlement->getCompany()->getCode();
        $preparedLine[self::COLUMN_ACCOUNTING_DATE] = $settlement->getCompletionDate()
            ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
            : '';

        $preparedLine[self::COLUMN_DOCUMENT_CURRENCY] = self::DEFAULT_CURRENCY_CODE;
        $preparedLine[self::COLUMN_REFERENCE] = $document->getNumber();


        /**
         * @var SelectedAccountDimensionViewObject $accountDimensionDocumentHeaderText
         * @description Account dimension Kasa oddziału has selected value
         */
        $accountDimensionDocumentHeaderText = $document
            ->getAccountingDimensions()
            ->filter(
                fn(SelectedAccountDimensionViewObject $accountDimension) => $accountDimension
                        ->getAccountDimensionViewObject()
                        ->getCode() === self::ACCOUNT_DIM_DOCUMENT_HEADER_TEXT_CODE
            )
            ->first();

        if ($accountDimensionDocumentHeaderText !== null) {
            $preparedLine[self::COLUMN_HEADER_TEXT] = $accountDimensionDocumentHeaderText
                ->getAccountDimensionItemViewObject()
                ->getCode();
        }

        $preparedLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_CREDIT;
        $preparedLine[self::COLUMN_ACCOUNT] = $settlement->getEmployee()->getErpId();
        $preparedLine[self::COLUMN_AMOUNT] = $document->getInstanceCurrencyGrossAmount();
        $preparedLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
        $preparedLine[self::COLUMN_DESCRIPTION] = $document->getDescription();

        if ($document->getPaymentMethod() === Document::PAYMENT_TYPE_OWN) {
            $preparedLine[self::COLUMN_ASSIGNMENT] = self::LINE_ASSIGNMENT_PAYMENT_TYPE_OWN;
        } elseif ($document->getPaymentMethod() === Document::PAYMENT_TYPE_SERVICE_CARD) {
            $preparedLine[self::COLUMN_ASSIGNMENT] = self::LINE_ASSIGNMENT_PAYMENT_TYPE_SERVICE_CARD;
        }

        $preparedLine[self::COLUMN_PAYMENT_DATE] = $document->getReceivedDate()
            ? $document->getReceivedDate()->format(self::DATE_FORMAT)
            : '';

        $preparedLine[self::COLUMN_PAYMENT_TERMS] = self::DEFAULT_PAYMENT_TERMS;
        $preparedLine[self::COLUMN_PAYMENT_FORM] = self::DEFAULT_PAYMENT_FORM;
        $preparedLine[self::COLUMN_TAX_DATE] = $settlement->getCompletionDate()
            ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
            : '';
        $preparedLine[self::COLUMN_REFERENCE_KEY] = $settlement->getNumber();

        return $preparedLine;
    }

    private function prepareDocumentPostingLine(
        DocumentElementViewObject $line,
        DocumentSummaryViewObject $document,
        array $summaryLine
    ): array {
        $preparedLine = $this->prepareBaseDocumentLineRow();
        $preparedLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_DEBIT;
        $preparedLine[self::COLUMN_ACCOUNT] = $line->getAccountingAccount()->getCode();
        $preparedLine[self::COLUMN_AMOUNT] = $line->getInstanceCurrencyNetAmount();
        $preparedLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
        $preparedLine[self::COLUMN_DESCRIPTION] = $line->getDescription();
        $preparedLine[self::COLUMN_MPK] = $line->getMpk()->getCode();
        $preparedLine[self::COLUMN_ASSIGNMENT] = $summaryLine[self::COLUMN_REFERENCE];

        /**
         * @var SelectedAccountDimensionViewObject $accountDimensionTaxGroup
         * @description Account dimension Kasa oddziału has selected value
         */
        $accountDimensionTaxGroup = $line
            ->getAccountingDimensions()
            ->filter(
                fn(SelectedAccountDimensionViewObject $accountDimension) => $accountDimension
                        ->getAccountDimensionViewObject()
                        ->getCode() === self::ACCOUNT_DIM_TAX_GROUP_TEXT_CODE
            )
            ->first();

        if ($accountDimensionTaxGroup !== null) {
            $preparedLine[self::COLUMN_TAX_GROUP] = $accountDimensionTaxGroup
                ->getAccountDimensionItemViewObject()
                ->getCode();
        }

        /**
         * @var SelectedAccountDimensionViewObject $accountDimensionTaxDifferenceKind
         * @description Account dimension Kasa oddziału has selected value
         */
        $accountDimensionTaxDifferenceKind = $line
            ->getAccountingDimensions()
            ->filter(
                fn(SelectedAccountDimensionViewObject $accountDimension) => $accountDimension
                        ->getAccountDimensionViewObject()
                        ->getCode() === self::ACCOUNT_DIM_TAX_DIFFERENCE_KIND_CODE
            )
            ->first();

        if ($accountDimensionTaxDifferenceKind !== null) {
            $preparedLine[self::COLUMN_TAX_DIFFERENCE_KIND] = $accountDimensionTaxDifferenceKind
                ->getAccountDimensionItemViewObject()
                ->getCode();
        }

        /**
         * @var SelectedAccountDimensionViewObject $accountDimensionTaxDifferenceSubject
         * @description Account dimension Kasa oddziału has selected value
         */
        $accountDimensionTaxDifferenceSubject = $line
            ->getAccountingDimensions()
            ->filter(
                fn(SelectedAccountDimensionViewObject $accountDimension) => $accountDimension
                        ->getAccountDimensionViewObject()
                        ->getCode() === self::ACCOUNT_DIM_TAX_DIFFERENCE_SUBJECT_CODE
            )
            ->first();

        if ($accountDimensionTaxDifferenceSubject !== null) {
            $code = $accountDimensionTaxDifferenceSubject->getAccountDimensionItemViewObject()->getCode();

            if ($code === self::LINE_ACCOUNT_DIM_TAX_DIFFERENCE_SUBJECT_EMPTY_CODE) {
                $code = '';
            }

            $preparedLine[self::COLUMN_TAX_DIFFERENCE_SUBJECT] = $code;
        }

        $preparedLine[self::COLUMN_REFERENCE_KEY] = $summaryLine[self::COLUMN_REFERENCE_KEY];

        return $preparedLine;
    }

    private function prepareDocumentVatLines(
        DocumentSummaryViewObject $document,
        SettlementSummaryViewObject $settlement
    ): array {
        $vatLines = [];
        /**
         * @var VatSummaryViewObject $vatSummary
         */
        foreach ($document->getVatSummaries() as $vatSummary) {
            if (!Math::isGreaterThanZero($vatSummary->getVatAmount())) {
                continue;
            }

            $vatLine = $this->prepareBaseDocumentLineRow();
            $vatLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_DEBIT;
            $vatLine[self::COLUMN_ACCOUNT] = $vatSummary->getAccountingAccount()->getCode();
            $vatLine[self::COLUMN_AMOUNT] = $vatSummary->getVatAmount();
            $vatLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
            $vatLine[self::COLUMN_DESCRIPTION] = $document->getDescription();
            $vatLine[self::COLUMN_TAX_CODE] = $vatSummary->getVatNumber()->getCode();
            $vatLine[self::COLUMN_ASSIGNMENT] = $document->getNumber();
            $vatLine[self::COLUMN_REFERENCE_KEY] = $settlement->getNumber();

            $vatLines[] = $vatLine;
        }

        return $vatLines;
    }

    private function needTransferOnEmployee(DocumentSummaryViewObject $document): bool
    {
        return $document->getProvider() !== null
            && $document->getProvider()->getEmployee() === null
            && in_array(
                $document->getPaymentMethod(),
                [Document::PAYMENT_TYPE_OWN, Document::PAYMENT_TYPE_SERVICE_CARD]
            );
    }

    protected function prepareDocumentSummaryLine(
        DocumentSummaryViewObject $document,
        SettlementSummaryViewObject $settlement
    ): array {
        $preparedLine = $this->prepareBaseDocumentLineRow();

        $preparedLine[self::COLUMN_DOCUMENT_DATE] = $document->getIssueDate() ? $document->getIssueDate()->format(
            self::DATE_FORMAT
        ) : '';

        /**
         * @var SelectedAccountDimensionViewObject $accountDimensionPurchaseRegisterType
         * @description Account dimension Kasa oddziału has selected value
         */
        $accountDimensionPurchaseRegisterType = $document
            ->getAccountingDimensions()
            ->filter(
                fn(SelectedAccountDimensionViewObject $accountDimension) => $accountDimension
                        ->getAccountDimensionViewObject()
                        ->getCode() === self::ACCOUNT_DIM_PURCHASE_REGISTER_TYPE_CODE
            )
            ->first();

        if ($accountDimensionPurchaseRegisterType !== null) {
            $preparedLine[self::COLUMN_DOCUMENT_TYPE] = $accountDimensionPurchaseRegisterType
                ->getAccountDimensionItemViewObject()
                ->getCode();
        }

        $preparedLine[self::COLUMN_UNIT] = $settlement->getCompany()->getCode();
        $preparedLine[self::COLUMN_ACCOUNTING_DATE] = $settlement->getCompletionDate()
            ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
            : '';

        $preparedLine[self::COLUMN_DOCUMENT_CURRENCY] = self::DEFAULT_CURRENCY_CODE;
        $preparedLine[self::COLUMN_REFERENCE] = $document->getNumber();

        /**
         * @var SelectedAccountDimensionViewObject $accountDimensionDocumentHeaderText
         * @description Account dimension Kasa oddziału has selected value
         */
        $accountDimensionDocumentHeaderText = $document
            ->getAccountingDimensions()
            ->filter(
                fn(SelectedAccountDimensionViewObject $accountDimension) => $accountDimension
                        ->getAccountDimensionViewObject()
                        ->getCode() === self::ACCOUNT_DIM_DOCUMENT_HEADER_TEXT_CODE
            )
            ->first();

        if ($accountDimensionDocumentHeaderText !== null) {
            $preparedLine[self::COLUMN_HEADER_TEXT] = $accountDimensionDocumentHeaderText
                ->getAccountDimensionItemViewObject()
                ->getCode();
        }

        $preparedLine[self::COLUMN_ACCOUNTING_CODE] = self::LINE_ACCOUNTING_CODE_CREDIT;
        $preparedLine[self::COLUMN_ACCOUNT] = $document->getProvider() ? $document->getProvider()->getErpId() : '';
        $preparedLine[self::COLUMN_AMOUNT] = $document->getInstanceCurrencyGrossAmount();
        $preparedLine[self::COLUMN_CURRENCY_CODE] = self::DEFAULT_CURRENCY_CODE;
        $preparedLine[self::COLUMN_DESCRIPTION] = $document->getDescription();

        if ($document->getPaymentMethod() === Document::PAYMENT_TYPE_OWN) {
            $preparedLine[self::COLUMN_ASSIGNMENT] = self::LINE_ASSIGNMENT_PAYMENT_TYPE_OWN;
        } elseif ($document->getPaymentMethod() === Document::PAYMENT_TYPE_SERVICE_CARD) {
            $preparedLine[self::COLUMN_ASSIGNMENT] = self::LINE_ASSIGNMENT_PAYMENT_TYPE_SERVICE_CARD;
        }

        $preparedLine[self::COLUMN_PAYMENT_DATE] = $document->getReceivedDate()
            ? $document->getReceivedDate()->format(self::DATE_FORMAT)
            : '';

        $preparedLine[self::COLUMN_PAYMENT_TERMS] = self::DEFAULT_PAYMENT_TERMS;
        $preparedLine[self::COLUMN_PAYMENT_FORM] = self::DEFAULT_PAYMENT_FORM;
        $preparedLine[self::COLUMN_TAX_DATE] = $settlement->getCompletionDate()
            ? $settlement->getCompletionDate()->format(self::DATE_FORMAT)
            : '';
        $preparedLine[self::COLUMN_REFERENCE_KEY] = $settlement->getNumber();

        return $preparedLine;
    }

    protected function prepareBaseDocumentLineRow(): array
    {
        return [
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '', // empty
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
        ];
    }

    private function getHeaders(): array
    {
        return [
            [
                'Data dok.',
                'Rdz.dok.',
                'Jedn. gospod.',
                'Rok obrotowy',
                'Data księgow.',
                'Okres',
                'Waluta',
                'Referen.',
                'Tek.nagł.dok.',
                'KK',
                'Konto',
                'Kwota',
                'Wal.',
                'Opis',
                'MPK',
                'Pd',
                'Przypis.',
                'Nr zlec.',
                'Pł. Netto',
                'War. płatn.',
                'Forma płatn,',
                'Kwal pod.',
                'Rodz.różn.pod.',
                'Tytuł różn.pod.',
                'Data dekl. pod.',
                'Klucz refer. 3',
            ],
        ];
    }

    protected function getPostingLines(
        DocumentSummaryViewObject $document,
        $documentLines,
        SettlementSummaryViewObject $settlement,
        array $documentLine
    ): array {
        foreach ($document->getDocumentElements() as $line) {
            if (!Math::isEqualZero($line->getInstanceCurrencyNetAmount())) {
                $documentLines = array_merge(
                    $documentLines,
                    $this->prepareDebitLines($line, $document, $settlement)
                );
            }
        }

        /** @var DocumentElementViewObject $line */
        foreach ($document->getDocumentElements() as $line) {
            if (!Math::isEqualZero($line->getInstanceCurrencyNetAmount())) {
                $preparedDocumentLine = $this->prepareDocumentPostingLine($line, $document, $documentLine);

                $documentLines[] = $preparedDocumentLine;
            }
        }
        return $documentLines;
    }
}
