<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services;

use App\Services\SlugGeneratorService;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Support\Collection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\ZipperServiceInterface;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;
use ZipArchive;

class ZipperService implements ZipperServiceInterface
{
    protected Filesystem $filesystem;
    protected SlugGeneratorService $slugGeneratorService;
    protected Collection $files;
    protected ZipArchive $archive;
    protected string $path;
    protected string $filesToCompressPath;
    protected string $archiveAbsolutePath;
    protected string $archiveRelativePath;
    protected bool $initialized;

    public function __construct(Filesystem $filesystem, SlugGeneratorService $slugGeneratorService)
    {
        $this->filesystem = $filesystem;
        $this->slugGeneratorService = $slugGeneratorService;
        $this->initialized = false;
    }

    protected function init(): void
    {
        $this->path = sprintf('tmp/%s', $this->slugGeneratorService->generate());
        $this->filesToCompressPath = sprintf('%s/%s', $this->path, $this->slugGeneratorService->generate());
        $this->files = collect();

        $this->archive = new ZipArchive();
        $archiveSlug = $this->slugGeneratorService->generate();
        $this->archiveAbsolutePath = sprintf(
            '%s/%s.%s',
            storage_path('app/'. $this->path),
            $archiveSlug,
            ExportFileTypeEnum::ZIP()
        );
        $this->archiveRelativePath = sprintf(
            '%s/%s.%s',
            $this->path,
            $archiveSlug,
            ExportFileTypeEnum::ZIP()
        );

        $this->archive->open($this->archiveAbsolutePath, ZipArchive::OVERWRITE|ZipArchive::CREATE);
        $this->initialized = true;
    }

    public function add(ExportFileDto $exportFileDto): void
    {
        if ($this->initialized === false) {
            $this->init();
        }

        if ($this->filesystem->exists($this->filesToCompressPath) === false) {
            $this->filesystem->makeDirectory($this->filesToCompressPath);
        }

        $file = sprintf('%s/%s.%s', $this->filesToCompressPath, $exportFileDto->getName(), $exportFileDto->getFileType());

        if ($this->filesystem->exists($file) === true) {
            $this->filesystem->delete($file);
        }

        $this->filesystem->move($exportFileDto->getTmpFilePath(), $file);
        $this->files->push($file);
    }

    public function compress(): ExportFileDto
    {
        $this->files->each(function (string $file) {
            $this->archive->addFromString(array_last(explode('/', $file)), $this->filesystem->get($file));
        });

        $this->archive->close();

        return new ExportFileDto(
            array_last(explode('/', $this->archiveRelativePath)),
            ExportFileTypeEnum::ZIP(),
            $this->archiveRelativePath
        );
    }

    public function clean(): void
    {
        $this->filesystem->deleteDirectory($this->path);
        $this->init();;
    }
}
