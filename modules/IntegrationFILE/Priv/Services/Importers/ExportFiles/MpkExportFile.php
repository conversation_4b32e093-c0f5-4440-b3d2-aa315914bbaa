<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Importers\ExportFiles;

use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Entities\Mpk;

class MpkExportFile implements ExportFileInterface
{
    private int $instanceId;
    private ?int $companyId;

    public function __construct(int $instanceId, ?int $companyId = null)
    {
        $this->instanceId = $instanceId;
        $this->companyId = $companyId;
    }

    public function collection(): Collection
    {
        $builder = Mpk::query()
            ->where('instance_id', $this->instanceId);

        if ($this->companyId) {
            $builder->where('company_id', $this->companyId);
        }

        return $builder
            ->get()
            ->map(function (Mpk $mpk) {
                return [
                    'code' => $mpk->code,
                    'name' => empty($mpk->name) ? $mpk->code : $mpk->name,
                    'company_code' => $mpk->company->code ?? '',
                    'is_active' => (string)$mpk->is_active,
                ];
            });
    }

    public function headings(): array
    {
        return [
            'code',
            'name',
            'company_code',
            'active',
        ];
    }
}