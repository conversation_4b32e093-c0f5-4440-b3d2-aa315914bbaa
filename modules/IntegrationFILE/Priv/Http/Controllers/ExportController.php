<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Responses\Response2;
use App\Services\Storage\StorageServiceDto;
use Modules\IntegrationFILE\Priv\Factories\InitializeExportDtoFactory;
use Modules\IntegrationFILE\Priv\Http\Requests\ExportClaimsRequest;
use Modules\IntegrationFILE\Priv\Http\Requests\ExportCreateRequest;
use Modules\IntegrationFILE\Priv\Http\Responses\ExportItemResponse;
use Modules\IntegrationFILE\Priv\Services\ExportService;
use Modules\IntegrationFILE\Pub\Factories\ExportClaimDtoFactory;

class ExportController extends Controller
{
    private ExportService $exportService;
    private ExportClaimDtoFactory $exportClaimDtoFactory;
    private InitializeExportDtoFactory $initializeExportDtoFactory;

    public function __construct(
        ExportService $exportService,
        ExportClaimDtoFactory $exportClaimDtoFactory,
        InitializeExportDtoFactory $initializeExportDtoFactory
    ) {
        $this->exportService = $exportService;
        $this->exportClaimDtoFactory = $exportClaimDtoFactory;
        $this->initializeExportDtoFactory = $initializeExportDtoFactory;
    }

    public function store(ExportCreateRequest $exportCreateRequest): Response2
    {
        $export = $this->exportService->initialize($this->initializeExportDtoFactory->create($exportCreateRequest));

        if ($export !== null) {
            return ExportItemResponse::item($export)->addSuccess(trans('import.export-scheduled-successfully'));
        }

        return Response2::create(false)->addError(trans('import.export-scheduling-error'));
    }

    public function exportClaims(ExportClaimsRequest $request): void
    {
        $exportClaimsFileDto = $this->exportService->processClaims(
            $this->exportClaimDtoFactory->createFromExportClaimsRequest($request),
        );
        $this->fileResponse(
            new StorageServiceDto(
                $exportClaimsFileDto->getFileName(),
                $exportClaimsFileDto->getFileTypeHeader(),
                $exportClaimsFileDto->getFileContent(),
            ),
        );
    }
}
