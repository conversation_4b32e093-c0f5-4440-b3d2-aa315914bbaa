<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Pub\Facades;

use App\Services\Storage\StorageServiceDto;
use Modules\Common\ValueObjects\Slug;
use Modules\IntegrationFILE\Priv\Interfaces\ExportServiceInterface;
use Modules\IntegrationFILE\Pub\Dtos\ExportClaimDto;

final class ExportFacade
{
    private ExportServiceInterface $exportService;

    public function __construct(ExportServiceInterface $exportService)
    {
        $this->exportService = $exportService;
    }

    public function export(ExportClaimDto $exportClaimDto): StorageServiceDto
    {
        return $this->exportService->processClaims($exportClaimDto);
    }

    public function process(Slug $exportSlug): void
    {
        $this->exportService->process($exportSlug);
    }
}
