<?php

declare(strict_types=1);

namespace Modules\TripPlanner\Pub\Dto;

final class RequestElementTypeDto
{
    /**
     * @var string
     */
    protected $slug;

    /**
     * @var string
     */
    protected $name;

    /**
     * RequestElementTypeDto constructor.
     * @param $slug
     * @param $name
     */
    public function __construct($slug, $name)
    {
        $this->slug = $slug;
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getSlug(): string
    {
        return $this->slug;
    }

    /**
     * @return mixed
     */
    public function getName(): string
    {
        return $this->name;
    }
}