<?php

use Illuminate\Http\Request;

Route::group(['middleware' => ['auth:api', 'auth.locale', 'auth.instance', 'auth.loggedAs']], function() {
    Route::group(['middleware' => ['auth.loggedAsOverwrite']], function() {
        Route::resource('request-element-types', 'RequestElementTypeController', ['only' => [
            'index',
        ]]);
        Route::resource('{slug}/travelers', 'RequestTravelerController', ['only' => ['store']]);
        Route::delete('{slug}/travelers/{request_traveler_slug}', 'RequestTravelerController@destroy');
    });

});