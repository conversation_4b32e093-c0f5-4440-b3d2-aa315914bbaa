<?php

declare(strict_types=1);

namespace Modules\TripPlanner\Priv\Modules\SpotSearcher\Mappings;

use Mo<PERSON>les\TripPlanner\Priv\Modules\SpotSearcher\Enums\SpotTypeEnum;
use Modules\TripPlanner\Priv\Modules\SpotSearcher\Interfaces\IndexIdentityInterface;

class TrainSpotMapping extends SpotMappingAbstract implements IndexIdentityInterface
{
    public function getMapping(): array
    {
        return self::MAPPING;
    }

    public function getSettings(): array
    {
        return self::SETTINGS;
    }

    public function getIndexName(): string
    {
        return SpotTypeEnum::TRAIN() . '_spot';
    }

    public function getSourceEndpoint(): string
    {
        return 'trainTrips/stationsWithCitiesVocabulary';
    }
}