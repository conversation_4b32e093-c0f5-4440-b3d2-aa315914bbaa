<?php

declare(strict_types=1);

namespace Modules\TripPlanner\Priv\Modules\SpotSearcher\SearchRules;

use App\ElasticSearchConfigurators\Rules\SearchInLangRuleInterface;
use App\ElasticSearchConfigurators\Rules\SearchInLangRuleTrait;
use App\ElasticSearchConfigurators\Rules\SearchRuleInterface;
use Modules\TripPlanner\Priv\Modules\SpotSearcher\ValueObjects\SearchPhrase;

class IataCodePlaneSpotRule implements SearchRuleInterface, SearchInLangRuleInterface
{
    use SearchInLangRuleTrait;

    protected const MAX_SEARCH_RESULTS = 1;

    protected const SEARCHABLE_FIELD = 'code';

    /** @var SearchPhrase */
    protected $searchPhrase;

    public function __construct(SearchPhrase $searchPhrase)
    {
        $this->searchPhrase = $searchPhrase;
    }

    public static function createFromSearchPhrase(SearchPhrase $searchPhrase): IataCodePlaneSpotRule
    {
        return new self($searchPhrase);
    }

    public function buildQueryPayload(): array
    {
        return [
            'bool' => [
                'must' => [
                    'match' => [
                        static::SEARCHABLE_FIELD => (string)$this->searchPhrase
                    ]
                ]
            ]
        ];
    }

    public function getMaxSearchResultLimit(): int
    {
        return self::MAX_SEARCH_RESULTS;
    }
}