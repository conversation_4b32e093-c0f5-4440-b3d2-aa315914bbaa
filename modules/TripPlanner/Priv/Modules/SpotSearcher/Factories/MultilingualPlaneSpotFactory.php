<?php

declare(strict_types=1);

namespace Modules\TripPlanner\Priv\Modules\SpotSearcher\Factories;

use App\Enum\AvailableLanguagesEnum;
use Modules\TripPlanner\Pub\Dto\SpotSearcher\MultilingualPlaneSpot;

class MultilingualPlaneSpotFactory
{
    public static function create(array $spot): MultilingualPlaneSpot
    {
        return new MultilingualPlaneSpot(
            self::prepareMultilingualValue($spot, 'country_name'),
            $spot['country_code'],
            self::prepareMultilingualValue($spot, 'city_name'),
            $spot['city_code'],
            null,
            $spot['long'],
            $spot['lat'],
            self::prepareMultilingualValue($spot, 'name'),
            $spot['code'],
            null,
            null,
            $spot['priority'],
            $spot['nearest_spot_codes'],
            $spot['timezone'],
        );
    }

    protected static function prepareMultilingualValue(array $spot, string $field): array
    {
        $response = [];
        $langs = AvailableLanguagesEnum::toArray();
        foreach($langs as $locale => $key) {
            $response[$key] = $spot[$field.'_'.strtolower($locale)];
        }

        return $response;
    }
}
