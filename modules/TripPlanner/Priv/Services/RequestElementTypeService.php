<?php

declare(strict_types=1);

namespace Modules\TripPlanner\Priv\Services;

use Mo<PERSON><PERSON>\TripPlanner\Priv\Entities\RequestElementType;
use Mo<PERSON><PERSON>\TripPlanner\Priv\Repositories\RequestElementTypeRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class RequestElementTypeService
{
    /**
     * @var RequestElementTypeRepository
     */
    protected $requestElementTypeRpository;

    /**
     * RequestElementTypeService constructor.
     * @param RequestElementTypeRepository $requestElementTypeRpository
     */
    public function __construct(RequestElementTypeRepository $requestElementTypeRpository)
    {
        $this->requestElementTypeRpository = $requestElementTypeRpository;
    }

    public function findBySlug(string $slug): ?RequestElementType
    {
        try {
            return $this->requestElementTypeRpository->findBySlug($slug);
        } catch (NotFoundHttpException $exception) {
            return null;
        }
    }
}