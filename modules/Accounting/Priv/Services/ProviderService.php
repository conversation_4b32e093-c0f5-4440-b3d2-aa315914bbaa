<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Services;

use App\Company;
use App\Database\TransactionManagerInterface;
use App\Provider;
use App\Repositories\Pagination\PaginatorInterface;
use App\Repositories\ProviderRepository;
use App\User;
use Modules\Accounting\Priv\Dtos\DeleteProviderDto;
use Modules\Accounting\Priv\Dtos\ProviderDto;
use Exception;
use Modules\Common\Dtos\PageInterface;
use Modules\Common\ValueObjects\Slug;
use Modules\Users\Pub\Facades\CompanyFacade;
use Modules\Users\Pub\Facades\UserFacade;

class ProviderService
{
    /**
     * @var ProviderRepository
     */
    protected $providerRepository;

    protected UserFacade $userFacade;

    /**
     * @var TransactionManagerInterface
     */
    protected $transactionManager;
    private CompanyFacade $companyFacade;

    /**
     * @param ProviderRepository $providerRepository
     * @param UserFacade $userFacade
     * @param TransactionManagerInterface $transactionManager
     */
    public function __construct(
        ProviderRepository $providerRepository,
        UserFacade $userFacade,
        CompanyFacade $companyFacade,
        TransactionManagerInterface $transactionManager
    ) {
        $this->providerRepository = $providerRepository;
        $this->userFacade = $userFacade;
        $this->companyFacade = $companyFacade;
        $this->transactionManager = $transactionManager;
    }

    public function create(ProviderDto $dto, User $creator): Provider
    {
        $employee = $this->findEmployeeByErpId($dto, $creator);
        $company = $this->findCompanyById($dto, $creator);

        $provider = new Provider();
        $provider->slug = $dto->getSlug();
        $provider->name = $dto->getName();
        $provider->registry_number = $dto->getRegistryNumber();
        $provider->address = $dto->getAddress();
        $provider->postcode = $dto->getPostCode();
        $provider->city = $dto->getCity();
        $provider->country_id = $dto->getCountryId();
        $provider->erp_id = $dto->getErpId();
        $provider->created_by = $creator->id;
        $provider->source = $dto->getSource();
        $provider->employee_id = $employee->id ?? null;
        $provider->company_id = $company->id ?? null;

        $provider->instance_id = $creator->instance_id;

        try {
            $this->transactionManager->beginTransaction();

            $provider = $this->providerRepository->persist($provider);
            $provider = $this->providerRepository->getFreshModel($provider);

            $this->transactionManager->commit();
        } catch (Exception $exception) {
            $this->transactionManager->rollBack();

            throw $exception;
        }


        return $provider;
    }

    public function update(Provider $provider, ProviderDto $dto, User $creator): Provider
    {
        $employee = $this->findEmployeeByErpId($dto, $creator);
        $company = $this->findCompanyById($dto, $creator);

        $provider->name = $dto->getName();
        $provider->registry_number = $dto->getRegistryNumber();
        $provider->address = $dto->getAddress();
        $provider->postcode = $dto->getPostCode();
        $provider->city = $dto->getCity();
        $provider->country_id = $dto->getCountryId();
        $provider->erp_id = $dto->getErpId();
        $provider->updated_by = $creator->id;
        $provider->source = $dto->getSource();
        $provider->employee_id = $employee->id ?? null;
        $provider->company_id = $company->id ?? null;

        try {
            $this->transactionManager->beginTransaction();

            if ($dto->isDeleted()) {
                $provider->delete();
            } else {
                $provider->restore();
            }

            $provider = $this->providerRepository->persist($provider);
            $provider = $this->providerRepository->getFreshModel($provider);

            $this->transactionManager->commit();
        } catch (Exception $exception) {
            $this->transactionManager->rollBack();

            throw $exception;
        }

        return $provider;
    }

    public function delete(DeleteProviderDto $dto): void
    {
        $this->providerRepository->remove($dto->getProvider());
    }

    protected function findEmployeeByErpId(ProviderDto $dto, User $creator): ?User
    {
        $employee = null;
        if (!empty($dto->getErpId())) {
            $employee = $this->userFacade->findUserByErpIdAndInstanceId($dto->getErpId(), $creator->instance_id);
        }
        return $employee;
    }

    protected function findCompanyById(ProviderDto $dto, User $creator): ?Company
    {
        $company = null;

        if (!empty($dto->getCompanyId())) {
            $company = $this->companyFacade->findCompanyBySlugAndInstanceSlug(
                new Slug($dto->getCompanyId()),
                new Slug($creator->instance->slug)
            );
        }

        return $company;
    }

    public function paginatedList(PageInterface $page, $instance): PaginatorInterface
    {
        return $this->providerRepository->getForCurrentInstance($page, $instance);
    }
}