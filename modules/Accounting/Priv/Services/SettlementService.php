<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Services;

use App\ERP\Dto\RequestStatusDto;
use App\ERP\Dto\TransferResultDto;
use App\Repositories\RequestRepository;
use App\Request;
use App\Services\RequestBusinessProcess\ChangeStatus\ChangeStatusToTransferError;
use Illuminate\Contracts\Events\Dispatcher;
use Modules\Accounting\Priv\Dtos\ErpAccountedSettlementDto;
use Modules\Accounting\Priv\Dtos\ErpCannotBeAccountedDto;
use Modules\Accounting\Priv\Events\ClaimPartAccountedInErpEvent;

class SettlementService
{
    /**
     * @var RequestRepository
     */
    protected $requestRepository;

    /**
     * @var Dispatcher
     */
    protected $eventDispatcher;

    /**
     * SettlementService constructor.
     * @param RequestRepository $requestRepository
     * @param Dispatcher $eventDispatcher
     */
    public function __construct(RequestRepository $requestRepository, Dispatcher $eventDispatcher)
    {
        $this->requestRepository = $requestRepository;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function setErpAccounted(Request $request, ErpAccountedSettlementDto $dto): Request
    {
        $request->setErpAccounted($dto);

        $this->requestRepository->persist($request);

        $this->eventDispatcher->dispatch(new ClaimPartAccountedInErpEvent($request->slug));

        return $this->requestRepository->getFreshModel($request);
    }

    public function cannotBeAccountedInErp(Request $request, ErpCannotBeAccountedDto $dto): Request
    {
        $result = TransferResultDto::create();
        $result->addRequest(RequestStatusDto::create($request->number)->addError($dto->getErpMessage()));

        (new ChangeStatusToTransferError($request, $request->accountingUser, $result, $dto->getErpMessage()))->run([]);

        return $request;
    }
}