<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Factories;

use App\Repositories\CountryRepository;
use App\Rules\ProviderNipUniqueInCurrentInstance;
use Illuminate\Validation\Rule;
use Illuminate\Database\Query\Builder;
use Modules\Accounting\Priv\Dtos\ProviderDto;
use Modules\Accounting\Pub\Interfaces\CreateProviderRequestInterface;
use Modules\Common\Factories\AbstractDtoFactory;

class NewProviderDtoFactory extends AbstractDtoFactory
{
    /**
     * @var ProviderNipUniqueInCurrentInstance
     */
    protected $providerNipUniqueInCurrentInstance;

    protected CountryRepository $countryRepository;

    public function __construct(
        ProviderNipUniqueInCurrentInstance $providerNipUniqueInCurrentInstance,
        CountryRepository $countryRepository
    ) {
        $this->providerNipUniqueInCurrentInstance = $providerNipUniqueInCurrentInstance;
        $this->countryRepository = $countryRepository;
    }

    /**
     * @param CreateProviderRequestInterface $request
     * @return array
     */
    protected function prepareValidationData($request): array
    {
        return [
            $request->getNameKey() => $request->getName(),
            $request->getTaxIdKey() => $request->getTaxId(),
            $request->getAddressKey() => $request->getAddress(),
            $request->getPostCodeKey() => $request->getPostCode(),
            $request->getCityKey() => $request->getCity(),
            $request->getCountryCodeKey() => $request->getCountryCode(),
            $request->getErpIdKey() => $request->getErpId(),
            $request->getCompanyIdKey() => $request->getCompanyId(),
            $request->getSlugKey() => $request->getSlug(),
        ];
    }

    /**
     * @param CreateProviderRequestInterface $request
     */
    protected function prepare($request)
    {
        return new ProviderDto(
            $request->getSlug(),
            $request->getName(),
            $request->getTaxId(),
            $request->getAddress(),
            $request->getPostCode(),
            $request->getCity(),
            $request->getCountryCode() !== null ? $this->countryRepository->getCountryByCode($request->getCountryCode())->id : null,
            $request->getErpId(),
            $request->getCompanyId(),
            $request->getSource()
        );
    }

    /**
     * @param CreateProviderRequestInterface $request
     * @return array
     */
    protected function rules($request): array
    {
        $instanceId = $request->getInstance()->id;

        return [
            $request->getNameKey() => 'required|string|min:2|max:255',
            $request->getTaxIdKey() => ['nullable', 'string', 'min:4', 'max:20', $this->providerNipUniqueInCurrentInstance->setCurrentInstance($request->getInstance())->setErpId($request->getErpId())],
            $request->getAddressKey() => 'nullable|string|max:255',
            $request->getPostCodeKey() => 'nullable|string|nullable|max:255',
            $request->getCityKey() => 'nullable|string|nullable|max:255',
            $request->getCountryCodeKey() => 'nullable|exists:countries,country_code',
            $request->getSlugKey() => ['nullable', 'string', 'max:36', Rule::unique('providers', 'slug')->where('instance_id', $instanceId)],
            $request->getErpIdKey() => [
                'nullable' => 'nullable',
                'string' => 'string',
                'max' => 'max:255',
                'unique' => Rule::unique('providers')->where(function ($query) use ($request) {
                    return $query->where([
                        'instance_id' => $request->getInstance()->id
                    ]);
                })
            ],
            $request->getCompanyIdKey() => [
                'nullable',
                'string',
                'nullable',
                'max:255',
                Rule::exists('companies', 'slug')->where(function (Builder $query) use ($request) {
                    return $query->where([
                        'slug' => $request->getCompanyId(),
                        'instance_id' => $request->getInstance()->id
                    ]);
                }),
            ],
        ];
    }
}