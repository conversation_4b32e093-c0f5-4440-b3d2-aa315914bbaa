<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Policies;

use Modules\Accounting\Priv\Entities\Mpk;
use App\Traits\Policies\SuperAdminCanDoAnything;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class MpkPolicy
{
    use HandlesAuthorization;
    use SuperAdminCanDoAnything;

    public function view(User $user, Mpk $mpk)
    {
        return $mpk->instance_id === $user->instance_id;
    }
}
