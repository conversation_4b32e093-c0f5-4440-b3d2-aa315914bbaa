<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\ElasticSearchConfigurators;

use ScoutElastic\IndexConfigurator;
use ScoutElastic\Migratable;

class MpkIndexConfigurator extends IndexConfigurator
{
    use Migratable;

    /**
     * @var array
     */

    protected $settings = [
        'analysis'  => [
            'filter' => [
                'nGram_filter' => [
                    'type' =>     'nGram',
                    'min_gram' =>  1,
                    'max_gram' => 20,
                    'token_chars'   => [
                        'letter',
                        'digit',
                        'punctuation',
                        'symbol'
                    ]
                ]
            ],
            'analyzer' => [
                'nGram_analyzer' => [
                    'type'  => 'custom',
                    'tokenizer' => 'whitespace',
                    'filter' => [
                        'lowercase',
                        'asciifolding',
                        'nGram_filter'
                    ]
                ],
                'whitespace_analyzer' => [
                    'type'  => 'custom',
                    'tokenizer' => 'whitespace',
                    'filter' => [
                        'lowercase',
                        'asciifolding',
                    ]
                ]
            ],

        ],
        'max_ngram_diff' => '25'
    ];
}
