<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Modules\Provider\ValueObjects;

use Modules\Accounting\Priv\Modules\Provider\Exceptions\InvalidNipException;

class Nip
{
    protected const MIN_LENGTH = 4;
    protected const MAX_LENGTH = 20;

    /** @var string */
    protected $value;

    public function __construct(string $value)
    {
        if(self::isValid($value) === false) {
            throw InvalidNipException::create();
        }

        $this->value = $value;
    }

    public function __toString(): string
    {
        return $this->value;
    }

    public static function isValid(string $value): bool
    {
        return strlen($value) >= self::MIN_LENGTH && strlen($value) <= self::MAX_LENGTH;
    }
}