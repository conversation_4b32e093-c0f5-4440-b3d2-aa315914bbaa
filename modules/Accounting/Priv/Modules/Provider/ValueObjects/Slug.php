<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Modules\Provider\ValueObjects;

use Modules\Accounting\Priv\Modules\Provider\Exceptions\InvalidSlugException;

class Slug
{
    /** @var string */
    protected $value;

    public function __construct(string $value)
    {
        if(self::isValid($value) === false) {
            throw InvalidSlugException::create();
        }

        $this->value = $value;
    }

    public function __toString(): string
    {
        return (string)$this->value;
    }

    public static function isValid(string $value): bool
    {
        return strlen($value) > 0 && strlen($value) <= 36;
    }
}