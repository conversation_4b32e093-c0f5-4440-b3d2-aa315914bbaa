<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Rules;

use App\Instance;
use Illuminate\Contracts\Validation\Rule;
use Modules\Analytics\Pub\Facades\AccountDimensionFacade;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AccountDImensionItemExistsRule implements Rule
{
    protected AccountDimensionFacade $accountDimensionFacade;

    protected AccountDimensionItemFacade $accountDimensionItemFacade;

    protected Instance $instance;

    public function __construct(
        AccountDimensionFacade $accountDimensionFacade,
        AccountDimensionItemFacade $accountDimensionItemFacade
    ) {
        $this->accountDimensionFacade = $accountDimensionFacade;
        $this->accountDimensionItemFacade = $accountDimensionItemFacade;
    }

    public function setInstance(Instance $instance): AccountDImensionItemExistsRule
    {
        $this->instance = $instance;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function passes($attribute, $value)
    {
        $attributeElements = explode('.', $attribute);
        $slug = array_pop($attributeElements);

        try {
            $accountDimension = $this->accountDimensionFacade->getBySlug($slug);

            $accountDimensionItem = $this->accountDimensionItemFacade->findAccountDimensionItem(
                $accountDimension->getId(),
                (int)$value,
                $this->instance->id
            );

            if ($accountDimensionItem === null) {
                return false;
            }
        } catch (NotFoundHttpException $exception) {
            return false;
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function message()
    {
        return trans('accounting::errors.account-dimension-item-not-found');
    }
}
