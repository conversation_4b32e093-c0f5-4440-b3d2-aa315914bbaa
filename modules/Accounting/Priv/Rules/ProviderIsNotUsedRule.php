<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Rules;

use App\Repositories\DocumentRepository;
use App\Repositories\ProviderRepository;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Validation\Rule;

class ProviderIsNotUsedRule implements Rule
{
    /**
     * @var Guard
     */
    protected $auth;

    /**
     * @var ProviderRepository
     */
    protected $providerRepository;

    /**
     * @var DocumentRepository
     */
    protected $documentRepository;

    /**
     * ProviderIsNotUsedRule constructor.
     * @param Guard $auth
     * @param ProviderRepository $providerRepository
     * @param DocumentRepository $documentRepository
     */
    public function __construct(
        Guard $auth,
        ProviderRepository $providerRepository,
        DocumentRepository $documentRepository
    ) {
        $this->auth = $auth;
        $this->providerRepository = $providerRepository;
        $this->documentRepository = $documentRepository;
    }

    /**
     * @inheritDoc
     */
    public function passes($attribute, $value)
    {
        $instanceId = $this->auth->user()->instance_id;
        $provider = $this->providerRepository->findBySlugAndInstanceId($value, $instanceId);

        if($this->documentRepository->existsForProviderId($provider->id, $instanceId)) {
            return false;
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function message()
    {
        return trans('accounting::errors.at-least-one-document-exists-for-this-supplier');
    }
}