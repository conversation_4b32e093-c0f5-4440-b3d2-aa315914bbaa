<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Rules;

use App\Enum\ActiveEnum;
use Illuminate\Contracts\Validation\Rule;
use Modules\Accounting\Priv\Entities\AccountingAccount;

class AccountingAccountNotUsedInInstanceSettingsRule implements Rule
{
    /**
     * @var AccountingAccount
     */
    protected $accountingAccount;

    /**
     * AccountingAccountIsNotInstanceAccountingAccountRule constructor.
     * @param AccountingAccount $accountingAccount
     */
    public function __construct(AccountingAccount $accountingAccount)
    {
        $this->accountingAccount = $accountingAccount;
    }

    /**
     * @inheritDoc
     */
    public function passes($attribute, $value)
    {
        if ($this->accountingAccount->is_active->equals(ActiveEnum::ACTIVE()) && $value == 0) {
            $accountingAccountId = $this->accountingAccount->id;
            $instance = $this->accountingAccount->instance;

            if ($instance->providers_account_id == $accountingAccountId) {
                return false;
            }

            if ($instance->technical_account_id == $accountingAccountId) {
                return false;
            }

            if ($instance->foreign_travel_expenses_debit_account_id == $accountingAccountId) {
                return false;
            }

            if ($instance->national_travel_expenses_debit_account_id == $accountingAccountId) {
                return false;
            }

            if ($instance->foreign_travel_expenses_credit_account_id == $accountingAccountId) {
                return false;
            }

            if ($instance->national_travel_expenses_credit_account_id == $accountingAccountId) {
                return false;
            }

            if ($instance->mileage_allowance_debit_account_id == $accountingAccountId) {
                return false;
            }
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function message()
    {
        return trans('accounting::errors.accounting-account-used-in-instance-settings');
    }
}
