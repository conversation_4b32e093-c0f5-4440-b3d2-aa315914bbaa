<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Console;

use App\Exceptions\ValidationException;
use App\Instance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Modules\Accounting\Priv\UseCases\AccountingAccounts\CreateAccountingAccountUseCase;

class ImportAccountFromFileCommand extends Command
{
    protected $signature = 'accounting:import-from-file {instance} {file}';
    protected $description = 'Creates accounts from file';
    private CreateAccountingAccountUseCase $createAccountingAccountUseCase;

    public function __construct(CreateAccountingAccountUseCase $createAccountingAccountUseCase)
    {
        parent::__construct();
        $this->createAccountingAccountUseCase = $createAccountingAccountUseCase;
    }

    public function handle(): void
    {
        $instances = Instance::whereId($this->argument('instance'))->get();

        if ($instances->isEmpty()) {
            $this->error('Instance not found');
            return;
        }

        $instances->each(fn($instance) => $this->callForInstance($instance));
    }

    private function callForInstance(Instance $instance): void
    {
        $this->info('Creating accounts for instance:  ' . $instance->name);

        $path = $this->argument('file');

        $accounts = [];
        $file = fopen($path, 'rb');

        while (($data = fgetcsv($file, 1000, ";")) !== false) {
            $accounts[] = [
                'number' => $data[0],
                'name' => $data[1],
                'type' => strtolower($data[2]),
            ];
        }

        fclose($file);

        try {
            DB::transaction(function () use ($accounts, $instance) {
                foreach ($accounts as $account) {
                    $this->info('Creating account: ' . $account['name']);
                    $this->createAccountingAccountUseCase->run(
                        new CreateAccountingAccountRequest(
                            $instance,
                            $account['number'],
                            $account['name'],
                            $account['type'],
                            true
                        )
                    );
                }
            });

            $this->info('');
            $this->info('Done');
        } catch (ValidationException $ex) {
            $this->error($ex->errorsToString());
        }
    }
}
