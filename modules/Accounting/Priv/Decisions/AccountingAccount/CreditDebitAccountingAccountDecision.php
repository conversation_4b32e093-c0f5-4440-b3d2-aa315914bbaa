<?php

namespace Modules\Accounting\Priv\Decisions\AccountingAccount;

use Modules\Accounting\Priv\Dtos\SelectedCreditDebitAccountingAccountsDto;
use Modules\Accounting\Priv\Services\AccountingAccountSelection\Strategy\CreditDebitAccountingAccountSelectionStrategy;
use Modules\DecisionMaker\Pub\Interfaces\DecisionInterface;

class CreditDebitAccountingAccountDecision implements DecisionInterface
{
    public const CODE = 'CreditDebitAccountingAccountDecision';

    private string $creditAccount;

    private string $debitAccount;

    public function __construct(string $credit, string $debit)
    {
        $this->creditAccount = $credit;
        $this->debitAccount = $debit;
    }

    /**
     * @param array $params
     * @return SelectedCreditDebitAccountingAccountsDto
     */
    public function execute(array $params = [])
    {
        return (new CreditDebitAccountingAccountSelectionStrategy(
            $this->creditAccount, $this->debitAccount
        ))->getAccountingAccounts();
    }
}