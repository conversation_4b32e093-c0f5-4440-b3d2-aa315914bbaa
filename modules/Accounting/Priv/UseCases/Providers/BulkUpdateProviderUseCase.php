<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\UseCases\Providers;

use App\User;
use Illuminate\Validation\ValidationException;
use Modules\Accounting\Priv\Factories\NewProviderDtoFactory;
use Modules\Accounting\Priv\Factories\UpdateProviderDtoFactory;
use Modules\Accounting\Priv\Services\ProviderService;
use Modules\Accounting\Pub\Interfaces\BulkUpsertProviderRequestInterface;
use Modules\Accounting\Pub\ViewObjects\ProviderBulkUpsertViewObject;
use Modules\IntegrationAPI\Priv\Dtos\UpsertProviderDto;

class BulkUpdateProviderUseCase
{
    private int $added = 0;
    private int $updated = 0;
    private array $failedRecords = [];
    private UpdateProviderDtoFactory $updateProviderDtoFactory;
    private ProviderService $providerService;
    private NewProviderDtoFactory $newProviderDtoFactory;

    public function __construct(
        NewProviderDtoFactory $newProviderDtoFactory,
        UpdateProviderDtoFactory $updateProviderDtoFactory,
        ProviderService $providerService
    ) {
        $this->updateProviderDtoFactory = $updateProviderDtoFactory;
        $this->providerService = $providerService;
        $this->newProviderDtoFactory = $newProviderDtoFactory;
    }

    public function run(BulkUpsertProviderRequestInterface $request, User $creator): ProviderBulkUpsertViewObject
    {
        $providers = $request->getProviders();
        foreach ($providers as $provider) {
            try {
                if ($provider->exists()) {
                    $this->updateProvider($provider, $creator);
                } else {
                    $this->createProvider($provider, $creator);
                }
            } catch (ValidationException $e) {
                $this->failed($provider->getSlug(), $e->getMessage(), $e->errors());
            }
        }

        return new ProviderBulkUpsertViewObject($this->added, $this->updated, $this->failedRecords);
    }

    private function updateProvider(UpsertProviderDto $provider, User $creator): void
    {
        $dto = $this->updateProviderDtoFactory->create($provider);
        $this->providerService->update(
            $provider->getProviderToChange(),
            $dto,
            $creator
        );
        ++$this->updated;
    }

    private function createProvider(UpsertProviderDto $provider, User $creator): void
    {
        $dto = $this->newProviderDtoFactory->create($provider);
        $this->providerService->create(
            $dto,
            $creator
        );
        ++$this->added;
    }

    private function failed(string $id, string $message, array $errorsDetails): void
    {
        $this->failedRecords[] = [
            'id' => $id,
            'reason' => $message,
            'details' => $errorsDetails,
        ];
    }
}