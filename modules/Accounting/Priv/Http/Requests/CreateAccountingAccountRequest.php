<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Http\Requests;

use App\Instance;
use App\Permission;
use App\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Modules\Accounting\Pub\Interfaces\CreateAccountingAccountRequestInterface;

class CreateAccountingAccountRequest extends FormRequest implements CreateAccountingAccountRequestInterface
{
    private Guard $auth;

    /**
     * CreateAccountingAccountDto constructor.
     * @param Guard $auth
     */
    public function __construct(Guard $auth)
    {
        $this->auth = $auth;
    }

    public function authorize()
    {
        /** @var User $user */
        $user = $this->auth->user();

        return
            $user->hasAbility(Permission::DICTIONARIES_MANAGE) ||
            $user->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_COMPANY);
    }

    protected function failedValidation(Validator $validator)
    {
        throw (new \App\Exceptions\ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }

    public function rules(): array
    {
        return [
            CreateAccountingAccountRequestInterface::KEY_ACCOUNT_NUMBER => ['nullable', 'string'],
            CreateAccountingAccountRequestInterface::KEY_NAME => ['nullable', 'string'],
            CreateAccountingAccountRequestInterface::KEY_TYPE => ['nullable', 'string'],
            CreateAccountingAccountRequestInterface::KEY_IS_ACTIVE => ['nullable', 'boolean'],
        ];
    }

    public function getInstance(): Instance
    {
        return $this->auth->user()->instance;
    }

    public function getNumber(): ?string
    {
        return $this->get(self::KEY_ACCOUNT_NUMBER);
    }

    public function getName(): ?string
    {
        return $this->get(self::KEY_NAME);
    }

    public function getType(): ?string
    {
        return $this->get(self::KEY_TYPE);
    }

    public function getIsActive(): ?int
    {
        $isActive = $this->get(self::KEY_IS_ACTIVE);
        if($isActive === null) {
            return null;
        }

        return $isActive === true ? 1 : 0;
    }

    public function getNumberKey(): string
    {
        return self::KEY_ACCOUNT_NUMBER;
    }

    public function getNameKey(): string
    {
        return self::KEY_NAME;
    }

    public function getTypeKey(): string
    {
        return self::KEY_TYPE;
    }

    public function getIsActiveKey(): string
    {
        return self::KEY_IS_ACTIVE;
    }
}
