<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Http\Dto;

use App\Company;
use App\Enum\ActiveEnum;
use App\Instance;
use App\Permission;
use App\Repositories\CompanyRepository;
use App\Repositories\UserRepository;
use App\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Repositories\MpkRepository;
use Modules\Accounting\Priv\Rules\MpkAllowCompanyChangeRule;
use Modules\Accounting\Priv\Rules\MpkIsNotUsedRule;
use Modules\Accounting\Pub\Interfaces\UpdateMpkDtoInterface;
use Symfony\Component\HttpFoundation\Response;

final class UpdateCreateMpkDto extends FormRequest implements UpdateMpkDtoInterface
{
    private const KEY_IS_ACTIVE = 'is_active';

    /**
     * @var Guard
     */
    private $auth;

    /**
     * @var MpkRepository
     */
    private $mpkRepository;

    /**
     * @var UserRepository
     */
    private $userRepository;

    /**
     * @var CompanyRepository
     */
    private $companyRepository;

    /**
     * EditMpkDto constructor.
     * @param Guard $auth
     * @param MpkRepository $mpkRepository
     * @param UserRepository $userRepository
     * @param CompanyRepository $companyRepository
     */
    public function __construct(
        Guard $auth,
        MpkRepository $mpkRepository,
        UserRepository $userRepository,
        CompanyRepository $companyRepository
    ) {
        $this->auth = $auth;
        $this->mpkRepository = $mpkRepository;
        $this->userRepository = $userRepository;
        $this->companyRepository = $companyRepository;
    }

    public function authorize()
    {
        /** @var User $user */
        $user = $this->auth->user();

        return
            $user->hasAbility(Permission::DICTIONARIES_MANAGE) ||
            $user->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_COMPANY);
    }

    public function rules(): array
    {
        $mpkToChange = $this->getMpkToChange();
        $instanceId = $mpkToChange->instance_id;

        return [
            self::KEY_CODE => 'required|max:255',
            self::KEY_NAME => 'required|max:255',
            self::KEY_COMPANY_ID => [
                'sometimes',
                'numeric',
                'exists:companies,id,instance_id,' . $instanceId,
                new MpkAllowCompanyChangeRule($this->userRepository, $mpkToChange)
            ],
            self::KEY_IS_ACTIVE => ['required', 'boolean', new MpkIsNotUsedRule($this->userRepository, $mpkToChange)]
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw (new \App\Exceptions\ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }

    public function getSlug(): string
    {
        return $this->route()->parameter('slug');
    }

    public function getCode(): string
    {
        return $this->get(self::KEY_CODE);
    }

    public function getName(): string
    {
        return $this->get(self::KEY_NAME);
    }

    public function getIsActive(): ActiveEnum
    {
        return new ActiveEnum(
            (int)$this->get(self::KEY_IS_ACTIVE)
        );
    }

    public function getCompany(): ?Company
    {
        $companyId = $this->getCompanyId();
        if ($companyId === null) {
            return null;
        }

        return $this->companyRepository->findById($companyId);
    }

    public function getCompanyId(): ?int
    {
        return empty($this->get(self::KEY_COMPANY_ID)) ? null : $this->get(self::KEY_COMPANY_ID);
    }

    public function getMpkToChange(): Mpk
    {
        $mpk = $this->mpkRepository->findBySlugAndInstanceId(
            $this->getSlug(),
            $this->auth->user()->instance_id
        );

        abort_if($mpk === null, Response::HTTP_NOT_FOUND);

        return $mpk;
    }

    public function getInstance(): Instance
    {
        return $this->auth->user()->instance;
    }
}
