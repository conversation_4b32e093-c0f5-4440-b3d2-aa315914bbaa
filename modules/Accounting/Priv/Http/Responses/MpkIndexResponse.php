<?php

namespace Modules\Accounting\Priv\Http\Responses;

use App\Http\Responses\Response2Paginated;
use Modules\Accounting\Priv\Entities\Mpk;
use stdClass;

class MpkIndexResponse extends Response2Paginated
{
    protected function transform(stdClass $mpk): array
    {
        $response = [
            'id' => $mpk->id,
            'name' => $mpk->name ? $mpk->code . Mpk::DELIMITER . $mpk->name : $mpk->code,
            'code' => $mpk->code,
            'slug' => $mpk->slug,
            'company_id' => $mpk->company_id,
            'is_active' => $mpk->is_active,
        ];

        if (empty($mpk->company_code) === false) {
            $response['company'] = [
                'code' => $mpk->company_code
            ];
        }

        return $response;
    }
}
