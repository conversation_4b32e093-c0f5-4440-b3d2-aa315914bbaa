<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Http\Responses;

use App\Http\Responses\AccountDimensionItemResponse;
use App\Http\Responses\Response2;
use Modules\Analytics\Pub\ViewObjects\DocumentElementTypeAccountDimensionItemViewObject;

class DocumentElementTypeAccountDimensionItemResponse extends Response2
{
    protected function transform(DocumentElementTypeAccountDimensionItemViewObject $accountDimensionItem)
    {
        return [
            'id' => $accountDimensionItem->getId(),
            'slug' => $accountDimensionItem->getSlug(),
            'document_element_type_id' => $accountDimensionItem->getDocumentElementTypeId(),
            'account_dimension_id' => $accountDimensionItem->getAccountDimensionId(),
            'account_dimension_item_id' => $accountDimensionItem->getAccountDimensionItemId(),
            'accountDimensionItem' => AccountDimensionItemResponse::item($accountDimensionItem->getAccountDimensionItem())->getData()
        ];
    }
}
