<?php

namespace Modules\Accounting\Priv\Dtos;

use Modules\Accounting\Priv\Entities\AccountingAccount;

class SelectedCreditDebitAccountingAccountsDto
{
    private ?AccountingAccount $creditAccountingAccount;
    private ?AccountingAccount $debitAccountingAccount;

    public function __construct(?AccountingAccount $creditAccountingAccount, ?AccountingAccount $debitAccountingAccount)
    {
        $this->creditAccountingAccount = $creditAccountingAccount;
        $this->debitAccountingAccount = $debitAccountingAccount;
    }

    public function getCreditAccountingAccount(): ?AccountingAccount
    {
        return $this->creditAccountingAccount;
    }

    public function getDebitAccountingAccount(): ?AccountingAccount
    {
        return $this->debitAccountingAccount;
    }
}