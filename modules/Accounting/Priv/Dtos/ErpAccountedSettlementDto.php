<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Dtos;

use Carbon\Carbon;

class ErpAccountedSettlementDto
{
    /**
     * @var string
     */
    protected $erpId;

    /**
     * @var Carbon $erpAccountedAt
     */
    protected $erpAccountedAt;

    /**
     * ErpAccountedDto constructor.
     * @param Carbon $erpVatAt
     * @param Carbon $erpAccountedAt
     */
    public function __construct(string $erpId, Carbon $erpAccountedAt)
    {
        $this->erpId = $erpId;
        $this->erpAccountedAt = $erpAccountedAt;
    }

    /**
     * @return string
     */
    public function getErpId(): string
    {
        return $this->erpId;
    }

    /**
     * @return Carbon
     */
    public function getErpAccountedAt(): Carbon
    {
        return $this->erpAccountedAt;
    }
}