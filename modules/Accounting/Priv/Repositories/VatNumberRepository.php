<?php

namespace Modules\Accounting\Priv\Repositories;

use App\Enum\ActiveEnum;
use App\Instance;
use App\Repositories\Repository;
use App\Services\SlugGeneratorService;
use Illuminate\Database\Eloquent\Model;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Entities\VatNumber;

class VatNumberRepository extends Repository
{
    /**
     * @var SlugGeneratorService
     */
    protected $slugGeneratorService;

    /**
     * VatNumberRepository constructor.
     * @param SlugGeneratorService $slugGeneratorService
     */
    public function __construct(SlugGeneratorService $slugGeneratorService)
    {
        parent::__construct();
        $this->slugGeneratorService = $slugGeneratorService;
    }

    public static function model()
    {
        return VatNumber::class;
    }

    public function existsWithAccountingAccount(AccountingAccount $accountingAccount): bool
    {
        $builder = $this->builder->where(['accounting_account_id' => $accountingAccount->id]);

        return $builder->count() > 0;
    }

    public function findByVatRate(int $rate, Instance $instance): ?VatNumber
    {
        return VatNumber::where([
            ['instance_id', '=', $instance->id],
            ['value', '=', $rate],
            ['deductibility', '=', VatNumber::FULL_DEDUCTIBILITY],
            ['is_active', '=', ActiveEnum::ACTIVE()],
        ])->orderBy('code', 'desc')->first();
    }

    public function findByCode(string $code, Instance $instance): ?VatNumber
    {
        return VatNumber::where([
            ['instance_id', '=', $instance->id],
            ['code', '=', $code],
            ['is_active', '=', ActiveEnum::ACTIVE()],
        ])->orderBy('code', 'desc')->first();
    }

    public function findByCodeAndInstanceId(string $code, int $instanceId): ?VatNumber
    {
        return VatNumber::where([
            'code' => $code,
            'instance_id' => $instanceId,
        ])->first();
    }

    public function findNotTaxable(Instance $instance): ?VatNumber
    {
        $this->prepare();

        return $this->builder
            ->where([
                ['instance_id', '=', $instance->id],
                ['default_non_taxable', '=', true],
                ['is_active', '=', ActiveEnum::ACTIVE()],
            ])
            ->where('value', '=', 0)
            ->first();
    }

    public function findBySlugAndInstanceId(string $slug, int $instanceId): ?Model
    {
        return VatNumber::where([
            'slug' => $slug,
            'instance_id' => $instanceId,
        ])->first();
    }

    /**
     * @return array
     */
    protected function createRules()
    {
        return [
            'instance_id' => 'required|exists:instances,id',
            'name' => 'required|string|max:255',
            'accounting_account_id' => 'required|exists:accounting_accounts,id',
            'code' => 'required|string|max:255',
            'value' => 'required|numeric',
        ];
    }

    /**
     * @param array $data
     *
     * @return array
     */
    protected function updateRules($data = [])
    {
        return [];
    }

    /**
     * @return array
     */
    protected function allowedWith()
    {
        return [
            'accountingAccount',
        ];
    }

    protected function beforeCreate(Model $model, $data)
    {
        $model->slug = $this->slugGeneratorService->generate();
        $model->is_active = ActiveEnum::ACTIVE();

        return $model;
    }
}
