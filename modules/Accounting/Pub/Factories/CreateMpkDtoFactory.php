<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\Factories;

use App\Enum\ActiveEnum;
use App\Exceptions\ValidationException;
use App\Repositories\CompanyRepository;
use App\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Repositories\MpkRepository;
use Modules\Accounting\Pub\Dtos\CreateCreateMpkDto;
use Modules\Accounting\Pub\Interfaces\CreateMpkDtoInterface;

class CreateMpkDtoFactory
{
    protected CompanyRepository $companyRepository;
    protected MpkRepository $mpkRepository;

    public function __construct(
        CompanyRepository $companyRepository,
        MpkRepository $mpkRepository
    ) {
        $this->companyRepository = $companyRepository;
        $this->mpkRepository = $mpkRepository;
    }

    public function createFromArrayByUser(array $row, User $byUser): CreateMpkDtoInterface
    {
        $data = $this->validate($row, $byUser);

        $company = null;
        if (!empty($row[CreateMpkDtoInterface::KEY_COMPANY_CODE])) {
            $company = $this->companyRepository->findCompanyByCodeAndInstanceId(
                $row[CreateMpkDtoInterface::KEY_COMPANY_CODE],
                $byUser->instance_id,
            );
        }

        return new CreateCreateMpkDto(
                $data[CreateMpkDtoInterface::KEY_SLUG] ?? Mpk::generateSlug() ?: Mpk::generateSlug(),
            $data[CreateMpkDtoInterface::KEY_CODE],
            $data[CreateMpkDtoInterface::KEY_NAME],
            $company,
            $company !== null ? $company->id : null,
            $byUser->instance,
            new ActiveEnum((int)$data[CreateMpkDtoInterface::KEY_ACTIVE]),
        );
    }

    protected function validate(array $row, User $byUser): array
    {
        $validator = Validator::make($row, $this->rules($byUser));

        if ($validator->fails()) {
            throw (new ValidationException($validator))
                ->errorBag($validator->errors());
        }

        return $validator->getData();
    }

    protected function rules(User $byUser): array
    {
        $instanceId = $byUser->instance_id;

        return [
            CreateMpkDtoInterface::KEY_CODE => ['required', 'string'],
            CreateMpkDtoInterface::KEY_NAME => ['required', 'string'],
            CreateMpkDtoInterface::KEY_COMPANY_ID => [
                'nullable',
                'numeric',
                'exists:companies,id,instance_id,' . $instanceId,
            ],
            CreateMpkDtoInterface::KEY_COMPANY_CODE => [
                'nullable',
                'string',
                'exists:companies,code,instance_id,' . $instanceId,
            ],
            CreateMpkDtoInterface::KEY_ACTIVE => ['required', 'numeric', 'in:0,1'],
            CreateMpkDtoInterface::KEY_SLUG => [
                'nullable',
                'string',
                'max:36',
                Rule::unique('mpks', 'slug')->where('instance_id', $instanceId),
            ],
        ];
    }
}