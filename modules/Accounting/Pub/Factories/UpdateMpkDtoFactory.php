<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\Factories;

use App\Enum\ActiveEnum;
use App\Exceptions\ValidationException;
use App\User;
use Illuminate\Support\Facades\Validator;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Pub\Dtos\UpdateCreateMpkDto;
use Modules\Accounting\Pub\Interfaces\CreateMpkDtoInterface;
use Modules\Accounting\Pub\Interfaces\UpdateMpkDtoInterface;

class UpdateMpkDtoFactory extends CreateMpkDtoFactory
{
    public function createFromArrayByUser(array $row, User $byUser): UpdateMpkDtoInterface
    {
        $data = $this->validate($row, $byUser);
        /** @var Mpk $mpk */
        $mpk = $this->mpkRepository->findByCodeAndInstanceId(
            $data[UpdateMpkDtoInterface::KEY_CODE],
            $byUser->instance_id,
        );

        $company = null;
        if (!empty($row[CreateMpkDtoInterface::KEY_COMPANY_CODE])) {
            $company = $this->companyRepository->findCompanyByCodeAndInstanceId(
                $row[CreateMpkDtoInterface::KEY_COMPANY_CODE],
                $byUser->instance_id,
            );
        }

        return new UpdateCreateMpkDto(
            $mpk->slug,
            $data[UpdateMpkDtoInterface::KEY_CODE],
            $data[UpdateMpkDtoInterface::KEY_NAME],
            new ActiveEnum((int)$data[UpdateMpkDtoInterface::KEY_ACTIVE]),
            $company,
            $company !== null ? $company->id : null,
            $mpk,
            $byUser->instance,
        );
    }

    protected function validate(array $row, User $byUser): array
    {
        $validator = Validator::make($row, $this->rules($byUser));

        if ($validator->fails()) {
            throw (new ValidationException($validator))
                ->errorBag($validator->errors());
        }

        return $validator->getData();
    }

    protected function rules(User $byUser): array
    {
        $instanceId = $byUser->instance_id;

        return [
            UpdateMpkDtoInterface::KEY_CODE => ['required', 'string'],
            UpdateMpkDtoInterface::KEY_NAME => ['required', 'string'],
            UpdateMpkDtoInterface::KEY_COMPANY_ID => [
                'nullable',
                'numeric',
                'exists:companies,id,instance_id,' . $instanceId,
            ],
            UpdateMpkDtoInterface::KEY_ACTIVE => ['required', 'numeric', 'in:0,1'],
        ];
    }
}