<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\Facades;

use Modules\Accounting\Priv\UseCases\AccountingAccounts\CreateAccountingAccountUseCase;
use Modules\Accounting\Priv\UseCases\AccountingAccounts\UpdateAccountingAccountUseCase;
use Modules\Accounting\Pub\Interfaces\CreateAccountingAccountRequestInterface;
use Modules\Accounting\Pub\Interfaces\UpdateAccountingAccountRequestInterface;
use Modules\Accounting\Pub\ViewObjects\AccountingAccountViewObject;

class AccountingAccountFacade
{
    protected CreateAccountingAccountUseCase $createAccountingAccountUseCase;

    protected UpdateAccountingAccountUseCase $updateAccountingAccountUseCase;

    public function __construct(
        CreateAccountingAccountUseCase $createAccountingAccountUseCase,
        UpdateAccountingAccountUseCase $updateAccountingAccountUseCase
    ) {
        $this->createAccountingAccountUseCase = $createAccountingAccountUseCase;
        $this->updateAccountingAccountUseCase = $updateAccountingAccountUseCase;
    }

    public function create(CreateAccountingAccountRequestInterface $createAccountingAccountRequest): AccountingAccountViewObject
    {
        return AccountingAccountViewObject::createFromAccountingAccount(
            $this->createAccountingAccountUseCase->run($createAccountingAccountRequest)
        );
    }

    public function update(UpdateAccountingAccountRequestInterface $request)
    {
        return AccountingAccountViewObject::createFromAccountingAccount(
            $this->updateAccountingAccountUseCase->run($request)
        );
    }
}
