<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\Dtos;

use App\Company;
use App\Enum\ActiveEnum;
use App\Instance;
use Modules\Accounting\Pub\Interfaces\CreateMpkDtoInterface;

class CreateCreateMpkDto implements CreateMpkDtoInterface
{
    protected string $slug;
    protected string $code;
    protected string $name;
    protected ?Company $company;
    protected ?int $companyId;
    protected Instance $instance;
    protected ActiveEnum $isActive;

    public function __construct(
        string $slug,
        string $code,
        string $name,
        ?Company $company,
        ?int $companyId,
        Instance $instance,
        ?ActiveEnum $isActive = null
    ) {
        $this->slug = $slug;
        $this->code = $code;
        $this->name = $name;
        $this->company = $company;
        $this->companyId = $companyId;
        $this->instance = $instance;
        $this->isActive = $isActive instanceof ActiveEnum ? $isActive : ActiveEnum::INACTIVE();
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function getInstance(): Instance
    {
        return $this->instance;
    }

    public function getIsActive(): ActiveEnum
    {
        return $this->isActive;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }
}
