<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use Modules\Analytics\Priv\Entities\RequestAccountingAccountDimensionItem;

class RequestAccountingAccountDimensionItemViewObject
{
    protected int $id;

    protected string $slug;

    protected int $requestId;

    protected int $accountDimensionId;

    protected int $accountDimensionItemId;

    protected AccountDimensionItemViewObject $accountDimensionItem;

    public function __construct(
        int $id,
        string $slug,
        int $requestId,
        int $accountDimensionId,
        int $accountDimensionItemId,
        AccountDimensionItemViewObject $accountDimensionItem
    ) {
        $this->id = $id;
        $this->slug = $slug;
        $this->requestId = $requestId;
        $this->accountDimensionId = $accountDimensionId;
        $this->accountDimensionItemId = $accountDimensionItemId;
        $this->accountDimensionItem = $accountDimensionItem;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getRequestId(): int
    {
        return $this->requestId;
    }

    public function getAccountDimensionId(): int
    {
        return $this->accountDimensionId;
    }

    public function getAccountDimensionItemId(): int
    {
        return $this->accountDimensionItemId;
    }

    public function getAccountDimensionItem(): AccountDimensionItemViewObject
    {
        return $this->accountDimensionItem;
    }

    public static function createFromRequestAccountingAccountDimensionItem(RequestAccountingAccountDimensionItem $item): RequestAccountingAccountDimensionItemViewObject
    {
        return new static(
            $item->id,
            $item->slug,
            $item->request_id,
            $item->account_dimension_id,
            $item->account_dimension_item_id,
            AccountDimensionItemViewObject::createFromAccountDimensionItem($item->accountDimensionItem)
        );
    }
}