<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Enum\ActiveEnum;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Enum\AccountingAccountTypeEnum;

class AccountingAccountViewObject
{
    protected string $slug;

    protected string $code;

    protected string $name;

    protected AccountingAccountTypeEnum $type;

    protected ActiveEnum $isActive;

    private function __construct(string $slug, string $code, string $name, AccountingAccountTypeEnum $type, ActiveEnum $isActive)
    {
        $this->slug = $slug;
        $this->code = $code;
        $this->name = $name;
        $this->type = $type;
        $this->isActive = $isActive;
    }

    public function getSlug()
    {
        return $this->slug;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getType(): AccountingAccountTypeEnum
    {
        return $this->type;
    }

    public function getIsActive(): ActiveEnum
    {
        return $this->isActive;
    }

    public static function createFromAccountingAccount(AccountingAccount $accountingAccount): AccountingAccountViewObject
    {
        return new self(
            $accountingAccount->slug,
            $accountingAccount->getCode(),
            $accountingAccount->name,
            $accountingAccount->type,
            $accountingAccount->is_active
        );
    }
}
