<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use Modules\Analytics\Priv\Entities\Project;

class ProjectViewObject
{
    protected string $slug;
    protected string $code;

    public function __construct(string $slug, string $code)
    {
        $this->slug = $slug;
        $this->code = $code;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public static function createFromProject(Project $project): ProjectViewObject
    {
        return new static($project->slug, $project->code);
    }
}