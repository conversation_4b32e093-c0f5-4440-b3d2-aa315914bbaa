<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Vendors\Math;
use Modules\Accounting\Priv\Entities\VatNumber;

class VatNumberViewObject
{
    /**
     * @var string
     */
    protected $code;

    protected string $deductibility;

    /**
     * @var string|null
     */
    protected $value;

    private AccountingAccountViewObject $accountingAccount;
    private ?AccountingAccountViewObject $reverseAccountingAccount;

    public function __construct(
        string $code,
        string $deductibility,
        ?string $value,
        AccountingAccountViewObject $accountingAccount,
        ?AccountingAccountViewObject $reverseAccountingAccount = null
    ) {
        $this->code = $code;
        $this->deductibility = $deductibility;
        $this->value = $value;
        $this->accountingAccount = $accountingAccount;
        $this->reverseAccountingAccount = $reverseAccountingAccount;
    }

    public static function createFromVatNumber(VatNumber $vatNumber): VatNumberViewObject
    {
        return new VatNumberViewObject(
            $vatNumber->code,
            Math::asString($vatNumber->deductibility),
            $vatNumber->value,
            AccountingAccountViewObject::createFromAccountingAccount($vatNumber->accountingAccount),
            $vatNumber->reverseAccountingAccount ? AccountingAccountViewObject::createFromAccountingAccount(
                $vatNumber->reverseAccountingAccount,
            ) : null,
        );
    }

    /**
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * @return string|null
     */
    public function getValue(): ?string
    {
        return $this->value;
    }

    public function getDeductibility(): string
    {
        return $this->deductibility;
    }

    public function getAccountingAccount(): AccountingAccountViewObject
    {
        return $this->accountingAccount;
    }

    public function getReverseAccountingAccount(): ?AccountingAccountViewObject
    {
        return $this->reverseAccountingAccount;
    }

    public function hasReverseVat(): bool
    {
        return null !== $this->reverseAccountingAccount;
    }
}