<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Request;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Services\AccountingAccountSelection\AccountingAccountSelectionService;
use Modules\Analytics\Priv\Entities\RequestAccountingAccountDimensionItem;

class AllowanceViewObject
{
    protected ?AccountingAccountViewObject $accountingAccount;

    protected ?UserViewObject $accountant;

    protected CurrencyViewObject $currency;

    protected AllowanceLineCollection $allowanceLines;

    protected Collection $accountingDimensions;

    public function __construct(
        ?AccountingAccountViewObject $accountingAccount,
        ?UserViewObject $accountant,
        CurrencyViewObject $currency,
        AllowanceLineCollection $allowanceLines,
        Collection $accountingDimensions
    ) {
        $this->accountingAccount = $accountingAccount;
        $this->accountant = $accountant;
        $this->currency = $currency;
        $this->allowanceLines = $allowanceLines;
        $this->accountingDimensions = $accountingDimensions;
    }

    public static function createFromRequest(Request $request, CurrencyViewObject $currencyViewObject, AllowanceLineCollection $allowanceLines): AllowanceViewObject
    {
        $accountingAccountSelectionService = resolve(AccountingAccountSelectionService::class);

        return new static(
            AccountingAccountViewObject::createFromAccountingAccount(
                $accountingAccountSelectionService->credit()->getTravelExpensesAccountingAccount($request->instance, $request)
            ),
            $request->accountingUser ? UserViewObject::createFromUser($request->accountingUser)
                ->setSupervisorFromUser($request->accountingUser)
                ->setAssistantFromUser($request->accountingUser) : null,
            $currencyViewObject,
            $allowanceLines,
            $request->accountingAccountDimensionItems->map(function(RequestAccountingAccountDimensionItem $item) {
                return SelectedAccountDimensionViewObject::createFromAccountDimensionItem($item->accountDimensionItem);
            }),
        );
    }

    public function getAccountingAccount(): AccountingAccountViewObject
    {
        return $this->accountingAccount;
    }

    public function getCurrency(): CurrencyViewObject
    {
        return $this->currency;
    }

    public function getAllowanceLines(): AllowanceLineCollection
    {
        return $this->allowanceLines;
    }

    public function getAccountant(): ?UserViewObject
    {
        return $this->accountant;
    }

    public function getAccountingDimensions(): Collection
    {
        return $this->accountingDimensions;
    }
}
