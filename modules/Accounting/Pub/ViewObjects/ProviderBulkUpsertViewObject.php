<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

class ProviderBulkUpsertViewObject
{
    private int $added;
    private int $updated;
    private int $failed;
    private array $failedRecords;

    public function __construct(int $added, int $updated, array $failedRecords)
    {
        $this->added = $added;
        $this->updated = $updated;
        $this->failed = count($failedRecords);
        $this->failedRecords = $failedRecords;
    }

    public function getAdded(): int
    {
        return $this->added;
    }

    public function getUpdated(): int
    {
        return $this->updated;
    }

    public function getFailed(): int
    {
        return $this->failed;
    }

    public function getFailedRecords(): array
    {
        return $this->failedRecords;
    }
}
