<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Currency;
use App\Instance;
use App\RequestAccountingMileageAllowance;
use App\RequestAccountingTravelExpenses;

class CurrencyViewObject
{
    /**
     * @var string
     */
    protected $code;

    /**
     * @var string
     */
    protected $exchangeRate;

    /**
     * CurrencyViewObject constructor.
     * @param string $code
     * @param string $exchangeRate
     */
    public function __construct(string $code, string $exchangeRate)
    {
        $this->code = $code;
        $this->exchangeRate = $exchangeRate;
    }

    /**
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * @return float
     */
    public function getExchangeRate(): string
    {
        return $this->exchangeRate;
    }

    public function getUnits(): string
    {
        return "1";
    }

    public static function createFromRequestAccountingTravelExpenses(RequestAccountingTravelExpenses $expense): CurrencyViewObject
    {
        return new static($expense->currency->code, $expense->exchange_rate);
    }

    public static function createFromRequestAccountingMileageAllowance(RequestAccountingMileageAllowance $mileageAllowance): CurrencyViewObject
    {
        return new static($mileageAllowance->currency->code, $mileageAllowance->exchange_rate);
    }

    public static function createFromCurrency(Currency $currency, string $exchangeRate): CurrencyViewObject
    {
        return new static($currency->code, $exchangeRate);
    }

    public static function getDefaultInstanceCurrency(Instance $instance): CurrencyViewObject
    {
        return new static($instance->currency->code, Currency::DEFAULT_CURRENCY_EXCHANGE_RATE);
    }
}