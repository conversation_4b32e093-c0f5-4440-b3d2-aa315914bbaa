<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Company;

class CompanyViewObject
{
    protected string $slug;

    protected string $code;

    protected string $name;

    protected InstanceViewObject $instance;

    /**
     * @param string $slug
     * @param string $code
     * @param string $name
     * @param InstanceViewObject $instance
     */
    public function __construct(string $slug, string $code, string $name, InstanceViewObject $instance)
    {
        $this->slug = $slug;
        $this->code = $code;
        $this->name = $name;
        $this->instance = $instance;
    }

    public static function createFromCompany(Company $company): CompanyViewObject
    {
        return new static(
            $company->slug,
            $company->code,
            $company->name,
            InstanceViewObject::createFromInstance($company->instance)
        );
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getInstance(): InstanceViewObject
    {
        return $this->instance;
    }
}
