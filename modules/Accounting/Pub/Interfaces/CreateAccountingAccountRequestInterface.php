<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\Interfaces;

use App\Instance;

interface CreateAccountingAccountRequestInterface
{
    public const KEY_NUMBER = 'number';

    public const KEY_ACCOUNT_NUMBER = 'account_number';

    public const KEY_CODE = 'code';

    public const KEY_NAME = 'name';

    public const KEY_TYPE = 'type';

    public const KEY_IS_ACTIVE = 'is_active';

    public function getNumber(): ?string;

    public function getName(): ?string;

    public function getType(): ?string;

    public function getIsActive(): ?int;

    public function getInstance(): Instance;

    public function getNumberKey(): string;

    public function getNameKey(): string;

    public function getTypeKey(): string;

    public function getIsActiveKey(): string;
}
