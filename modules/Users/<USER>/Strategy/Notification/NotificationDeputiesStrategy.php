<?php

namespace Modules\Users\Priv\Strategy\Notification;

use Illuminate\Notifications\Notification;
use App\User;
use App\UserDeputy;

class NotificationDeputiesStrategy extends NotificationStrategy
{
    public function notify(User $user, Notification $notification, bool $queued, ?array $channels = null): void
    {
        $userDeputies = $user->getActiveUserDeputies();

        $userDeputies->each(function (UserDeputy $userDeputy) use ($notification, $queued, $channels) {
            $userDeputy->deputy->notifyInternal($notification, $channels, $queued);
        });
    }

    public function shouldNotify(User $user, Notification $notification): bool
    {
        return
            $user->isActive()
            && $user->haveDeputies()
            && !$this->isNotificationExcludedFromRedirect($notification);
    }
}