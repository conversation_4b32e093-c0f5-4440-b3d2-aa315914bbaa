<?php

namespace Modules\Users\Pub\Factories;

use App\Company;
use App\Instance;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Modules\Analytics\Priv\Entities\UserAccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\AccountDimensionRepository;
use Modules\IntegrationFILE\Pub\Exceptions\AccountDimensionItemNotExistsException;
use Modules\IntegrationFILE\Pub\Exceptions\AccountDimensionNotExistsException;
use Modules\IntegrationFILE\Pub\Exceptions\UserNotExistsException;
use Modules\Users\Pub\Facades\UserFacade;
use Modules\Users\Pub\Requests\NewUserDimensionItemRequest;

class NewUserDimensionItemRequestFactory
{
    use DatabaseMigrations;

    private UserFacade $userFacade;
    private AccountDimensionRepository $accountDimensionRepository;
    private AccountDimensionItemRepository $accountDimensionItemRepository;

    public function __construct(
        UserFacade $userFacade,
        AccountDimensionRepository $accountDimensionRepository,
        AccountDimensionItemRepository $accountDimensionItemRepository
    ) {
        $this->userFacade = $userFacade;
        $this->accountDimensionRepository = $accountDimensionRepository;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
    }

    public function createFromImportRowData(
        Instance $instance,
        Company $company,
        array $importRowData
    ): NewUserDimensionItemRequest {
        $emailString = strtolower($importRowData[NewUserDimensionItemRequest::KEY_EMAIL]);
        $user = $this->userFacade->findUserByEmailAndCompanyId($emailString, $company->id);

        if ($user === null) {
            throw UserNotExistsException::create();
        }

        $accountDimension = $this->accountDimensionRepository->findActiveByCodeForInstanceAndCompany(
            $instance,
            $company,
            $importRowData[NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSIONS],
        );

        if ($accountDimension === null) {
            throw AccountDimensionNotExistsException::create();
        }

        $accountDimensionItem = $this->accountDimensionItemRepository->findActiveByAccountDimensionAndCode(
            $accountDimension,
            $importRowData[NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSION_ITEMS],
        );

        if ($accountDimensionItem === null) {
            throw AccountDimensionItemNotExistsException::create();
        }

        $uniqueItemPerUser = $importRowData[NewUserDimensionItemRequest::KEY_UNIQUE_DIMENSION_ITEM_FOR_USER] ?? true;

        $type = empty($importRowData[NewUserDimensionItemRequest::KEY_RELATIONSHIP_TYPE]) ? UserAccountDimensionItem::TYPE_DEFAULT : $importRowData[NewUserDimensionItemRequest::KEY_RELATIONSHIP_TYPE];

        return new NewUserDimensionItemRequest(
            $user,
            $accountDimensionItem,
            (bool)$uniqueItemPerUser,
            $type,
        );
    }
}
