<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Dtos;

use App\Enum\AvailableLanguagesEnum;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class UserDto
{
    /**
     * @var string
     */
    protected $firstName;

    /**
     * @var string
     */
    protected $lastName;

    /**
     * @var int
     */
    protected $companyId;

    /**
     * @var ?int
     */
    protected $supervisorId;

    /**
     * @var Collection
     */
    protected $assistants;

    /**
     * @var string
     */
    protected $email;

    /**
     * @var string
     */
    protected $sex;

    /**
     * @var Collection
     */
    protected $groups;

    /**
     * @var int
     */
    protected $citizenshipId;

    /**
     * @var int
     */
    protected $mpkId;

    /**
     * @var Carbon
     */
    protected $birthDate;

    /**
     * @var string
     */
    protected $phoneNumber;

    /**
     * @var int
     */
    protected $gradeId;

    /**
     * @var ?string
     */
    protected $erpId;

    protected ?string $hrId;

    /**
     * @var AvailableLanguagesEnum
     */
    protected $language;

    protected ?string $workLocation;

    protected Collection $accountDimensionItems;

    protected string $employeeUniqueIdentifier;

    private string $slug;
    private array $mpk;

    public function __construct(
        string $firstName,
        string $lastName,
        int $companyId,
        ?int $supervisorId,
        Collection $assistants,
        string $email,
        string $sex,
        Collection $groups,
        int $citizenshipId,
        int $mpkId,
        array $mpk,
        Carbon $birthDate,
        string $phoneNumber,
        int $gradeId,
        ?string $erpId,
        ?string $hrId,
        AvailableLanguagesEnum $language,
        ?string $workLocation,
        Collection $accountDimensionItems,
        string $employeeUniqueIdentifier,
        string $slug
    ) {
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->companyId = $companyId;
        $this->supervisorId = $supervisorId;
        $this->assistants = $assistants;
        $this->email = $email;
        $this->sex = $sex;
        $this->groups = $groups;
        $this->citizenshipId = $citizenshipId;
        $this->mpkId = $mpkId;
        $this->birthDate = $birthDate;
        $this->phoneNumber = $phoneNumber;
        $this->gradeId = $gradeId;
        $this->erpId = $erpId;
        $this->hrId = $hrId;
        $this->language = $language;
        $this->workLocation = $workLocation;
        $this->accountDimensionItems = $accountDimensionItems;
        $this->employeeUniqueIdentifier = $employeeUniqueIdentifier;
        $this->slug = $slug;
        $this->mpk = empty($mpk) ? [['id' => $this->getMpkId(), 'percentage' => 100, 'main' => true]] : $mpk;
    }

    /**
     * @return string
     */
    public function getFirstName(): string
    {
        return $this->firstName;
    }

    /**
     * @return string
     */
    public function getLastName(): string
    {
        return $this->lastName;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @return int|null
     */
    public function getSupervisorId(): ?int
    {
        return $this->supervisorId;
    }

    public function getAssistants(): Collection
    {
        return $this->assistants;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @return string
     */
    public function getSex(): string
    {
        return $this->sex;
    }

    /**
     * @return Collection
     */
    public function getGroups(): Collection
    {
        return $this->groups;
    }

    /**
     * @return int
     */
    public function getCitizenshipId(): int
    {
        return $this->citizenshipId;
    }

    /**
     * @return int
     */
    public function getMpkId(): int
    {
        return $this->mpkId;
    }

    /**
     * @return Carbon
     */
    public function getBirthDate(): Carbon
    {
        return $this->birthDate;
    }

    /**
     * @return string
     */
    public function getPhoneNumber(): string
    {
        return $this->phoneNumber;
    }

    /**
     * @return int
     */
    public function getGradeId(): int
    {
        return $this->gradeId;
    }

    /**
     * @return string|null
     */
    public function getErpId(): ?string
    {
        return $this->erpId;
    }

    /**
     * @return string|null
     */
    public function getHrId(): ?string
    {
        return $this->hrId;
    }

    /**
     * @return AvailableLanguagesEnum
     */
    public function getLanguage(): AvailableLanguagesEnum
    {
        return $this->language;
    }

    public function getWorkLocation(): ?string
    {
        return $this->workLocation;
    }

    public function getAccountDimensionItems(): Collection
    {
        return $this->accountDimensionItems;
    }

    public function getEmployeeUniqueIdentifier(): string
    {
        return $this->employeeUniqueIdentifier;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getMpk(): array
    {
        return $this->mpk;
    }
}
