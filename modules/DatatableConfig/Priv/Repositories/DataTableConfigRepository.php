<?php

declare(strict_types=1);

namespace Modules\DatatableConfig\Priv\Repositories;

use App\Repositories\Repository;
use Illuminate\Database\Eloquent\Collection;
use Modules\DatatableConfig\Priv\Entities\DataTableConfig;

class DataTableConfigRepository extends Repository
{
    static function model()
    {
        return DataTableConfig::class;
    }

    public function findByCompanyId(int $companyId): Collection
    {
        $this->prepare();

        return $this->builder->where('company_id', $companyId)->get();
    }
}
