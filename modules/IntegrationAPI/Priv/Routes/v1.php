<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
$middlewares = [
    'logRequests:integration-api',
    'integration_api.is_enabled',
    'client',
    'integration_api.client_auth',
    'integration_api.is_user_from_current_instance',
    'auth.locale',
    'auth.instance',
];

if (app()->environment() !== 'testing') {
    $middlewares[] = 'throttleRequestsWithRedis:2000';
}

Route::group([
    'middleware' => $middlewares
], function () {
    Route::get('companies', 'CompanyController@index');
    Route::get('cost-centers', 'CostCenterController@index');
    Route::get('groups', 'GroupController@index');
    Route::get('users', 'UserController@index');
    Route::post('users', 'UserController@store')->name('users.store');
    Route::put('users/{slug}', 'UserController@update')->name('users.update');
    Route::patch('users/{slug}', 'UserController@patch')->name('users.patch');
    Route::post('users/{slug}/activate', 'UserController@activate');
    Route::post('users/{slug}/deactivate', 'UserController@deactivate');

    Route::get('requests/{slug}', 'SettlementController@getRequest');

    Route::get('claims', 'SettlementController@index');
    Route::get('claims/{slug}/summary', 'SettlementController@summary');
    Route::post('claims/{slug}/accounted', 'SettlementController@accountedInErp');
    Route::post('claims/{slug}/cannot-be-accounted', 'SettlementController@cannotBeAccounted');

    Route::post('documents/{slug}/accounted', 'DocumentController@accountedInErp');
    Route::get('documents/{slug}/file', 'DocumentController@file');

    Route::get('suppliers', 'ProviderController@index');
    Route::post('suppliers', 'ProviderController@store');
    if (!in_array(app()->environment(), ['testing', 'local'])) {
        Route::middleware('throttleRequestsWithRedis:12,1')->post('suppliers/bulk', 'ProviderController@bulkUpdate');
    } else {
        Route::post('suppliers/bulk', 'ProviderController@bulkUpdate');
    }
    Route::put('suppliers/{slug}', 'ProviderController@update');
    Route::delete('suppliers/{slug}', 'ProviderController@destroy');

    Route::get('account-dimensions', 'AccountDimensionController@index');
    Route::post('account-dimensions', 'AccountDimensionController@store');
    Route::put('account-dimensions/{slug}', 'AccountDimensionController@update');

    Route::post('account-dimension-items', 'AccountDimensionItemController@store');
    Route::put('account-dimension-items/{slug}', 'AccountDimensionItemController@update');
    Route::post('account-dimension-items/{slug}/deactivate', 'AccountDimensionItemController@deactivate');

    Route::post('accounting-accounts', 'AccountingAccountController@store');
    Route::put('accounting-accounts/{slug}', 'AccountingAccountController@update');

    Route::get('accounts', 'MyCardController@accounts');
    Route::get('accounts/{accountId}/statements', 'MyCardController@statements');
    Route::get('accounts/statements/{statementId}/export', 'MyCardController@statementExport');
});
