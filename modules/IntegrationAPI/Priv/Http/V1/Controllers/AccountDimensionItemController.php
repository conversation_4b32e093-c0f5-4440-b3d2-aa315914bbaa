<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;
use Modules\IntegrationAPI\Priv\Http\V1\Requests\StoreAccountDimensionItemRequest;
use Modules\IntegrationAPI\Priv\Http\V1\Requests\UpdateAccountDimensionItemRequest;
use Modules\IntegrationAPI\Priv\Http\V1\Resources\AccountDimensionItemResource;
use Modules\IntegrationAPI\Priv\Http\V1\Transformers\BasicTransformer;
use Symfony\Component\HttpFoundation\Response;

class AccountDimensionItemController
{
    protected AccountDimensionItemFacade $facade;

    public function __construct(AccountDimensionItemFacade $facade)
    {
        $this->facade = $facade;
    }

    /**
     * @OA\Post (
     *     path="/v1/account-dimension-items",
     *     tags={"Account dimension items"},
     *     security={
     *         {"passport": {}}
     *     },
     *     description="Create new account dimension item",
     *     operationId="createAccountDimensionItem",
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/AccountDimensionItemResource")
     *      ),
     *      @OA\Response(
     *          response=201,
     *          description="Account dimension item created",
     *          @OA\JsonContent(
     *                  @OA\Property(
     *                      property="data",
     *                      @OA\Property(
     *                          property="id",
     *                          type="string",
     *                          example="f1cf0783-aa9f-42e1-a58e-459e87bf486b"
     *                      ),
     *                      @OA\Property(
     *                          property="type",
     *                          type="string",
     *                          example="account-dimension-items"
     *                      ),
     *                      @OA\Property(
     *                          property="attributes",
     *                          ref="#/components/schemas/AccountDimensionItemResource"
     *                      )
     *                  )
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="errors",
     *                  type="array",
     *                  minimum=1,
     *                  @OA\Items(ref="#/components/schemas/AuthenticationErrorSchema")
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable Entity",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="errors",
     *                  type="array",
     *                  minimum=1,
     *                  @OA\Items(ref="#/components/schemas/ValidationErrorSchema")
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=429,
     *          description="Too Many Requests",
     *          @OA\JsonContent()
     *      )
     * )
     */
    public function store(StoreAccountDimensionItemRequest $request): JsonResponse
    {
        $dimensionItem = $this->facade->create($request);

        $transformer = new BasicTransformer(AccountDimensionItemResource::map($dimensionItem));
        $response = $transformer->response()->setStatusCode(Response::HTTP_CREATED);

        return $response;
    }

    /**
     * @OA\Put (
     *     path="/v1/account-dimension-items/{mindento_account_dimension_item_id}",
     *     tags={"Account dimension items"},
     *     security={
     *         {"passport": {}}
     *     },
     *     description="Update existing account dimension item",
     *     operationId="updateAccountDImensionItem",
     *      @OA\Parameter(
     *          name="mindento_account_dimension_item_id",
     *          description="Mindento Account Dimension Item Identifier received from API after resource creation",
     *          required=true,
     *          example="f1cf0783-aa9f-42e1-a58e-459e87bf486b",
     *          in="path",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/AccountDimensionItemResource")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Account dimension item updated",
     *          @OA\JsonContent(
     *                  @OA\Property(
     *                      property="data",
     *                      @OA\Property(
     *                          property="id",
     *                          type="string",
     *                          example="f1cf0783-aa9f-42e1-a58e-459e87bf486b"
     *                      ),
     *                      @OA\Property(
     *                          property="type",
     *                          type="string",
     *                          example="account-dimension-items"
     *                      ),
     *                      @OA\Property(
     *                          property="attributes",
     *                          ref="#/components/schemas/AccountDimensionItemResource"
     *                      )
     *                  )
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="errors",
     *                  type="array",
     *                  minimum=1,
     *                  @OA\Items(ref="#/components/schemas/AuthenticationErrorSchema")
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Resource Not Found",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="errors",
     *                  type="array",
     *                  minimum=1,
     *                  @OA\Items(ref="#/components/schemas/ResourceNotFoundErrorSchema")
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable Entity",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="errors",
     *                  type="array",
     *                  minimum=1,
     *                  @OA\Items(ref="#/components/schemas/ValidationErrorSchema")
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=429,
     *          description="Too Many Requests",
     *          @OA\JsonContent()
     *      )
     * )
     */
    public function update(UpdateAccountDimensionItemRequest $request): JsonResponse
    {
        $dimensionItem = $this->facade->update($request);

        $transformer = new BasicTransformer(AccountDimensionItemResource::map($dimensionItem));

        $response = $transformer->response()->setStatusCode(Response::HTTP_OK);

        return $response;
    }

    /**
     * @OA\Post (
     *     path="/v1/account-dimension-items/{mindento_account_dimension_item_id}/deactivate",
     *     tags={"Account dimension items"},
     *     security={
     *         {"passport": {}}
     *     },
     *     description="Deactivate existing account dimension item",
     *     operationId="deactivate",
     *      @OA\Parameter(
     *          name="mindento_account_dimension_item_id",
     *          description="Mindento Account Dimension Item Identifier received from API after resource creation",
     *          required=true,
     *          example="f1cf0783-aa9f-42e1-a58e-459e87bf486b",
     *          in="path",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Response(
     *          response=204,
     *          description="Successful operation - resource deactivated",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="errors",
     *                  type="array",
     *                  minimum=1,
     *                  @OA\Items(ref="#/components/schemas/AuthenticationErrorSchema")
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Resource Not Found",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="errors",
     *                  type="array",
     *                  minimum=1,
     *                  @OA\Items(ref="#/components/schemas/ResourceNotFoundErrorSchema")
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=429,
     *          description="Too Many Requests",
     *          @OA\JsonContent()
     *      )
     * )
     */
    public function deactivate(string $slug, Request $httpRequest)
    {
        $this->facade->deactivate($slug, $httpRequest->user());

        return response(null, Response::HTTP_NO_CONTENT);
    }
}
