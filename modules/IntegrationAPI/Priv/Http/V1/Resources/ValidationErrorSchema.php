<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Resources;

use OpenApi\Annotations as OA;
use OpenApi\Annotations\Items;

/**
 * @OA\Schema(
 *     title="ValidationErrorSchema",
 *     @OA\Xml(
 *         name="ValidationErrorSchema"
 *     )
 * )
 */
class ValidationErrorSchema
{
    /**
     * @OA\Property(
     *     title="source",
     *     type="object",
     *     @OA\Property(
     *         title="pointer",
     *         property="pointer",
     *         type="string",
     *         example="/data/attributes/birth_date"
     * )
     * )
     *
     */
    private $source;

    /**
     * @OA\Property(
     *     title="title",
     *     example="The birth date is not a valid date",
     * )
     *
     * @var string
     */
    private $title;

    /**
     * @OA\Property(
     *     title="detail",
     *     example="",
     * )
     *
     * @var string
     */
    private $detail;

}