<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Resources;

use App\Enum\ActiveEnum;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionViewObject;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

/**
 * @OA\Schema(
 *     title="AccountDimensionResource",
 *     @OA\Xml(
 *         name="AccountDimensionResource"
 *     ),
 *     required={
 *          "code",
 *          "name",
 *          "visibility",
 *          "order"
 *      }
 * )
 */
class AccountDimensionResource extends AbstractResource
{
    public const TYPE = 'account-dimensions';

    private string $slug;

    /**
     * @OA\Property(
     *  property="company_id",
     *  type="string",
     *  example=null
     * )
     *
     * @var string
     */
    private ?string $companyId;

    /**
     * @OA\Property(
     *     title="code",
     *     type="string",
     *     example="DIM1"
     * )
     *
     * @var string
     */
    private string $code;

    /**
     * @OA\Property(
     *     title="name",
     *     type="string",
     *     example="My first dimension"
     * )
     *
     * @var string
     */
    private string $name;

    /**
     * @OA\Property(
     *     title="visibility",
     *     type="string",
     *     enum={"general", "accounting", "document_header"},
     *     example="general"
     * )
     *
     * @var string
     */
    private AccountDimensionVisibilityEnum $visibility;

    /**
     * @OA\Property(
     *     title="order",
     *     type="integer",
     *     example=1
     * )
     *
     * @var int
     */
    private int $order;

    /**
     * @OA\Property(
     *  property="is_active",
     *  type="integer",
     *  enum={0, 1},
     *  example=1
     * )
     *
     * @var string
     */
    private ActiveEnum $isActive;

    public function getIdentifierValue()
    {
        return $this->slug;
    }

    /**
     * @param AccountDimensionViewObject $entity
     */
    public static function map($entity)
    {
        $resource = new static();
        $resource->slug = $entity->getSlug();
        $resource->code = $entity->getCode();
        $resource->name = $entity->getName();
        $resource->companyId = $entity->getCompanySlug();
        $resource->visibility = $entity->getVisibility();
        $resource->order = $entity->getOrder();
        $resource->isActive = $entity->getIsActive();

        return $resource;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'company_id' => $this->companyId,
            'code' => $this->code,
            'name' => $this->name,
            'order' => $this->order,
            'visibility' => $this->visibility,
            'is_active' => $this->isActive,
        ];
    }
}
