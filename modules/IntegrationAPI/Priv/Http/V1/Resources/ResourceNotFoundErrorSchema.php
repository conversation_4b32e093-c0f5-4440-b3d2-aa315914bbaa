<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Resources;

/**
 * @OA\Schema(
 *     title="ResourceNotFoundErrorSchema",
 *     @OA\Xml(
 *         name="ResourceNotFoundErrorSchema"
 *     )
 * )
 */
class ResourceNotFoundErrorSchema
{
    /**
     * @OA\Property(
     *     title="source",
     *     type="object",
     *     @OA\Property(
     *         title="pointer",
     *         property="pointer",
     *         type="string",
     *         example=""
     * )
     * )
     *
     */
    private $source;

    /**
     * @OA\Property(
     *     title="title",
     *     example="Item does not exist",
     * )
     *
     * @var string
     */
    private $title;

    /**
     * @OA\Property(
     *     title="detail",
     *     example="",
     * )
     *
     * @var string
     */
    private $detail;

}