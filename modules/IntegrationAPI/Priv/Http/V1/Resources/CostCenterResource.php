<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Resources;

use Modules\Accounting\Pub\ViewObjects\MpkViewObject;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     title="CostCenterResource",
 *     @OA\Xml(
 *         name="CostCenterResource"
 *     )
 * )
 */
class CostCenterResource extends AbstractResource
{
    public const TYPE = 'cost-centers';

    /**
     * @OA\Property(
     *     title="code",
     *     type="string",
     *     example="PL1"
     * )
     *
     * @var string
     */
    private string $code;

    /**
     * @OA\Property(
     *     title="name",
     *     type="string",
     *     example="Cost center name"
     * )
     *
     * @var string|null
     */
    private ?string $name;

    private string $slug;

    /**
     * @param string $code
     * @param string|null $name
     * @param string $slug
     */
    public function __construct(string $code, ?string $name, string $slug)
    {
        $this->code = $code;
        $this->name = $name;
        $this->slug = $slug;
    }

    public function getIdentifierValue()
    {
        return $this->slug;
    }

    /**
     * @param MpkViewObject $entity
     */
    public static function map($entity)
    {
        return new static(
            $entity->getCode(),
            $entity->getName(),
            $entity->getSlug(),
        );
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'code' => $this->code,
            'name' => $this->name,
        ];
    }
}
