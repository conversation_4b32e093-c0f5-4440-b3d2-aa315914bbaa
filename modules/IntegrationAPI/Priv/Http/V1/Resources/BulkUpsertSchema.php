<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Resources;

use Modules\Accounting\Pub\ViewObjects\ProviderBulkUpsertViewObject;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     title="BulkUpsertSchema",
 *     @OA\Xml(
 *         name="BulkUpsertSchema"
 *     )
 * )
 */
class BulkUpsertSchema
{
    /**
     * @OA\Property(
     *     title="added",
     *     type="integer",
     *     example="0"
     * )
     */
    public int $added = 0;

    /**
     * @OA\Property(
     *     title="updated",
     *     type="integer",
     *     example="0"
     * )
     */
    public int $updates = 0;

    /**
     * @OA\Property(
     *     title="failed",
     *     type="integer",
     *     example="0"
     * )
     */
    public int $failed = 0;

    /**
     * @OA\Property(
     *     title="failed_records",
     *     type="array",
     *     @OA\Items(ref="#/components/schemas/BulkUpsertErrorSchema")
     * )
     */
    public array $failedRecords = [];

    private function __construct(int $added, int $updates, int $failed)
    {
        $this->added = $added;
        $this->updates = $updates;
        $this->failed = $failed;
    }

    public static function fromBulkView(ProviderBulkUpsertViewObject $bulkUpsertViewObject): BulkUpsertSchema
    {
        $schema = new self($bulkUpsertViewObject->getAdded(), $bulkUpsertViewObject->getUpdated(), $bulkUpsertViewObject->getFailed());

        $failedRecords = $bulkUpsertViewObject->getFailedRecords();
        foreach ($failedRecords as $failedRecord) {
            $schema->addFailedRecord($failedRecord['id'], $failedRecord['reason'], $failedRecord['details']);
        }

        return $schema;
    }

    public function addFailedRecord(string $id, string $reason, array $details): void
    {
        $this->failedRecords[] = new BulkUpsertErrorSchema($id, $reason, $details);
    }
}