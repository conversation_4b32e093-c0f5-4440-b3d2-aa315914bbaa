<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Resources;

use Illuminate\Support\Collection;
use Modules\Accounting\Pub\ViewObjects\AllowanceViewObject;

/**
 * @OA\Schema(
 *     title="AllowanceResource",
 *     @OA\Xml(
 *         name="AllowanceResource"
 *     )
 * )
 */
class AllowanceResource extends AbstractResource
{
    /**
     * @OA\Property(
     *  property="accounting_account_id",
     *  type="string",
     *  example="5a3bd9f9-9d0e-4533-9d73-d11b96d5ccd1",
     *  description=""
     * )
     *
     */
    protected string $accountingAccountId;

    /**
     * @OA\Property(
     *     ref="#/components/schemas/CurrencyResource"
     * )
     */
    protected CurrencyResource $currency;

    /**
     * @OA\Property(
     *  property="account_dimensions",
     *  type="array",
     *  @OA\Items(ref="#/components/schemas/SelectedAccountDimensionResource"),
     *  nullable=false,
     * )
     *
     * @var Collection
     */
    protected $accountDimensions;

    /**
     * @OA\Property(
     *     title="local_currency_amount",
     *     property="local_currency_amount",
     *     example="30.0000",
     *     description="allowance amount in instance currency"
     * )
     *
     */
    protected string $localCurrencyAmount;

    /**
     * @OA\Property(
     *     title="foreign_currency_amount",
     *     property="foreign_currency_amount",
     *     example="6.7560",
     *     description="allowance amount in foreign currency"
     * )
     *
     */
    protected string $foreignCurrencyAmount;

    /**
     * @OA\Property(
     *     property="allowance_lines",
     *     type="array",
     *     minimum=1,
     *     @OA\Items(ref="#/components/schemas/AllowanceLineResource")
     * )
     *
     * @var Collection<AllowanceLineResource>
     */
    protected $allowanceLines;

    public function getIdentifierValue()
    {
        // TODO: Implement getIdentifierValue() method.
    }

    /**
     * @param AllowanceViewObject $entity
     * @return static
     */
    public static function map($entity)
    {
        $resource = new static();
        $resource->accountingAccountId = $entity->getAccountingAccount()->getSlug();
        $resource->currency = CurrencyResource::map($entity->getCurrency());
        $resource->localCurrencyAmount = $entity->getAllowanceLines()->getTotalAmount();
        $resource->foreignCurrencyAmount = $entity->getAllowanceLines()->getTotalAmountInForeignCurrency();
        $resource->allowanceLines = AllowanceLineResource::collection($entity->getAllowanceLines());
        $resource->accountDimensions = $entity->getAccountingDimensions();

        return $resource;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'accounting_account_id' => $this->accountingAccountId,
            'account_dimensions' => SelectedAccountDimensionResource::collection($this->accountDimensions),
            'currency' => $this->currency->toArray(),
            'local_currency_amount' => sprintf('%.4f', $this->localCurrencyAmount),
            'foreign_currency_amount' => sprintf('%.4f', $this->foreignCurrencyAmount),
            'allowance_lines' => $this->allowanceLines,
        ];
    }
}