<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Transformers;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Modules\IntegrationAPI\Priv\Http\V1\Resources\AbstractResource;

final class BasicTransformer extends JsonResource
{
    private const ID = 'id';
    private const TYPE = 'type';
    private const ATTRIBUTES = 'attributes';
    private const INCLUDED = 'included';
    private const DATA = 'data';

    private ?string $wrapper = self::DATA;

    public function __construct($resource)
    {
        parent::__construct($resource);
        self::withoutWrapping();
    }



    /**
     * Transform the resource into an array.
     *
     * @param AbstractResource|AbstractResource[] $request
     * @return array
     */
    public function toArray($request)
    {
        $response = [];
        if ($this->resource instanceof Collection) {
            foreach ($this->resource as $resource) {
                $response[] = $this->itemToArray($resource);
            }
        } else {
            $response = $this->itemToArray($this->resource);
        }

        return $response;
    }

    /**
     * @return array
     */
    private function itemToArray(AbstractResource $resource): array
    {
        $response = [
            self::ID => $resource->getIdentifierValue(),
            self::TYPE => $resource->getType(),
            self::ATTRIBUTES => $resource->toArray(),
        ];

        $response = $this->wrapResponse($response);

        if($resource->hasIncluded()) {
            $included = [];
            foreach ($resource->getIncluded() as $type => $resources) {
                    $included[$type] = (new self(new Collection($resources)))->setWrapper(null)->toArray(new Collection($resources));
            }

            $response[self::INCLUDED] = $included;
        }

        return $response;
    }

    public function setWrapper(?string $wrapper): BasicTransformer
    {
        $this->wrapper = $wrapper;

        return $this;
    }

    private function wrapResponse(array $response): array
    {
        if($this->wrapper === null) {
            return $response;
        }

        return [
            $this->wrapper => $response
        ];
    }
}
