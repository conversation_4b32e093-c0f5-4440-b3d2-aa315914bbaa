<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Requests;

use App\Permission;
use App\Repositories\ProviderRepository;
use App\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Http\FormRequest;
use Modules\Accounting\Pub\Interfaces\BulkUpsertProviderRequestInterface;
use Modules\Accounting\Pub\Interfaces\CreateProviderRequestInterface;
use Modules\IntegrationAPI\Priv\Dtos\UpsertProviderDto;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class BulkUpsertProviderRequest extends FormRequest implements BulkUpsertProviderRequestInterface
{
    protected ProviderRepository $providerRepository;
    private Guard $auth;

    public function __construct(Guard $auth, ProviderRepository $providerRepository)
    {
        $this->providerRepository = $providerRepository;
        $this->auth = $auth;
        parent::__construct();
    }

    public function rules(): array
    {
        return [
            'providers' => ['required', 'array', 'max:' . self::MAX_ELEMENTS],
            'providers.*.id' => ['nullable', 'string'],
            'providers.*.' . CreateProviderRequestInterface::KEY_NAME => ['nullable', 'string'],
            'providers.*.' . CreateProviderRequestInterface::KEY_TAX_ID => ['nullable', 'string'],
            'providers.*.' . CreateProviderRequestInterface::KEY_ADDRESS => ['nullable', 'string'],
            'providers.*.' . CreateProviderRequestInterface::KEY_POST_CODE => ['nullable', 'string'],
            'providers.*.' . CreateProviderRequestInterface::KEY_CITY => ['nullable', 'string'],
            'providers.*.' . CreateProviderRequestInterface::KEY_COUNTRY => ['nullable', 'string'],
            'providers.*.' . CreateProviderRequestInterface::KEY_ERP_ID => ['nullable', 'string'],
            'providers.*.' . CreateProviderRequestInterface::KEY_COMPANY_ID => ['nullable', 'string'],
        ];
    }

    public function authorize(): bool
    {
        /** @var User $user */
        $user = $this->auth->user();

        return $user->hasAbility(Permission::DICTIONARIES_MANAGE);
    }

    public function getProviders(): array
    {
        /** @var User $user */
        $user = $this->auth->user();
        $providers = $this->json('providers');

        $providersToUpsert = [];
        foreach ($providers as $provider) {
            if (!isset($provider['id'])) {
                $providersToUpsert[] = UpsertProviderDto::fromInstanceAndArray($user->instance, $provider);
                continue;
            }
            try {
                $providerModel = $this->providerRepository->findBySlugAndInstanceId(
                    $provider['id'],
                    $user->instance_id
                );

                $providersToUpsert[] = UpsertProviderDto::fromProviderAndArray($providerModel, $provider);
            } catch (NotFoundHttpException $e) {
                $providersToUpsert[] = UpsertProviderDto::fromInstanceAndArray($user->instance, $provider);
            }
        }

        return $providersToUpsert;
    }

}