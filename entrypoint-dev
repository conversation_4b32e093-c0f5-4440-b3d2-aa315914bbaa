#!/bin/sh
set -ex

echo "Starting development entrypoint..."

# Fix Git ownership issue
echo "Fixing Git ownership..."
git config --global --add safe.directory /var/www/html

# Run composer install first
echo "Running composer install..."
composer install --ignore-platform-reqs --no-interaction

# Check if vendor directory exists after composer install
if [ ! -d "vendor" ]; then
    echo "ERROR: Composer install failed - vendor directory not found"
    exit 1
fi

echo "Composer install completed successfully"

# Now run the original entrypoint logic
echo "Running Laravel setup commands..."

# first arg is `-f` or `--some-option`
if [ "${1#-}" != "$1" ]; then
	set -- php-fpm "$@"
fi

if [ "$1" = 'php-fpm' ]; then
    echo "Setting up Laravel application..."
    php artisan config:cache -v
    php artisan route:cache -v
    php artisan view:cache -v
    php artisan filament:optimize-clear
    php artisan optimize -v
    php artisan filament:optimize -v
    php artisan migrate --force
    echo "Laravel setup completed"
fi

echo "Starting PHP-FPM..."
exec "$@"
