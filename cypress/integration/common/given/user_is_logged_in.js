Given('user is logged in as {word}', (type) => {
    cy.fixture('users').then((users) => {
        const baseUrl = Cypress.config('baseUrl');
        const key = Object.keys(users).filter((domain) => baseUrl.includes(domain)).pop();
        const email = users[key][type];

        cy.visit('/login');

        cy.get('input[name="email"]').type(email);
        cy.get('input[name="password"]').type(`${Cypress.env('E2E_USER_PASSWORD')}{enter}`);

        // cy.get(`.profile-dropdown__header-name[data-test="${email}"]`).should('be.visible');
    });
});
