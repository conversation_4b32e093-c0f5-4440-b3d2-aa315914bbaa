// type definitions for Cypress object "cy"
/// <reference types="cypress" />

// type definitions for custom commands like "createDefaultTodos"
/// <reference types="../support" />
import {MENU, TRIP_SECTIONS} from "../common/selectors";

describe('Trip Request', () => {
    beforeEach(() => {
        cy.login(Cypress.env('E2E_USER_GU_EMAIL'));
        cy.visit('/');
    });

    context('Create Trip Request', () => {
        beforeEach(() => {
            cy.intercept('/api/requests?*').as('getRequests');
            cy.intercept('/api/requests/*').as('getRequest');
            cy.get(MENU.TRIP_LIST).click();
            cy.wait('@getRequests')
            cy.get(MENU.TRIP_ADD).click();
            cy.wait('@getRequest')
        });

        context('General tests', () => {
            it('Should contain basic sections', () => {
                cy.get(TRIP_SECTIONS.BASIC_INFO_FORM).should('exist');
                cy.get(TRIP_SECTIONS.TRIP_TIMELINE).should('exist');
                cy.get(TRIP_SECTIONS.INSTALLMENTS).should('exist');
                cy.get(TRIP_SECTIONS.TRIP_SUMMARY).should('exist');
                cy.get(TRIP_SECTIONS.COMMENTS).should('exist');
                cy.get(TRIP_SECTIONS.OTHER_COSTS).should('exist');
            });

            it('Should save request', async () => {
                cy.get('.form-group__label').contains('Koszyk').next().within(() => {
                    cy.get('.icon-edit').click();
                    cy.get('.react-select__dropdown-indicator').click();
                    cy.get('.react-select__menu').contains('1.04 - Zakwaterowanie').click();
                });

                cy.getTestElement('basic-info-form').find('[name="purpose"]').type('My purpose');

                cy.get('.timeline-target-point').fillTargetPoint('Warszawa', 'Warszawa, Polska', '24.08.2021');

                cy.location('pathname').then(pathname => {
                    cy.getTestElement('save-send').should('not.have.class', 'btn--waiting').click();
                    cy.wait('@getRequests');

                    cy.get('.table-accordion__row-link').should('have.attr', 'href', pathname);
                });
            });
        });

        context.only('Timeline elements', () => {
            beforeEach(() => {
                cy.get('.timeline-target-point').as('targetPoint').fillTargetPoint('Warszawa', 'Warszawa, Polska', '24.08.2021');
            });

            it('Should have visible list of available trip elements', () => {
                cy.get('.timeline-empty')
                    .should('be.visible')
                    .find('.timeline-icon')
                    .should('have.length', 7);
            });

            it.only('Create target point', () => {
                cy.get('@targetPoint').addTimelineElement('type');
                // cy.root().fillTargetPoint('a', 'b', 'c', 'd');
            });
        });

    });
});
