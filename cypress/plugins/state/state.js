const {get, set, cloneDeep} = require('lodash');
let state = {};

const getState = (path) => {
    if (!path) {
        return cloneDeep(state);
    }

    return get(cloneDeep(state), path);
};

const setState = (value) => {
    state = {
        ...state,
        ...value,
    }

    return state;
}

const resetState = () => {
    state = {};
}

module.exports = {
    getState,
    setState,
    resetState,
}
