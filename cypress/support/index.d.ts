/// <reference types="cypress" />

declare namespace Cypress {
    interface Chainable<Subject> {
        /**
         * Create several Todo items via UI
         * @example
         * cy.createDefaultTodos()
         */
        clickNTimes(count: number): Chainable<any>
        /**
         * Creates one Todo using UI
         * @example
         * cy.createTodo('new item')
         */
        datepicker(date: string, time?: string): Chainable<any>

        /**
         * Command that injects Axe core library into app html.
         * @example
         *  cy.visit('/')
         *  cy.v()
         */
        autocomplete(search: string, select: string): Chainable<any>


        /**
         * Command that get element by [data-test] attribute
         *
         * @param selector string
         */
        getTestElement(selector: string): Chainable<any>

        /**
         * Command that create and save target point
         *
         * @param searchPhrase
         * @param selectPhrase
         * @param date
         * @param time
         */
        createTargetPoint(searchPhrase: string, selectPhrase: string, date: string, time: string): Chainable<any>

        /**
         * Command that create and save target point
         *
         * @param searchPhrase
         * @param selectPhrase
         * @param date
         * @param time
         */
        fillTargetPoint(searchPhrase: string, selectPhrase: string, date: string, time?: string): Chainable<any>

        /**
         * Command that add new empty element in timeline
         *
         * @param type
         */
        addTimelineElement(type: string): Chainable<any>

        /**
         * Run a11y tests or only a subset of all tests
         * @see https://github.com/avanslaars/cypress-axe
         * @example
         *  cy.checkA11y()
         */
        login(): Chainable<any>
    }
}