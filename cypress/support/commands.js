// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
import autocomplete from "./commands/autocomplete";
import login from "./commands/login";
import datepicker from "./commands/datepicker";
import clickNTimes from "./commands/click-n-times";
import getTestElement from "./commands/get-test-element";
import createTargetPoint from "./commands/create-target-point";
import fillTargetPoint from "./commands/fill-target-point";
import addTimelineElement from "./commands/add-timeline-element";

Cypress.Commands.add('login', login);

Cypress.Commands.add('autocomplete', { prevSubject: 'element' }, autocomplete);
Cypress.Commands.add('datepicker', { prevSubject: 'element' }, datepicker);
Cypress.Commands.add('clickNTimes', { prevSubject: 'element' }, clickNTimes);
Cypress.Commands.add('createTargetPoint', { prevSubject: 'element' }, createTargetPoint);
Cypress.Commands.add('fillTargetPoint', { prevSubject: 'element' }, fillTargetPoint);
Cypress.Commands.add('addTimelineElement', { prevSubject: 'element' }, addTimelineElement);
Cypress.Commands.add('getTestElement', getTestElement);
