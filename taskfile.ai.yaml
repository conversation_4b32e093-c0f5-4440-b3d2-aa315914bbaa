version: '3'

vars:
  DOCKER_COMPOSE: 'docker compose -f docker-compose.ai.all-hands.yaml'

tasks:
  run:
    desc: 'Run a command in the container'
    cmds:
      - docker compose -f docker-compose.ai.all-hands.yaml {{.CLI_ARGS}}

  all-hands:
    desc: Start All Hands AI OpenHands application
    cmds:
      - cp -n .env.ai.example .env.ai || true
      - task ai:run -- up --remove-orphans -d
      - task ai:run -- exec -it ollama ollama run eramax/openhands-lm-32b-v0.1:q4_K_M
      - |
        curl http://localhost:${OLLAMA_PORT}/api/generate -d '{
          "model": "eramax/openhands-lm-32b-v0.1",
          "prompt": "Hello, how are you?"
        }'
      - echo "Open http://localhost:{{.OPENHANDS_PORT}}"

  all-hands:down:
    desc: Stop All Hands AI OpenHands application
    cmds:
      - docker compose -f docker-compose.ai.all-hands.yaml down

  all-hands:logs:
    desc: Show logs from All Hands AI OpenHands application
    cmds:
      - docker compose -f docker-compose.ai.all-hands.yaml logs -f

  all-hands:clean:
    desc: Clean up All Hands AI containers and images
    cmds:
      - docker compose -f docker-compose.ai.all-hands.yaml down --rmi all --volumes --remove-orphans
