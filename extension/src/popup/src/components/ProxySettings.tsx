import React, { useEffect, useState } from "react"
import { ConfigService, CONFIG_KEYS } from "Shared/config"
import { Switch } from "@radix-ui/react-switch"
import { Input } from "@radix-ui/react-input"
import { Label } from "@radix-ui/react-label"
import { Button } from "@radix-ui/react-button"

export const ProxySettings = () => {
  const [useProxy, setUseProxy] = useState(true)
  const [proxyUrl, setProxyUrl] = useState("")
  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState("")

  // Load settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      const useProxyValue = await ConfigService.useProxy()
      const proxyUrlValue = await ConfigService.getProxyUrl()
      
      setUseProxy(useProxyValue)
      setProxyUrl(proxyUrlValue)
    }
    
    loadSettings()
  }, [])

  // Save settings
  const saveSettings = async () => {
    setIsSaving(true)
    setSaveMessage("")
    
    try {
      await ConfigService.setUseProxy(useProxy)
      await ConfigService.setProxyUrl(proxyUrl)
      
      setSaveMessage("Settings saved successfully!")
    } catch (error) {
      console.error("Failed to save settings:", error)
      setSaveMessage("Failed to save settings.")
    } finally {
      setIsSaving(false)
    }
  }

  // Reset to defaults
  const resetToDefaults = async () => {
    setIsSaving(true)
    setSaveMessage("")
    
    try {
      await ConfigService.set(CONFIG_KEYS.USE_PROXY, undefined)
      await ConfigService.set(CONFIG_KEYS.PROXY_URL, undefined)
      
      // Reload values
      const useProxyValue = await ConfigService.useProxy()
      const proxyUrlValue = await ConfigService.getProxyUrl()
      
      setUseProxy(useProxyValue)
      setProxyUrl(proxyUrlValue)
      
      setSaveMessage("Settings reset to defaults.")
    } catch (error) {
      console.error("Failed to reset settings:", error)
      setSaveMessage("Failed to reset settings.")
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="p-4 space-y-4 border rounded-md">
      <h2 className="text-lg font-semibold">HTTP/2 Proxy Settings</h2>
      
      <div className="flex items-center space-x-2">
        <Switch
          id="use-proxy"
          checked={useProxy}
          onCheckedChange={setUseProxy}
          disabled={isSaving}
        />
        <Label htmlFor="use-proxy">
          Use HTTP/2 Proxy for Requests
        </Label>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="proxy-url">
          Proxy URL
        </Label>
        <Input
          id="proxy-url"
          type="text"
          value={proxyUrl}
          onChange={(e) => setProxyUrl(e.target.value)}
          disabled={isSaving || !useProxy}
          className="w-full p-2 border rounded"
        />
      </div>
      
      <div className="flex space-x-2">
        <Button
          onClick={saveSettings}
          disabled={isSaving}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          {isSaving ? "Saving..." : "Save Settings"}
        </Button>
        
        <Button
          onClick={resetToDefaults}
          disabled={isSaving}
          className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
        >
          Reset to Defaults
        </Button>
      </div>
      
      {saveMessage && (
        <div className="p-2 text-sm bg-gray-100 rounded">
          {saveMessage}
        </div>
      )}
    </div>
  )
}