import browser, {Tabs} from "webextension-polyfill";
import Tab = Tabs.Tab;

type ActiveTabResult = {
    tab: Tab | null;
    url: URL | null;
}

export interface ICurrentTab {
    get(): Promise<ActiveTabResult>;
    url(): Promise<URL|null>;
    isSamDomain(toCheckUrl: string | URL): Promise<boolean>;
}

export class CurrentTab implements ICurrentTab {
    private lastTab: ActiveTabResult | null = null;

    async get(): Promise<ActiveTabResult> {
        return new Promise(async (resolve, reject) => {
            const [tab] = await browser.tabs.query({active: true, currentWindow: true});

            if (tab && tab?.url) {
                this.lastTab = {tab, url: new URL(tab.url)};
            }

            resolve(this.lastTab)
        });
    }

    async isSamDomain(toCheckUrl: string | URL): Promise<boolean> {
        const {url} = await this.get();
        if (!url) {
            return false;
        }

        return url.hostname === new URL(toCheckUrl).hostname;
    }

    async url(): Promise<URL|null> {
        const {url} = await this.get();
        return url;
    }
}
