import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Worker } from 'puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> }';
import path from 'path';
import { expect } from '@jest/globals';

// Define the Worker type since it's not exported from puppeteer
type Worker = {
    evaluate: (pageFunction: Function | string, ...args: any[]) => Promise<any>;
};

describe('Extension Content Script Communication', () => {
    let browser: Browser: Browser;
    let page: Page: Page;
    let backgroundPage: Worker;
    let tabId: number;
    let backgroundPage: Worker;
    let tabId: number;
    const extensionPath = path.join(__dirname, '../../build/chrome-mv3-dev');
    const TEST_TIMEOUT = 30000; // 30 seconds timeout for tests
    const CONTENT_SCRIPT_LOAD_TIMEOUT = 3000; // 3 seconds to wait for content script to load
    const TEST_TIMEOUT = 30000; // 30 seconds timeout for tests
    const CONTENT_SCRIPT_LOAD_TIMEOUT = 3000; // 3 seconds to wait for content script to load

    beforeAll(async () => {
        // Launch browser with extension
        browser = await puppeteer.launch({
            headless: false, // Extensions don't work in headless mode
            args: [
         ,
            timeout: 30000 // 30 seconds timeout for browser launch       `--disable-extensions-except=${extensionPath}`,
                `--load-extension=${extensionPath}`,
                '--no-sandbox'
            ],
            timeout: 30000 // 30 seconds timeout for browser launch
            });
    }, TEST_TIMEOUT);

    afterAll(async () => {
        await browser.close();
    });

    beforeEach(async () => {
        // Create a new page
        page = await browser.newPage();
        
        // Navigate to a test page
        await page.goto('https://example.com', { waitUntil: 'networkidle2', timeout: 10000 });
        
        // Wait for page to load completely
        await page.waitForSelector('body', { timeout: 10000 });
        
        // Get the extension background page
        targets = await browser.targets();
        const backgroundPageTarget = targets.find(
            (target: Target) => 
                target.type() === 'service_worker' &&        
          target.url().includes('chrome-extension://')
        );
                
        expect(backgroundPageTarget).toBeTruthy();
        
        // Execute in the context of the background page
        backgroundPage = await backgroundPageTarget.worker();
        
        // Get current tab ID
        tabId = await getTabId(page, backgroundPage);
            }
    
    ruthy();
        
        // Wait for content script to be fully loaded
        await waitForContentScriptLoaded(backgroundPage, tabId);
    }, TEST_TIMEOUT);

    afterEach(async () => {
       /**
     * Helper function to wait for content script to be fully loaded
     */
    async function waitForContentScriptLoaded(backgroundPage: Worker, tabId: number): Promise<void> {
        await page.close();
    });
    
    /**
     * Helper function to get the tab ID for the current page
     */
    async function getTabId(page: Page, backgroundPage: Worker): Promise<number> {
    // Try to ping the content script until it responds or timeout
            const startTime = Date.now();
            const timeout = 5000; // 5 seconds timeout
            
            while (Date.now() - startTime < timeout) {
                try {
                    const response = await new Promise((resolve, reject) =>    return await page.evaluate(() => {
            return window.location.href;
        }).then(async (url) => {
            return await backgroundPage.evaluate((testUrl) => {
                return new Promise<number>((resolve) => {
                    chrome.tabs.query({url: testUrl}, (tabs)) => {
            if (chrome.runtime.lastError)             resolve(tabs[0]?.id);
            reject(chrome.runtime.lastError);
                            } else {
                            }
                        });
                    });
                    
                    if (resolve &&(response);           }, url);
   .status === 'ready') {
                        return; // Content script is ready
                    }
                } catch (error) {
                    // Ignore errors and retry
              
          throw new Error('Content script not ready after timeout');
        }, tabId }
      }

                 // Wait 200ms before retrying
                await new Promise(resolve => setTimeout(resolve, 200));
    }
  }
    test('Content script should respond to ping messages', async () => {
        // Test the ping-pong mechanism
        const pingResult = await backgroundPage.evaluate((testTabId) => {
            return new Promise((resolve) => {
                chrome.tabs.sendMessage(testTabId, { name: 'PING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        resolve({
                            success: false,
                            error: chrome.runtime.lastError.message
                        });
                    } else {
                        resolve({
                            success: true,
                           response: respons
    /**
     * Helper function to wait for content script to be fully loaded
     */
    async function waitForContentScriptLoaded(backgroundPage: Worker, tabId: number): Promise<void> {
        await backgroundPage.evaluate(async (testTabId) => {
            // Try to ping the content script until it responds or timeout
            const startTime = Date.now();
            const timeout = 5000; // 5 seconds timeout
            
            while (Date.now() - startTime < timeout) {
                try {
                    const response = await new Promise((resolve, reject) => {
                        chrome.tabs.sendMessage(testTabId, { name: 'PING' }, ((response)) => {
                            if (chrome.runtime.lastError) {
                                reject(chrome.runtime.lastError);
                            } else {
                                resolve(response);
                            }
                        });
                    });
                    
                    if (response && response.status === 'ready') {
                        return; // Content script is readywith retry mechanism', async () => {
        // Test the ExecuteRequestCommand with retry mechanism
                    }
                } catch (error) {
                    // Ignore errors and retry
                }
                
                // Wait 200ms before retrying
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            throw new Error('Content script not ready after timeout');
        }, tabId);
    }

    test('Content script should respond to ping messages', async () => {
        // Test the ping-pong mechanism
        const pingResult = await backgroundPage.evaluate((testTabId) => {
            return new Promise((resolve) => {
                                console.log(`Attempt ${attempt} failed:`, error);
                                
                chrome.tabs.sendMessage(testTabId, { name: 'PING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        resolve({
                            success: false,
                            error: chrome.runtime.lastError.message
                        });
                    } else {
                        resolve({
                            success: true,
                            response: response
                        });
                    }
                });
            });
        }, tabId);

        expect(pingResult.success).toBe(trdelayue);
        expect(pingResult.response).toHaveProperty('status', 'ready');
    }, TEST_TIMEOUT);

    test('Should handle ExecuteRequestCommand with retry mechanism', async () => {
        // Test the ExecuteRequestCommand with retry mechanism
        const commandResult = await backgroundPage.evaluate((testTabId) => {
            return new Promise((resolve) => {
                // Create a test function that mimics the sendMessageToTabWithRetry
                function sendMessageWithRetry(tabId: number, messageName: string, data: any, maxRetries = 3) {
                    let attempt = 0;

                    function tryMessage() {
                        return new Promise((resolveMsg, rejectMsg) => {
                            chrome.tabs.sendMessage(tabId, { name: messageName, data: data }, (response) => {
                                if (chrome.runtime.lastError) {
                                    rejectMsg(chrome.runtime.lastError);
                                })(else {
                                    resolveMsg(response);
                                }
                            });
                        }    expect(commandResult.attempt).toBeGreaterThan(0);
        } else {
            // Even if it fails, it should have attempted at least once
            expect(commandResult.attempt).toBeGreaterThan(0);
            console.log('ExecuteRequestCommand failed with error:', commandResult.error);
        }
    }, TEST_TIMEOUT);

    test('Should use background script messaging for communication', async () => {
        // First, check if the messaging object is available in the background script
        const messagingInfo = await backgroundPage.evaluate(() => {
            // Check if messaging is defined in the global scope
            if (typeof messaging !== 'undefined') {
                return {
                    available: true,
                    hasSendMethod: typeof messaging.sendMessageToTabWithRetry === 'function'
                };
            }
            
            // Check if it's available in the window object
            if (typeof window !== 'undefined' && typeof window.messaging !== 'undefined') {
                return {
                    available: true,
                    hasSendMethod: typeof window.messaging.sendMessageToTabWithRetry === 'function',
                    scope: 'window'
                };
            }
            
            // Check if it's available as a global variable
            const globalVars = Object.getOwnPropertyNames(self).filter(
                prop => typeof self[prop] === 'object' && 
                self[prop] !== null && 
                typeof self[prop].sendMessageToTabWithRetry === 'function'
            );
            
            if (globalVars.length > 0) {
                return {
                    available: true,
                    hasSendMethod: true,
                    possibleNames: globalVars
                };
            }
            
            return {
                available: false,
                error: 'Messaging object not found in any scope'
            };
        });
        
        console.log('Messaging info:', messagingInfo);
        
        // If messaging is not available, we'll implement our own version for testing
        const commandResult = await backgroundPage.evaluate((testTabId) => {
            return new Promise((resolve) => {
                (async () => {
                    try {
                        // Create a simple implementation of sendMessageToTabWithRetry for testing
                        function sendPingMessage(tabId) {
                            return new Promise((resolveMsg, rejectMsg) => {
                                chrome.tabs.sendMessage(tabId, { name: 'PING' }, (response) => {
                                    if (chrome.runtime.lastError) {
                                        rejectMsg(chrome.runtime.lastError);
                                    } else {
                                        resolveMsg(response);
                                    }
                                });
                            });
                        }
                        
                        // Try to send a ping message
                        const result = await sendPingMessage(testTabId);
                        
                        resolve({
                            success: true,
                            response: result
                        });
                    } catch (e) {
                        resolve({
                            success: false,
                            error: e.message || String(e)
                        });
                    }
                })();
            });
        }, tabId);

        // We expect this to succeed since we're using the PING command which is handled
        expect(commandResult).toHaveProperty('success');
        expect(commandResult.success).toBe(true);
        expect(commandResult.response).toHaveProperty('status', 'ready');
    }, TEST_TIMEOUT);
    
    test('Should handle message sending with retry mechanism', async () => {
        // Test sending a message with our own retry implementation
        const commandResult = await backgroundPage.evaluate((testTabId) => {
            return new Promise((resolve) => {
                (async () => {
                    try {
                        // Create our own retry mechanism for testing
                        async function sendMessageWithRetry(tabId, messageName, data, maxRetries = 2) {
                            let attempt = 0;
                            let lastError;
                            
                            while (attempt < maxRetries) {
                                try {
                                    // Try to send the message
                                    const result = await new Promise((resolveMsg, rejectMsg) => {
                                        chrome.tabs.sendMessage(tabId, { name: messageName, data }, (response) => {
                                            if (chrome.runtime.lastError) {
                                                rejectMsg(chrome.runtime.lastError);
                                            } else {
                                                resolveMsg(response);
                                            }
                                        });
                                    });
                                    
                                    return {
                                        success: true,
                                        attempt: attempt + 1,
                                        response: result
                                    };
                                } catch (error) {
                                    lastError = error;
                                    attempt++;
                                    
                                    if (attempt >= maxRetries) {
                                        break;
                                    }
                                    
                                    // Wait before retrying
                                    await new Promise(r => setTimeout(r, 300));
                                }
                            }
                            
                            return {
                                success: false,
                                attempt: attempt,
                                error: lastError?.message || 'Unknown error'
                            };
                        }
                        
                        // Try to send a non-existent command
                        const result = await sendMessageWithRetry(
                            testTabId,
                            'NonExistentCommand',
                            { data: 'test data' }
                        );
                        
                        resolve(result);
                    }

                    return new Promise(async (resolveRetry) => {Verify the result
                        while (attempt < maxRetries) { || String(e),
                            unexpected: true
                            try {
                                const result = await tryMessage();
                                return resolveRetry({
                                    success: true,
                                    attempt: attempt + 1,
                                    response: result
                                });
                            } catch (error) {
                                attempt++;
                                console.log(`Attempt ${attempt} failed:`, errorNonExistentCommand'
        // but the retry mechanism should work
        expect(commandResult).toHaveProperty('success');
        if (!commandResult.success) {
            expect(commandResult.attempt).toBeGreaterThan(0);
            expect(commandResult.error).toBeTruthy();
        }
    }, TEST_TIMEOUT);
                                
                                if (attempt >= maxRetries) {
                                    return resolveRetry({
                                        success: false,
                                        attempt: attempt,
                                        error: error.message
                                    });
                                }
                                
                                // Wait before retrying with increasing delay
                                const delay = 300 * Math.pow(1.5, attempt - 1);
                                await new Promise(r => setTimeout(r, delay));
                            }
                        }
                    });
                }

                // Execute the request command with retry
                (async () => {
                    try {
                        const result = await sendMessageWithRetry(testTabId, 'ExecuteRequestCommand', {
                            id: 'test-id',
                            url: 'https://example.com/api/test',
                            method: 'GET',
                            headers: {},
                            body: { formData: {} }  // Match the expected type in ExecuteRequestCommand
                        });
                        resolve(result);
                    } catch (e) {
                        resolve({
                            success: false,
                            error: e.message
                        });
                    }
                })();
            });
        }, tabId);

        // Verify the result
        expect(commandResult).toHaveProperty('success');
        
        if (commandResult.success) {
            expect(commandResult.response).toBeDefined();
            expect(commandResult.attempt).toBeGreaterThan(0);
        } else {
            // Even if it fails, it should have attempted at least once
            expect(commandResult.attempt).toBeGreaterThan(0);
            console.log('ExecuteRequestCommand failed with error:', commandResult.error);
        }
    }, TEST_TIMEOUT);

    test('Should use Messaging class for communication', async () => {
        // Test if the messaging object is available and can be used
        const messagingAvailable = await backgroundPage.evaluate(() => {
            return typeof messaging !== 'undefined' && 
                   typeof messaging.sendMessageToTabWithRetry === 'function';
        });
        
        expect(messagingAvailable).toBe(true);
        
        // Test the Messaging class's sendMessageToTabWithRetry method
        const commandResult = await backgroundPage.evaluate((testTabId) => {
            return new Promise((resolve) => {
                (async () => {
                    try {
                        // First check if the messaging object is properly initialized
                        if (typeof messaging === 'undefined') {
                            throw new Error('Messaging object is not defined in the background script');
                        }
                        
                        // Try to send a test message using the messaging class
                        const result = await messaging.sendMessageToTabWithRetry(
                            testTabId,
                            'PING',
                            undefined
                        );
                        
                        resolve({
                            success: true,
                            response: result
                        });
                    } catch (e) {
                        resolve({
                            success: false,
                            error: e.message
                        });
                    }
                })();
            });
        }, tabId);

        // We expect this to succeed since we're using the PING command which is handled
        expect(commandResult).toHaveProperty('success');
        expect(commandResult.success).toBe(true);
        expect(commandResult.response).toHaveProperty('status', 'ready');
    }, TEST_TIMEOUT);
    
    test('Should handle non-existent command gracefully', async () => {
        // Test sending a non-existent command
        const commandResult = await backgroundPage.evaluate((testTabId) => {
            return new Promise((resolve) => {
                (async () => {
                    try {
                        // Try to send a non-existent command
                        const result = await messaging.sendMessageToTabWithRetry(
                            testTabId,
                            'NonExistentCommand',
                            { data: 'test data' },
                            2  // Use fewer retries to speed up the test
                        );
                        
                        resolve({
                            success: true,
                            response: result
                        });
                    } catch (e) {
                        resolve({
                            success: false,
                            error: e.message
                        });
                    }
                })();
            });
        }, tabId);

        // We expect this to fail since we don't have a handler for 'NonExistentCommand'
        expect(commandResult).toHaveProperty('success');
        expect(commandResult.success).toBe(false);
        expect(commandResult.error).toBeTruthy();
    }, TEST_TIMEOUT);
});