apiVersion: batch/v1
kind: CronJob
metadata:
  name: "{{ .Release.Name }}-cleanup-failed-jobs"
  labels:
    app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
    app.kubernetes.io/instance: {{ .Release.Name | quote }}
    app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
    helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
spec:
  schedule: "0 2 * * *" # Runs at 2 AM every day
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    metadata:
      name: "{{ .Release.Name }}-cleanup-failed-jobs"
      labels:
        app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
        app.kubernetes.io/instance: {{ .Release.Name | quote }}
        helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
            app.kubernetes.io/instance: {{ .Release.Name | quote }}
            helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
        spec:
          restartPolicy: Never
          imagePullSecrets:
            - name: nexus-registry
          containers:
            - name: "{{ .Release.Name }}-cleanup-failed-jobs"
              image: "{{ .Values.image.repository }}:php-{{ .Values.image.tag }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              command: ["/bin/sh", "-c"]
              args:
                - |
                  php artisan cleanup:failed-jobs --days=7
              envFrom:
                - configMapRef:
                    name: "{{ .Release.Name }}"
                - secretRef:
                    name: "{{ .Release.Name }}"
