apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ .Release.Name }}-scheduler"
  labels:
    app: "{{ .Release.Name }}-scheduler"
    tier: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: "{{ .Release.Name }}-scheduler"
      tier: backend
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        timestamp: {{ now | quote }} # force re-deploy
      labels:
        app: "{{ .Release.Name }}-scheduler"
        service: mindento-legacy
        tier: backend

    spec:
      imagePullSecrets:
        - name: nexus-registry

      nodeSelector:
        app: {{ .Release.Name }}

      tolerations:
        - key: "app"
          operator: "Equal"
          value: "{{ .Release.Name }}"
          effect: "NoSchedule"

      volumes:
        - name: scripts
          configMap:
            name: "{{ .Release.Name }}-scheduler"
            defaultMode: 0777

        - name: passport
          secret:
            secretName: "{{ .Release.Name }}-passport"

        - name: data
          persistentVolumeClaim:
            claimName: "{{ .Release.Name }}-data"

      containers:
        - name: {{ .Release.Name }}-scheduler
          image: "{{ .Values.image.repository }}:php-{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["sh", "-c"]
          args:
            - /app/scheduler.sh
          envFrom:
            - configMapRef:
                name: "{{ .Release.Name }}-version"
            - configMapRef:
                name: "{{ .Release.Name }}"
            - secretRef:
                name: "{{ .Release.Name }}"
          volumeMounts:
            - name: scripts
              subPath: scheduler.sh
              mountPath: /app/scheduler.sh
            - name: passport
              subPath: oauth-private.key
              mountPath: /var/www/html/storage/oauth-private.key
            - name: passport
              subPath: oauth-public.key
              mountPath: /var/www/html/storage/oauth-public.key
            - name: data
              mountPath: /var/www/html/storage/app

          resources:
            requests:
              cpu: {{ .Values.scheduler.resources.requests.cpu }}
              memory: {{ .Values.scheduler.resources.requests.memory }}
            limits:
              cpu: {{ .Values.scheduler.resources.limits.cpu }}
              memory: {{ .Values.scheduler.resources.limits.memory }}
