image:
  repository: docker.mindento.com/billing
  phpFpm: docker.mindento.com/php-fpm:7.4
  tag: latest
  pullPolicy: IfNotPresent

release_date: ~

application:
  version: "none"
  replicas: 2
  maxReplicas: 5
  resources:
    php:
      requests:
        cpu: 0.7
        memory: 2Gi
      limits:
        cpu: 0.7
        memory: 2Gi
    nginx:
      requests:
        cpu: 0.3
        memory: 300Mi
      limits:
        cpu: 0.3
        memory: 300Mi

horizon:
  replicas: 1
  maxReplicas: 5
  resources:
    requests:
      cpu: 1
      memory: 1.5Gi
    limits:
      cpu: 1
      memory: 1.5Gi
  queues:
    invoice:
      tries: 1
      timeout: 1000
      minProcesses: 1
      maxProcesses: 1
    payments:
      tries: 1
      timeout: 0
      minProcesses: 1
      maxProcesses: 1
    default:
      tries: 3
      timeout: 2000
      minProcesses: 1
      maxProcesses: 5
    searchQueries:
      tries: 2
      timeout: 2000
      minProcesses: 1
      maxProcesses: 10
    external:
      tries: 3
      timeout: 2000
      minProcesses: 1
      maxProcesses: 1
    import:
      tries: 3
      timeout: 3600
      minProcesses: 1
      maxProcesses: 1
    booking:
      tries: 1
      timeout: 0
      minProcesses: 1
      maxProcesses: 10
    outboxSupervisor:
      tries: 1
      timeout: 60
      minProcesses: 1
      maxProcesses: 1
    outbox:
      tries: 1
      timeout: 120
      minProcesses: 1
      maxProcesses: 2
    sync:
      tries: 1
      timeout: 1000
      minProcesses: 1
      maxProcesses: 1

scheduler:
  resources:
    requests:
      cpu: 0.3
      memory: 800Mi
    limits:
      cpu: 0.3
      memory: 800Mi

queueConsumers:
  # Invoice related queues
  invoice_correction:
    enabled: false
    replicas: 1
    connection: redis
    queue: invoice:correction
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  invoice_issue:
    enabled: false
    replicas: 1
    connection: redis
    queue: invoice:issue
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  invoice_schedule:
    enabled: false
    replicas: 1
    connection: redis
    queue: invoice:schedule
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  # Reservation related queues
  reservation_cancel:
    enabled: false
    replicas: 1
    connection: redis
    queue: reservation:cancel
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  reservation_dispatcher:
    enabled: false
    replicas: 1
    connection: redis
    queue: reservation:dispatcher
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  reservation_notifications:
    enabled: false
    replicas: 1
    connection: redis
    queue: reservation:notifications
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  # Payment related queues
  payment_create:
    enabled: false
    replicas: 1
    connection: redis
    queue: payment:create
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  payment_cancel:
    enabled: false
    replicas: 1
    connection: redis
    queue: payment:cancel
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  # Ticket related queues
  reservation_ticket:
    enabled: false
    replicas: 1
    connection: redis
    queue: reservation:ticket
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  # Additional queues from horizon.php
  invoices:
    enabled: false
    replicas: 1
    connection: redis
    queue: invoices
    tries: 3
    timeout: 600
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  payments:
    enabled: false
    replicas: 1
    connection: redis
    queue: payments
    tries: 1
    timeout: 0
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi
  booking_supervisor:
    enabled: false
    replicas: 1
    connection: redis
    queue: booking_supervisor
    tries: 1
    timeout: 0
    resources:
      requests:
        cpu: 0.2
        memory: 500Mi

admin:
  allowedIP: "**************"

dns:
  hostname: 'billing.testing.mindento.com'

persistence:
  data:
    size: 12Gi
  logs_obt:
    size: 10Gi
