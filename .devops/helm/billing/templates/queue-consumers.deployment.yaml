{{- range $name, $config := .Values.queueConsumers }}
{{- if $config.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ $.Release.Name }}-queue-{{ $name }}"
  labels:
    app: "{{ $.Release.Name }}-queue-{{ $name }}"
    tier: backend
spec:
  replicas: {{ $config.replicas }}
  selector:
    matchLabels:
      app: "{{ $.Release.Name }}-queue-{{ $name }}"
      tier: backend
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        timestamp: {{ now | quote }} # force re-deploy
      labels:
        app: "{{ $.Release.Name }}-queue-{{ $name }}"
        service: billing
        tier: backend

    spec:
      imagePullSecrets:
        - name: artifactory-registry
        - name: nexus-registry

      volumes:
        - name: passport
          secret:
            secretName: "{{ $.Release.Name }}-passport"

        - name: data
          persistentVolumeClaim:
            claimName: "{{ $.Release.Name }}-data"

        - name: logs-obt
          persistentVolumeClaim:
            claimName: "{{ $.Release.Name }}-logs-obt"

      nodeSelector:
        app: {{ $.Release.Name }}

      tolerations:
        - key: "app"
          operator: "Equal"
          value: "{{ $.Release.Name }}"
          effect: "NoSchedule"

      containers:
        - name: "{{ $.Release.Name }}-queue-{{ $name }}"
          image: "{{ $.Values.image.repository }}:php-{{ $.Values.image.tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
          command:
            - php
          args:
            - /var/www/html/artisan
            - queue:work
            - {{ $config.connection }}
            - --queue={{ $config.queue }}
            - --tries={{ $config.tries }}
            - --timeout={{ $config.timeout }}
          envFrom:
            - configMapRef:
                name: "{{ $.Release.Name }}-version"
            - configMapRef:
                name: "{{ $.Release.Name }}"
            - secretRef:
                name: "{{ $.Release.Name }}"
          volumeMounts:
            - name: data
              mountPath: /var/www/html/storage/app
            - name: passport
              subPath: oauth-private.key
              mountPath: /var/www/html/storage/oauth-private.key
            - name: passport
              subPath: oauth-public.key
              mountPath: /var/www/html/storage/oauth-public.key
            - name: logs-obt
              mountPath: /var/www/html/storage/logs/obt
          resources:
            requests:
              cpu: {{ $config.resources.requests.cpu }}
              memory: {{ $config.resources.requests.memory }}
            limits:
              cpu: {{ $config.resources.limits.cpu }}
              memory: {{ $config.resources.limits.memory }}
{{- end }}
{{- end }}
