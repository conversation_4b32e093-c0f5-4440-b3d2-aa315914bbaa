apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ .Release.Name }}-import-elastic"
  labels:
    app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
    app.kubernetes.io/instance: {{ .Release.Name | quote }}
    app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
    helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
  annotations:
    "helm.sh/hook": "post-install,post-upgrade"
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  template:
    metadata:
      name: "{{ .Release.Name }}-import-elastic"
      labels:
        app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
        app.kubernetes.io/instance: {{ .Release.Name | quote }}
        helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    spec:
      restartPolicy: Never
      imagePullSecrets:
        - name: artifactory-registry
      containers:
        - name: "{{ .Release.Name }}-import-elastic"
          image: "{{ .Values.image.repository }}:php-{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - |
              php artisan import:searchable
              php artisan trip-planner:spot-searcher:load
          envFrom:
            - configMapRef:
                name: "{{ .Release.Name }}"
            - secretRef:
                name: "{{ .Release.Name }}"
