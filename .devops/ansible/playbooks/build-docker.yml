- name: Build and deploy Docker
  hosts: localhost

  vars:
    docker_registry: "{{ lookup('env', 'DOCKER_REGISTRY_NEXUS') }}"

    user: "{{ lookup('env', 'DOCKER_REGISTRY_USER') }}"
    password: "{{ lookup('env', 'DOCKER_REGISTRY_PASSWORD') }}"

    workdir: "{{ lookup('env', 'WORKDIR') }}"
    dockerfile: "{{ lookup('env', 'DOCKERFILE') }}"
    app_name: "{{ lookup('env', 'APP_NAME') }}"
    target: "{{ lookup('env', 'TARGET') }}"
    tag: "{{ lookup('env', 'APP_TAG') }}"
    force_build: "{{ lookup('env', 'FORCE_BUILD') | default(false)}}"

  tasks:
    - name: Compose tag with target
      ansible.builtin.set_fact:
        tag: "{{ target }}-{{ tag }}"
      when: target

    - name: Login to private docker registry
      shell: "docker login -u {{ user }} -p {{ password }} {{ docker_registry }}"

    - name: "Pull image with tag {{ tag }}"
      community.docker.docker_image:
        name: "{{ docker_registry }}/{{ app_name }}:{{ tag }}"
        source: pull
      ignore_errors: yes
      register: docker

    - name: Build Docker
      community.docker.docker_image:
        build:
          path: "{{ workdir }}"
          dockerfile: "{{ dockerfile }}"
          target: "{{ target }}"
          network: host
          args:
            VERSION: "{{ tag }}"
        name: "{{ app_name }}"
        tag: latest
        push: false
        source: build
      when: docker is failed or force_build

    - name: Deploy with latest tag
      community.docker.docker_image:
        name: "{{ app_name }}:latest"
        repository: "{{ docker_registry }}/{{ app_name }}:latest"
        push: true
        force_tag: true
        source: local
      when: docker is failed or force_build

    - name: "Deploy with {{ tag }} tag"
      community.docker.docker_image:
        name: "{{ app_name }}:latest"
        repository: "{{ docker_registry }}/{{ app_name }}:{{ tag }}"
        push: true
        source: local
      when: docker is failed or force_build
