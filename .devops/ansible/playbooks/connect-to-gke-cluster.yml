- name: Connect to GKE Cluster
  hosts: localhost

  vars:
    project_id: "{{ lookup('env', 'GKE_PROJECT_ID') }}"
    cluster_name: "{{ lookup('env', 'GKE_CLUSTER_NAME') }}"
    zone_id: "{{ lookup('env', 'GKE_ZONE_ID') }}"
    key_path: "{{ lookup('env', 'GKE_SA_KEY') }}"

  tasks:
    - name: "Authenticate to Google Cloud"
      shell: "gcloud auth activate-service-account --key-file={{ key_path }} --project {{ project_id }}"

    - name: "Create ~/.kube/config file"
      shell: "gcloud container clusters get-credentials {{ cluster_name }} --zone {{ zone_id }} --project {{ project_id }}  --internal-ip"
