# Decision Process Graph Visualization

This feature provides an interactive graph visualization for the decision processes stored in your MySQL database. It helps visualize the flow of decision-making logic with nodes, conditions, and transitions.

## Database Structure

The visualization is based on three main tables:

### `decision_processes`
- `id` - Primary key
- `slug` - Unique identifier for the process
- Contains the main decision process definitions

### `decision_process_nodes`
- `id` - Primary key
- `decision_process_id` - Foreign key to decision_processes
- `operator_code` - Logic operator ('and', 'or')
- `decision_code` - The decision to execute
- `decision_parameters` - JSON parameters for the decision
- `order` - Order of execution within the process
- `description` - Human-readable description

### `decision_process_conditions`
- `id` - Primary key
- `decision_process_node_id` - Foreign key to decision_process_nodes
- `code` - Condition code/class name
- `parameters` - JSON parameters for the condition
- `negate` - <PERSON><PERSON><PERSON> to negate the condition result

## How It Works

1. **Process Flow**: Each decision process contains multiple nodes that are executed in order
2. **Node Execution**: For each node, all conditions are evaluated using the specified operator
3. **Flow Control**: If conditions pass, the node's decision is executed and flow breaks
4. **Fallback**: If conditions fail, the flow continues to the next node

## Features

### Interactive Graph
- **Nodes**: Represent decision process nodes with their decision codes
- **Colors**: Different colors group nodes by process
- **Condition Indicators**: Small circles show condition count (red = has conditions, green = no conditions)
- **Arrows**: Show the flow between nodes (next if conditions fail)

### Interactivity
- **Click nodes** to see detailed information including conditions
- **Drag nodes** to rearrange the layout
- **Zoom and pan** to navigate large graphs
- **Process filtering** to view specific processes

### Node Details Panel
When clicking a node, you'll see:
- Decision code and description
- Process information
- Operator type (and/or)
- List of all conditions with parameters
- Negation indicators

## Usage

### Accessing the Graph
1. Navigate to **Reports > Decision Process Graph** in the admin interface
2. Select a specific process from the dropdown or view all processes
3. Use the interactive controls to explore the graph

### API Endpoints

#### Get All Processes
```
GET /api/decision-processes/graph
```

#### Get Specific Process
```
GET /api/decision-processes/graph/{slug}
```

Both endpoints return:
```json
{
  "nodes": [...],
  "edges": [...],
  "processGroups": [...],
  "metadata": {
    "totalProcesses": 5,
    "totalNodes": 15,
    "totalEdges": 10,
    "generatedAt": "2024-01-01T12:00:00Z"
  }
}
```

## Technical Implementation

### Backend
- **Controller**: `DecisionProcessGraphController`
- **Models**: Uses existing DecisionMaker module models
- **API**: RESTful endpoints with JSON responses

### Frontend
- **Framework**: React with TypeScript
- **Visualization**: D3.js for interactive graphs
- **Styling**: SCSS with responsive design
- **Integration**: Added to existing route structure

### Graph Layout
- **Algorithm**: Force-directed layout with D3.js
- **Collision Detection**: Prevents node overlap
- **Responsive**: Adapts to different screen sizes
- **Performance**: Optimized for large graphs

## Permissions

Access to the decision process graph requires:
- Admin or Super Admin role
- The feature is located under the Reports section

## Troubleshooting

### Common Issues

1. **Empty Graph**: Check if decision processes exist in the database
2. **API Errors**: Verify user permissions and authentication
3. **Performance**: Large graphs may take time to render
4. **Mobile**: Use pinch-to-zoom and touch gestures

### Browser Support
- Modern browsers with ES6+ support
- Chrome, Firefox, Safari, Edge
- Mobile browsers supported

## Development

### Adding New Features
1. Backend changes in `DecisionProcessGraphController`
2. Frontend changes in `DecisionProcessGraph` component
3. Update tests in `DecisionProcessGraphControllerTest`

### Customization
- Colors and styling in `DecisionProcessGraph.scss`
- Graph layout parameters in the D3.js configuration
- Node and edge rendering in the React component

## Examples

### Simple Process
A basic acceptor strategy selection with 2 nodes:
1. Check if user has direct supervisor → Use DirectSupervisorStrategy
2. Fallback → Use DefaultStrategy

### Complex Process
Exchange rate selection with multiple conditions:
1. Check document type and date → Use historical rate
2. Check payment method → Use settlement rate
3. Check trip status → Use estimation rate
4. Fallback → Use current NBP rate

## Future Enhancements

Potential improvements:
- Export graph as image/PDF
- Historical view of process changes
- Performance metrics overlay
- Real-time execution tracing
- Process validation tools
