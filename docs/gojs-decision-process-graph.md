# Decision Process Flowchart - GoJS Implementation

## Overview

This document describes the GoJS-based flowchart implementation for visualizing decision processes in the Mindento application. The implementation provides a clear, interactive flowchart that shows exactly how decision processes work with their conditions, Yes/No paths, and execution flow.

**Key Features:**
- **Conditions displayed directly on nodes** - No need to click to see what conditions are being evaluated
- **Clear Yes/No paths** - Green arrows for "YES" (conditions pass), red dashed arrows for "NO" (conditions fail)
- **Traditional flowchart style** - Diamond shapes for decisions, rectangles for actions
- **Single implementation** - Removed D3.js and React Flow, keeping only the best solution

## Architecture

### Components

1. **GoJSDecisionProcessGraph.tsx** - Main and only graph component
2. **DecisionProcessGraphPage.tsx** - Simplified page with process selection
3. **Existing API** - Uses the same `/api/decision-processes/graph` endpoint

### Database Structure

The visualization is based on three main tables:

- `decision_processes` - Contains process definitions with unique slugs
- `decision_process_nodes` - Contains nodes with decision logic, ordered by `order` field
- `decision_process_conditions` - Contains conditions that determine if a node should execute

## Decision Flow Logic

The decision process follows this logic:

1. **Start** - Process begins
2. **Node Evaluation** - Each node is evaluated in order
3. **Condition Check** - If node has conditions:
   - **Conditions Pass** → Execute decision and exit (success path)
   - **Conditions Fail** → Continue to next node (failure path)
4. **Next Node** - If no conditions or conditions fail, try next node
5. **End** - Process completes when a node executes or all nodes are exhausted

## GoJS Implementation Details

### Node Types

1. **Start Nodes** (Green Circles)
   - Entry point for each process
   - One per process

2. **Decision Nodes** (Orange Diamonds)
   - Nodes with conditions
   - Diamond shape indicates decision point

3. **Action Nodes** (Blue Rectangles)
   - Nodes without conditions or execution nodes
   - Rectangular shape for actions

4. **End Nodes** (Red Circles)
   - Terminal nodes for each process
   - One per process

5. **Success Nodes** (Green Rectangles)
   - Virtual nodes representing successful execution
   - Created for nodes with conditions

### Link Types

1. **Flow Links** (Gray Arrows)
   - Standard flow between nodes
   - Default sequential flow

2. **Success Links** (Green Arrows)
   - When conditions pass
   - Bold green arrows with "Conditions pass" label

3. **Failure Links** (Red Dashed Arrows)
   - When conditions fail
   - Dashed red arrows with "If conditions fail" label

### Layout

- Uses GoJS LayeredDigraphLayout
- Vertical flow (top to bottom)
- Automatic node positioning
- Collision avoidance for links

## Features

### Interactive Elements

1. **Node Selection** - Click nodes to see details
2. **Pan and Zoom** - Navigate large diagrams
3. **Hover Effects** - Visual feedback on interaction
4. **Side Panel** - Shows detailed node information

### Node Details Panel

When a node is selected, the side panel shows:
- Decision Code
- Operator Code (and/or logic)
- Execution Order
- Description
- Conditions list with:
  - Condition code
  - Negation status
  - Description

### Process Filtering

- Select specific processes from dropdown
- View all processes or individual ones
- Real-time filtering without page reload

## Data Transformation

The component transforms the API data to GoJS format:

1. **Group by Process** - Nodes are grouped by process slug
2. **Sort by Order** - Nodes within each process are sorted by order
3. **Create Virtual Nodes** - Start, end, and success nodes are generated
4. **Generate Links** - Flow connections are created based on logic
5. **Apply Styling** - Colors and shapes are assigned based on node type

## Usage

### Navigation

1. Choose process from dropdown or view all processes
2. Click nodes to see detailed conditions and logic
3. Use mouse to pan and zoom around the flowchart
4. View detailed node information in the side panel
5. Follow the Yes/No paths to understand decision flow

## Benefits of GoJS Implementation

1. **Performance** - Optimized for large diagrams
2. **Interactivity** - Rich user interactions
3. **Clarity** - Clear visual representation of decision flow
4. **Maintainability** - Clean, structured code
5. **Extensibility** - Easy to add new features

## Future Enhancements

Potential improvements:
1. **Editing Capabilities** - Allow diagram editing
2. **Export Options** - PDF, PNG, SVG export
3. **Animation** - Animated flow visualization
4. **Grouping** - Visual process grouping
5. **Search** - Find specific nodes or conditions
6. **Themes** - Light/dark mode support

## Technical Notes

### Dependencies

- GoJS 3.0.22 - Main diagramming library
- React 18+ - Component framework
- TypeScript - Type safety

### Browser Support

- Modern browsers with ES6+ support
- Chrome, Firefox, Safari, Edge
- Mobile browsers (with touch support)

### Performance

- Handles hundreds of nodes efficiently
- Lazy loading for large datasets
- Optimized rendering pipeline

## Troubleshooting

### Common Issues

1. **Blank Diagram** - Check API endpoint and data format
2. **Layout Issues** - Verify node positioning and layout settings
3. **Missing Links** - Check edge data transformation
4. **Performance** - Consider data pagination for very large processes

### Debug Mode

Enable console logging to debug:
```javascript
console.log('Raw API response:', rawData)
console.log('Transformed data:', transformedData)
```

## Conclusion

The GoJS implementation provides a modern, interactive way to visualize decision processes. It clearly shows the flow logic, conditions, and decision points, making it easier for users to understand and debug complex decision trees.
