FROM php:8.4 AS build

RUN apt-get update && apt-get install -y zip unzip

COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
WORKDIR /var/www/html

COPY . .
RUN composer install --no-dev --ignore-platform-reqs --no-interaction --optimize-autoloader

FROM node:20.11 AS front

WORKDIR /var/www/html

COPY . .
COPY --from=build /var/www/html/vendor /var/www/html/vendor
RUN npm install
RUN npm run build

FROM php:8.4-fpm-alpine AS base

RUN curl -sSLf \
    -o /usr/local/bin/install-php-extensions \
    https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions && \
    chmod +x /usr/local/bin/install-php-extensions

RUN apk update && \
    apk add postgresql-client curl

RUN apk update && \
    install-php-extensions \
    pdo_mysql \
    curl \
    opcache \
    pdo_pgsql \
    pgsql \
    intl \
    zip \
    pcntl \
    bcmath

COPY docker/php-fpm/docker.conf /usr/local/etc/php-fpm.d/docker.conf
COPY docker/php-fpm/php8.3.fpm.conf /usr/local/etc/php-fpm.d/zz-fpm.conf
COPY docker/php-fpm/php8.3.opcache.conf /usr/local/etc/php/php-fpm.d/zz-opcache.ini
COPY docker/php-fpm/php.ini /usr/local/etc/php/conf.d/app.ini
COPY docker/php-fpm/php8.3.opcache.conf /usr/local/etc/php/conf.d/zz-php8.3.opcache.conf

FROM base AS prod

COPY . .
COPY --from=front /var/www/html/public/build /var/www/html/public/build
COPY --from=build /var/www/html/vendor /var/www/html/vendor
RUN chown -R www-data:www-data /var/www/html
RUN mv .env.prod .env && \
    rm .env.*

RUN rm -rf /tmp/* /var/cache/apk/* /var/tmp/* /root/.composer /root/.npm /root/.cache /root/.config

COPY entrypoint-prod /usr/local/bin/entrypoint
RUN chmod +x /usr/local/bin/entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint"]
CMD ["php-fpm"]

# Add healthcheck to verify PHP-FPM is ready
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD php-fpm -t || exit 1

USER www-data
EXPOSE 9000

FROM base AS dev

### Local user in container ###
ARG LOCAL_USER=salesto
ARG USERID=1000
ARG GROUPID=1000
ENV LOCAL_USER=$LOCAL_USER
RUN echo "CURRENT_USER: ${LOCAL_USER}" && \
    echo "GROUPID: ${GROUPID}" && \
    echo "USERID: ${USERID}" && \
    EXISTING_GROUP_NAME=$(getent group ${GROUPID} | cut -d: -f1) && \
    if [ -z "$EXISTING_GROUP_NAME" ]; then \
    addgroup -g ${GROUPID} ${LOCAL_USER} && \
    GROUP_TO_USE=${LOCAL_USER}; \
    else \
    GROUP_TO_USE=${EXISTING_GROUP_NAME}; \
    fi && \
    EXISTING_USER_NAME=$(getent passwd ${USERID} | cut -d: -f1) && \
    echo "EXISTING_USER_NAME: ${EXISTING_USER_NAME}" && \
    echo "GROUP_TO_USE: ${GROUP_TO_USE}" && \
    if [ -n "${EXISTING_USER_NAME}" ]; then deluser ${EXISTING_USER_NAME}; fi && \
    adduser -D -u ${USERID} -G ${GROUP_TO_USE} ${LOCAL_USER};
### /Local user in container ###

RUN install-php-extensions xdebug

COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
COPY docker/php-fpm/php8.3.dev.conf /usr/local/etc/php-fpm.d/zz-dev.conf

# Copy and set up development entrypoint
COPY entrypoint-dev /usr/local/bin/entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint"]
RUN chmod +x /usr/local/bin/entrypoint
CMD ["php-fpm"]

# Add healthcheck to verify composer dependencies are installed and Laravel is ready
HEALTHCHECK --interval=30s --timeout=10s --start-period=180s --retries=5 \
    CMD test -d vendor && php artisan --version || exit 1

RUN apk add --no-cache git sudo && \
    git config --global --add safe.directory /var/www/html

USER $LOCAL_USER
RUN composer global require laravel/installer
ENV PATH="$PATH:$HOME/home/<USER>/.composer/vendor/bin"

WORKDIR /var/www/html

FROM nginx:alpine AS http-base

COPY docker/nginx/nginx.conf /etc/nginx/

RUN apk update \
    && apk upgrade \
    && apk --update add logrotate \
    && apk add --no-cache openssl \
    && apk add --no-cache bash

RUN apk add --no-cache curl

RUN set -x ; \
    addgroup -g 82 -S www-data ; \
    adduser -u 82 -D -S -G www-data www-data && exit 0 ; exit 1

ARG PHP_UPSTREAM_CONTAINER=php
ARG PHP_UPSTREAM_PORT=9000

RUN touch /var/log/messages

COPY docker/nginx/logrotate/nginx /etc/logrotate.d/
COPY docker/nginx/site.conf /etc/nginx/sites-available/site.conf

# Set upstream conf and remove the default conf
RUN echo "upstream php-upstream { server ${PHP_UPSTREAM_CONTAINER}:${PHP_UPSTREAM_PORT}; }" > /etc/nginx/conf.d/upstream.conf \
    && rm /etc/nginx/conf.d/default.conf

ADD docker/nginx/startup.sh /opt/startup.sh
RUN sed -i 's/\r//g' /opt/startup.sh
CMD ["/bin/bash", "/opt/startup.sh"]

FROM http-base AS http-prod

COPY --from=prod /var/www/html/public /var/www/html/public

# Add healthcheck to verify Laravel application is responding
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/up || exit 1

EXPOSE 80 81 443
