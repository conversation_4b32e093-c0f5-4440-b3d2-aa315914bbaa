apiVersion: v1
stringData:
  APP_SECRET: tNoJnsLi9o1KpUy7MZs4VKbgOZhUvRPb
  DATABASE_URL: mysql://testing_mindento:lhVKUhefqs1uwpWg0bHNsMNR0Fhos2Aa@10.99.2.3:3306/testing_mindento?serverVersion=8.0
  MESSENGER_TRANSPORT_DSN: amqp://mindento:YXF41wk3PlKFcuHIonJSMjKLIeLKfMny@rabbitmq
  MINDENTO_SHARED_DATABASE_URL: mysql://testing_mindento_legacy_shared:3aXgvCr7wdTuNsiZdgGatpqynNNnibTjjL3@10.99.2.3:3306/testing_mindento_legacy?serverVersion=8.0
  SENTRY_DSN: https://<EMAIL>/5
  TRAVELGATE_KEY: f7424a0c-a7d6-41b2-54ca-700a21b1bafe
  PLIANT_CLIENT_ID: "PBZK5tAQoxFZ23TJZ7lUx7KqUIA4OpWw"
  PLIANT_CLIENT_SECRET: "IrRtXLSc5BjdvvbX2KQtOGPpXyu4O_010TjgcM1VoQxfwZHonD5a1xdimuCFB7XB"
kind: Secret
metadata:
  name: mindento
  namespace: app-testing
type: Opaque

