<?php

declare(strict_types=1);

namespace Domain\Marketplace\Hotel\Request;

use Domain\Marketplace\Hotel\DTO\IdentifierDTO;

class CancelRequest implements RequestInterface, RequestIdentifiableInterface
{
    private const METHOD = 'POST';

    private const ENDPOINT = '/marketplace/offers/cancel';

    private const LOG_CHANNEL = 'marketplace_requests';
    private IdentifierDTO $requestIdentifier;
    private IdentifierDTO $bookingId;
    private string $supplierCode;
    private string $paymentCardId;


    private function __construct(
        IdentifierDTO $requestIdentifier,
        IdentifierDTO $bookingId,
        string $supplierCode,
        string $paymentCardId
    ) {
        $this->requestIdentifier = $requestIdentifier;
        $this->bookingId = $bookingId;
        $this->supplierCode = $supplierCode;
        $this->paymentCardId = $paymentCardId;
    }

    public static function fromValues(
        IdentifierDTO $requestIdentifier,
        IdentifierDTO $bookingId,
        string $supplierCode,
        string $paymentCardId
    ): self {
        return new self($requestIdentifier, $bookingId, $supplierCode, $paymentCardId);
    }

    public function getMethod(): string
    {
        return self::METHOD;
    }

    public function getEndpoint(): string
    {
        return self::ENDPOINT;
    }

    public function getLogChannel(): string
    {
        return self::LOG_CHANNEL;
    }

    public function getIdentifier(): string
    {
        return $this->requestIdentifier->getId();
    }

    public function toArray(): array
    {
        return [
            'booking_id' => $this->bookingId->getId(),
            'supplier_code' => $this->supplierCode,
            'payment_card_id' => $this->paymentCardId,
        ];
    }
}
