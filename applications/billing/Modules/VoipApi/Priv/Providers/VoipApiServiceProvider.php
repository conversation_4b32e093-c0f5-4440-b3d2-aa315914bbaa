<?php

namespace Modules\VoipApi\Priv\Providers;

use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Factory;
use Modules\IntegrationAPI\Priv\Exceptions\ApiHandler;
use Modules\VoipApi\Priv\Exceptions\VoipApiHandler;
use Modules\VoipApi\Priv\Http\Middleware\CheckMindentoUserIdHeaderMiddleware;

class VoipApiServiceProvider extends ServiceProvider
{
    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    public function boot(Router $router)
    {
        $this->app->singleton(
            ExceptionHandler::class,
            VoipApiHandler::class
        );

        $this->registerTranslations();
        $this->registerConfig();
        $this->registerFactories();
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');

        $router->aliasMiddleware('voipapi.check_mindento_user_id_header', CheckMindentoUserIdHeaderMiddleware::class);
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__.'/../Config/config.php' => config_path('voipapi.php'),
        ], 'config');
        $this->mergeConfigFrom(
            __DIR__.'/../Config/config.php', 'voipapi'
        );
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/voipapi');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'voipapi');
        } else {
            $this->loadTranslationsFrom(__DIR__ .'/../Resources/lang', 'voipapi');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (! app()->environment('production')) {
            app(Factory::class)->load(__DIR__ . '/../Database/factories');
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
