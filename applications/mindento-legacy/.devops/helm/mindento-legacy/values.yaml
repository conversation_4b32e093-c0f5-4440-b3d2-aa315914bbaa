image:
  repository: docker.mindento.com/mindento-legacy
  phpFpm: docker.mindento.com/php-fpm:7.4
  tag: latest
  pullPolicy: Always

application:
  version: "none"
  replicas: 2
  maxReplicas: 5
  resources:
    php:
      requests:
        cpu: 0.7
        memory: 2Gi
      limits:
        cpu: 0.7
        memory: 2Gi

    nginx:
      requests:
        cpu: 0.3
        memory: 300Mi
      limits:
        cpu: 0.3
        memory: 300Mi


horizon:
  replicas: 1
  maxReplicas: 5
  resources:
    requests:
      cpu: 2
      memory: 3Gi
    limits:
      cpu: 2
      memory: 3Gi
  queues:
    default:
      tries: 3
      timeout: 600
      minProcesses: 1
      maxProcesses: 5
    summary:
      tries: 2
      timeout: 120
      minProcesses: 1
      maxProcesses: 10
    scout:
      tries: 3
      timeout: 30
      minProcesses: 1
      maxProcesses: 3
    ocr:
      tries: 1
      timeout: 600
      minProcesses: 1
      maxProcesses: 3
    mail:
      tries: 1
      timeout: 600
      minProcesses: 1
      maxProcesses: 1
    userSync:
      tries: 1
      timeout: 120
      minProcesses: 1
      maxProcesses: 1
    import:
      tries: 1
      timeout: 600
      minProcesses: 1
      maxProcesses: 1
    search:
      tries: 3
      timeout: 600
      minProcesses: 1
      maxProcesses: 5
    reports:
      tries: 1
      timeout: 600
      minProcesses: 1
      maxProcesses: 5
    status_changes:
      tries: 1
      timeout: 30
    users:
      tries: 3
      timeout: 120
      minProcesses: 1
      maxProcesses: 10

scheduler:
  resources:
    requests:
      cpu: 0.3
      memory: 800Mi
    limits:
      cpu: 0.3
      memory: 800Mi

admin:
  allowedIP: "**************"

ingress:
  hostnames:
    - cloud048.testing.mindento.com

  external_hostnames: []

persistence:
  data:
    size: 10Gi
  cache:
    size: 5Gi
