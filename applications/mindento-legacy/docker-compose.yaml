services:
  mindento-legacy.php:
    working_dir: /var/www
    user: ${USER}
    environment:
      PHP_IDE_CONFIG: serverName=mindentolegacy
    build:
      context: .devops/docker/php-fpm
      dockerfile: php7.4.Dockerfile
      target: dev
    volumes:
      - ./applications/mindento-legacy:/var/www
      - ./packages:/var/packages:ro

  mindento-legacy.nginx:
    working_dir: /var/www
    build:
      context: .devops/docker/nginx
      dockerfile: Dockerfile
      target: dev
      args:
        - PHP_UPSTREAM_CONTAINER=mindento-legacy.php
    volumes:
      - ./applications/mindento-legacy/public:/var/www/public/
      - ./.devops/docker/nginx/site.conf:/etc/nginx/sites-available/site.conf
    depends_on:
      - mindento-legacy.php
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindento-legacy-nginx.rule=Host(`vtl.localhost`)"
      - "traefik.http.routers.mindento-legacy-nginx.entrypoints=web"
      - "traefik.http.routers.panel-nginx.rule=Host(`panel.localhost`)"
      - "traefik.http.routers.panel-nginx.entrypoints=web"

  mindento-legacy.front:
    working_dir: /var/www
    build:
      context: .devops/docker/nodejs-builder
      dockerfile: Dockerfile
    volumes:
      - ./applications/mindento-legacy:/var/www
      - ./packages:/var/packages:ro
    command: bash -c "yarn install && yarn front:start"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindento-legacy-front.rule=Host(`vtl.localhost`)"
      - "traefik.http.routers.mindento-legacy-front.entrypoints=front"
      - "traefik.http.services.mindento-legacy-front.loadbalancer.server.port=3000"

  mindento-legacy.keycloak:
    image: docker.mindento.com/keycloak:1.0.0
    command: 'start-dev'
    restart: always
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
      KC_DB: mysql
      KC_DB_URL_HOST: mysql
      KC_DB_USERNAME: root
      KC_DB_PASSWORD: root
      KC_DB_URL_DATABASE: keycloak
      KC_HTTP_RELATIVE_PATH: /oauth
      KC_HOSTNAME_STRICT_HTTPS: 'false'
      KC_HOSTNAME_STRICT: 'false'
      KC_HOSTNAME_STRICT_BACKCHANNEL: 'false'
      KC_PROXY: 'edge'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindento-legacy-keycloak.rule=Host(`keycloak.localhost`)"
      - "traefik.http.routers.mindento-legacy-keycloak.entrypoints=web"
      - "traefik.http.services.mindento-legacy-keycloak.loadbalancer.server.port=8080"

  mindento-legacy.horizon:
    working_dir: /var/www
    restart: always
    user: ${USER}
    build:
      context: .devops/docker/php-fpm
      dockerfile: php7.4.Dockerfile
      target: dev
    volumes:
      - ./applications/mindento-legacy:/var/www
      - ./packages:/var/packages:ro
    command: bash -c "php artisan horizon"

  mindento-legacy.schedule:
    working_dir: /var/www
    restart: always
    user: ${USER}
    build:
      context: .devops/docker/php-fpm
      dockerfile: php7.4.Dockerfile
      target: dev
    volumes:
      - ./applications/mindento-legacy:/var/www
      - ./packages:/var/packages:ro
    command: bash -c "while true; do php artisan schedule:run; sleep 60; done"

  mindento-legacy.composer:
    working_dir: /var/www
    user: ${USER}
    build:
      context: .devops/docker/php-fpm
      dockerfile: php7.4.Dockerfile
      target: dev
    volumes:
      - ./applications/mindento-legacy:/var/www
      - ./packages:/var/packages:ro
    command: bash -c "composer install"

  mindento-legacy.elasticsearch-seed:
    working_dir: /var/www
    user: ${USER}
    build:
      context: .devops/docker/php-fpm
      dockerfile: php7.4.Dockerfile
      target: dev
    volumes:
      - ./applications/mindento-legacy:/var/www
      - ./packages:/var/packages:ro
    command: "bash bin/seed_es.sh"

volumes:
  mindento-legacy.mysql-data: ~
