<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use App\Services\SlugGeneratorService;
use Carbon\Carbon;
use Excel;
use Modules\Accounting\Pub\ViewObjects\AllowanceLineViewObject;
use Modules\Accounting\Pub\ViewObjects\AllowanceViewObject;
use Modules\Accounting\Pub\ViewObjects\CompanyViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentElementViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\ProviderViewObject;
use Modules\Accounting\Pub\ViewObjects\SelectedAccountDimensionViewObject;
use Modules\Accounting\Pub\ViewObjects\SettlementSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\UserViewObject;
use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Priv\Services\Exporters\CLG\CLGExcelExporter;
use Modules\IntegrationFILE\Priv\Services\Exporters\CLG\LineCollection;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;

/**
 * CLG Exporter for SAP integration
 *
 * This class exports financial data in the SAP CLG format.
 * It processes settlement summaries, documents, and allowances,
 * generating a structured Excel file compatible with SAP import requirements.
 */
class CLGExporter implements SingleFileExporterInterface
{
    private const TMP_DIR_PATH = 'tmp/clg';
    private const DATE_FORMAT = 'dmy';
    private const LINE_TYPE_HEADER = 'H';
    private const LINE_TYPE_DOCUMENT = 'D';
<<<<<<< HEAD
    private const ACCOUNT_TYPE_21 = '21';
    private const ACCOUNT_TYPE_31 = '31';

=======
    private const ACCOUNT_TYPE_40 = '40';
    private const ACCOUNT_TYPE_50 = '50';
    private const ACCOUNT_TYPE_21 = '21';
    private const ACCOUNT_TYPE_31 = '31';
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
    private const DEFAULT_CODE = '0001';
    private const HEADER_TYPE_SA = 'SA';
    private const VAT_CODE_XV = 'XV';
    private const INTERNAL_ORDER_CODE = 'WA2';

    private SlugGeneratorService $slugGeneratorService;

    public function __construct(SlugGeneratorService $slugGeneratorService)
    {
        $this->slugGeneratorService = $slugGeneratorService;
    }

    public function processCollection(ClaimSummaryCollection $claimSummaryCollection): ExportFileDtoCollection
    {
        $exportFileDtoCollection = new ExportFileDtoCollection();
        $lines = new LineCollection();

        /** @var SettlementSummaryViewObject $settlementSummaryViewObject */
        foreach ($claimSummaryCollection as $settlementSummaryViewObject) {
            /** @var UserViewObject $employeeViewObject */
            $employeeViewObject = $settlementSummaryViewObject->getEmployee();
            /** @var CompanyViewObject $company */
            $company = $settlementSummaryViewObject->getCompany();
            $companyCode = $company->getCode();

            /** @var DocumentSummaryViewObject $documentSummaryViewObject */
            foreach ($settlementSummaryViewObject->getDocuments() as $documentSummaryViewObject) {
                $lines->push(
                    $this->generateHeaderLine($settlementSummaryViewObject, $documentSummaryViewObject, $companyCode)
                );

                $documentElements = $documentSummaryViewObject->getDocumentElements();
                $firstElement = $documentElements->first();
                $costCenter = $firstElement && $firstElement->getMpk() ? $firstElement->getMpk()->getCode() : '';

                $provider = $documentSummaryViewObject->getProvider();
                /** @var DocumentElementViewObject $documentElementViewObject */
                foreach ($documentSummaryViewObject->getDocumentElements() as $documentElementViewObject) {
                    $lines = $lines->merge(
                        $this->generateDocumentElementLines(
                            $documentElementViewObject,
                            $provider,
                            $costCenter,
                            $documentSummaryViewObject
                        )
                    );
                    if (null === $provider->getEmployee()) {
                        $lines = $lines->merge(
                            $this->generateProviderToEmployeeLines(
                                $provider,
                                $employeeViewObject,
                                $documentElementViewObject,
                                $documentSummaryViewObject
                            )
                        );
                    }
                }
            }
            /** @var AllowanceViewObject $allowance */
            foreach ($settlementSummaryViewObject->getAllowances() as $allowance) {
                $lines->push($this->generateAllowanceHeaderLine($settlementSummaryViewObject, $companyCode, $company));
                /** @var AllowanceLineViewObject $allowanceLine */
                foreach ($allowance->getAllowanceLines() as $allowanceLine) {
                    $lines = $lines->merge(
                        $this->generateAllowanceLines($allowanceLine, $employeeViewObject, $settlementSummaryViewObject)
                    );
                }
            }
        }

        $relativeTmpPath = $this->persist($lines->all());
        $exportFileDtoCollection->push(
            new ExportFileDto(
                sprintf(
                    '%s-%s',
                    'sap-clg',
                    Carbon::now()->format('Y-m-d_H-i-s'),
                ),
                ExportFileTypeEnum::XLSX(),
                $relativeTmpPath,
            ),
        );
        return $exportFileDtoCollection;
    }

    private function generateHeaderLine(
        SettlementSummaryViewObject $settlementSummaryViewObject,
        DocumentSummaryViewObject $documentSummaryViewObject,
        string $companyCode
    ): array {
        $completionDate = $settlementSummaryViewObject->getCompletionDate(
        ) ? $settlementSummaryViewObject->getCompletionDate()->format(self::DATE_FORMAT) : '';
        $exchangeRate = $documentSummaryViewObject->getCurrency()->getExchangeRate();

        return [
            self::LINE_TYPE_HEADER,
            $documentSummaryViewObject->getNumber(),
            $documentSummaryViewObject->getNumber(),
            $documentSummaryViewObject->getIssueDate()->format(self::DATE_FORMAT),
            $completionDate,
            $companyCode,
            $documentSummaryViewObject->getCurrency()->getCode(),
            self::HEADER_TYPE_SA,
            $exchangeRate != 1 ? $exchangeRate : '',
        ];
    }

    private function generateDocumentElementLines(
        DocumentElementViewObject $documentElementViewObject,
        ProviderViewObject $provider,
        string $costCenter,
        DocumentSummaryViewObject $documentSummaryViewObject
    ): array {
        $vatNumber = $documentElementViewObject->getVatNumber()->getCode();
        $netAmountRaw = $documentElementViewObject->getInstanceCurrencyNetAmount();
        $isCorrection = floatval($netAmountRaw) < 0;
        $netAmount = $isCorrection ? ltrim($netAmountRaw, '-') : $netAmountRaw; // Usuwamy minus dla korekt
        $accountingCode = $documentElementViewObject->getAccountingAccount()->getCode() ?? '';
        $providerErpId = $provider->getErpId();
        $issueDate = $documentSummaryViewObject->getIssueDate()->format(self::DATE_FORMAT);

        /** @var SelectedAccountDimensionViewObject $internalOrder */
        $internalOrder = $documentElementViewObject->getAccountingDimensions()->first(
            function (SelectedAccountDimensionViewObject $accountDimensionViewObject) {
                return $accountDimensionViewObject->getAccountDimensionViewObject()->getCode(
                    ) === self::INTERNAL_ORDER_CODE;
            }
        );

        $internalOrderItemValue = null !== $internalOrder ? $internalOrder->getAccountDimensionItemViewObject(
        )->getCode() : '';

        $description = $documentElementViewObject->getDescription() ?? '';

        // Dla faktur korekt
        if ($isCorrection) {
            return [
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_50,
                    $accountingCode,
                    $netAmount,
                    $costCenter,
                    $internalOrderItemValue,
                    $description,
                    $vatNumber,
                    $issueDate
                ),
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_21,
                    $providerErpId,
                    $netAmount,
                    '',
                    '',
                    $description,
                    '',
                    $issueDate
                ),
            ];
        }

        // Dla normalnych faktur
        return [
<<<<<<< HEAD
            [
                self::LINE_TYPE_DOCUMENT,
                self::ACCOUNT_TYPE_21,
=======
            $this->generateDocumentLine(
                self::ACCOUNT_TYPE_40,
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
                $accountingCode,
                $netAmount,
                $costCenter,
                $internalOrderItemValue,
<<<<<<< HEAD
                $documentElementViewObject->getDescription(),
                '',
                $vatNumber,
                '',
                '',
                '',
                $issueDate,
                self::DEFAULT_CODE,
            ],
            [
                self::LINE_TYPE_DOCUMENT,
=======
                $description,
                $vatNumber,
                $issueDate
            ),
            $this->generateDocumentLine(
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
                self::ACCOUNT_TYPE_31,
                $providerErpId,
                $netAmount,
                '',
                '',
                $description,
                '',
<<<<<<< HEAD
                $documentElementViewObject->getDescription(),
                '',
                '',
                '',
                '',
                '',
                $issueDate,
                self::DEFAULT_CODE,
            ],
=======
                $issueDate
            ),
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
        ];
    }

    private function generateProviderToEmployeeLines(
        ProviderViewObject $provider,
        UserViewObject $employeeViewObject,
        DocumentElementViewObject $documentElementViewObject,
        DocumentSummaryViewObject $documentSummaryViewObject
    ): array {
        $netAmountRaw = $documentElementViewObject->getInstanceCurrencyNetAmount();
        $isCorrection = floatval($netAmountRaw) < 0;
        $netAmount = $isCorrection ? ltrim($netAmountRaw, '-') : $netAmountRaw; // Usuwamy minus dla korekt
        $providerErpId = $provider->getErpId();
        $issueDate = $documentSummaryViewObject->getIssueDate()->format(self::DATE_FORMAT);

        $description = $documentElementViewObject->getDescription() ?? '';
        $employeeErpId = $employeeViewObject->getErpId() ?? '';

        // Dla faktur korekt
        if ($isCorrection) {
            return [
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_50,
                    $providerErpId,
                    $netAmount,
                    '',
                    '',
                    $description,
                    '',
                    $issueDate
                ),
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_21,
                    $employeeErpId,
                    $netAmount,
                    '',
                    '',
                    $description,
                    '',
                    $issueDate
                ),
            ];
        }

        // Dla normalnych faktur
        return [
<<<<<<< HEAD
            [
                self::LINE_TYPE_DOCUMENT,
                self::ACCOUNT_TYPE_21,
=======
            $this->generateDocumentLine(
                self::ACCOUNT_TYPE_40,
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
                $providerErpId,
                $netAmount,
                '',
                '',
                $description,
                '',
<<<<<<< HEAD
                $documentElementViewObject->getDescription(),
                '',
                '',
                '',
                '',
                '',
                $issueDate,
                self::DEFAULT_CODE,
            ],
            [
                self::LINE_TYPE_DOCUMENT,
=======
                $issueDate
            ),
            $this->generateDocumentLine(
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
                self::ACCOUNT_TYPE_31,
                $employeeErpId,
                $netAmount,
                '',
                '',
                $description,
                '',
<<<<<<< HEAD
                $documentElementViewObject->getDescription(),
                '',
                '',
                '',
                '',
                '',
                $issueDate,
                self::DEFAULT_CODE,
            ],
=======
                $issueDate
            ),
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
        ];
    }

    private function generateAllowanceHeaderLine(
        SettlementSummaryViewObject $settlementSummaryViewObject,
        string $companyCode,
        CompanyViewObject $company
    ): array {
        $exchangeRate = $company->getInstance()->getCurrency()->getExchangeRate();

        return [
            self::LINE_TYPE_HEADER,
            $settlementSummaryViewObject->getNumber(),
            $settlementSummaryViewObject->getNumber(),
            $settlementSummaryViewObject->getSettledAt()->format(self::DATE_FORMAT),
            $settlementSummaryViewObject->getCompletionDate()->format(self::DATE_FORMAT),
            $companyCode,
            $company->getInstance()->getCurrency()->getCode(),
            self::HEADER_TYPE_SA,
            $exchangeRate != 1 ? $exchangeRate : '',
        ];
    }

    private function generateAllowanceLines(
        AllowanceLineViewObject $allowanceLine,
        UserViewObject $employeeViewObject,
        SettlementSummaryViewObject $settlementSummaryViewObject
    ): array {
        $date = $settlementSummaryViewObject->getSettledAt()->format(self::DATE_FORMAT);
        $amountRaw = $allowanceLine->getAmount();
        $isCorrection = floatval($amountRaw) < 0;
        $amount = $isCorrection ? ltrim($amountRaw, '-') : $amountRaw; // Usuwamy minus dla korekt
        $accountingCode = $allowanceLine->getAccountingAccount()->getCode();
        $mpk = $allowanceLine->getMpk()->getCode();
        $employeeErpId = $employeeViewObject->getErpId();

        // Dla korekt
        if ($isCorrection) {
            return [
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_50,
                    $accountingCode,
                    $amount,
                    $mpk,
                    '',
                    '',
                    self::VAT_CODE_XV,
                    $date
                ),
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_21,
                    $employeeErpId,
                    $amount,
                    '',
                    '',
                    '',
                    '',
                    $date
                ),
            ];
        }

        // Dla normalnych zapisów
        return [
<<<<<<< HEAD
            [
                self::LINE_TYPE_DOCUMENT,
                self::ACCOUNT_TYPE_21,
=======
            $this->generateDocumentLine(
                self::ACCOUNT_TYPE_40,
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
                $accountingCode,
                $amount,
                $mpk,
                '',
                '',
<<<<<<< HEAD
                '',
                'XV',
                '',
                '',
                '',
                $date,
                self::DEFAULT_CODE,
            ],
            [
                self::LINE_TYPE_DOCUMENT,
=======
                self::VAT_CODE_XV,
                $date
            ),
            $this->generateDocumentLine(
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
                self::ACCOUNT_TYPE_31,
                $employeeErpId,
                $amount,
                '',
                '',
                '',
                '',
<<<<<<< HEAD
                '',
                '',
                '',
                '',
                '',
                $date,
                self::DEFAULT_CODE,
            ],
=======
                $date
            ),
        ];
    }

    /**
     * Helper method to generate a document line with common structure
     *
     * @param string $accountType The account type code
     * @param string $accountCode The account code
     * @param string $amount The amount
     * @param string $costCenter The cost center code
     * @param string $internalOrderValue The internal order value
     * @param string|null $description The description
     * @param string $vatNumber The VAT number
     * @param string $date The date
     * @return array The generated document line
     */
    private function generateDocumentLine(
        string $accountType,
        string $accountCode,
        string $amount,
        string $costCenter = '',
        string $internalOrderValue = '',
        ?string $description = '',
        string $vatNumber = '',
        string $date = ''
    ): array {
        return [
            self::LINE_TYPE_DOCUMENT,
            $accountType,
            $accountCode,
            $amount,
            '',
            $costCenter,
            $internalOrderValue,
            $description ?? '',
            '',
            $vatNumber,
            '',
            '',
            '',
            $date,
            self::DEFAULT_CODE,
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15
        ];
    }

    private function persist(array $content): string
    {
        try {
            $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::XLSX());
            $filePath = sprintf(self::TMP_DIR_PATH . '/%s', $fileName);

            // Ensure directory exists
            $dirPath = dirname($filePath);
            if (!is_dir($dirPath)) {
                if (!mkdir($dirPath, 0755, true)) {
                    throw new \RuntimeException("Failed to create directory: {$dirPath}");
                }
            }

<<<<<<< HEAD
        $filePath = sprintf(self::TMP_DIR_PATH . '/%s', $fileName);
        Excel::store(new CLGExcelExporter($content), $filePath);
=======
            Excel::store(new CLGExcelExporter($content), $filePath);
>>>>>>> 97ece2f07d05fe65c3d4e26692db9763977b3a15

            return $filePath;
        } catch (\Exception $e) {
            // Log error
            \Log::error("Failed to persist export file: " . $e->getMessage());
            throw $e;
        }
    }
}
