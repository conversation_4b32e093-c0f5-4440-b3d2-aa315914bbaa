<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Listeners;

use App\IdentityProviders\IdentityProviderFacade;
use App\Notifications\WelcomeUserNotification;
use App\Repositories\UserRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Users\Priv\Events\UserCreatedEvent;
use Modules\Users\Priv\Services\UserService;

class SendNewUserWelcomeMessageListener implements ShouldQueue
{
    /**
     * @var UserService
     */
    protected $userService;

    /** @var UserRepository */
    protected $userRepository;

    protected IdentityProviderFacade $identityProviderFacade;

    public function __construct(
        UserService $userService,
        UserRepository $userRepository,
        IdentityProviderFacade $identityProviderFacade
    ) {
        $this->userService = $userService;
        $this->userRepository = $userRepository;
        $this->identityProviderFacade = $identityProviderFacade;
    }

    public function handle(UserCreatedEvent $event)
    {
        $user = $this->userRepository->getUserBySlug($event->getUser()->slug);
        $pinCode = $this->userService->createNewPinCode($user);

        $user->notify(new WelcomeUserNotification($user->instance->domain, $user->email, '[USE PASSWORD RESET]', $user->full_name, $pinCode, $this->identityProviderFacade->passwordResetUrl($user)),['mail']);
    }
}