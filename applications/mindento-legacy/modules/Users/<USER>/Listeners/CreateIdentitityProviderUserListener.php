<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Listeners;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Mindento\Tracer\Traits\Traceable;
use Modules\Users\Priv\Events\UserCreatedEvent;
use Modules\Users\Priv\Services\UserService;

class CreateIdentitityProviderUserListener implements ShouldQueue
{
    use InteractsWithQueue, Queueable, Traceable;

    protected UserService $userService;

    public function __construct(UserService $userService)
    {
        $this->onQueue('users');
        $this->userService = $userService;
    }

    public function handle(UserCreatedEvent $event): void
    {
        $this->userService->connectWithIdentityProvider($event->getUser());
    }
}
