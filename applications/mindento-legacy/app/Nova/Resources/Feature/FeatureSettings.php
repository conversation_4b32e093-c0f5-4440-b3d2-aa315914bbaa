<?php

declare(strict_types=1);

namespace App\Nova\Resources\Feature;

use App\Nova\Filters\InstanceFilter;
use App\Nova\Resources\Resource;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;

class FeatureSettings extends Resource
{
    public static $model = \Modules\FeatureSwitcher\Priv\Entities\FeatureSetting::class;

    public static $displayInNavigation = false;

    public static $group = 'Feature';

    public static $title = 'slug';

    public static $search = [
        'id',
    ];

    public function fields(Request $request): array
    {
        if ($this->model()) {
            $settings = $this->settingField();
        }

        return [
            ID::make()->sortable(),
            ...[$settings ?? null],
            Textarea::make('Parameters')
                ->nullable()
                ->hideFromIndex()
                ->hideWhenCreating()
                ->rules($this->verifySavingJson())
                ->resolveUsing($this->renderJsonParameter()),

            BelongsTo::make('Feature', 'feature', Feature::class)
                ->readonly($this->readonlyWhenExists()),

            $this->belongsToCompany()->nullable(),
            $this->belongsToInstance()
        ];
    }

    public function cards(Request $request)
    {
        return [];
    }

    public function filters(Request $request)
    {
        return [
            new InstanceFilter()
        ];
    }

    public function lenses(Request $request)
    {
        return [];
    }

    public function actions(Request $request)
    {
        return [];
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }
}
