<?php

declare(strict_types=1);

namespace App\Ocr;

use <PERSON>ento\Shared\InvoiceOcr\Adapter\DisabledOcr;
use Mindento\Shared\InvoiceOcr\Adapter\GeminiOcr;

use Mindento\Shared\InvoiceOcr\Adapter\OpenAIResponsesOcr;
use Mindento\Shared\InvoiceOcr\Port\OcrInterface;
use Psr\Log\LoggerInterface;

class OcrFactory
{
    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    public function make(InstanceOcr $instanceOcr): OcrInterface
    {
        switch ($instanceOcr->driver) {
            case InstanceOcr::DRIVER_OPENAI:
                return $this->makeOpenAIOcr($instanceOcr);
            case InstanceOcr::DRIVER_GEMINI:
                return $this->makeGeminiOcr($instanceOcr);
            case InstanceOcr::DRIVER_DISABLED:
                return $this->makeDisabledOcr();
            default:
                throw new \InvalidArgumentException('Invalid OCR driver');
        }
    }

    private function makeOpenAIOcr(InstanceOcr $instanceOcr): OcrInterface
    {
        return new OpenAIResponsesOcr(
            $this->logger,
            config('ocr.openai.key'),
            $instanceOcr->config[InstanceOcr::CONFIG_OPENAI_MODEL],
            $instanceOcr->config[InstanceOcr::CONFIG_OPENAI_PROMPT]
        );
    }

    private function makeGeminiOcr(InstanceOcr $instanceOcr): OcrInterface
    {
        return new GeminiOcr(
            $this->logger,
            config('ocr.gemini.url'),
            config('ocr.gemini.key'),
            $instanceOcr->config[InstanceOcr::CONFIG_GEMINI_PROMPT],
            $instanceOcr->config[InstanceOcr::CONFIG_GEMINI_MODEL],
            config('ocr.gemini.geminiResponseSchema'),
        );
    }

    private function makeDisabledOcr(): OcrInterface
    {
        return new DisabledOcr();
    }
}
