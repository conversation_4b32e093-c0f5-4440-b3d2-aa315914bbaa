FROM docker-mirror.mindento.com/node:14 as builder

WORKDIR /app

COPY ["./package.json", "./yarn.lock", "./tsconfig*", "./.devops/docker/entrypoint.sh", "/app/"]
COPY src /app/src

RUN yarn install --frozen-lockfile
RUN yarn build
RUN chmod +x /app/entrypoint.sh

FROM docker-mirror.mindento.com/node:14-slim

WORKDIR /app

COPY --from=builder "/app/entrypoint.sh" "/usr/bin/entrypoint.sh"
COPY --from=builder "/app" "/app"

ENV NO_COLOR=true

EXPOSE 3000

ENTRYPOINT ["/usr/bin/entrypoint.sh"]
