FROM node:20 as build

RUN npm install cheerio -g

FROM n8nio/n8n:1.93.0

USER root

# wkhtmltopdf
RUN apk add --no-cache \
    libstdc++ \
    libx11 \
    libxrender \
    libxext \
    libssl3 \
    ca-certificates \
    fontconfig \
    freetype \
    ttf-dejavu \
    ttf-droid \
    ttf-freefont \
    ttf-liberation \
    # more fonts
  && apk add --no-cache --virtual .build-deps \
    msttcorefonts-installer \
  # Install microsoft fonts
  && update-ms-fonts \
  && fc-cache -f \
  # Clean up when done
  && rm -rf /tmp/* \
  && apk del .build-deps

COPY --from=surnet/alpine-wkhtmltopdf:3.19.0-0.12.6-full /bin/wkhtmltopdf /usr/local/bin/wkhtmltopdf
COPY --from=surnet/alpine-wkhtmltopdf:3.19.0-0.12.6-full /bin/wkhtmltoimage /usr/local/bin/wkhtmltoimage
COPY --from=surnet/alpine-wkhtmltopdf:3.19.0-0.12.6-full /bin/libwkhtmltox* /usr/local/bin/
# /wkhtmltopdf

ENV PYTHONUNBUFFERED=1
RUN apk add --update --no-cache python3 py-pip
USER node

COPY --from=build /usr/local/lib/node_modules /usr/local/lib/node_modules

ENV NODE_FUNCTION_ALLOW_EXTERNAL=*
ENV NODE_FUNCTION_ALLOW_BUILTIN=fs,child_process,path,os
