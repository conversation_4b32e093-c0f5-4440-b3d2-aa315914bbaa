### List
GET {{ PLIANT_BASE_URL }}/cards?organizationId={{ ORGANIZATION_ID }}
Content-Type: application/json
Accept: application/json
Pliant-API-Version: {{ PLIANT_API_VERSION }}
Authorization: Bearer {{$auth.token("pliant")}}


### Issue Card
@cardholderId = 4803b4a3-fe97-4202-a833-17316a21720b

POST {{ PLIANT_BASE_URL }}/cards/{{cardholderId}}
Content-Type: application/json
Accept: application/json
Pliant-API-Version: {{ PLIANT_API_VERSION }}
Authorization: Bearer {{$auth.token("pliant")}}

{
  "organizationId": "{{ ORGANIZATION_ID }}",
  "limit": {
    "value": 1000,
    "currency": "EUR"
  },
  "transactionLimit": {
    "value": 1000,
    "currency": "EUR"
  },
  "cardConfig": "PLIANT_VIRTUAL",
  "validityPeriod": 6
}

### Create card
@cardholderId2 = 68c7cbc0-3764-4774-85b5-e4c62c4b2bc3

POST {{ PLIANT_BASE_URL }}/cards/requests
Content-Type: application/json
Accept: application/json
Pliant-API-Version: {{ PLIANT_API_VERSION }}
Authorization: Bearer {{$auth.token("pliant")}}

{
  "cardholderId": "{{ cardholderId2 }}",
  "organizationId": "{{ ORGANIZATION_ID }}",
  "requestedLimit": {
    "value": 1000,
    "currency": "EUR"
  },
  "requestedTransactionLimit": {
    "value": 1000,
    "currency": "EUR"
  },
  "cardConfig": "PLIANT_VIRTUAL"
}


### Details
POST {{ PLIANT_BASE_URL }}/cards/details
Pliant-API-Version: {{ PLIANT_API_VERSION }}
accept: application/json
Content-Type: application/json
Authorization: Bearer {{$auth.token("pliant")}}

{
  "cardIds": [
    "336be325-0950-45f6-bfd1-8f40fcd11660"
  ],
  "pagination": {
    "sortBy": {
      "field": "updatedAt",
      "direction": "ASC"
    }
  }
}

### Card DETAIL - Single
@cardId = 4ceaa5bc-ecbb-49df-b644-1e9c834a1582

GET {{ PLIANT_BASE_URL }}/cards/{{ cardId }}
Content-Type: application/json
Accept: application/json
Pliant-API-Version: {{ PLIANT_API_VERSION }}
Authorization: Bearer {{$auth.token("pliant")}}

### Card LIST
GET {{ PLIANT_BASE_URL }}/cards?organizationId={{ ORGANIZATION_ID }}
Content-Type: application/json
Accept: application/json
Pliant-API-Version: {{ PLIANT_API_VERSION }}
Authorization: Bearer {{$auth.token("pliant")}}


### Generate a test transaction
POST {{ PLIANT_BASE_URL }}/test-data-generator/transactions
Content-Type: application/json
Accept: application/json
Pliant-API-Version: {{ PLIANT_API_VERSION }}
Authorization: Bearer {{$auth.token("pliant")}}

// A cardToken you can find in Details resopnse - request above
{
  "cardToken": "115629930",
  "numberOfTransactions": 1,
  "status": "CONFIRMED",
  "type": "PURCHASE",
  "transactionCategory": "ADVERTISING_AND_MARKETING",
  "amount":{
    "value": -22577,
    "currency": "EUR"
  },
  "fxAmount": {
    "value": -4500,
    "currency": "EUR"
  }
}

### Subscription card webhook
POST {{ PLIANT_BASE_URL }}/cards/subscription
Content-Type: application/json
Accept: application/json
Pliant-API-Version: {{ PLIANT_API_VERSION }}
Authorization: Bearer {{$auth.token("pliant")}}

{
  "eventTypes": [
    "CARD_EXPIRED",
    "CARD_TERMINATED",
    "CARD_CREATED",
    "CARD_ISSUED",
    "CARD_ACTIVATED",
    "CARD_LOCKED",
    "CARD_UNLOCKED",
    "CARD_LIMIT_CHANGED",
    "CARD_DETAILS_CHANGED",
    "CARD_BALANCE_UPDATED",
    "CARD_STATUS_CHANGED",
    "CARD_SHIPPED"
  ],
  "callbackUrl": "https://n8n.mindento.com/webhook/1301a1c0-b3ea-4d40-8a2c-02302c58a2ca"
}
