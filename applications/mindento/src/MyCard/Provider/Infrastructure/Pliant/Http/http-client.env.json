{"pliant-staging-dev": {"ORGANIZATION_ID": "5b4d40e5-ff7c-4f03-a9a4-0f60869f1d9c", "PLIANT_OAUTH_URL": "https://infinnitystaginginternal.eu.auth0.com/oauth/token", "PLIANT_BASE_URL": "https://sandbox.partner-api.getpliant.com/api/", "PLIANT_API_VERSION": "2.1.0", "Security": {"Auth": {"pliant": {"Type": "OAuth2", "Token URL": "{{ PLIANT_OAUTH_URL }}", "Grant Type": "Client Credentials", "Client ID": "{{ PLIANT_CLIENT_ID }}", "Client Secret": "{{ PLIANT_CLIENT_SECRET }}", "Custom Request Parameters": {"audience": "api.staging.v2.infinnitytest.com/api/integration"}}}}}, "pliant-staging-demo": {"ORGANIZATION_ID": "12de2ac0-6a4b-41b3-a4e4-a2b564bacf7a", "PLIANT_OAUTH_URL": "https://infinnitystaginginternal.eu.auth0.com/oauth/token", "PLIANT_BASE_URL": "https://sandbox.partner-api.getpliant.com/api/", "PLIANT_API_VERSION": "2.1.0", "Security": {"Auth": {"pliant": {"Type": "OAuth2", "Token URL": "{{ PLIANT_OAUTH_URL }}", "Grant Type": "Client Credentials", "Client ID": "{{ PLIANT_CLIENT_ID }}", "Client Secret": "{{ PLIANT_CLIENT_SECRET }}", "Custom Request Parameters": {"audience": "api.staging.v2.infinnitytest.com/api/integration"}}}}}, "pliant-staging-prod": {"ORGANIZATION_ID": "df56c46b-7c2d-4aae-95d1-7b87e68bd3c8", "PLIANT_OAUTH_URL": "https://infinnityprodinternal.eu.auth0.com/oauth/token", "PLIANT_BASE_URL": "https://partner-api.getpliant.com/api/", "PLIANT_API_VERSION": "2.1.0", "Security": {"Auth": {"pliant": {"Type": "OAuth2", "Token URL": "{{ PLIANT_OAUTH_URL }}", "Grant Type": "Client Credentials", "Client ID": "{{ PLIANT_CLIENT_ID }}", "Client Secret": "{{ PLIANT_CLIENT_SECRET }}", "Custom Request Parameters": {"audience": "api.getpliant.com/api/integration"}}}}}}