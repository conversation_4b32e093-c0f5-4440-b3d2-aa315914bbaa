<?php

declare(strict_types=1);

namespace MyCard\Cardholder\Infrastructure\Query;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityManagerInterface;
use MyCard\Cardholder\Application\Query\CardAccountingPeriodQuery;
use MyCard\Cardholder\Application\Query\CardAccountingPeriodQueryHandlerInterface;
use MyCard\Cardholder\Application\Query\CardView;
use MyCard\Cardholder\Application\Query\CardViewCollection;
use MyCard\Cardholder\Domain\Card;
use MyCard\Cardholder\Domain\Cardholder;

class DoctrineCardAccountingPeriodQueryHandler implements CardAccountingPeriodQueryHandlerInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    ) {
    }

    public function __invoke(CardAccountingPeriodQuery $query): CardViewCollection
    {
        $qb = $this->entityManager->createQueryBuilder();
        $results = $qb->select('c', 'ch')
            ->from(Card::class, 'c')
            ->join(Cardholder::class, 'ch')
            ->where('c.accountingPeriodDay = :accountingPeriodFrom')
            ->setParameter('accountingPeriodFrom', $query->accountingPeriodDay, Types::INTEGER)
            ->getQuery()
            ->getResult(AbstractQuery::HYDRATE_SCALAR);

        $cards = [];
        foreach ($results as $result) {
            $cards[] = new CardView(
                id: $result['c_id']->toRfc4122(),
                tenantId: $result['ch_tenantId']->toRfc4122(),
                tenantProviderId: $result['ch_tenantProviderId']->toRfc4122(),
                cardholderId: $result['ch_id']->toRfc4122(),
                accountId: $result['c_accountId']->toRfc4122(),
                accountingPeriodDay: $result['c_accountingPeriodDay'],
                referenceId: $result['c_referenceId'],
                limit: $result['c_limit.value'],
                limitCurrency: $result['c_limit.currency'],
                transactionLimit: $result['c_transactionLimit.value'],
                transactionLimitCurrency: $result['c_transactionLimit.currency'],
                cardNumber: $result['c_cardNumber'],
                availableBalance: $result['c_availableBalance.value'],
                availableBalanceCurrency: $result['c_availableBalance.currency'],
                terminatedAt: $result['c_terminatedAt'],
                isLocked: $result['c_isLocked'],
            );
        }

        return new CardViewCollection($cards);
    }
}
