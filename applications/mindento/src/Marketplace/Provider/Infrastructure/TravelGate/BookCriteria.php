<?php

declare(strict_types=1);

namespace Marketplace\Provider\Infrastructure\TravelGate;

use Marketplace\Provider\Application\Model\GuestView;

readonly class BookCriteria
{
    public string $referenceId;

    public function __construct(
        public string $quoteId,
        private array $occupancies,
        public string $supplierCode,
        private string $remarks = '',
        private ?float $amount = null
    ) {
        $this->referenceId = $this->generateUniqueReference($supplierCode);
    }

    public function getOccupanciesAsQueryString(): string
    {
        $occupancies = [];
        $i = 1;
        foreach ($this->occupancies as $occupancy) {
            $room = new \stdClass();
            $room->occupancyRefId = $i;
            $room->paxes = [];

            /** @var GuestView $guest */
            foreach ($occupancy->guests as $guest) {
                $room->paxes[] = $guest;
            }

            $occupancies[] = $room;
            $i++;
        }

        return str_replace(['"occupancyRefId"', '"paxes"', '"name"', '"surname"', '"age"'],
            ['occupancyRefId', 'paxes', 'name', 'surname', "age"],
            json_encode($occupancies, JSON_THROW_ON_ERROR));
    }

    public function getHolderAsQueryString(): string
    {
        if (!isset($this->occupancies[0])) {
            return '{}';
        }

        $holderData = new \stdClass();
        $firstGuest = $this->occupancies[0]->guests[0];
        $holderData->name = $firstGuest->name;
        $holderData->surname = $firstGuest->surname;
        $holderData->contactInfo = new \stdClass();
        $holderData->contactInfo->email = $firstGuest->email ?? '<EMAIL>';

        return str_replace(['"name"', '"surname"', '"contactInfo"', '"email"'],
            ['name', 'surname', 'contactInfo', 'email'],
            json_encode($holderData, JSON_THROW_ON_ERROR));
    }

    public function getPlugins(): string
    {
        $plugins = '';
        if (AccessCodeProvider::AMH->value === (int)$this->supplierCode) {
            if ($this->amount === null) {
                $vccPlugin = '
                {
                    step: REQUEST
                    pluginsType: {
                        type: PRE_STEP
                        name: "genvcc"
                    }
                }';
            } else {
                $vccPlugin = '
                {
                    step: REQUEST
                    pluginsType: {
                        type: PRE_STEP
                        name: "genvcc"
                        parameters: [
                            {
                                key: "amount"
                                value: "' . $this->amount . '"
                            }
                        ]
                    }
                }';
            }
            $plugins .= $vccPlugin;
        }

        return $plugins;
    }

    public function getRemarks(): string
    {
        if (AccessCodeProvider::AMH->value === (int)$this->supplierCode) {
            return 'Charge VCC.Bill to PL1132923883.Send <NAME_EMAIL>';
        }

        return $this->remarks;
    }

    private function generateUniqueReference(string $supplierCode): string
    {
        $totalLength = 18;
        $prefix = 'MND';

        $randomLength = $totalLength - strlen($prefix) - strlen($supplierCode);
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomPart = '';

        for ($i = 0; $i < $randomLength; $i++) {
            $randomPart .= $characters[random_int(0, strlen($characters) - 1)];
        }

        $reference = $prefix . '-' . $randomPart;

        if (!empty($supplierCode)) {
            $reference .= '-' . $supplierCode;
        }

        return $reference;
    }
}