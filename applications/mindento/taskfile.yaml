version: '3'

vars:
  PROJECTS: marketplace mycard
  MINDENTO_DIR: 'applications/mindento'
  APP_NAME: mindento

includes:
  db: taskfile.db.legacy.yaml
  queue: taskfile.queue.yaml

tasks:
  install:
    cmds:
      - task m:db:dump:testing
  db:create:
    cmds:
      - task m:c -- doctrine:database:create --if-not-exists {{.CLI_ARGS}}
  db:drop:
    cmds:
      - task m:c -- doctrine:database:drop --force --if-exists {{.CLI_ARGS}}
  db:migrate:
    cmds:
      - task m:db:create -- {{.CLI_ARGS}}
      - task m:c -- doctrine:migration:migrate --no-interaction -vv --all-or-nothing {{.CLI_ARGS}}
  db:load:
    cmds:
      - task m:c -- doctrine:fixtures:load {{.CLI_ARGS}}
  db:migrate:diff:
    cmds:
      - task m:db:migrate
      - task m:c -- doctrine:migrations:diff --allow-empty-diff
  db:refresh:
    cmds:
      - task m:db:drop
      - task m:db:create
      - task m:db:migrate
  db:refresh:testing:
    cmds:
      - task m:db:drop -- --env=test
      - task m:db:create -- --env=test
      - task m:db:migrate -- --env=test
  db:fixture:testing:
    cmds:
      - task m:db:load -- --env=test
  links:
    silent: true
    cmds:
      - echo "Mindento http://mindento.localhost"
      - echo "Mindento Swagger http://swagger.mindento.localhost"
  exec:
    cmds:
      - task docker:exec -- mindento.php {{.CLI_ARGS}}
  c:
    cmds:
      - task m:exec -- bin/console {{.CLI_ARGS}}
  sh:
    cmds:
      - task docker:exec -- mindento.php sh
  cache:clear:
    cmds:
      - task m:c -- cache:clear
  openapi:
    cmds:
      - task m:c -- nelmio:apidoc:dump -v --format=yaml > {{.MINDENTO_DIR}}/openapi.yaml
  openapi:debug:
    cmds:
      - task m:c -- nelmio:apidoc:dump -vv --format=yaml > {{.MINDENTO_DIR}}/openapi.yaml

  tests:
    cmds:
      - task docker:exec -- mindento.php bin/phpunit

  tests:snapshot:refresh:
    cmds:
      - task docker:exec -- mindento.php bin/phpunit -d --update-snapshots

  tests:units:
    cmds:
      - task docker:exec -- mindento.php bin/phpunit tests/Unit

  rabbit:prod:
    silent: true
    cmds:
      - kubectl config use-context mindento-production
      - echo "User admin"
      - echo "Password $(kubectl --context=mindento-production get secret rabbitmq-password -n app-production  -o jsonpath="{.data.rabbitmq-password}" | base64 --decode)"
      - open http://localhost:15672
      - kubectl port-forward -n app-production rabbitmq-0 15672:15672

  rabbit:dev:
    silent: true
    cmds:
      - kubectl config use-context mindento-developer
      - echo "User admin"
      - echo "Password $(kubectl --context=mindento-developer get secret rabbitmq-password -n app-testing  -o jsonpath="{.data.rabbitmq-password}" | base64 --decode)"
      - open http://localhost:15673
      - kubectl port-forward -n app-testing rabbitmq-0 15673:15672
