version: '3'

tasks:
  dump:testing:
    dotenv: [ '{{.MINDENTO_DIR}}/.env' ]
    vars:
      SOURCE_DB_NAME: 'testing_mindento'
      SOURCE_DB_PASS: $CLOUD_DEVELOPER_DB_PASS
      SOURCE_DB_USER: $CLOUD_DEVELOPER_DB_USER
      SOURCE_DB_HOST: $CLOUD_DEVELOPER_DB_HOST
      MYSQLDUMP_ARGS: '--ignore-table=testing_mindento.messenger_messages --ignore-table=testing_mindento.marketplace_hotel --ignore-table=testing_mindento.mycard_statement__documents --ignore-table=testing_mindento.mycard_statement__matches --ignore-table=testing_mindento.mycard_statement__reconciliations --ignore-table=testing_mindento.mycard_statement__transactions'
    cmds:
      - task toolkit:db:dump SOURCE_DB_NAME={{.SOURCE_DB_NAME}} SOURCE_DB_USER={{.SOURCE_DB_USER}} SOURCE_DB_PASS="{{.SOURCE_DB_PASS}}" SOURCE_DB_HOST={{.SOURCE_DB_HOST}} MYSQLDUMP_ARGS="{{.MYSQLDUMP_ARGS}}"
      - task m:db:import:testing

  import:testing:
    dotenv: ['{{.MINDENTO_DIR}}/.env']
    vars:
      DATABASE: 'testing_mindento'
      FILE: 'testing_mindento_dump'
      APP_DIR: '{{.MINDENTO_DIR}}'
    cmds:
      - task toolkit:db:import DATABASE={{.DATABASE}} FILE={{.FILE}} APP_DIR={{.APP_DIR}}

  dump:production:
    dotenv: ['{{.MINDENTO_DIR}}/.env']
    vars:
      SOURCE_DB_NAME: 'production_mindento'
      SOURCE_DB_PASS: $CLOUD_PRODUCTION_DB_PASS
      SOURCE_DB_USER: $CLOUD_PRODUCTION_DB_USER
      SOURCE_DB_HOST: $CLOUD_PRODUCTION_DB_HOST
      MYSQLDUMP_ARGS: '--ignore-table=production_mindento.messenger_messages --ignore-table=production_mindento.marketplace_hotel --ignore-table=production_mindento.mycard_statement__documents --ignore-table=production_mindento.mycard_statement__matches --ignore-table=production_mindento.mycard_statement__reconciliations --ignore-table=production_mindento.mycard_statement__transactions'
    cmds:
      - task toolkit:db:dump SOURCE_DB_NAME={{.SOURCE_DB_NAME}} SOURCE_DB_USER={{.SOURCE_DB_USER}} SOURCE_DB_PASS="{{.SOURCE_DB_PASS}}" SOURCE_DB_HOST={{.SOURCE_DB_HOST}} MYSQLDUMP_ARGS="{{.MYSQLDUMP_ARGS}}"
      - task toolkit:sed -- "s/DB_DATABASE=.*/DB_DATABASE={{.DATABASE}}/g" {{.APP_DIR}}/.env
      - task m:db:import:production

  import:production:
    dotenv: ['{{.MINDENTO_DIR}}/.env']
    vars:
      DATABASE: 'production_mindento'
      FILE: 'production_mindento_dump'
      APP_DIR: '{{.MINDENTO_DIR}}'
    cmds:
      - task toolkit:db:import DATABASE={{.DATABASE}} FILE={{.FILE}} APP_DIR={{.APP_DIR}}
