<?php

declare(strict_types=1);

namespace Tests\Unit\MyCard\Statement\Domain;

use MyCard\Statement\Domain\Event\ReconciliationWasOccurred;
use PHPUnit\Framework\TestCase;
use Tests\Helper\MyCard\ObjectMother\OwnerMother;
use Tests\Helper\SnapshotTrait;

class OwnerAutoReconcileTest extends TestCase
{
    use SnapshotTrait;

    public function test_should_auto_reconcile_zero_value_transaction(): void
    {
        // Arrange
        $owner = OwnerMother::aOwner();
        $cardId = 'd424ba2a-ee99-4670-b28d-4b400b67fe76';
        $transactionId = '5a7d1e01-5691-4fdb-ab43-d523534ad2e6';
        $transactionDate = $this->date('2021-01-01');
        
        $owner->newStatement(cardId: $cardId, to: $transactionDate);
        
        // Act
        $owner->autoReconcileZeroValueTransaction(
            cardId: $cardId,
            transactionId: $transactionId,
            transactionDate: $transactionDate
        );
        
        // Assert
        $events = $owner->findEventsByName(ReconciliationWasOccurred::class);
        $this->assertCount(1, $events);
        
        /** @var ReconciliationWasOccurred $event */
        $event = $events[0];
        $this->assertEquals($transactionId, $event->transactionId);
        $this->assertNull($event->documentId, 'Zero-value auto-reconciliation should not have a document');
    }

    public function test_should_remove_existing_reconciliation_before_auto_reconciling(): void
    {
        // Arrange
        $owner = OwnerMother::aOwner();
        $cardId = 'd424ba2a-ee99-4670-b28d-4b400b67fe76';
        $transactionId = '5a7d1e01-5691-4fdb-ab43-d523534ad2e6';
        $documentId = 'c97234a2-09bf-4b0e-9e9c-3d1d7b2e4b4c';
        $transactionDate = $this->date('2021-01-01');
        
        $owner->newStatement(cardId: $cardId, to: $transactionDate);
        
        // First, create a regular reconciliation
        $owner->reconcile(
            cardId: $cardId,
            transactionId: $transactionId,
            transactionDate: $transactionDate,
            documentId: $documentId,
            documentDate: $transactionDate
        );
        
        $this->assertCount(1, $owner->findEventsByName(ReconciliationWasOccurred::class));
        
        // Act - Auto-reconcile the same transaction (should replace the existing one)
        $owner->autoReconcileZeroValueTransaction(
            cardId: $cardId,
            transactionId: $transactionId,
            transactionDate: $transactionDate
        );
        
        // Assert
        $events = $owner->findEventsByName(ReconciliationWasOccurred::class);
        $this->assertCount(2, $events); // One for original reconciliation, one for auto-reconciliation
        
        /** @var ReconciliationWasOccurred $autoReconcileEvent */
        $autoReconcileEvent = $events[1];
        $this->assertEquals($transactionId, $autoReconcileEvent->transactionId);
        $this->assertNull($autoReconcileEvent->documentId, 'Auto-reconciliation should not have a document');
    }

    private function date(string $date): \DateTimeImmutable
    {
        return \DateTimeImmutable::createFromFormat('Y-m-d', $date)
            ->setTime(23, 59, 59);
    }
}