<?php

declare(strict_types=1);

namespace Tests\Integration\Shared\CQRS;

use Shared\CQRS\Application\AsyncCommandInterface;
use Shared\CQRS\Application\CommandBusInterface;
use Shared\CQRS\Application\CommandInterface;
use <PERSON>ymfony\Component\Messenger\MessageBusInterface;
use S<PERSON>fony\Component\Messenger\Stamp\ReceivedStamp;
use Symfony\Component\Messenger\Transport\TransportInterface;
use Tests\Integration\IntegrationTestCaseAbstract;

final class CommandBusDispatchTest extends IntegrationTestCaseAbstract
{
    private CommandBusInterface $commandBus;
    private MessageBusInterface $messageBus;
    private ?TransportInterface $commandsTransport;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->commandBus = self::getContainer()->get(CommandBusInterface::class);
        $this->messageBus = self::getContainer()->get('command.bus');
        
        // Try to get the commands transport if it exists
        try {
            $this->commandsTransport = self::getContainer()->get('messenger.transport.commands');
        } catch (\Exception $e) {
            $this->commandsTransport = null;
        }
    }

    public function test_command_with_only_command_interface_is_handled_synchronously(): void
    {
        // Arrange
        $command = new SynchronousTestCommand();
        $handler = new SynchronousTestCommandHandler();
        
        // Register the handler in the container
        self::getContainer()->set(SynchronousTestCommandHandler::class, $handler);
        
        // Act
        $this->commandBus->dispatch($command);
        
        // Assert
        $this->assertEquals(1, $handler->callCount, 'The synchronous command handler should be called exactly once');
        
        // If we have access to the async transport, verify no messages were sent there
        if ($this->commandsTransport) {
            $this->assertCount(0, $this->getMessagesFromTransport(), 'No messages should be in the async transport');
        }
    }

    public function test_command_with_only_async_interface_is_sent_to_transport(): void
    {
        // Skip if we don't have access to the transport
        if (!$this->commandsTransport) {
            $this->markTestSkipped('Cannot test async commands without access to the transport');
        }
        
        // Arrange
        $command = new AsynchronousTestCommand();
        $handler = new AsynchronousTestCommandHandler();
        
        // Register the handler in the container
        self::getContainer()->set(AsynchronousTestCommandHandler::class, $handler);
        
        // Clear the transport
        $this->clearTransport();
        
        // Act
        $this->commandBus->dispatch($command);
        
        // Assert
        $this->assertEquals(0, $handler->callCount, 'The async command handler should not be called synchronously');
        
        // Check that the message was sent to the transport
        $messages = $this->getMessagesFromTransport();
        $this->assertCount(1, $messages, 'One message should be in the async transport');
        
        // Process the message from the transport to verify it works
        $this->processMessages();
        
        // Now the handler should have been called
        $this->assertEquals(1, $handler->callCount, 'The async command handler should be called after processing the queue');
    }

    public function test_command_with_both_interfaces_is_handled_only_once(): void
    {
        // Arrange
        $command = new DualInterfaceTestCommand();
        $handler = new DualInterfaceTestCommandHandler();
        
        // Register the handler in the container
        self::getContainer()->set(DualInterfaceTestCommandHandler::class, $handler);
        
        // Clear the transport if available
        if ($this->commandsTransport) {
            $this->clearTransport();
        }
        
        // Act
        $this->commandBus->dispatch($command);
        
        // Assert - check if it was handled synchronously or asynchronously, but not both
        if ($handler->callCount === 1) {
            // If it was handled synchronously, make sure it wasn't also sent to the transport
            if ($this->commandsTransport) {
                $messages = $this->getMessagesFromTransport();
                $this->assertCount(0, $messages, 'Command was handled synchronously, so no messages should be in the async transport');
            }
        } else {
            // If it wasn't handled synchronously, it should be in the transport
            if ($this->commandsTransport) {
                $messages = $this->getMessagesFromTransport();
                $this->assertCount(1, $messages, 'Command was not handled synchronously, so it should be in the async transport');
                
                // Process the message from the transport
                $this->processMessages();
                
                // Now the handler should have been called exactly once
                $this->assertEquals(1, $handler->callCount, 'The handler should be called exactly once after processing the queue');
            } else {
                $this->fail('Command was neither handled synchronously nor sent to a transport');
            }
        }
    }

    /**
     * Get all messages from the commands transport
     */
    private function getMessagesFromTransport(): array
    {
        if (!$this->commandsTransport) {
            return [];
        }
        
        return $this->commandsTransport->get();
    }
    
    /**
     * Clear all messages from the commands transport
     */
    private function clearTransport(): void
    {
        if (!$this->commandsTransport) {
            return;
        }
        
        foreach ($this->getMessagesFromTransport() as $envelope) {
            $this->commandsTransport->reject($envelope);
        }
    }
    
    /**
     * Process messages from the transport
     */
    private function processMessages(): void
    {
        if (!$this->commandsTransport) {
            return;
        }
        
        foreach ($this->getMessagesFromTransport() as $envelope) {
            // Add a ReceivedStamp to the envelope to indicate it was received from the transport
            $envelope = $envelope->with(new ReceivedStamp('commands'));
            
            // Dispatch the message to the message bus
            $this->messageBus->dispatch($envelope);
            
            // Acknowledge the message in the transport
            $this->commandsTransport->ack($envelope);
        }
    }
}

/**
 * Test command that implements only CommandInterface
 */
class SynchronousTestCommand implements CommandInterface
{
}

/**
 * Handler for SynchronousTestCommand
 */
class SynchronousTestCommandHandler
{
    public int $callCount = 0;
    
    public function __invoke(SynchronousTestCommand $command): void
    {
        $this->callCount++;
    }
}

/**
 * Test command that implements only AsyncCommandInterface
 */
class AsynchronousTestCommand implements AsyncCommandInterface
{
}

/**
 * Handler for AsynchronousTestCommand
 */
class AsynchronousTestCommandHandler
{
    public int $callCount = 0;
    
    public function __invoke(AsynchronousTestCommand $command): void
    {
        $this->callCount++;
    }
}
