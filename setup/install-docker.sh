#!/bin/bash

# install-docker.sh - Docker installation script
# This script handles Docker and Docker Compose installation across different platforms

# Source the toolkit
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/toolkit.sh"

# Function to install Docker on macOS
install_docker_macos() {
    log_info "Installing Docker on macOS..."

    if command_exists brew; then
        brew install --cask docker
        log_warning "Please start Docker Desktop manually and then re-run the setup script"
        exit 1
    else
        log_error "Please install Docker Desktop manually from https://www.docker.com/products/docker-desktop"
        exit 1
    fi
}

# Function to install Docker on Linux
install_docker_linux() {
    log_info "Installing Docker on Linux..."

    # Download and run the official Docker installation script
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    rm get-docker.sh

    # Add current user to docker group if sudo is available and not in container
    if has_sudo && ! is_container; then
        run_with_sudo usermod -aG docker $USER
        log_warning "Please log out and log back in for Docker group changes to take effect, then re-run this script"
        exit 1
    fi
}

# Function to install Docker Compose plugin on Linux
install_docker_compose_linux() {
    log_info "Installing Docker Compose plugin on Linux..."

    DOCKER_CONFIG=${DOCKER_CONFIG:-$HOME/.docker}
    mkdir -p "$DOCKER_CONFIG/cli-plugins"

    if curl -SL https://github.com/docker/compose/releases/download/v2.24.1/docker-compose-linux-x86_64 -o "$DOCKER_CONFIG/cli-plugins/docker-compose"; then
        chmod +x "$DOCKER_CONFIG/cli-plugins/docker-compose"
        log_success "Docker Compose plugin installed successfully"
    else
        log_error "Failed to download Docker Compose plugin"
        return 1
    fi
}

# Function to check if Docker is installed
check_docker_installed() {
    if command_exists docker; then
        log_success "Docker is already installed"
        return 0
    else
        log_warning "Docker not found"
        return 1
    fi
}

# Function to check if we're in a container environment where Docker installation should be skipped
should_skip_docker_install() {
    log_info "Checking container environment..."
    if [ -f /.dockerenv ]; then
        log_info "Found /.dockerenv file"
    else
        log_info "/.dockerenv file not found"
    fi

    if grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null; then
        log_info "Found docker/lxc in /proc/1/cgroup"
    else
        log_info "No docker/lxc found in /proc/1/cgroup"
    fi

    if is_container; then
        log_info "Running in container environment - skipping Docker installation"
        log_info "Docker access should be provided via volume mount or base image"
        return 0
    fi
    log_info "Not in container environment - proceeding with Docker installation"
    return 1
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if docker compose version >/dev/null 2>&1; then
        log_success "Docker Compose plugin is already installed"
        return 0
    else
        log_warning "Docker Compose plugin not found"
        return 1
    fi
}

# Function to install Docker based on OS
install_docker() {
    # Skip installation if in container
    if is_container; then
        log_info "Container environment detected - skipping Docker installation"
        return 0
    fi

    local os=$(detect_os)

    case $os in
        "macos")
            install_docker_macos
            ;;
        "linux")
            install_docker_linux
            ;;
        *)
            log_error "Unsupported operating system: $OSTYPE"
            exit 1
            ;;
    esac
}

# Function to install Docker Compose
install_docker_compose() {
    # Skip installation if in container
    if is_container; then
        log_info "Container environment detected - skipping Docker Compose installation"
        return 0
    fi

    local os=$(detect_os)

    case $os in
        "macos")
            # On macOS with Docker Desktop, compose plugin is included
            log_success "Docker Compose plugin is included with Docker Desktop"
            ;;
        "linux")
            install_docker_compose_linux
            ;;
        *)
            log_error "Unsupported operating system: $OSTYPE"
            exit 1
            ;;
    esac
}

# Function to verify Docker installation
verify_docker() {
    log_info "Verifying Docker installation..."

    # Check if Docker command exists
    if ! verify_command "docker" "Docker"; then
        return 1
    fi

    # Skip Docker daemon check in container environments
    if is_container; then
        log_info "Container environment detected - skipping Docker daemon check"
        log_info "Docker daemon access will be provided via volume mount at runtime"
    else
        # Check if Docker is running
        if ! is_docker_running; then
            log_error "Docker is not running. Please start Docker and re-run this script"
            return 1
        fi
    fi

    # Check Docker Compose plugin (skip in container environments)
    if is_container; then
        log_info "Skipping Docker Compose verification in container environment"
    else
        if ! docker compose version >/dev/null 2>&1; then
            log_error "Docker Compose plugin is not available"
            return 1
        fi
    fi

    log_success "Docker installation verified successfully"
    return 0
}

# Main function for Docker setup
setup_docker() {
    show_header "Docker Setup" "Installing and configuring Docker"

    # Skip Docker installation if running in container
    if should_skip_docker_install; then
        log_success "Skipping Docker installation in container environment"
        show_completion "Docker Setup"
        return 0
    fi

    # Check and install Docker
    if ! check_docker_installed; then
        install_docker
    fi

    # Check and install Docker Compose
    if ! check_docker_compose; then
        install_docker_compose
    fi

    # Verify installation
    if ! verify_docker; then
        log_error "Docker setup failed"
        exit 1
    fi

    show_completion "Docker Setup"
}

setup_docker "$@"
