stages:
  - test
  - build
  - docker
  - deploy
  - elasticsearch
  - post deploy

include:
  - project: mindento/mindento
    file:
      - .devops/gitlab/general-template.yml
      - .devops/gitlab/docker-template.yml
      - .devops/gitlab/kubernetes-template.yml
  - local: .devops/gitlab/kubernetes.yml

variables:
  CHANGES_IN_DIR: "**/*"
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  K8S_AUTH_KUBECONFIG: $KUBECONFIG
  WORKDIR: $CI_PROJECT_DIR


.test-e2e:
  image: cypress/browsers:node14.19.0-chrome100-ff99-edge
  stage: post deploy
  when: manual
  before_script:
    - yarn install --frozen-lock --cache-folder .yarn
  script:
    - npx cypress run --browser chrome
  after_script:
    - yarn e2e:report
  artifacts:
    name: "$CI_JOB_NAME-$CI_COMMIT_REF_NAME"
    when: always
    paths:
      - cypress/videos/**/*.mp4
      - cypress/screenshots/**/*.png
      - cypress/cucumber-report/
    expire_in: 7 days
  cache:
    key: $CI_PROJECT_ID
    policy: pull
    paths:
      - .yarn
      - node_modules

test/phpunit:
  stage: test
  when: manual # todo - fix db access from php
  image: registry.gitlab.com/mindento/application/mindento-web/web:0.0.4
  services:
    - name: registry.gitlab.com/mindento/application/mindento-web/db:0.0.1
      alias: mariadb
    - name: bitnami/redis:latest
      alias: redis
  except:
    - schedules
  script:
    - echo "127.0.0.1 mindento-web.testing" >> /etc/hosts
    - pwd
    - cat /etc/hosts
    - cat $ENV_TESTING_FILE
    - cp $ENV_TESTING_FILE .env
    - cp $ENV_TESTING_FILE .env.testing
    - ls -la
    - cp storage.example storage -a
    - chmod -R 777 storage/
    - composer install
    - phpunit
  after_script:
    - cat storage/logs/laravel-*.log


ui/unit-test:
  image: registry.gitlab.com/mindento/internal/docker/nodejs-builder:1.0.0
  stage: test
  when: manual
  only:
    - testing
    - master
  except:
    - schedules
  before_script:
    - |
      # Sprawdzenie czy doszło do zmian w plikach .js, katalogu resources/ lub pliku yarn.json
      if git diff $CI_COMMIT_BEFORE_SHA $CI_COMMIT_SHA --name-only | grep -E 'resources/|yarn.json'; then
          export TEST_REQUIRED=true
      fi
  script:
    - |
      if [ "$TEST_REQUIRED" == "true" ]; then
        cp .env.production .env
        yarn install --frozen-lock --cache-folder .yarn
        yarn run test:ci
      else
        echo "No changes detected in .js files, resources/ or yarn.json. Skipping tests."
      fi
  cache:
    key: $CI_PROJECT_ID
    policy: pull-push
    paths:
      - .yarn
      - node_modules

automation/update-jira:
  stage: post deploy
  image: registry.gitlab.com/mindento/internal/docker/jira-automation:latest
  only:
    - testing
  needs:
    - "[TESTING] Deploy Mindento Legacy"
  script:
    - sh /entrypoint.sh

test/e2e-testing:
  extends: .test-e2e
  variables:
    CYPRESS_BASE_URL: https://testing-048.mindento.com
  only:
    - testing
  except:
    - schedules
  needs:
    - "[TESTING] Deploy Mindento Legacy"

