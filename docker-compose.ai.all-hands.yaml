version: '3.8'

# https://deepwiki.com/search/how-run-a-docker-service-in-do_06324ae9-3d9c-4d7b-a588-aef2455acffa
services:
  openhands:
    image: docker.all-hands.dev/all-hands-ai/openhands:0.39
    container_name: openhands-app
    pull_policy: always
    ports:
      - "${OPENHANDS_PORT}:3000"
    env_file:
      - .env.ai
    environment:
      - SANDBOX_RUNTIME_CONTAINER_IMAGE=${SANDBOX_RUNTIME_CONTAINER_IMAGE:-docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik}
      - LOG_ALL_EVENTS=${LOG_ALL_EVENTS:-true}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ~/.openhands-state:/.openhands-state
    extra_hosts:
      - "host.docker.internal:host-gateway"
    stdin_open: true
    tty: true

  ollama:
    image: ollama/ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
    entrypoint: ["/bin/ollama"]
    command: ["run", "eramax/openhands-lm-32b-v0.1"]

volumes:
  ollama_data:
