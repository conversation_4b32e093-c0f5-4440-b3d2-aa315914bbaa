<?php

declare(strict_types=1);

namespace <PERSON>ento\Shared;

use Illuminate\Support\ServiceProvider;
use Mindento\Shared\Img2pdf\Adapter\ImagicImg2Pdf;
use Mindento\Shared\Img2pdf\Port\Img2pdfInterface;
use Mindento\Shared\PdfMerge\Adapter\GhostscriptPdfMerge;
use Mindento\Shared\PdfMerge\Port\PdfMergeInterface;
use Mindento\Shared\PdfOptimize\Adapter\GhostscriptPdfOptimize;
use Mindento\Shared\PdfOptimize\Port\PdfOptimizeInterface;
use Mindento\Shared\Zipper\Adapter\ZipArchiverAdapter;
use Mindento\Shared\Zipper\Port\ArchiverInterface;

class SharedServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(Img2pdfInterface::class, ImagicImg2Pdf::class);
        $this->app->bind(PdfMergeInterface::class, GhostscriptPdfMerge::class);
        $this->app->bind(ArchiverInterface::class, ZipArchiverAdapter::class);
        $this->app->bind(PdfOptimizeInterface::class, GhostscriptPdfOptimize::class);
    }
}
