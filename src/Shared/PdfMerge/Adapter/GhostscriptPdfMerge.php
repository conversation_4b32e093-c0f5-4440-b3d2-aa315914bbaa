<?php

declare(strict_types=1);

namespace <PERSON>ento\Shared\PdfMerge\Adapter;

use Mindento\Shared\PdfMerge\Port\PdfMergeException;
use Mindento\Shared\PdfMerge\Port\PdfMergeInterface;
use Mindento\Shared\PdfMerge\Port\PdfFile;

class GhostscriptPdfMerge implements PdfMergeInterface
{
    private string $tmpPath;

    public function __construct(string $tmpPath = null)
    {
        $this->tmpPath = $tmpPath ?? sys_get_temp_dir();
    }

    public function merge(PdfFile ...$source): PdfFile
    {
        $name = uniqid('pdf-merge-');
        $outputName = sprintf('%s/%s.pdf', $this->tmpPath, $name);
        $files = $this->xargs(...$source);

        $result = $this->exec($outputName, $files);

        if (false === file_exists($outputName) && null === $result) {
            throw new PdfMergeException('Error while merging pdf files ' . $files);
        }

        return new PdfFile($outputName);
    }

    private function xargs(PdfFile ...$source): string
    {
        $files = array_map(function (PdfFile $item) {
            return $item->getPathname();
        }, $source);

        return implode(' ', $files);
    }

    /**
     * @return false|null|string
     */
    private function exec(string $outputName, string $files)
    {
        return shell_exec("gs -q -dNOPAUSE -dBATCH -dPDFSETTINGS=/ebook -sDEVICE=pdfwrite -sPAPERSIZE=a4 -sOutputFile=$outputName " . $files);
    }
}
