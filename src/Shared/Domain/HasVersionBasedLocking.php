<?php

declare(strict_types=1);

namespace Shared\Domain;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Trait HasVersionBasedLocking
 *
 * Provides optimistic locking functionality for Eloquent models based on a version number.
 * This approach is more explicit than timestamp-based locking and works better with aggregates.
 *
 * Usage:
 * 1. Add this trait to your model
 * 2. Add a 'version' column to your table (integer, default 0)
 * 3. Use the scopeWithVersion method in your queries
 * 4. Use the updateWithVersion method to update records with optimistic locking
 */
trait HasVersionBasedLocking
{
    /**
     * @throws OptimisticLockingException
     */
    public function optimisticPush(int $version, string $message): void
    {
        $this->getConnection()->transaction(function() use ($version) {
            $this->withVersion($version)
        });

        throw new OptimisticLockingException(
            'This recoard was updated already - your version is not actual',
            $this
        );
    }

    /**
     * Boot the trait.
     *
     * @return void
     */
    public static function bootHasVersionBasedLocking(): void
    {
        static::updating(function (Model $model) {
            if (isset($model->attributes['version'])) {
                $model->attributes['version'] = (int)$model->attributes['version'] + 1;
            }
        });
    }

    /**
     * Scope a query to only include records with a specific version.
     *
     * @param Builder $query
     * @param int|null $version
     * @return Builder
     */
    public function scopeWithVersion(Builder $query, ?int $version = null): Builder
    {
        if ($version !== null) {
            return $query->where('version', $version);
        }

        return $query;
    }

    /**
     * Update the model with optimistic locking based on version
     *
     * @param array $attributes
     * @param int|null $version
     * @return bool
     * @throws OptimisticLockingException
     */
    public function updateWithVersion(array $attributes = [], ?int $version = null): bool
    {
        // If no version is provided, use the current version
        $currentVersion = $version ?? $this->version;

        // If the model is new or has no version, just update it
        if (!$this->exists || $currentVersion === null) {
            return (bool)$this->update($attributes);
        }

        // Add the version check to the query
        $result = $this->withVersion($currentVersion)->update($attributes);

        if (!$result) {
            throw new OptimisticLockingException(
                'The record has been modified since it was loaded.',
                $this
            );
        }

        return (bool)$result;
    }

    /**
     * Static helper to update a model with optimistic locking
     *
     * @param Model $model
     * @param array $attributes
     * @param int|null $version
     * @return bool
     * @throws OptimisticLockingException
     */
    public static function updateModelWithVersion(Model $model, array $attributes = [], ?int $version = null): bool
    {
        if (!method_exists($model, 'updateWithVersion')) {
            throw new \RuntimeException('The model does not use the HasVersionBasedLocking trait');
        }

        return $model->updateWithVersion($attributes, $version);
    }

    /**
     * Get the optimistic lock version
     *
     * @return int|null
     */
    public function getVersion(): ?int
    {
        if (!$this->exists || !isset($this->attributes['version'])) {
            return null;
        }

        return (int)$this->attributes['version'];
    }
}
