<?php

declare(strict_types=1);

namespace Shared\Domain;

use Exception;
use Illuminate\Database\Eloquent\Model;

/**
 * Exception thrown when an optimistic locking conflict occurs
 */
class OptimisticLockingException extends Exception
{
    protected ?Model $model = null;

    /**
     * Create a new OptimisticLockingException instance
     *
     * @param string $message The exception message
     * @param Model|null $model The model that caused the conflict
     * @param int $code The exception code (default: 409 Conflict)
     * @param \Throwable|null $previous The previous exception
     */
    public function __construct(
        string $message = 'The record has been modified since it was loaded.',
        ?Model $model = null,
        int $code = 409, // HTTP 409 Conflict
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->model = $model;
    }

    /**
     * Get the model that caused the conflict
     *
     * @return Model|null
     */
    public function getModel(): ?Model
    {
        return $this->model;
    }
}
