<?php

declare(strict_types=1);

namespace Mindento\Provider\Disposable\Controller;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Collection;
use Mindento\Provider\Disposable\Model\ProviderService;
use Mindento\Provider\Disposable\View\ProviderResource;

final class ApiProviderController
{
    public function __construct(ProviderService $providerService)
    {
        $this->providerService = $providerService;
    }

    public function store(ProviderRequest $request): ProviderResource
    {
        $provider = $this->providerService->create(
            (int) $request->instance_id,
            intval($request->company_id) ?: null,
            (int) $request->country_id,
            $request->name,
            $request->tax_id,
            $request->erp_id,
            $request->address,
            $request->city,
            $request->postcode
        );

        return new ProviderResource($provider);
    }

    public function index(Request $request): ResourceCollection
    {
        $provider = $this->providerService->findByInstanceAndCompany(
            (int) $request->instance_id,
            intval($request->company_id) ?: null,
        );

        return ProviderResource::collection(new Collection(array_filter([$provider])));
    }
}
