<?php

declare(strict_types=1);

namespace Mindento\Provider\Disposable\Model;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\InteractsWithTime;

/**
 * Mindento\Provider\Disposable\Model\Provider
 *
 * @property int $id
 * @property string $slug
 * @property int $instance_id
 * @property int|null $employee_id
 * @property string $name
 * @property string|null $registry_number
 * @property int|null $country_id
 * @property string|null $address
 * @property string|null $city
 * @property string|null $postcode
 * @property string|null $erp_id
 * @property string $source
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property int|null $created_by
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder|Provider newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Provider newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Provider query()
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereEmployeeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereErpId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider wherePostcode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereRegistryNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereUpdatedBy($value)
 * @mixin \Eloquent
 * @property int|null $company_id
 * @property string $tax_id
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Provider whereTaxId($value)
 */
final class Provider extends Model
{
    protected $table = 'provider_disposables';
    protected $fillable = [
        'instance_id',
        'country_id',
        'company_id',
        'name',
        'tax_id',
        'erp_id',
        'address',
        'city',
        'postcode',
        'country',
    ];
}
