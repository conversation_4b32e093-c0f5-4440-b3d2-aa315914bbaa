import { HttpService } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Card, CARD_STATUS } from '../../entities/Card';
import { CARD_SOAP_CLIENT } from '../../providers/soap';
import { CardService } from './card.service';
import { GenerateCardDto } from './GenerateCardDto';
import qs from "qs";
import {generateControlData} from "../../utils";

describe('CardService', () => {
    let service: CardService;
    let card!: Card;
    const authData = { data: { access_token: 'FAKE_ACCESS_TOKEN' } };
    const httpPromise = jest.fn(() => authData);
    const httpHandler = jest.fn(() => ({ toPromise: httpPromise }));
    process.env.MINDENTO_SECRET = 'fake-secret';
    let cardDeletePromise!: Promise<void>;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                {
                    provide: getRepositoryToken(Card),
                    useValue: {
                        save: jest.fn(async (object: any) => object),
                        update: jest.fn(async (id: string, cardInfo: Partial<Card>) => card = { ...card, ...cardInfo }),
                        findOne: jest.fn(async ({ id }: Partial<Card>) => id === card.id ? card : undefined),
                        findByIds: jest.fn(async (ids: string[]) => ids.filter(id => card.id === id).map(id => card)),
                    },
                },
                { provide: CARD_SOAP_CLIENT, useValue: {
                    unregisterCreditCardAsync: jest.fn(() => {
                        cardDeletePromise = new Promise(r => r());
                        return cardDeletePromise;
                    }),
                } },
                {
                    provide: HttpService,
                    useValue: {
                        post: httpHandler,
                    },
                },
                CardService,
            ],
        }).compile();

        service = module.get<CardService>(CardService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should add new card', async () => {
        const generateCardDto = new GenerateCardDto();
        generateCardDto.returnUrl = 'http://example.com/path-to-return';
        generateCardDto.errorUrl = 'http://example.com/path-to-error';
        generateCardDto.lang = 'pl';

        const addFunction = async () => {
            card = await service.addCard(generateCardDto);
            return card;
        };

        await expect(addFunction()).resolves.toBeTruthy();

        expect(card).toBeTruthy();
        expect(card.mindentoInstance).toBe('http://example.com');
        expect(card.status).toBe(CARD_STATUS.ADDING);
        expect(card.number).toBeFalsy();

        generateCardDto.returnUrl = 'notreallyanurl!_';
        await expect(addFunction()).rejects.toMatchObject({
            message: {
                error: 'Bad Request',
                message: `returnUrl is wrong`,
                statusCode: 400,
            },
        });
    });

    it('should get card', async () => {
        await expect(service.getCard(card.id)).resolves.toMatchObject(card);

        await expect(service.getCard('not-card-id')).rejects.toMatchObject({
            message: {
                error: 'Not Found',
                statusCode: 404,
                message: 'Card not found',
            },
        });
    });

    it('should get multiple cards by ids', async () => {
        let ids = [card.id];
        let cards = await service.getCardsByIds(ids);

        expect(cards.length).toBe(1);
        expect(cards[0]).toMatchObject(card);

        ids = [card.id, 'not-card-id'];
        cards = await service.getCardsByIds(ids);

        expect(cards.length).toBe(1);
        expect(cards[0]).toMatchObject(card);

        ids = ['not-card-id'];
        cards = await service.getCardsByIds(ids);

        expect(cards.length).toBe(0);

        ids = [];
        cards = await service.getCardsByIds(ids);

        expect(cards.length).toBe(0);
    });

    it('should generate correct URL', () => {
        process.env.VPOS_MERCHANT_CODE = '123456';
        process.env.VPOS_SHARED_KEY = '123456';
        const generateCardDto = new GenerateCardDto();
        generateCardDto.returnUrl = 'http://example.com/path-to-return';
        generateCardDto.errorUrl = 'http://example.com/path-to-error';
        generateCardDto.lang = 'pl';

        const newCard = new Card();
        newCard.status = CARD_STATUS.ADDING;
        newCard.id = 'dummyid';
        newCard.createdAt = new Date();
        newCard.updatedAt = new Date();

        const url = service.generateUrl(newCard, generateCardDto);
        const body = service.generateBody(newCard, generateCardDto);
        expect(url).toMatchSnapshot();
        expect(body).toMatchSnapshot();
    });

    it('should generate correct URL with 3ds', () => {
        process.env.VPOS_MERCHANT_CODE = '123456';
        process.env.VPOS_SHARED_KEY = '123456';
        const generateCardDto = new GenerateCardDto();
        generateCardDto.returnUrl = 'http://example.com/path-to-return';
        generateCardDto.errorUrl = 'http://example.com/path-to-error';
        generateCardDto.lang = 'pl';
        generateCardDto.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3';
        generateCardDto.javaEnabled = true;
        generateCardDto.jsEnabled = true;
        generateCardDto.ip = '************';
        generateCardDto.screenWidth = '1920';
        generateCardDto.screenHeight = '1080';
        generateCardDto.acceptHeader = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8';
        generateCardDto.email = '<EMAIL>';
        generateCardDto.mobilePhone = '123456789';
        generateCardDto.cardholderName = 'John Doe';

        const newCard = new Card();
        newCard.status = CARD_STATUS.ADDING;
        newCard.id = 'dummyid';
        newCard.createdAt = new Date();
        newCard.updatedAt = new Date();

        const url = service.generateUrl(newCard, generateCardDto);
        const body = service.generateBody(newCard, generateCardDto);
        expect(url).toMatchSnapshot();
        expect(body).toMatchSnapshot();
    });

    it('test generating control data', async () => {
        const formData = 'pos_id=72029248&order_id=0008&session_id=0008&amount=10100&currency=PLN&test=N&language=pl&client_ip=***********&street=Testowa&street_n1=12&street_n2=23&addr2=adres2&addr3=adres3&city=Warszawa&postcode=01-152&country=PL&email=<EMAIL>&ba_firstname=Jan&ba_lastname=Kowalski'
        const sharedKey = 'd0e66bfafaf5253157d1b5464e34b0d8d1b53bafc88ae4b7adb0f62d6812c24c';
        const controlData = generateControlData(formData, sharedKey);

        expect(controlData).toBe('f596f036c01534e8c571ea4a35c06f7e104ee261588c0433b8d55f7a9175e2bf');
    });

    it('test generating query string', async () => {
        const formData = {
            pos_id: "72029248",
            order_id: "0008",
            session_id: "0008",
            amount: "10100",
            currency: "PLN",
            test: "N",
            language: "pl",
            client_ip: "***********",
            street: "Testowa",
            street_n1: "12",
            street_n2: "23",
            addr2: "adres2",
            addr3: "adres3",
            city: "Warszawa",
            postcode: "01-152",
            country: "PL",
            email: "<EMAIL>",
            ba_firstname: "Jan",
            ba_lastname: "Kowalski"
        };

        expect(
            qs.stringify(formData, {encode: false})
        ).toBe(
            'pos_id=72029248&order_id=0008&session_id=0008&amount=10100&currency=PLN&test=N&language=pl&client_ip=***********&street=Testowa&street_n1=12&street_n2=23&addr2=adres2&addr3=adres3&city=Warszawa&postcode=01-152&country=PL&email=<EMAIL>&ba_firstname=Jan&ba_lastname=Kowalski'
        );
    });

    it('should update card status', async () => {
        const updateMindentoMock = jest.fn();
        Object.defineProperty(service, 'updateCardInMindento', {
            value: updateMindentoMock,
        });

        const expiredCard = await service.updateCardStatus(
            card.id,
            CARD_STATUS.EXPIRED,
        );
        expect(expiredCard.status).toBe(CARD_STATUS.EXPIRED);
        expect(updateMindentoMock).toHaveBeenCalledTimes(1);
    });

    it('should send card update notification', async () => {
        const authorizeInMindentoMock = jest.fn(() => 'FAKE_TOKEN');
        Object.defineProperty(service, 'authorizeInMindento', {
            value: authorizeInMindentoMock,
        });

        await service.updateCardInMindento(card.id);
        expect(httpHandler).toHaveBeenCalledWith(
            'http://example.com/internal-api/cards/update-card-details',
            {
                id: card.id,
                mindentoInstance: 'http://example.com',
                status: 'EXPIRED',
            },
            {
                headers: { Authorization: 'Bearer FAKE_TOKEN' },
            },
        );
    });

    it('should authorize in Mindento', async () => {
        await service.authorizeInMindento('https://example.com');
        expect(httpHandler).toHaveBeenCalledWith('https://example.com/oauth/token', {
            client_id: 4,
            client_secret: process.env.MINDENTO_SECRET,
            grant_type: 'client_credentials',
            scope: '*',
        });

        process.env.MINDENTO_SECRET = '';
        await expect(service.authorizeInMindento('https://example.com')).rejects.toMatchObject({
            message: 'MINDENTO_SECRET not found, check your .env',
        });
    });

    it('should delete card', async () => {
        const updateMindentoMock = jest.fn();
        Object.defineProperty(service, 'updateCardInMindento', {
            value: updateMindentoMock,
        });

        card.status = CARD_STATUS.ADDING;
        await service.deleteCard(card.id);
        expect(card.status).toBe(CARD_STATUS.DELETED);
        expect(updateMindentoMock).toHaveBeenCalled();

        card.status = CARD_STATUS.EXPIRED;
        await service.deleteCard(card.id);
        expect(card.status).toBe(CARD_STATUS.DELETING);
        await cardDeletePromise;
        expect(card.status).toBe(CARD_STATUS.DELETED);

        updateMindentoMock.mockReset();
        card.status = CARD_STATUS.FAILED;
        await service.deleteCard(card.id);
        expect(card.status).toBe(CARD_STATUS.DELETED);
        expect(updateMindentoMock).toHaveBeenCalled();

        updateMindentoMock.mockReset();
        card.status = CARD_STATUS.DELETED;
        await expect(service.deleteCard(card.id)).rejects.toMatchObject({
            message: {
                error: 'Bad Request',
                message: `Cannot delete already deleted card`,
                statusCode: 400,
            },
        });
        expect(updateMindentoMock).not.toHaveBeenCalled();
    });

    it('should confirm card', async () => {
        const updateMindentoMock = jest.fn();
        Object.defineProperty(service, 'updateCardInMindento', {
            value: updateMindentoMock,
        });

        const expirationDate = new Date();

        card.status = CARD_STATUS.ADDING;
        await service.confirmCard(card.id, '123456', 'VIS', expirationDate, '654321');
        expect(card.expirationDate).toBe(expirationDate);
        expect(card.status).toBe(CARD_STATUS.ADDED);
        expect(card.number).toBe('123456');
        expect(card.type).toBe('VIS');
        expect(card.referenceCode).toBe('654321');
        expect(updateMindentoMock).toHaveBeenCalledWith(card.id);

        updateMindentoMock.mockReset();
        card.status = CARD_STATUS.ADDED;
        await expect(service.confirmCard(card.id, '123456', 'VIS', expirationDate, '654321')).rejects.toMatchObject({
            message: {
                error: 'Bad Request',
                message: `Cannot confirm this card`,
                statusCode: 400,
            },
        });
        expect(updateMindentoMock).not.toHaveBeenCalled();
    });

});
