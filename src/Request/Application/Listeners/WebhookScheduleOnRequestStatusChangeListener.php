<?php

declare(strict_types=1);

namespace <PERSON>ento\Request\Application\Listeners;

use App\Request;
use Mindento\Request\Application\WebhookType;
use Mindento\Request\Domain\RequestRepository;
use <PERSON>ento\Webhook\UserInterface\Facade\Webhook;
use <PERSON>ent<PERSON>\Webhook\UserInterface\Facade\WebhookFacade;
use Modules\Common\Events\RequestAcceptedEvent;
use Modules\Common\Events\RequestCancelledEvent;
use Modules\Common\Events\RequestChangedStatusToTransferredToErp;

/**
 * Listens for Request related events and schedule the webhook execution
 */
final class WebhookScheduleOnRequestStatusChangeListener
{
    private RequestRepository $requestRepository;
    private WebhookFacade $webhookScheduler;

    public function __construct(
        RequestRepository $requestRepository,
        WebhookFacade $webhookScheduler
    ) {
        $this->requestRepository = $requestRepository;
        $this->webhookScheduler = $webhookScheduler;
    }

    /**
     * Handles send to ERP event
     */
    public function scheduleNewClaimTransferred(RequestChangedStatusToTransferredToErp $event): void
    {
        $this->scheduleWebhook($event->requestSlug(), WebhookType::NEW_CLAIM_TRANSFERRED());
    }

    /**
     * Handles request accepted event
     */
    public function scheduleRequestAccepted(RequestAcceptedEvent $event): void
    {
        $this->scheduleWebhook($event->requestSlug(), WebhookType::REQUEST_ACCEPTED());
    }

    /**
     * Handles request cancelled event
     */
    public function scheduleRequestCancelled(RequestCancelledEvent $event): void
    {
        $this->scheduleWebhook($event->requestSlug(), WebhookType::REQUEST_CANCELLED());
    }

    /**
     * Schedules the webhook of given type for given request slug
     *
     * @param string $requestSlug the slug of the request the webhook should be scheduled for
     * @param WebhookType $webhookType the type of the webhook to be scheduled
     */
    private function scheduleWebhook(string $requestSlug, WebhookType $webhookType): void
    {
        $request = $this->getRequest($requestSlug);
        if (!$request) {
            return;
        }

        $this->webhookScheduler->scheduleWebhook(
            new Webhook(
                (string)$request->getSlug(),
                $webhookType->getValue()
            )
        );
    }

    /**
     * Creates a new WebhookAttempt instance for given request slug and webhook type
     *
     * @param string $requestSlug the request slug the webhook attempt should be created for
     * @return null|Request a request instance or null if request could not be found
     */
    private function getRequest(string $requestSlug): ?Request
    {
        $request = $this->requestRepository->requestOfSlug($requestSlug);
        if (!$request) {
            return null;
        }

        return $request;
    }
}
