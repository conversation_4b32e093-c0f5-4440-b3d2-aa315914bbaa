<?php

declare(strict_types=1);

namespace Mindento\Webhook\Infrastructure\Http\Executor\Authenticator;

use Modules\Common\Http\Client\RequestInterface;
use Mindento\Webhook\Domain\ConfigProvider\WebhookConfig;

/**
 * This AuthProvider provides no authentication
 */
final class HttpRequestNoAuthAuthenticator implements HttpRequestAuthenticator
{

    /**
     * @inheritDoc
     */
    public function authenticate(RequestInterface $httpRequest, WebhookConfig $webhookConfig): RequestInterface
    {
        return $httpRequest;
    }
}
