<?php

declare(strict_types=1);

namespace Mindento\Webhook\Infrastructure\Http\Executor\Authenticator;

use Modules\Common\Http\Client\RequestInterface;
use Mindento\Webhook\Domain\ConfigProvider\WebhookConfig;

/**
 * The main entry for HttpRequestAuthenticator. This implementation selects
 * a proper AuthProvider for given configuration and then uses it to authenticate the HTTP request.
 */
final class HttpRequestFrontAuthenticator implements HttpRequestAuthenticator
{
    private array $authProviders = [];

    /**
     * @inheritDoc
     */
    public function authenticate(
        RequestInterface $httpRequest,
        WebhookConfig $webhookConfig
    ): RequestInterface {
        $authProvider = $this->selectAuthProvider($webhookConfig);

        return $authProvider->authenticate($httpRequest, $webhookConfig);
    }

    /**
     * Registers AuthProvider for given AuthType
     *
     * @param HttpRequestAuthenticator $authProvider the provider to be registered
     * @param AuthMethod $authType the authentication method the provider should be registered for
     */
    public function registerProvider(HttpRequestAuthenticator $authProvider, AuthMethod $authType): void
    {
        $this->authProviders[(string)$authType] = $authProvider;
    }

    /**
     * Selects the AuthProvider to be used based on given notification config
     *
     * @param WebhookConfig $notificationConfig the notification config AuthProvider is to be selected
     * @return HttpRequestAuthenticator the selected provider
     * @throws \OutOfBoundsException if no provider can be selected
     */
    private function selectAuthProvider(WebhookConfig $notificationConfig): HttpRequestAuthenticator
    {
        $authMethod = $this->resolveAuthMethod($notificationConfig);
        if (isset($this->authProviders[(string)$authMethod])) {
            return $this->authProviders[(string)$authMethod];
        }

        throw  UnsupportedAuthMethodException::create($authMethod);
    }

    /**
     * Resolves the AuthMethod for given notification config
     *
     * @param WebhookConfig $webhookConfig the config the AuthMethod should be resolved for
     * @return AuthMethod resolved authentication method
     */
    private function resolveAuthMethod(WebhookConfig $webhookConfig): AuthMethod
    {
        if (!$webhookConfig->getAuthConfig()) {
            return AuthMethod::NO_AUTH();
        }

        return AuthMethod::OAUTH_TOKEN();
    }
}
