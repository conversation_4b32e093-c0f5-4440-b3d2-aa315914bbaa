<?php

declare(strict_types=1);

namespace Mindento\Webhook\Infrastructure\Http\Executor\Authenticator\OAuth;

use Modules\Common\Http\Client\AbstractResponse;

/**
 * Represents the HTTP Response to received from the OAuth service
 */
final class OAuthResponse extends AbstractResponse
{
    /**
     * Gets the authorization token from the response
     *
     * @return null|string the authorization token
     */
    public function getToken(): ?string
    {
        if (!$this->isAccepted()) {
            return null;
        }

        $this->getBody()->rewind();
        $json = @json_decode($this->getBody()->getContents());
        $this->getBody()->rewind();

        if (json_last_error() !== JSON_ERROR_NONE) {
            return null;
        }

        return @$json->access_token;
    }
}
