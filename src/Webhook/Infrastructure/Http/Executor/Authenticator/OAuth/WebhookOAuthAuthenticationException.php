<?php

declare(strict_types=1);

namespace Mindento\Webhook\Infrastructure\Http\Executor\Authenticator\OAuth;

use Mindento\Webhook\Domain\Executor\Exception\WebhookExecutorException;

/**
 * Represents the error occurred during OAuth authentication process
 */
final class WebhookOAuthAuthenticationException extends \RuntimeException implements WebhookExecutorException
{
    private ?OAuthRequest $request;
    private ?OAuthResponse $response;

    public function __construct($message, ?OAuthRequest $request = null, ?OAuthResponse $response = null)
    {
        parent::__construct($message);
        $this->request = $request;
        $this->response = $response;
    }

    /**
     * A named constructor.
     * Creates a generic authentication error exception
     *
     * @return WebhookOAuthAuthenticationException an instance of the exception
     */
    public static function genericAuthError(OAuthRequest $request, ?OAuthResponse $response): self
    {
        return new self("External system authentication issue", $request, $response);
    }

    /**
     * A named constructor.
     * Creates an exception in case the authorization token was not received
     *
     * @return WebhookOAuthAuthenticationException an instance of the exception
     */
    public static function noTokenProvided(OAuthRequest $request, ?OAuthResponse $response): self
    {
        return new self("OAuth service did not return the expected authorization token", $request, $response);
    }

    /**
     * A named constructor.
     * Creates an exception in case the authorization token was not received
     *
     * @return WebhookOAuthAuthenticationException an instance of the exception
     */
    public static function noOAuthConfigurationProvided(): self
    {
        return new self("OAuth configuration has not been provided.");
    }

    /**
     * Gets the HTTP Request
     *
     * @return null|OAuthRequest the HTTP request
     */
    public function request(): ?OAuthRequest
    {
        return $this->request;
    }

    /**
     * Gets the HTTP Response
     *
     * @return null|OAuthResponse the HTTP response
     */
    public function response(): ?OAuthResponse
    {
        return $this->response;
    }
}
