<?php

declare(strict_types=1);

namespace Mindento\Webhook\Infrastructure\Http\Executor\Authenticator;

use Mindento\Webhook\Infrastructure\Http\Executor\Authenticator\OAuth\HttpRequestOAuthTokenAuthenticator;
use Modules\Common\Http\Client\HttpClientInterface;

/**
 * The factory of the AuthProvider component
 */
final class AuthProviderFactory
{
    private HttpClientInterface $httpClient;

    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient;
    }

    /**
     * Creates AuthProvider instance
     *
     * @return HttpRequestAuthenticator the AuthProvider instance
     */
    public function create(): HttpRequestAuthenticator
    {
        $frontProvider = new HttpRequestFrontAuthenticator();

        $frontProvider->registerProvider(new HttpRequestNoAuthAuthenticator(), AuthMethod::NO_AUTH());
        $frontProvider->registerProvider(
            new HttpRequestOAuthTokenAuthenticator($this->httpClient),
            AuthMethod::OAUTH_TOKEN()
        );

        return $frontProvider;
    }
}
