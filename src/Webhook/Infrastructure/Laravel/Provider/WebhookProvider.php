<?php

declare(strict_types=1);

namespace Mindento\Webhook\Infrastructure\Laravel\Provider;

use Illuminate\Foundation\Support\Providers\EventServiceProvider;
use Mindento\Webhook\Domain\ConfigProvider\WebhookConfigProvider;
use Mindento\Webhook\Domain\ConfigProvider\WebhookRetryIntervals;
use Mindento\Webhook\Domain\Executor\WebhookExecutor;
use Mindento\Webhook\Domain\Executor\WebhookExecutorFactory;
use Mindento\Webhook\Domain\Scheduler\WebhookFromConfigScheduler;
use Mindento\Webhook\Domain\Scheduler\WebhookScheduler;
use Mindento\Webhook\Domain\WebhookAttemptRepository;
use Mindento\Webhook\Infrastructure\Domain\ConfigProvider\WebhookFromFeatureConfigProvider;
use Mindento\Webhook\Infrastructure\Http\Executor\Authenticator\AuthProviderFactory;
use Mindento\Webhook\Infrastructure\Http\Executor\Authenticator\HttpRequestAuthenticator;
use Mindento\Webhook\Infrastructure\Http\Executor\WebhookHttpExecutor;
use Mindento\Webhook\Infrastructure\Laravel\Repository\WebhookAttemptEloquentRepository;
use Mindento\Webhook\UserInterface\Cli\ExecuteWebhooksCommand;
use Mindento\Webhook\UserInterface\Facade\WebhookDefaultFacade;
use Mindento\Webhook\UserInterface\Facade\WebhookFacade;

/**
 * Configures the Webhook package in the Laravel's service container
 */
final class WebhookProvider extends EventServiceProvider
{
    /**
     * Registers configuration and services in the Service Container
     */
    public function register(): void
    {
        $this->registerConfig();

        $this->app->singleton(WebhookFacade::class, WebhookDefaultFacade::class);
        $this->app->singleton(WebhookAttemptRepository::class, WebhookAttemptEloquentRepository::class);

        $this->app->singleton(HttpRequestAuthenticator::class, function ($app) {
            /** @var AuthProviderFactory $factory */
            $factory = $app->make(AuthProviderFactory::class);

            return $factory->create();
        });

        $this->app->when(WebhookExecutorFactory::class)
            ->needs(WebhookExecutor::class)
            ->give(function () {
                return $this->app->make(WebhookHttpExecutor::class);
            });

        $this->app->singleton(WebhookExecutor::class, function ($app) {
            /** @var WebhookExecutorFactory $factory */
            $factory = $app->make(WebhookExecutorFactory::class);

            return $factory->create();
        });

        $this->app->singleton(WebhookRetryIntervals::class);
        $this->app->when(WebhookRetryIntervals::class)
            ->needs('$retryIntervals')
            ->give(
                config('integrationapi.notifications.next-tries')
            );

        $this->app->singleton(WebhookScheduler::class, WebhookFromConfigScheduler::class);
        $this->app->singleton(WebhookConfigProvider::class, WebhookFromFeatureConfigProvider::class);

        $this->commands([
            ExecuteWebhooksCommand::class
        ]);
    }

    private function registerConfig()
    {
        $this->publishes(
            [
                __DIR__ . '/../Config/config.php' => config_path('integrationapi.php'),
            ],
            'config'
        );

        $this->mergeConfigFrom(
            __DIR__ . '/../Config/config.php',
            'integrationapi'
        );
    }
}
