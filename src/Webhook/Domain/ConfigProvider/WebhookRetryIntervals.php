<?php

declare(strict_types=1);

namespace Mindento\Webhook\Domain\ConfigProvider;

use Carbon\Carbon;
use Carbon\CarbonInterval;
use Mindento\Webhook\Domain\WebhookAttempt;

/**
 * Wraps a configuration of retry intervals
 */
final class WebhookRetryIntervals
{
    /** @var string[] */
    private array $retryIntervals;

    public function __construct(array $retryIntervals = [])
    {
        $this->retryIntervals = $retryIntervals;
    }

    /**
     * Gets the number of the webhook execution attempts
     *
     * @return int the number of webhook execution attempts
     */
    public function maxRetries(): int
    {
        return count($this->retryIntervals);
    }

    /**
     * Calculates time of the next webhook execution attempt
     *
     * @param WebhookAttempt $webhookAttempt the webhook attempt the time should be calculated for
     * @return null|\DateTimeInterface the date time of the next webhook execution
     */
    public function nextAttemptAt(WebhookAttempt $webhookAttempt): ?\DateTimeInterface
    {
        $interval = $this->interval($webhookAttempt->getNumberOfTries());

        return $interval ? Carbon::now()->add(CarbonInterval::fromString($interval)) : null;
    }

    /**
     * Returns the interval of given retry attempt
     *
     * @param int $retryNumber the number of retry attempt
     * @return null|string the interval or null if not configured for given retry attempt
     */
    private function interval(int $retryNumber): ?string
    {
        return $this->retryIntervals[$retryNumber] ?? null;
    }
}
