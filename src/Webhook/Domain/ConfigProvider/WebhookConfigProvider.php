<?php

declare(strict_types=1);

namespace Mindento\Webhook\Domain\ConfigProvider;

use Mindento\Webhook\Domain\WebhookAttempt;

/**
 * Provides the webhook configuration for given attempt
 */
interface WebhookConfigProvider
{
    /**
     * Checks if the webhook is enabled
     *
     * @param WebhookAttempt $webhookAttempt the attempt to be checked
     * @return bool the flag indicating if webhook is enabled
     */
    public function isWebhookEnabled(WebhookAttempt $webhookAttempt): bool;

    /**
     * Gets the webhook configuration for given attempt
     *
     * @param WebhookAttempt $webhookAttempt the attempt the configuration should be got for
     * @return WebhookConfig the webhook configuration
     */
    public function getWebhookConfig(WebhookAttempt $webhookAttempt): WebhookConfig;
}
