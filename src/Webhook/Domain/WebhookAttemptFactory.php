<?php

declare(strict_types=1);

namespace Mindento\Webhook\Domain;

/**
 * Creates an instance of WebhookAttempt
 * This class should be considered final. It's not mark such for mocking purpose.
 */
class WebhookAttemptFactory
{
    /**
     * Creates a new instance of WebhookAttempt
     *
     * @param string $requestSlug the slug of the request related to the webhook
     * @param string $webhookType the type of the webhook
     * @return WebhookAttempt a new instance of the webhook attempt
     */
    public function create(string $requestSlug, string $webhookType): WebhookAttempt
    {
        return WebhookAttempt::create($requestSlug, $webhookType);
    }
}
