<?php

namespace <PERSON><PERSON><PERSON>\Webhook\UserInterface\Facade;

use Mindento\Webhook\Domain\Scheduler\WebhookScheduler;
use Mindento\Webhook\Domain\WebhookAttemptFactory;

final class WebhookDefaultFacade implements WebhookFacade
{
    private WebhookScheduler $webhookScheduler;

    private WebhookAttemptFactory $webhookAttemptFactory;

    public function __construct(WebhookScheduler $webhookScheduler, WebhookAttemptFactory $webhookAttemptFactory)
    {
        $this->webhookScheduler = $webhookScheduler;
        $this->webhookAttemptFactory = $webhookAttemptFactory;
    }

    /**
     * @inheritDoc
     */
    public function scheduleWebhook(Webhook $webhook): void
    {
        try {
            $this->webhookScheduler->scheduleWebhook(
                $this->webhookAttemptFactory->create($webhook->requestSlug(), $webhook->type())
            );
        } catch (\Throwable $e) {
            throw WebhookException::forWebhookSchedule($webhook, $e);
        }
    }
}
