<?php

declare(strict_types=1);

namespace Order\UI\Filament;


use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Order\Domain\Order;
use Order\UI\Filament\OrderResource\Pages;
use Order\UI\Filament\OrderResource\RelationManagers\OrderAddressesRelationManager;
use Order\UI\Filament\OrderResource\RelationManagers\OrderBillingsRelationManager;
use Order\UI\Filament\OrderResource\RelationManagers\OrderBuyersRelationManager;
use Order\UI\Filament\OrderResource\RelationManagers\OrderItemsRelationManager;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    protected static ?string $navigationLabel = 'Orders';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__d('order', 'Szczegóły zamówienia'))
                    ->schema([
                        Select::make('currency')
                            ->label(__d('order', 'Waluta'))
                            ->options([
                                'PLN' => 'PLN (zł)',
                                'EUR' => 'EUR (€)',
                                'USD' => 'USD ($)',
                                'GBP' => 'GBP (£)',
                            ])
                            ->default('PLN')
                            ->required(),
                        Textarea::make('notes')
                            ->label(__d('order', 'Uwagi'))
                            ->rows(3)
                            ->columnSpan('full'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('currency')
                    ->label(__d('order', 'Waluta'))
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label(__d('order', 'Data utworzenia'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            OrderItemsRelationManager::class,
            OrderBuyersRelationManager::class,
            OrderBillingsRelationManager::class,
            OrderAddressesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'view' => Pages\ViewOrder::route('/{record}'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return true;
    }
}
