<?php

declare(strict_types=1);

namespace <PERSON>ento\Slack\UserInterface\Facade;

class SlackUserDTO
{
    private string $slackId;
    private string $accessToken;

    public function __construct(string $slackId, string $accessToken)
    {
        $this->slackId = $slackId;
        $this->accessToken = $accessToken;
    }

    public function getSlackId(): string
    {
        return $this->slackId;
    }

    public function getAccessToken(): string
    {
        return $this->accessToken;
    }
}
