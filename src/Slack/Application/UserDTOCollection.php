<?php

declare(strict_types=1);

namespace Mindento\Slack\Application;

use Illuminate\Support\Arr;

class UserDTOCollection
{
    /**
     * @var UserDTO[]
     */
    public array $users;

    public function __construct(array $users)
    {
        $this->users = $users;
    }

    public function getEmails(): array
    {
        return array_map(fn(UserDTO $user) => $user->email, $this->users);
    }

    public function byEmail($email): ?UserDTO
    {
        return Arr::first($this->users, fn(UserDTO $user) => $user->email === $email);
    }
}
