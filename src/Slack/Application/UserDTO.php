<?php

declare(strict_types=1);

namespace Mindento\Slack\Application;

class UserDTO
{
    public int $id;
    public string $name;
    public string $email;
    public ?string $slackId;

    public function __construct(
        int $id,
        string $name,
        string $email,
        string $slackId = null
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->email = $email;
        $this->slackId = $slackId;
    }
}
