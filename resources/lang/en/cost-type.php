<?php

return array(
    'taxi' => 'Taxi',
    'parking' => 'Parking',
    'other-business-trips' => 'Other travel arrangements',
    'internal-meeting' => 'Internal meeting',
    'meeting-with-contractor' => 'Hospitality restaurants',
    'representation' => 'Entertainment',
    'service-materials' => 'Maintenance materials',
    'shopping-for-the-office' => 'Office supplies',
    'small-equipment' => 'Small equipment',
    'other-materials' => 'Other materials',
    'other-services' => 'Other services',
    'fuel' => 'Fuel',
    'car-maintenance' => 'Car maintenance',
    'subscriptions' => 'Subscriptions',
    'others' => ' Others',
    'taxi_' => 'Taxi :details',
    'parking_' => 'Parking :details',
    'other-business-trips_' => 'Other travel arrangement :details',
    'internal-meeting_' => 'Internal meeting :details',
    'meeting-with-contractor_' => 'Hospitality restaurants :details',
    'representation_' => 'Entertainment :details',
    'service-materials_' => 'Maintenance materials :details',
    'shopping-for-the-office_' => 'Office supplies :details',
    'small-equipment_' => 'Small equipment :details',
    'other-materials_' => 'Other materials :details',
    'other-services_' => 'Other services :details',
    'fuel_' => 'Fuel :details',
    'car-maintenance_' => 'Car maintenance :details',
    'subscriptions_' => 'Subscriptions :details',
    'others_' => 'Others :details',
    'private-expense' => 'Private expense',
    'periodic' => 'Periodic expenses',
    'periodic_' => 'Periodic expenses :details',
    'fairs-conferences' => 'Fairs Conferences',
    'fairs-conferences_' => 'Fairs Conferences :details',
    'post' => 'Post',
    'tools' => 'Tools',
    'trainings' => 'Trainings',
    'tasting' => 'Tasting',
    'groceries' => 'Groceries',
    'administration-fees' => 'Administration fees',
    'qhs' => 'QHS',
    'other-costs' => 'Other costs',
    'fixed-assets' => 'Fixed assets',
    'hotel-non-statutory' => 'Hotel non-statutory trip',
    'appliances' => 'Appliances',
    'post_' => 'Post :details',
    'tools_' => 'Tools :details',
    'trainings_' => 'Trainings :details',
    'tasting_' => 'Tasting :details',
    'groceries_' => 'Groceries :details',
    'administration-fees_' => 'Administration fees :details',
    'qhs_' => 'QHS :details',
    'other-costs_' => 'Other costs :details',
    'fixed-assets_' => 'Fixed assets :details',
    'hotel-non-statutory_' => 'Hotel non-statutory trip :details',
    'appliances_' => 'Appliances :details',
    'meal-domestic' => 'Meal domestic',
    'meal-abroad' => 'Meal abroad',
    'hygiene-items' => 'Hygiene items',
    'representation-own' => 'Representation own purpose',
    'meal-domestic_' => 'Meal domestic :details',
    'meal-abroad_' => 'Meal abroad :details',
    'hygiene-items_' => 'Hygiene items :details',
    'representation-own_' => 'Representation own purpose :details',
    'team-integration' => 'Team integration',
    'meeting-with-the-consultant' => 'Meeting with the consultant',
    'meeting-with-the-contractor' => 'Meeting with the contractor',
    'refund-for-glasses' => 'Refund for glasses',
    'marketing-materials' => 'Marketing materials',
);
