<?php
return array (
  'request-canceled-by-owner' => ':user cancelled request',
  'request-rejected-owner-notification' => 'Your request has been rejected',
  'request-rejected-acceptor-notification' => ':user \'s request has been rejected',
  'request-returned-for-improvement-owner-notification' => 'Your request has been returned for correction',
  'request-returned-for-improvement-acceptor-notification' => ':user \'s request has been returned for correction',
  'request-settlement-has-been-accepted' => 'Claim has been approved',
  'request-settlement-has-been-accounted' => 'Your claim has been posted',
  'request-settlement-has-been-rejected' => 'Claim has been returned for correction',
  'request-settlement-to-improvement' => 'Your settlement has been returned for improvement',
  'request-trip-started' => 'Trip started',
  'request-waiting-for-acceptance' => 'You received request waiting for your acceptance',
  'request-waiting-for-settlement' => 'You received new claim waiting for your approval',
  'unaccounted-request-reminder' => 'Open claim reminder',
  'not-accepted-settlement-request-reminder' => 'Claim waiting for approval reminder',
  'your-request-has-been-accepted' => 'Your request has been accepted',
  'subordinate-sent-request-to-acceptance-with-project' => 'User :user has submitted request concerning (:project)/ concerning cost centre (:mpk)',
  'subordinate-sent-request-to-acceptance-without-project' => 'User :user has submitted request concerning cost centre (:mpk)',
  'subordinate-request-has-been-auto-accepted' => 'User :user received auto-acceptance',
  'subordinate-request-has-been-settlement-auto-accepted' => 'User :user received auto-approve',
  'offer-remarks' => 'Offer remarks',
  'subordinate-request-has-been-auto-accepted-message' => 'Wniosek :name został‚ zaakceptowany',
  'request-created-by-agent' => 'Travel Support has created request for you',
  'payment-failed' => 'Payment for element :element failed',
  'request-auto-reservation-failed-notification' => 'Auto-booking :name_short could not be completed',
  'request-reservation-failed-notification' => 'Booking confirmation :name_short could not be completed',
  'request-installment-has-been-paid' => 'Request for cash advance :route has been completed',
  'request-did-not-have-place' => 'Trip did not take place',
  'request-for-cash-advance' => 'Request for cash advance :user',
);
