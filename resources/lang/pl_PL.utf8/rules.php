<?php

return [
    /**
     * NOWE TŁUMACZENIA - które zostały znalezione w kodzie, a nie występują w plikach tłumaczeń.
     */

    /**
     * PUSTE TŁUMACZENIA - które występują zarówno w plikach tłumaczeń oraz w kodzie, ale nie mają przypisanej wartości
     */

    /**
     * USUNIĘTE TŁUMACZENIA - które występują w plikach tłumaczeń, a nie zostały znalezione w kodzie
     */

    /**
     * POZOSTAŁE TŁUMACZENIA - były dostępne wcześniej i mają tłumaczenie
     */
    'all-documents-are-settled' => 'Wszystkie dokumenty zostały rozliczone',
    'not-all-documents-are-settled' => 'Nie wszystkie dokumenty zostały rozliczone',
    'request-has-unmarked-documents' => 'Rozliczenie posiada nieoznaczone dokumenty',
    'lump-sum-document-not-settled' => 'Oświadczenie o ryczałtach nie zostało rozliczone',
    'accounting-travel-expenses-not-equal' => 'Kwota dekretacji diet jest nieprawidłowa',
    'accounting-cover-message' => 'Oświadczenie o ryczałtach nie może zostać zaksięgowane:',
    'plane-trip-not-allowed-warning' => 'Podróż samolotem jest niedozwolona',
    'plane-trip-flight-class-warning' => 'Klasa lotu :name (:modelFlightClass) przekracza :warningFlightClass.',
    'national-plane-trip-warning' => 'Cena za przelot :name przekracza limit :amount :currency.',
    'national-plane-trip-error' => 'Cena za przelot przekracza :amount :currency. Pozycja nie zostanie zapisana.',
    'international-plane-trip-warning' => 'Cena za przelot :name przekracza limit :amount :currency.',
    'international-plane-trip-error' => 'Cena za przelot przekracza :amount :currency. Pozycja nie zostanie zapisana.',
    'intercontinental-plane-trip-warning' => 'Cena za przelot przekracza :amount :currency.',
    'intercontinental-plane-trip-error' => 'Cena za przelot przekracza :amount :currency. Pozycja nie zostanie zapisana.',
    'error-mileage-allowance' => 'Długość trasy przekracza :distance km. Pozycja nie zostanie zapisana.',
    'warning-mileage-allowance' => 'Trasa :route jest dłuższa niż :distance km.',
    'train-trip-error' => 'Cena za przejazd przekracza :amount :currency. Pozycja nie zostanie zapisana.',
    'train-trip-warning' => 'Cena za przejazd :name przekracza limit :amount :currency.',
    'accommodation-error' => 'Cena za zakwaterowanie przekracza :amount :currency. Pozycja nie zostanie zapisana.',
    'accommodation-warning' => 'Cena za zakwaterowanie :name przekracza limit :amount :currency',
    'accounted-amount-greater-than-requested' => 'Kwota rozliczona w :name jest większa od kwoty wnioskowanej',
    'unrequested-element-appeared' => 'Rozliczenie posiada niezawnioskowane koszty',
    'not-all-access-lump-sums-are-confirmed' => 'Rozliczenie zawiera niepotwierdzone dojazdy',
    'not-all-mileage-allowances-in-trip-duration' => 'Dane w sekcji kilometrówki są różne od danych przebiegu podróży',
    'trip-course-not-confirmed' => 'Przebieg podróży nie został zatwierdzony',
    'request-has-not-requested-amount' => 'Kwota wnioskowana musi być większa od zera',
    'request-has-not-settled-amount' => 'Kwota rozliczona musi być większa od zera',
    'request-trip-not-ended' => 'Według planu Twoja podróż służbowa jeszcze trwa. Rozliczenie możesz zakończyć i przesłać do zatwierdzenia zaraz po jej zakończeniu',
    'taxi-amount-error' => 'Cena za taksówkę przekracza limit :amount :currency.',
    'train-trip-max-service-class-warning' => 'Klasa przejazdu jest wyższa niż przyznany limit',
    'request-not-isset-acceptor' => 'Przed wysłaniem wniosku wybierz akceptującego',
    'request-not-isset-acceptor_of_settlement' => 'Przed wysłaniem rozliczenia wybierz zatwierdzającego',
    'request_is_not_chronological' => 'Data rozpoczęcia podróży nie może być późniejsza od daty miejsca docelowego',
    'request-has-not-mpk' => 'Wybrane MPK nie istnieje',
    'request-not-selected-mpk' => 'Wybierz MPK',
    'request-trip-took-at-least-six-hours' => 'Podróż zajęła minimum 6 godzin',
    'request-trip-between-hours' => 'Podróż nocna pomiędzy godzinami',
    'request-can-auto-acceptation' => 'Wniosek został zaakceptowany automatycznie',
    'request-can-not-auto-acceptation' => 'Wniosek nie został zaakceptowany automatycznie',
    'request-can-settlement-auto-acceptation' => 'Wniosek został zatwierdzony automatycznie',
    'request-can-not-settlement-auto-acceptation' => 'Wniosek nie został zatwierdzony automatycznie',
    'document-can-not-settlement' => 'Nie możesz rozliczyć dokument',
    'low-grade-for-automatically-plane-trip-warning' => 'Niski poziom użytkownika do automatycznego zakupu samolotu',
    'settled-amount-does-not-match-requested-expense-type' => 'Wnioskowana i rozliczona kwota dotyczy różnych typów wydatku ',
    'border-crossing-state-valid' => 'Nie możesz zakończyć rozliczenia przed zapisaniem przebiegu podróży',
    'all-foreign-access-lump-sums-are-valid' => 'Dane w sekcji Ryczałt za dojazdy do dworca/lotniska w wyjazdach zagranicznych są różne od danych przebiegu podróży',
    'accounting-mileage-summary-amount-not-equal' => 'Kilometrówka nie może zostać zadekretowana. Obliczona kwota różni się od kwoty pozycji dekretacji',
    'accounting-dimensions-are-required' => 'Uzupełnij wszystkie wymiary analityczne',
    'mileage-allowance-accounting-dimensions-are-required' => 'Uzupełnij dane wymiarów analitycznych kilometrówki',
    'request-accounting-allowances-dimensions-are-required' => 'Uzupełnij dane wymiarów analitycznych diet i ryczałtów',
    'total-settled-amount-limit-exceeded' => 'Przekroczony dozwolony limit całkowitej wartości rozliczenia',
    'total-requested-amount-limit-exceeded' => 'Przekroczony dozwolony limit całkowitej wartości wniosku',
    'delegation-trip' => 'Podróż została zmieniona na delegacyjną',
    'not-delegation-trip' => 'Podróż została zmieniona na niedelegacyjną',
    'request-comment-error' => 'Podróż przekracza limity, wobec czego pole komentarz musi zostać wypełnione',
];
