<?php

return [
    /**
    * NOWE TŁUMACZENIA - które zostały znalezione w kodzie, a nie występują w plikach tłumaczeń.
    */
    'accounting-travel-expenses-has-been-saved' => 'Księgowanie diety została zapisana',
    'accounting-travel-expenses-has-been-deleted' => 'Księgowanie diety została usunięta',

    /**
    * PUSTE TŁUMACZENIA - które występują zarówno w plikach tłumaczeń oraz w kodzie, ale nie mają przypisanej wartości
    */

    /**
    * USUNIĘTE TŁUMACZENIA - które występują w plikach tłumaczeń, a nie zostały znalezione w kodzie
    */

    /**
    * POZOSTAŁE TŁUMACZENIA - były dostępne wcześniej i mają tłumaczenie
    */
    'request-has-been-saved' => 'Wniosek został zapisany',
    'document-has-been-saved' => 'Dokument został zapisany',
    'request-has-been-deleted' => 'Wniosek został usnięty',
    'acceptor-has-been-attached' => 'Akceptator został przypisany',
    'acceptor-has-been-deleted' => 'Akceptator został usunięty',
    'settlement-acceptor-has-been-attached' => 'Akceptator rozliczenia został przypisany',
    'settlement-acceptor-has-been-deleted' => 'Akceptator rozliczenia zostął usunięty',
    'element-has-been-saved' => 'Element został zapisany',
    'element-has-been-deleted' => 'Element został usunięty',
    'element-has-been-updated' => 'Element został zaktualizowany',
    'group-has-been-saved' => 'Grupa została zapisana',
    'group-has-been-deleted' => 'Grupa została usunięta',
    'ocr-hint-has-been-saved' => 'Element OCR został zapisany',
    'travel-expense-has-been-saved' => 'Element diety został zapisany',
    'travel-expense-has-been-deleted' => 'Element diety został usunięty',
    'request-mileage-has-been-saved' => 'Kilometrówka została zapisana',
    'request-mileage-has-been-deleted' => 'Kilometrówka została usunięta',
    'comment-has-been-saved' => 'Komentarz został zapisany',
    'document-has-been-deleted' => 'Dokument został usnięty',
    'you-are-logged-in' => 'Jesteś zalogowany',
    'you-are-logged-in-as' => 'Jesteś zalogowany jako',
    'installment-has-been-saved' => 'Element zaliczka został zapisany',
    'installment-has-been-deleted' => 'Element zaliczka zostął usunięty',
    'instance-has-been-saved' => 'Instancja została zapisana',
    'instance-has-been-deleted' => 'Instancja została usunięta',
    'border-crossing-has-been-saved' => 'Element przekroczenie granicy został zapisany',
    'border-crossing-has-been-deleted' => 'Element przekroczenie granicy został usunięty',
    'user-has-been-saved' => 'Użytkownik został zapisany',
    'user-blocking-has-been-saved' => 'Blokada użtkownika została ustawiona',
    'user-welcome-email-sent' => 'Email powitalny został wysłany',
    'project-has-been-saved' => 'Projekt został zapisany',
    'project-has-been-deleted' => 'Projekt został usunięty',
    'company-has-been-saved' => 'Firma została zapisana',
    'user-assigned-accounting-of-this-request' => 'Użytkownik :user przypisał na siebie księgowanie tego wniosku',
    'accountant-has-been-assigned' => 'Księgowy został przypisany do rozliczenia',
    'target-point-has-been-deleted' => 'Punkt docelowy został usunięty',
    'target-point-has-been-saved' => 'Punkt docelowy został zapisany',
    'private-accomodation-has-been-deleted' => 'Nocleg we własnym zakresie został usunięty',
    'private-accomodation-has-been-saved' => 'Nocleg we własnym zakresie został zapisany',
    'provided-accommodation-has-been-saved' => 'Nocleg zapewniony został zapisany',
    'provided-accommodation-has-been-deleted' => 'Nocleg zapewniony został usunięty',
    'document-has-been-removed-from-request-element' => 'Dokument został usunięty z elementu wniosku',
    'document-has-been-added-to-request-element' => 'Dokument został dodany do elementu wniosku',

    'user-deputy-has-been-added-to-user' => 'Zastępca pomyślnie dodany',
    'user-deputy-has-been-deleted' => 'Zastępca został usunięty',
    'user-deputy-given-user-does-not-exists' => 'Użytkownik nie istnieje',
    'user-deputy-cannot-add-in-the-past' => 'Nie można dodać zastępstwa z datą która minęła',
    'user-deputy-cannot-add-yourself' => 'Nie można ustawić siebie jako zastępcę',
    'user-deputy-cannot-add-in-given-time-period' => 'W tym okresie czasowym nie można już dodać zastępcy',

    'user-assistant-has-been-deleted' => 'Asystent uzytkownika został usunięty',
    'user-assistant-has-been-added-to-user' => 'Asystent uzytkownika pomyślnie dodany',
    'user-assistant-given-user-does-not-exists' => 'Użytkownik nie istnieje',
    'user-assistant-cannot-add-in-the-past' => 'Nie można dodać asystenta z datą która minęła',
    'user-assistant-cannot-add-yourself' => 'Nie można ustawić siebie jako asystenta',
    'user-assistant-cannot-add-in-given-time-period' => 'W tym okresie czasowym nie można już dodać asystenta',

    'provider-has-been-saved' => 'Dostawca został zapisany',
    'provider-has-been-updated' => 'Dostawca został zaktualizowany',
    'provider-has-been-deleted' => 'Dostawca został usunięty',
    'provider-accountant-notified' => 'Prośba o założenie dostawcy została wysłana',
];
