import { Paginator } from './response';
import { ITransactionSuggestion } from './transaction-suggestion';
import { HttpLink } from './http-link';

export interface Account {
  id: string;
  card: string;
  account_date: string;
  description: string;
  employee: string;
  amount: number;
  balance: number;
  status: string;
}

export interface AccountResponse {
  data: Account[];
  paginator: Paginator;
}

export interface IAccountResponse {
  data: IAccount[];
  paginator: Paginator;
}

export interface IAccount {
  bank_name: string;
  bic: string;
  currency: string;
  iban: string;
  id: string;
  name: string;
  reference_id: string;
  status: string;
  tenant_id: string;
  tenant_provider_id: string;

  _links: {
    statements: HttpLink<[]>;
  };
}

export interface IAccountStatementsResponse {
  data: IAccountStatement[];
  paginator: Paginator;
}

export interface IAccountStatement {
  id: string;
  iban: string;
  number: string;
  created_at: string;
  from: string;
  to: string;
  closed: boolean;

  _links: {
    entries: HttpLink<[]>;
  };
}

export interface IAccountPaymentsResponse {
  data: IAccountPayment[];
  paginator: Paginator;
}

export interface IAccountPayment {
  id: string;
  card_number: string;
  booked_at: string;
  description: string;
  employee: string;
  amount: string;
  balance: string;
  exported_at: string;
}
