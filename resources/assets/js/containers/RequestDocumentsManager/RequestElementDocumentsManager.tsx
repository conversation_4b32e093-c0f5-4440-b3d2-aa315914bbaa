import React from 'react';

import { isEqual } from 'lodash';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';

import APIClient from '../../services/APIClient';
import { documents } from '../../store/app/document-list';
import { getCurrency as getInstanceCurrency } from '../../store/app/instance';
import { RequestType } from '../../types/request';

class RequestTravelDocumentsManagerBase extends React.Component<any, any> {
  uploadDocument = (request, files) => {
    const { element, upload } = this.props;
    const documentType = request.type === 'expense' ? 'accounting' : null;

    for (let file of files) {
      upload(file, () => {
        return APIClient.uploadElementDocument(
          request['slug'],
          element.type,
          element.id,
          file,
          null,
          documentType,
        );
      });
    }
  };

  shouldComponentUpdate = (nextProps, nextState) => {
    return !isEqual(nextProps.documentsList, this.props.documentsList);
  };

  canUploadDocuments(): boolean {
    const { request, documentsList } = this.props;

    if (request.type !== RequestType.INVOICE) {
      return true;
    }

    if (documentsList.length === 0) {
      return true;
    }

    return !request.periodic;
  }

  render() {
    const { children, instanceCurrency, request, currentUser, documentsList } = this.props;
    const canUploadDocuments = this.canUploadDocuments();

    const renderProps = {
      instanceCurrency,
      request,
      currentUser,
      documentsList,
      canUploadDocuments,
      uploadDocument: this.uploadDocument,
      deleteDocument: this.props.delete,
    };

    return children(renderProps);
  }
}

RequestTravelDocumentsManagerBase.propTypes = {
  request: PropTypes.object.isRequired,
  element: PropTypes.object.isRequired,
  documents: PropTypes.array.isRequired,
};

const mapStateToProps = (state) => ({
  instanceCurrency: getInstanceCurrency(state),
  currentUser: state.get('global').get('currentUser'),
});

const withConnect = connect(mapStateToProps);
const withDocuments = documents();

const RequestElementDocumentsManager = compose(
  withDocuments,
  withConnect,
)(RequestTravelDocumentsManagerBase);

export { RequestElementDocumentsManager };
export default { RequestElementDocumentsManager };
