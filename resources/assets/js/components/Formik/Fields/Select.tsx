import { connect } from 'formik'
import { trans } from '../../../trans'
import { FormGroup } from '../../../ui'
import Error from '../../../ui/Input/Error'
import { SelectField } from '../../ui/Form'

export default connect((props) => {
  const { label, name, type, options } = props

  return (
    <div>
      <FormGroup label={label} labeltop>
        <SelectField
          options={options}
          value={input.value}
          placeholder={trans('trains-booking.carriage-type')}
          onChange={(value) => input.onChange(value)}
          disabled={false}
        />

        <Error errors={props.formik.errors[name]} />
      </FormGroup>
    </div>
  )
})
