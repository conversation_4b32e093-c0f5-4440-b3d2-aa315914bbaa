import React from 'react';

import moment from 'moment';
import PropTypes from 'prop-types';

import { Cell, Row, Table, TBody, THead } from '../../components/ui/AccordionTable';
import trans from '../../trans';
import { AmountFormatter } from '../AmountFormatter';
import TruncateBreakable from '../TruncateBreakable/TruncateBreakable';

export class List extends React.Component<any, any> {
  render() {
    const { requests, isLoading, getRouteByName, pagination, changePage } = this.props;

    return (
      <Table
        striped
        pagination={{
          ...pagination,
          changePage,
          align: 'center',
        }}
      >
        <THead>
          <Row>
            <Cell fixedWidth={120}>{trans('request.date')}</Cell>
            <Cell></Cell>
            <Cell fixedWidth={'auto'}>{trans('request.name')}</Cell>
            <Cell fixedWidth={140} alignRight>
              {trans('request.accounted-sum-amount')}
            </Cell>
            <Cell fixedWidth={160} alignRight>
              {trans('request.sum-amount')}
            </Cell>
            <Cell fixedWidth={130}>{trans('request.applicant')}</Cell>
            <Cell fixedWidth={130}>{trans('request.acceptor')}</Cell>
            <Cell fixedWidth={115}>{trans('request.status')}</Cell>
            <Cell fixedWidth={95} style={{ alignSelf: 'center' }}>
              {trans('global.company_code')}
            </Cell>
          </Row>
        </THead>
        <TBody isLoading={isLoading}>
          {requests.map((request, i) => {
            const acceptors = request.acceptors
              ? request.acceptors.map((item, index) => {
                  return (
                    <div key={index}>
                      {item.first_name} {item.last_name}
                    </div>
                  );
                })
              : null;

            return (
              <Row
                className='request-list__table'
                key={i}
                href={getRouteByName('main', `${request.type}RequestShow`, { id: request.slug })}
              >
                <Cell fixedWidth={120}>
                  {request['sent_at'] && (
                    <span>
                      {request['sent_at'] ? moment(request['sent_at']).format('YYYY-MM-DD') : ''}
                    </span>
                  )}
                  {!request['sent_at'] && <span>---</span>}
                </Cell>
                <Cell />
                <Cell fixedWidth={'auto'} className='request-list__column-name'>
                  <TruncateBreakable text={request.name} lines={1} />
                </Cell>
                <Cell fixedWidth={140} alignRight>
                  <div className='is-allign-end'>
                    <AmountFormatter amount={request['requestElementsAccountedSumAmount']} />{' '}
                    {request['requestElementsAccountedSumCurrency']}
                  </div>
                </Cell>
                <Cell fixedWidth={160} alignRight>
                  <div className='is-allign-end'>
                    <AmountFormatter amount={request['requestElementsSumAmount']} />{' '}
                    {request['requestElementsSumCurrency']}
                  </div>
                </Cell>
                <Cell fixedWidth={130}>
                  {request['user']
                    ? `${request['user']['first_name']} ${request['user']['last_name']}`
                    : ''}
                </Cell>
                <Cell fixedWidth={130}>{acceptors}</Cell>
                <Cell fixedWidth={125}>{trans(`global.request-status-${request['status']}`)}</Cell>
                <Cell fixedWidth={95} style={{ alignSelf: 'center' }}>
                  {request.company ? request.company.code : ''}
                </Cell>
              </Row>
            );
          })}
        </TBody>
      </Table>
    );
  }
}

List.propTypes = {
  requests: PropTypes.array.isRequired,
  isLoading: PropTypes.bool.isRequired,
  getRouteByName: PropTypes.func.isRequired,
  currentUser: PropTypes.object.isRequired,
  changePage: PropTypes.func.isRequired,
};
