import React from 'react'
import PropTypes from 'prop-types'
import { SmartCheckbox } from '../../../ui/Input/SmartCheckbox'
import trans from '../../../trans'

const Direct = (props) => {
  const checked = true
  return (
    <div className='train-trip__checkbox'>
      <SmartCheckbox
        checked={checked}
        onChange={(v) => props.setFilter({ attributes: { direct: v } })}
        label={trans('trains-booking.direct')}
      />
    </div>
  )
}

Direct.propTypes = {}

export default Direct
export { Direct }
