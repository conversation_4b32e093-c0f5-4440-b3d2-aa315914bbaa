import React from 'react'
import { getFormSubmitErrors, reduxForm, SubmissionError } from 'redux-form/immutable'
import { connect } from 'react-redux'
import { fromJS } from 'immutable'
import { RentedCar as Form } from '../Forms'
import { bindActionCreators } from 'redux'
import { processAPIerrorResponseToFormErrors } from '../../../../services/APIClient'
import { prepareRequestDates } from '../../../../utils/prepareRequestDates'
import { Factory as ElementFactory } from '../../../../models/timeline/index'
import { getFormValues } from '../../../../utils/forms'
import { DateSuggester } from '../../../../store/app/trip-timeline/services/date-suggester'
import { getCurrency } from '../../../../store/app/instance'
import { LocationSuggester } from '../../../../store/app/trip-timeline/services/location-suggester'
import { setEndOfDay } from '../../../../utils/setEndOfDay'

class RentedCar extends React.Component<any, any> {
  render() {
    const { ...props } = this.props

    if (!this.props.initialized) {
      return null
    }

    return <Form {...props} />
  }
}

export const submit = (values, dispatch, props) => {
  const { onSave, request, change } = props

  values = prepareRequestDates(values, ['pickup_at', 'return_at'])
  return onSave(request, values, props.element, change).then(
    () => {
      dispatch(change('isOpen', false))
    },
    (alerts) => {
      throw new SubmissionError(processAPIerrorResponseToFormErrors(alerts))
    },
  )
}

export const change = (values, dispatch, props) => {
  const { change } = props
  if (values.get('return_at') < values.get('pickup_at')) {
    dispatch(change('return_at', values.get('pickup_at')))
  }
}

const withForm = reduxForm({
  enableReinitialize: true,
  keepDirtyOnReinitialize: true,
  onSubmit: submit,
  onChange: change,
  destroyOnUnmount: false,
})(RentedCar)

const mapStateToProps = (state, props) => {
  const { request, element, currencies } = props
  const car = ElementFactory.create(element)
  const formErrors = getFormSubmitErrors(car.key)

  const dateSuggester = new DateSuggester(state, car)
  const locationSuggester = new LocationSuggester(state, car)

  const instanceCurrency = getCurrency(state)
  const currentValues = getFormValues(car.key, state)

  return {
    initialValues: fromJS({
      uuid: car.uuid,
      pickup_at: car.draft ? dateSuggester.suggestStartDate() : car.getStartDate(),
      return_at: car.draft ? dateSuggester.suggestEndDate() : car.getEndDate(),
      departure_location: car.draft
        ? locationSuggester.suggestStartLocation()
        : car.getStartLocation(),
      destination_location: car.draft
        ? locationSuggester.suggestEndLocation()
        : car.getEndLocation(),
      rent_cost: car.rent_cost,
      rent_cost_currency: !car.draft ? car.rent_cost_currency : instanceCurrency,
      fuel_cost: car.fuel_cost,
      fuel_cost_currency: !car.draft ? car.fuel_cost_currency : instanceCurrency,
      other_costs_amount: car.other_costs_amount,
      other_costs_currency: !car.draft ? car.other_costs_currency : instanceCurrency,
      amount: car.amount,
      amount_currency: !car.draft ? car.amount_currency : instanceCurrency,
      id: car.id,
      type: car.type,
      converted_amount: car.converted_amount,
      calculated_amount_currency: car.calculated_amount_currency
        ? car.calculated_amount_currency
        : instanceCurrency,
      isOpen: car.isOpen,
      draft: car.draft,
      virtual: car.virtual,
      order: car.order,
      order_car_types: car.order_car_types,
      order_car_additional_equipment: car.order_car_additional_equipment,
      order_car_other_requirement: car.order_car_other_requirement,
    }),
    formErrors: formErrors(state).toJS(),
    form: car.key,
    data: currentValues,
    request,
    car: ElementFactory.create(getFormValues(car.key, state)),
    currencies,
    minDate: dateSuggester.suggestMinDate(),
    maxDate: dateSuggester.suggestMaxDate(),
    maxStartDate: dateSuggester.suggestMaxStartDate(),
    instanceCurrency,
  }
}

const mapDispatchToProps = (dispatch) => {
  return bindActionCreators({}, dispatch)
}

const connected = connect(mapStateToProps, mapDispatchToProps)(withForm)

RentedCar = connected

export { RentedCar }
export default { RentedCar }
