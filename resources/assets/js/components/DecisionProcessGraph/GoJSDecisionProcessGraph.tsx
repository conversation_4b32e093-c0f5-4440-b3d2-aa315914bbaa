import React, { useCallback, useEffect, useRef, useState } from 'react'
import * as go from 'gojs'
import { Panel as UIPanel, PanelContent, PanelTitle } from '../ui/Panel'

interface DecisionNode {
  id: string
  label: string
  type: string
  processId: number
  processSlug: string
  nodeId: number
  operatorCode: string
  decisionCode: string
  decisionParameters: any
  order: number
  description?: string
  conditions?: Condition[]
  group: number
  instanceId?: number
  isDefault: boolean
}

interface Condition {
  id: number
  code: string
  parameters: any
  negate: boolean
  description: string
}

interface GraphData {
  nodes: DecisionNode[]
  edges: any[]
  processGroups: any[]
  metadata: {
    totalProcesses: number
    totalNodes: number
    totalEdges: number
    currentInstanceId: number
    currentInstanceName: string
    generatedAt: string
  }
}

interface GoJSDecisionProcessGraphProps {
  processSlug?: string
  width?: number
  height?: number
}

const GoJSDecisionProcessGraph: React.FC<GoJSDecisionProcessGraphProps> = ({
  processSlug,
  width = 1200,
  height = 800,
}) => {
  const diagramRef = useRef<HTMLDivElement>(null)
  const diagramInstanceRef = useRef<go.Diagram | null>(null)
  const [graphData, setGraphData] = useState<GraphData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedNode, setSelectedNode] = useState<DecisionNode | null>(null)

  const createMockData = (): GraphData => {
    return {
      nodes: [
        {
          id: 'process_1_node_1',
          label: 'Check User Status',
          type: 'decision_node',
          processId: 1,
          processSlug: 'user-validation',
          nodeId: 1,
          operatorCode: 'and',
          decisionCode: 'VALIDATE_USER',
          decisionParameters: { min_age: 18 },
          order: 1,
          description: 'Validate user eligibility',
          conditions: [
            {
              id: 1,
              code: 'USER_AGE_ABOVE',
              parameters: { min_age: 18 },
              negate: false,
              description: 'User must be 18 or older'
            },
            {
              id: 2,
              code: 'USER_VERIFIED',
              parameters: {},
              negate: false,
              description: 'User account must be verified'
            }
          ],
          group: 1,
          instanceId: 1,
          isDefault: false
        },
        {
          id: 'process_1_node_2',
          label: 'Check Premium Status',
          type: 'decision_node',
          processId: 1,
          processSlug: 'user-validation',
          nodeId: 2,
          operatorCode: 'or',
          decisionCode: 'GRANT_PREMIUM',
          decisionParameters: {},
          order: 2,
          description: 'Check if user has premium access',
          conditions: [
            {
              id: 3,
              code: 'HAS_PREMIUM_SUBSCRIPTION',
              parameters: {},
              negate: false,
              description: 'User has active premium subscription'
            }
          ],
          group: 1,
          instanceId: 1,
          isDefault: false
        }
      ],
      edges: [
        {
          id: 'edge_1',
          source: 'process_1_node_1',
          target: 'process_1_node_2',
          type: 'flow',
          label: 'Next if conditions fail'
        }
      ],
      processGroups: [
        {
          id: 1,
          slug: 'user-validation',
          name: 'User Validation Process'
        }
      ],
      metadata: {
        totalProcesses: 1,
        totalNodes: 2,
        totalEdges: 1,
        currentInstanceId: 1,
        currentInstanceName: 'Test Instance',
        generatedAt: new Date().toISOString()
      }
    }
  }

  const fetchGraphData = async () => {
    try {
      setLoading(true)
      setError(null)

      const url = processSlug
        ? `/api/decision-processes/graph/${processSlug}`
        : '/api/decision-processes/graph'

      console.log('Fetching graph data from:', url)
      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const rawData = await response.json()
      console.log('Raw API response:', rawData)

      // If no data from API, use mock data for testing
      if (!rawData || !rawData.nodes || rawData.nodes.length === 0) {
        console.log('No data from API, using mock data for testing')
        setGraphData(createMockData())
      } else {
        setGraphData(rawData)
      }
    } catch (err) {
      console.log('API error, using mock data for testing:', err)
      setGraphData(createMockData())
    } finally {
      setLoading(false)
    }
  }

  const initializeDiagram = useCallback(() => {
    if (!diagramRef.current) return

    const $ = go.GraphObject.make

    // Create the diagram
    const diagram = $(go.Diagram, diagramRef.current, {
      'undoManager.isEnabled': true,
      layout: $(go.LayeredDigraphLayout, {
        direction: 90,
        layerSpacing: 50,
        columnSpacing: 30,
        setsPortSpots: false
      }),
      'toolManager.hoverDelay': 100,
      allowCopy: false,
      allowDelete: false,
      allowMove: true,
      hasHorizontalScrollbar: true,
      hasVerticalScrollbar: true,
    })

    // Define node templates
    diagram.nodeTemplateMap.add('Start',
      $(go.Node, 'Auto',
        $(go.Shape, 'Circle',
          { fill: '#4CAF50', stroke: '#2E7D32', strokeWidth: 2 },
          new go.Binding('fill', 'color')
        ),
        $(go.TextBlock,
          { margin: 8, stroke: 'white', font: 'bold 12px sans-serif' },
          new go.Binding('text', 'label')
        )
      )
    )

    diagram.nodeTemplateMap.add('End',
      $(go.Node, 'Auto',
        $(go.Shape, 'Circle',
          { fill: '#F44336', stroke: '#C62828', strokeWidth: 2 },
          new go.Binding('fill', 'color')
        ),
        $(go.TextBlock,
          { margin: 8, stroke: 'white', font: 'bold 12px sans-serif' },
          new go.Binding('text', 'label')
        )
      )
    )

    diagram.nodeTemplateMap.add('Decision',
      $(go.Node, 'Auto',
        $(go.Shape, 'Diamond',
          { fill: '#FF9800', stroke: '#E65100', strokeWidth: 2, minSize: new go.Size(120, 80) },
          new go.Binding('fill', 'color')
        ),
        $(go.Panel, 'Vertical',
          { margin: 8 },
          $(go.TextBlock,
            {
              stroke: 'white',
              font: 'bold 10px sans-serif',
              wrap: go.TextBlock.WrapFit,
              maxSize: new go.Size(100, NaN),
              margin: new go.Margin(0, 0, 4, 0)
            },
            new go.Binding('text', 'label')
          ),
          $(go.TextBlock,
            {
              stroke: 'white',
              font: '8px sans-serif',
              wrap: go.TextBlock.WrapFit,
              maxSize: new go.Size(100, NaN),
              opacity: 0.9
            },
            new go.Binding('text', 'conditionsText')
          )
        )
      )
    )

    diagram.nodeTemplateMap.add('Action',
      $(go.Node, 'Auto',
        $(go.Shape, 'RoundedRectangle',
          { fill: '#2196F3', stroke: '#0D47A1', strokeWidth: 2, minSize: new go.Size(100, 40) },
          new go.Binding('fill', 'color')
        ),
        $(go.TextBlock,
          { 
            margin: 8, 
            stroke: 'white', 
            font: 'bold 10px sans-serif',
            wrap: go.TextBlock.WrapFit,
            maxSize: new go.Size(90, NaN)
          },
          new go.Binding('text', 'label')
        )
      )
    )

    // Default node template
    diagram.nodeTemplate =
      $(go.Node, 'Auto',
        $(go.Shape, 'RoundedRectangle',
          { fill: '#9C27B0', stroke: '#4A148C', strokeWidth: 2, minSize: new go.Size(100, 40) },
          new go.Binding('fill', 'color')
        ),
        $(go.TextBlock,
          { 
            margin: 8, 
            stroke: 'white', 
            font: 'bold 10px sans-serif',
            wrap: go.TextBlock.WrapFit,
            maxSize: new go.Size(90, NaN)
          },
          new go.Binding('text', 'label')
        )
      )

    // Define link templates
    diagram.linkTemplate =
      $(go.Link,
        { routing: go.Link.AvoidsNodes, corner: 5 },
        $(go.Shape,
          { strokeWidth: 2, stroke: '#666' }
        ),
        $(go.Shape,
          { toArrow: 'Standard', fill: '#666', stroke: null }
        ),
        $(go.TextBlock,
          {
            segmentIndex: 0,
            segmentOffset: new go.Point(NaN, NaN),
            segmentOrientation: go.Link.OrientUpright,
            font: '9px sans-serif',
            stroke: '#333',
            background: 'white',
            margin: 2
          },
          new go.Binding('text', 'label')
        )
      )

    // Success link template (when conditions pass)
    diagram.linkTemplateMap.add('success',
      $(go.Link,
        { routing: go.Link.AvoidsNodes, corner: 5 },
        $(go.Shape,
          { strokeWidth: 3, stroke: '#4CAF50' }
        ),
        $(go.Shape,
          { toArrow: 'Standard', fill: '#4CAF50', stroke: null }
        ),
        $(go.TextBlock,
          {
            segmentIndex: 0,
            segmentOffset: new go.Point(NaN, NaN),
            segmentOrientation: go.Link.OrientUpright,
            font: 'bold 9px sans-serif',
            stroke: '#2E7D32',
            background: 'white',
            margin: 2
          },
          new go.Binding('text', 'label')
        )
      )
    )

    // Failure link template (when conditions fail)
    diagram.linkTemplateMap.add('failure',
      $(go.Link,
        { routing: go.Link.AvoidsNodes, corner: 5 },
        $(go.Shape,
          { strokeWidth: 2, stroke: '#F44336', strokeDashArray: [5, 5] }
        ),
        $(go.Shape,
          { toArrow: 'Standard', fill: '#F44336', stroke: null }
        ),
        $(go.TextBlock,
          {
            segmentIndex: 0,
            segmentOffset: new go.Point(NaN, NaN),
            segmentOrientation: go.Link.OrientUpright,
            font: '9px sans-serif',
            stroke: '#C62828',
            background: 'white',
            margin: 2
          },
          new go.Binding('text', 'label')
        )
      )
    )

    // Add selection changed listener
    diagram.addDiagramListener('ObjectSingleClicked', (e) => {
      const part = e.subject.part
      if (part instanceof go.Node) {
        const nodeData = part.data
        console.log('Node clicked:', nodeData)
        setSelectedNode(nodeData.originalNode || nodeData)
      }
    })

    diagramInstanceRef.current = diagram
    return diagram
  }, [])

  const transformDataForGoJS = useCallback((data: GraphData) => {
    console.log('Transforming data for GoJS:', data)

    if (!data || !data.nodes || data.nodes.length === 0) {
      console.log('No data or nodes found')
      return { nodeDataArray: [], linkDataArray: [] }
    }

    // Group nodes by process and sort by order
    const processesBySlug = data.nodes.reduce((acc, node) => {
      if (!acc[node.processSlug]) {
        acc[node.processSlug] = []
      }
      acc[node.processSlug].push(node)
      return acc
    }, {} as Record<string, DecisionNode[]>)

    console.log('Processes by slug:', processesBySlug)

    // Sort nodes within each process by order
    Object.keys(processesBySlug).forEach(slug => {
      processesBySlug[slug].sort((a, b) => (a.order || 0) - (b.order || 0))
    })

    const nodeDataArray: any[] = []
    const linkDataArray: any[] = []

    // Add start node for each process
    Object.entries(processesBySlug).forEach(([processSlug, nodes], processIndex) => {
      const startNodeKey = `start_${processSlug}`
      nodeDataArray.push({
        key: startNodeKey,
        label: `Start: ${processSlug.replace(/-/g, ' ')}`,
        category: 'Start',
        color: '#4CAF50',
        processSlug: processSlug,
        order: -1
      })

      // Transform process nodes
      nodes.forEach((node, nodeIndex) => {
        let category = 'Action'
        let color = '#2196F3'
        let conditionsText = ''

        // Determine node type based on conditions
        if (node.conditions && node.conditions.length > 0) {
          category = 'Decision'
          color = '#FF9800'

          // Create conditions text for display
          const conditionTexts = node.conditions.map(condition => {
            const negateText = condition.negate ? 'NOT ' : ''
            return `${negateText}${condition.code}`
          })

          if (conditionTexts.length === 1) {
            conditionsText = conditionTexts[0]
          } else {
            const operator = node.operatorCode?.toUpperCase() || 'AND'
            conditionsText = conditionTexts.join(` ${operator} `)
          }

          // Truncate if too long
          if (conditionsText.length > 40) {
            conditionsText = conditionsText.substring(0, 37) + '...'
          }
        }

        nodeDataArray.push({
          key: node.id,
          label: node.decisionCode || node.label,
          category: category,
          color: color,
          originalNode: node,
          conditionCount: node.conditions?.length || 0,
          conditionsText: conditionsText,
          description: node.description || '',
          processSlug: node.processSlug,
          order: node.order
        })

        // Create flow links within process (sequential flow)
        if (nodeIndex === 0) {
          // Link start to first node
          linkDataArray.push({
            from: startNodeKey,
            to: node.id,
            label: '',
            category: 'flow'
          })
        } else {
          // Link previous node to current node (if conditions fail)
          const previousNode = nodes[nodeIndex - 1]
          const label = previousNode.conditions && previousNode.conditions.length > 0 ? 'NO' : ''
          linkDataArray.push({
            from: previousNode.id,
            to: node.id,
            label: label,
            category: 'failure'
          })
        }

        // Add success path for nodes with conditions
        if (node.conditions && node.conditions.length > 0) {
          // Create a virtual "success" node or link to end
          const successNodeKey = `success_${node.id}`
          nodeDataArray.push({
            key: successNodeKey,
            label: `Execute: ${node.decisionCode}`,
            category: 'Action',
            color: '#4CAF50',
            processSlug: node.processSlug,
            order: node.order + 0.5
          })

          linkDataArray.push({
            from: node.id,
            to: successNodeKey,
            label: 'YES',
            category: 'success'
          })
        }
      })

      // Add end node for process
      if (nodes.length > 0) {
        const endNodeKey = `end_${processSlug}`
        const lastNode = nodes[nodes.length - 1]

        nodeDataArray.push({
          key: endNodeKey,
          label: `End: ${processSlug.replace(/-/g, ' ')}`,
          category: 'End',
          color: '#F44336',
          processSlug: processSlug,
          order: 999
        })

        linkDataArray.push({
          from: lastNode.id,
          to: endNodeKey,
          label: 'Complete',
          category: 'flow'
        })
      }
    })

    // Add existing edges from API
    data.edges.forEach(edge => {
      linkDataArray.push({
        from: edge.source,
        to: edge.target,
        label: edge.label || '',
        category: edge.type || 'flow'
      })
    })

    console.log('Transformed nodeDataArray:', nodeDataArray)
    console.log('Transformed linkDataArray:', linkDataArray)

    return { nodeDataArray, linkDataArray }
  }, [])

  const updateDiagram = useCallback((data: GraphData) => {
    if (!diagramInstanceRef.current || !data) return

    const transformedData = transformDataForGoJS(data)
    
    diagramInstanceRef.current.model = new go.GraphLinksModel(
      transformedData.nodeDataArray,
      transformedData.linkDataArray
    )
  }, [transformDataForGoJS])

  useEffect(() => {
    fetchGraphData()
  }, [processSlug])

  useEffect(() => {
    if (!diagramInstanceRef.current) {
      initializeDiagram()
    }
  }, [initializeDiagram])

  useEffect(() => {
    if (graphData && diagramInstanceRef.current) {
      updateDiagram(graphData)
    }
  }, [graphData, updateDiagram])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (diagramInstanceRef.current) {
        diagramInstanceRef.current.div = null
      }
    }
  }, [])

  if (loading) {
    return (
      <UIPanel>
        <PanelContent>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            Loading graph...
          </div>
        </PanelContent>
      </UIPanel>
    )
  }

  if (error) {
    return (
      <UIPanel>
        <PanelContent>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <h4 style={{ color: '#dc3545' }}>Error loading graph</h4>
            <p>{error}</p>
            <button onClick={fetchGraphData} style={{ padding: '0.5rem 1rem' }}>
              Retry
            </button>
          </div>
        </PanelContent>
      </UIPanel>
    )
  }

  if (!graphData || !graphData.nodes || graphData.nodes.length === 0) {
    return (
      <UIPanel>
        <PanelContent>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <h4>No Decision Processes Found</h4>
            <p>No decision processes are available to display.</p>
            {graphData && (
              <div style={{ marginTop: '1rem', textAlign: 'left', backgroundColor: '#f8f9fa', padding: '1rem', borderRadius: '4px' }}>
                <h5>Debug Information:</h5>
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {JSON.stringify(graphData, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </PanelContent>
      </UIPanel>
    )
  }

  return (
    <div style={{ display: 'flex', height: `${height}px` }}>
      {/* Main Graph */}
      <div style={{ flex: 1, border: '1px solid #ddd', position: 'relative' }}>
        <div
          ref={diagramRef}
          style={{ width: '100%', height: '100%', backgroundColor: '#f8f9fa' }}
        />
        
        {/* Info Panel */}
        {graphData && (
          <div style={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            background: 'white',
            padding: '10px',
            borderRadius: '5px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            fontSize: '12px'
          }}>
            <h4 style={{ margin: '0 0 5px 0' }}>Decision Process Flowchart</h4>
            <div style={{ color: '#666' }}>
              <div>Instance: {graphData.metadata.currentInstanceName}</div>
              <div>Processes: {graphData.metadata.totalProcesses}</div>
              <div>Nodes: {graphData.metadata.totalNodes}</div>
              <div>Edges: {graphData.metadata.totalEdges}</div>
            </div>
          </div>
        )}
      </div>

      {/* Side Panel for Node Details */}
      {selectedNode && (
        <div style={{
          width: '300px',
          borderLeft: '1px solid #ddd',
          backgroundColor: '#f8f9fa',
          padding: '1rem',
          overflowY: 'auto'
        }}>
          <h4>Node Details</h4>
          <div style={{ marginBottom: '1rem' }}>
            <strong>Decision Code:</strong> {selectedNode.decisionCode}
          </div>
          <div style={{ marginBottom: '1rem' }}>
            <strong>Operator:</strong> {selectedNode.operatorCode}
          </div>
          <div style={{ marginBottom: '1rem' }}>
            <strong>Order:</strong> {selectedNode.order}
          </div>
          {selectedNode.description && (
            <div style={{ marginBottom: '1rem' }}>
              <strong>Description:</strong> {selectedNode.description}
            </div>
          )}
          {selectedNode.conditions && selectedNode.conditions.length > 0 && (
            <div>
              <strong>Decision Logic:</strong>
              <div style={{
                marginTop: '0.5rem',
                padding: '0.5rem',
                backgroundColor: '#fff3cd',
                borderRadius: '4px',
                border: '1px solid #ffeaa7'
              }}>
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Operator:</strong> {selectedNode.operatorCode?.toUpperCase() || 'AND'}
                </div>
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>If conditions are TRUE:</strong>
                  <div style={{ color: '#28a745', fontWeight: 'bold', marginLeft: '1rem' }}>
                    ✓ Execute "{selectedNode.decisionCode}" and EXIT process
                  </div>
                </div>
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>If conditions are FALSE:</strong>
                  <div style={{ color: '#dc3545', fontWeight: 'bold', marginLeft: '1rem' }}>
                    ✗ Continue to next node in sequence
                  </div>
                </div>
              </div>

              <div style={{ marginTop: '1rem' }}>
                <strong>Conditions ({selectedNode.conditions.length}):</strong>
                <ul style={{ marginTop: '0.5rem', paddingLeft: '1.5rem' }}>
                  {selectedNode.conditions.map((condition, index) => (
                    <li key={condition.id} style={{ marginBottom: '0.5rem' }}>
                      <div>
                        <strong>{condition.negate ? 'NOT ' : ''}{condition.code}</strong>
                      </div>
                      {condition.description && (
                        <div style={{ fontSize: '0.9em', color: '#666' }}>
                          {condition.description}
                        </div>
                      )}
                      {condition.parameters && Object.keys(condition.parameters).length > 0 && (
                        <div style={{ fontSize: '0.8em', color: '#888', marginTop: '0.2rem' }}>
                          Parameters: {JSON.stringify(condition.parameters)}
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default GoJSDecisionProcessGraph
