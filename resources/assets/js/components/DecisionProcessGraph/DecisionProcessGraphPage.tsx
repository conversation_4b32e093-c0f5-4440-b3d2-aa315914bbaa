import React, { useState } from 'react';
import GoJSDecisionProcessGraph from './GoJSDecisionProcessGraph';
import { Section } from '../ui/Section';
import { Panel, PanelContent, PanelTitle } from '../ui/Panel';
import { Col, Row } from '../../ui/Grid';

const DecisionProcessGraphPage: React.FC = () => {
  const [selectedProcess, setSelectedProcess] = useState<string>('');
  const [availableProcesses] = useState([
    'document-element-accounting-accounts-strategy-selection',
    'access-lump-sum-accounting-accounts-strategy-selection',
    'travel-expense-accounting-accounts-strategy-selection',
    'technical-accounting-accounts-strategy-selection',
    'provider-accounting-accounts-strategy-selection',
    'mileage-allowance-accounting-accounts-strategy-selection',
    'drive-lump-sum-accounting-accounts-strategy-selection',
    'accommodation-lum-sum-accounting-accounts-strategy-selection',
    'foreign-travel-allowance-estimation-strategy-selection',
    'foreign-travel-allowance-strategy-selection',
    'document-exchange-rate-strategy-selection',
    'settlement-acceptor-strategy-selection',
    'request-acceptor-strategy-selection',
  ]);

  return (
    <Section>
      <Row>
        <Col xs={12}>
          <Panel>
            <PanelTitle>
              <h2>Decision Process Flow Visualization</h2>
            </PanelTitle>
            <PanelContent>

              <div className='controls' style={{ marginBottom: '1rem' }}>
                <label htmlFor='process-select' style={{ marginRight: '0.5rem' }}>
                  Select Process:
                </label>
                <select
                  id='process-select'
                  value={selectedProcess}
                  onChange={(e) => setSelectedProcess(e.target.value)}
                  style={{
                    padding: '0.5rem',
                    borderRadius: '4px',
                    border: '1px solid #ddd',
                    marginRight: '1rem',
                  }}
                >
                  <option value=''>All Processes</option>
                  {availableProcesses.map((process) => (
                    <option key={process} value={process}>
                      {process.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>

                <button
                  onClick={() => setSelectedProcess('')}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                  }}
                >
                  Show All
                </button>
              </div>

              <div
                className='graph-info'
                style={{
                  marginBottom: '1rem',
                  padding: '1rem',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                }}
              >
                <h4>Decision Process Flowchart Legend:</h4>
                <div style={{ margin: '0.5rem 0', display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '0.5rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.3rem' }}>
                    <div style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      backgroundColor: '#4CAF50',
                      marginRight: '0.5rem',
                      border: '2px solid #2E7D32'
                    }}></div>
                    <span><strong>Start</strong> - Process entry point</span>
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.3rem' }}>
                    <div style={{
                      width: '20px',
                      height: '20px',
                      backgroundColor: '#FF9800',
                      marginRight: '0.5rem',
                      transform: 'rotate(45deg)',
                      border: '2px solid #E65100'
                    }}></div>
                    <span><strong>Decision</strong> - Conditions to evaluate</span>
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.3rem' }}>
                    <div style={{
                      width: '20px',
                      height: '15px',
                      backgroundColor: '#2196F3',
                      marginRight: '0.5rem',
                      borderRadius: '3px',
                      border: '2px solid #0D47A1'
                    }}></div>
                    <span><strong>Action</strong> - Execute decision</span>
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.3rem' }}>
                    <div style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      backgroundColor: '#F44336',
                      marginRight: '0.5rem',
                      border: '2px solid #C62828'
                    }}></div>
                    <span><strong>End</strong> - Process completion</span>
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.3rem' }}>
                    <div style={{
                      width: '30px',
                      height: '3px',
                      backgroundColor: '#4CAF50',
                      marginRight: '0.5rem',
                      position: 'relative'
                    }}>
                      <div style={{
                        position: 'absolute',
                        right: '-5px',
                        top: '-3px',
                        width: '0',
                        height: '0',
                        borderLeft: '6px solid #4CAF50',
                        borderTop: '4px solid transparent',
                        borderBottom: '4px solid transparent'
                      }}></div>
                    </div>
                    <span><strong>YES</strong> - Conditions pass</span>
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.3rem' }}>
                    <div style={{
                      width: '30px',
                      height: '3px',
                      backgroundColor: '#F44336',
                      marginRight: '0.5rem',
                      backgroundImage: 'repeating-linear-gradient(90deg, transparent, transparent 3px, white 3px, white 6px)',
                      position: 'relative'
                    }}>
                      <div style={{
                        position: 'absolute',
                        right: '-5px',
                        top: '-3px',
                        width: '0',
                        height: '0',
                        borderLeft: '6px solid #F44336',
                        borderTop: '4px solid transparent',
                        borderBottom: '4px solid transparent'
                      }}></div>
                    </div>
                    <span><strong>NO</strong> - Conditions fail</span>
                  </div>
                </div>

                <div style={{ marginTop: '1rem', fontSize: '0.9em', color: '#666' }}>
                  <strong>How to use:</strong> Click nodes for details • Pan and zoom to navigate • Follow YES/NO paths to understand flow
                </div>
              </div>
            </PanelContent>
          </Panel>
        </Col>
      </Row>

      <Row>
        <Col xs={12}>
          {useReactFlow ? (
            <ReactFlowGraph
              processSlug={selectedProcess || undefined}
              width={1200}
              height={800}
            />
          ) : (
            <GoJSDecisionProcessGraph
              processSlug={selectedProcess || undefined}
              width={1200}
              height={800}
            />
          )}
        </Col>
      </Row>
    </Section>
  );
};

export default DecisionProcessGraphPage;
