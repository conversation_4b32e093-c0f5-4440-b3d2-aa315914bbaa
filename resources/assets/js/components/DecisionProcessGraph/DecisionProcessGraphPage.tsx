import React, { useState } from 'react'
import DecisionProcessGraph from './DecisionProcessGraph'
import { Section } from '../ui/Section'
import { Panel, PanelContent, PanelTitle } from '../ui/Panel'
import { Col, Row } from '../../ui/Grid'

const DecisionProcessGraphPage: React.FC = () => {
  const [selectedProcess, setSelectedProcess] = useState<string>('')
  const [availableProcesses] = useState([
    'REQUEST_ACCEPTOR_STRATEGY_SELECTION',
    'SETTLEMENT_ACCEPTOR_STRATEGY_SELECTION',
    'DOCUMENT_EXCHANGE_RATE_STRATEGY_SELECTION',
    'FOREIGN_TRAVEL_ALLOWANCE_STRATEGY_SELECTION',
    'FOREIGN_TRAVEL_ALLOWANCE_ESTIMATION_STRATEGY_SELECTION',
    'DOCUMENT_ELEMENT_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION'
  ])

  return (
    <Section>
      <Row>
        <Col xs={12}>
          <Panel>
            <PanelTitle>
              <h2>Decision Process Flow Visualization</h2>
            </PanelTitle>
            <PanelContent>
              <div className="controls" style={{ marginBottom: '1rem' }}>
                <label htmlFor="process-select" style={{ marginRight: '0.5rem' }}>
                  Select Process:
                </label>
                <select
                  id="process-select"
                  value={selectedProcess}
                  onChange={(e) => setSelectedProcess(e.target.value)}
                  style={{
                    padding: '0.5rem',
                    borderRadius: '4px',
                    border: '1px solid #ddd',
                    marginRight: '1rem'
                  }}
                >
                  <option value="">All Processes</option>
                  {availableProcesses.map(process => (
                    <option key={process} value={process}>
                      {process.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>
                
                <button
                  onClick={() => setSelectedProcess('')}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  Show All
                </button>
              </div>
              
              <div className="graph-info" style={{ 
                marginBottom: '1rem', 
                padding: '1rem', 
                backgroundColor: '#f8f9fa', 
                borderRadius: '4px' 
              }}>
                <h4>How to use this graph:</h4>
                <ul style={{ margin: '0.5rem 0', paddingLeft: '1.5rem' }}>
                  <li><strong>Nodes</strong> represent decision process nodes with their decision codes</li>
                  <li><strong>Colored circles</strong> group nodes by process</li>
                  <li><strong>Small circles</strong> on nodes show condition count (red = has conditions, green = no conditions)</li>
                  <li><strong>Arrows</strong> show the flow between nodes (next if conditions fail)</li>
                  <li><strong>Click nodes</strong> to see detailed information including conditions</li>
                  <li><strong>Drag nodes</strong> to rearrange the layout</li>
                  <li><strong>Zoom and pan</strong> to navigate large graphs</li>
                </ul>
              </div>
            </PanelContent>
          </Panel>
        </Col>
      </Row>
      
      <Row>
        <Col xs={12}>
          <DecisionProcessGraph 
            processSlug={selectedProcess || undefined}
            width={1200}
            height={800}
          />
        </Col>
      </Row>
    </Section>
  )
}

export default DecisionProcessGraphPage
