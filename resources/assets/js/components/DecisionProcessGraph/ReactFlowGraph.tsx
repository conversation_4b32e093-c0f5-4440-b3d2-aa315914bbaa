import React, { useCallback, useEffect, useState } from 'react'
import React<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  Panel,
} from 'reactflow'
import 'reactflow/dist/style.css'

interface DecisionNode {
  id: string
  label: string
  type: string
  processId: number
  processSlug: string
  nodeId: number
  operatorCode: string
  decisionCode: string
  decisionParameters: any
  order: number
  description?: string
  conditions?: Condition[]
  group: number
  instanceId?: number
  isDefault: boolean
}

interface Condition {
  id: number
  code: string
  parameters: any
  negate: boolean
  description: string
}

interface GraphData {
  nodes: DecisionNode[]
  edges: any[]
  processGroups: any[]
  metadata: {
    totalProcesses: number
    totalNodes: number
    totalEdges: number
    currentInstanceId: number
    currentInstanceName: string
    generatedAt: string
  }
}

interface ReactFlowGraphProps {
  processSlug?: string
  width?: number
  height?: number
}

const ReactFlowGraph: React.FC<ReactFlowGraphProps> = ({
  processSlug,
  width = 1200,
  height = 800,
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [graphData, setGraphData] = useState<GraphData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedNode, setSelectedNode] = useState<DecisionNode | null>(null)

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const fetchGraphData = async () => {
    try {
      setLoading(true)
      setError(null)

      const url = processSlug
        ? `/api/decision-processes/graph/${processSlug}`
        : '/api/decision-processes/graph'

      console.log('ReactFlow: Fetching graph data from:', url)
      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const rawData = await response.json()
      console.log('ReactFlow: Raw API response:', rawData)
      setGraphData(rawData)
      
      // Transform data to React Flow format
      transformToReactFlow(rawData)
    } catch (err) {
      console.error('ReactFlow: Error fetching data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch graph data')
    } finally {
      setLoading(false)
    }
  }

  const transformToReactFlow = (data: GraphData) => {
    console.log('ReactFlow: Transform input data:', data)
    
    if (!data || !data.nodes) {
      console.log('ReactFlow: No nodes in data:', data)
      setNodes([])
      setEdges([])
      return
    }
    
    console.log('ReactFlow: Found nodes:', data.nodes.length)
    if (data.nodes.length > 0) {
      console.log('ReactFlow: First node:', data.nodes[0])
    }

    // Create color mapping for process groups
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff']
    const processColors: { [key: number]: string } = {}
    
    if (data.processGroups) {
      data.processGroups.forEach((group, index) => {
        processColors[group.id] = colors[index % colors.length]
      })
    }

    // Transform nodes
    const reactFlowNodes: Node[] = data.nodes.map((node, index) => {
      const color = processColors[node.group] || '#95a5a6'
      const conditionCount = node.conditions?.length || 0
      
      const reactFlowNode: Node = {
        id: node.id,
        type: 'default',
        position: { 
          x: (index % 3) * 300 + Math.random() * 100, 
          y: Math.floor(index / 3) * 200 + Math.random() * 50 
        },
        data: {
          label: (
            <div style={{ textAlign: 'center', padding: '8px' }}>
              <div style={{ fontWeight: 'bold', fontSize: '12px' }}>
                {node.decisionCode || 'Unknown Decision'}
              </div>
              <div style={{ fontSize: '10px', color: '#666' }}>
                {node.processSlug || 'Unknown Process'}
              </div>
              <div style={{ 
                fontSize: '8px', 
                backgroundColor: conditionCount > 0 ? '#ff6b6b' : '#51cf66',
                color: 'white',
                borderRadius: '10px',
                padding: '2px 6px',
                marginTop: '4px',
                display: 'inline-block'
              }}>
                {conditionCount} conditions
              </div>
            </div>
          ),
          originalNode: node
        },
        style: {
          background: color,
          color: 'white',
          border: '2px solid #fff',
          borderRadius: '8px',
          fontSize: '12px',
          width: 200,
          height: 100,
        },
      }
      
      console.log(`ReactFlow: Created node ${index}:`, reactFlowNode)
      return reactFlowNode
    })

    // Transform edges
    const reactFlowEdges: Edge[] = data.edges?.map((edge) => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      type: 'smoothstep',
      animated: true,
      label: edge.label,
      style: { stroke: '#999', strokeWidth: 2 },
      labelStyle: { fontSize: '10px', fill: '#666' },
    })) || []

    console.log('ReactFlow: Transformed nodes:', reactFlowNodes)
    console.log('ReactFlow: Transformed edges:', reactFlowEdges)

    setNodes(reactFlowNodes)
    setEdges(reactFlowEdges)
  }

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    console.log('ReactFlow: Node clicked:', node)
    const originalNode = node.data.originalNode as DecisionNode
    console.log('ReactFlow: Original node data:', originalNode)
    setSelectedNode(originalNode)
  }, [])

  useEffect(() => {
    fetchGraphData()
  }, [processSlug])

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem', border: '1px solid #ddd', height: `${height}px` }}>
        <h4>Loading React Flow Graph...</h4>
        <p>Fetching decision process data...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem', border: '1px solid #ddd', height: `${height}px` }}>
        <h4 style={{ color: '#dc3545' }}>Error loading graph</h4>
        <p>{error}</p>
        <button onClick={fetchGraphData} style={{ padding: '0.5rem 1rem' }}>
          Retry
        </button>
      </div>
    )
  }

  return (
    <div style={{ display: 'flex', height: `${height}px`, border: '1px solid #ddd' }}>
      {/* Main Graph */}
      <div style={{ flex: 1 }}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          fitView
        >
          <Controls />
          <MiniMap />
          <Background variant={BackgroundVariant.Dots} />
          <Panel position="top-left">
            <div style={{ background: 'white', padding: '10px', borderRadius: '5px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
              <h4 style={{ margin: '0 0 5px 0' }}>Decision Process Flowchart</h4>
              {graphData && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  <div>Instance: {graphData.metadata?.currentInstanceName || 'Unknown'}</div>
                  <div>Processes: {graphData.metadata?.totalProcesses || 0}</div>
                  <div>Nodes: {graphData.metadata?.totalNodes || 0}</div>
                  <div>Edges: {graphData.metadata?.totalEdges || 0}</div>
                </div>
              )}
              {nodes.length === 0 && !loading && (
                <div style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                  No nodes to display
                </div>
              )}
            </div>
          </Panel>
        </ReactFlow>
      </div>

      {/* Side Panel for Node Details */}
      {selectedNode && (
        <div style={{ 
          width: '300px', 
          background: 'white', 
          borderLeft: '1px solid #ddd',
          padding: '1rem',
          overflowY: 'auto'
        }}>
          <h4>{selectedNode.decisionCode || 'Unknown Decision'}</h4>
          <p><strong>Process:</strong> {selectedNode.processSlug || 'Unknown'}</p>
          <p><strong>Operator:</strong> {selectedNode.operatorCode || 'Unknown'}</p>
          <p><strong>Instance:</strong> {
            selectedNode.isDefault 
              ? 'Default (All Instances)' 
              : selectedNode.instanceId 
                ? `Instance ${selectedNode.instanceId}` 
                : 'Unknown Instance'
          }</p>
          <p><strong>Order:</strong> {selectedNode.order !== undefined ? selectedNode.order : 'Unknown'}</p>
          
          {selectedNode.description && (
            <p><strong>Description:</strong> {selectedNode.description}</p>
          )}

          {selectedNode.conditions && selectedNode.conditions.length > 0 && (
            <div>
              <h5>Conditions ({selectedNode.conditions.length})</h5>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {selectedNode.conditions.map(condition => (
                  <li key={condition.id} style={{ 
                    padding: '0.5rem', 
                    margin: '0.25rem 0', 
                    background: '#f8f9fa', 
                    borderRadius: '4px',
                    fontSize: '0.8rem'
                  }}>
                    <strong>{condition.code}</strong>
                    {condition.negate && <span style={{ color: '#dc3545' }}> (NOT)</span>}
                    {condition.parameters && (
                      <div style={{ 
                        marginTop: '0.25rem', 
                        fontFamily: 'monospace', 
                        fontSize: '0.75rem', 
                        color: '#666' 
                      }}>
                        {JSON.stringify(condition.parameters)}
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <button 
            onClick={() => setSelectedNode(null)}
            style={{ 
              marginTop: '1rem',
              padding: '0.5rem 1rem',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Close
          </button>
        </div>
      )}
    </div>
  )
}

export default ReactFlowGraph
