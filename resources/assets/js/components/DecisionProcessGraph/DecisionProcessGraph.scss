.decision-process-graph {
  .graph-metadata {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: #666;
    
    span {
      padding: 0.25rem 0.5rem;
      background: #f5f5f5;
      border-radius: 4px;
    }
  }

  .graph-container {
    position: relative;
    
    svg {
      background: #fafafa;
      border-radius: 4px;
    }
  }

  .node {
    transition: all 0.2s ease;
    
    &:hover {
      circle {
        stroke-width: 3;
        filter: brightness(1.1);
      }
    }
  }

  .node-details {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 300px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 10;
    
    h4 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.1rem;
    }
    
    h5 {
      margin: 1rem 0 0.5rem 0;
      color: #555;
      font-size: 1rem;
    }
    
    p {
      margin: 0.25rem 0;
      font-size: 0.875rem;
      
      strong {
        color: #333;
      }
    }
    
    .conditions {
      margin-top: 1rem;
      
      ul {
        list-style: none;
        padding: 0;
        margin: 0.5rem 0;
        
        li {
          padding: 0.5rem;
          margin: 0.25rem 0;
          background: #f8f9fa;
          border-radius: 4px;
          font-size: 0.8rem;
          
          .negate {
            color: #dc3545;
            font-weight: bold;
          }
          
          .parameters {
            margin-top: 0.25rem;
            font-family: monospace;
            font-size: 0.75rem;
            color: #666;
            background: #e9ecef;
            padding: 0.25rem;
            border-radius: 2px;
            word-break: break-all;
          }
        }
      }
    }
    
    .btn {
      margin-top: 1rem;
    }
  }

  .error-message {
    text-align: center;
    padding: 2rem;
    
    h4 {
      color: #dc3545;
      margin-bottom: 1rem;
    }
    
    p {
      color: #666;
      margin-bottom: 1rem;
    }
  }
}

// Graph legend
.graph-legend {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h5 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #333;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    margin: 0.25rem 0;
    font-size: 0.8rem;
    
    .legend-symbol {
      width: 16px;
      height: 16px;
      margin-right: 0.5rem;
      border-radius: 50%;
      
      &.has-conditions {
        background: #ff6b6b;
      }
      
      &.no-conditions {
        background: #51cf66;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .decision-process-graph {
    .graph-container {
      svg {
        width: 100% !important;
        height: 600px !important;
      }
    }
    
    .node-details {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 90%;
      max-width: 400px;
      max-height: 80vh;
      overflow-y: auto;
    }
    
    .graph-metadata {
      flex-direction: column;
      gap: 0.5rem;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .decision-process-graph {
    .graph-container svg {
      background: #2d3748;
    }
    
    .node-details {
      background: #2d3748;
      border-color: #4a5568;
      color: #e2e8f0;
      
      h4, h5 {
        color: #e2e8f0;
      }
      
      .conditions ul li {
        background: #4a5568;
        color: #e2e8f0;
        
        .parameters {
          background: #1a202c;
          color: #a0aec0;
        }
      }
    }
    
    .graph-legend {
      background: #2d3748;
      border-color: #4a5568;
      color: #e2e8f0;
    }
  }
}
