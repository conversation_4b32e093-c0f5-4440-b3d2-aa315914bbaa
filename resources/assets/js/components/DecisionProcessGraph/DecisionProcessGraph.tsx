import React, { useEffect, useRef, useState } from 'react'
import * as d3 from 'd3'
import { Panel, PanelContent, PanelTitle } from '../ui/Panel'
import { Loader } from '../ui/LoadingOverlay/Loader'
import './DecisionProcessGraph.scss'

interface Node {
  id: string
  label: string
  type: string
  processId: number
  processSlug: string
  nodeId: number
  operatorCode: string
  decisionCode: string
  decisionParameters: any
  order: number
  description?: string
  conditions?: Condition[]
  group: number
  instanceId?: number
  isDefault: boolean
  x?: number
  y?: number
  fx?: number
  fy?: number
}

interface Edge {
  id: string
  source: string | Node
  target: string | Node
  type: string
  label: string
}

interface Condition {
  id: number
  code: string
  parameters: any
  negate: boolean
  description: string
}

interface ProcessGroup {
  id: number
  slug: string
  name: string
}

interface GraphData {
  nodes: Node[]
  edges: Edge[]
  processGroups: ProcessGroup[]
  metadata: {
    totalProcesses: number
    totalNodes: number
    totalEdges: number
    currentInstanceId: number
    currentInstanceName: string
    generatedAt: string
  }
}

interface DecisionProcessGraphProps {
  processSlug?: string
  width?: number
  height?: number
}

const DecisionProcessGraph: React.FC<DecisionProcessGraphProps> = ({
  processSlug,
  width = 1200,
  height = 800,
}) => {
  const svgRef = useRef<SVGSVGElement>(null)
  const [graphData, setGraphData] = useState<GraphData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const simulationRef = useRef<d3.Simulation<Node, Edge> | null>(null)

  useEffect(() => {
    fetchGraphData()
  }, [processSlug])

  useEffect(() => {
    return () => {
      // Cleanup simulation on unmount
      if (simulationRef.current) {
        simulationRef.current.stop()
      }
    }
  }, [])

  const validateAndNormalizeData = (data: any): GraphData => {
    // Ensure nodes have conditions array
    if (data.nodes) {
      data.nodes = data.nodes.map((node: any) => ({
        ...node,
        conditions: node.conditions || []
      }))
    }

    return data as GraphData
  }

  const fetchGraphData = async () => {
    try {
      setLoading(true)
      setError(null)

      const url = processSlug
        ? `/api/decision-processes/graph/${processSlug}`
        : '/api/decision-processes/graph'

      console.log('Fetching graph data from:', url)
      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const rawData = await response.json()
      const validatedData = validateAndNormalizeData(rawData)
      setGraphData(validatedData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch graph data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!graphData || !svgRef.current) return

    renderGraph()
  }, [graphData])

  const renderGraph = () => {
    if (!graphData || !svgRef.current || !graphData.nodes || graphData.nodes.length === 0) return

    const svg = d3.select(svgRef.current)
    svg.selectAll('*').remove()

    const container = svg.append('g')

    // Create zoom behavior
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        if (event && event.transform) {
          container.attr('transform', event.transform)
        }
      })

    svg.call(zoom)

    // Create color scale for process groups
    const colorScale = d3.scaleOrdinal(d3.schemeCategory10)

    // Stop previous simulation if it exists
    if (simulationRef.current) {
      simulationRef.current.stop()
    }

    // Create simulation
    const simulation = d3.forceSimulation<Node>(graphData.nodes)
      .force('link', d3.forceLink<Node, Edge>(graphData.edges)
        .id(d => d.id)
        .distance(150))
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(60))

    // Store simulation reference for cleanup
    simulationRef.current = simulation

    // Define drag functions
    function dragstarted(event: d3.D3DragEvent<SVGGElement, Node, Node>, d: Node) {
      if (!event) return
      if (!event.active) simulation.alphaTarget(0.3).restart()
      if (d && typeof d === 'object' && 'x' in d && 'y' in d) {
        d.fx = d.x
        d.fy = d.y
      }
      // Store initial position to detect if this was a click or drag
      event.subject.startX = event.x
      event.subject.startY = event.y
    }

    function dragged(event: d3.D3DragEvent<SVGGElement, Node, Node>, d: Node) {
      if (!event || !d || typeof d !== 'object') return
      d.fx = event.x
      d.fy = event.y
    }

    function dragended(event: d3.D3DragEvent<SVGGElement, Node, Node>, d: Node) {
      if (!event || !d || typeof d !== 'object') return
      if (!event.active) simulation.alphaTarget(0)

      // Check if this was a click (minimal movement) or actual drag
      const deltaX = Math.abs(event.x - (event.subject.startX || event.x))
      const deltaY = Math.abs(event.y - (event.subject.startY || event.y))
      const isClick = deltaX < 5 && deltaY < 5

      if (isClick) {
        console.log('Click detected on node:', d)
        setSelectedNode(d)
      }

      d.fx = null
      d.fy = null
    }

    // Create edges
    const links = container.append('g')
      .selectAll('line')
      .data(graphData.edges)
      .enter()
      .append('line')
      .attr('stroke', '#999')
      .attr('stroke-opacity', 0.6)
      .attr('stroke-width', 2)
      .attr('marker-end', 'url(#arrowhead)')

    // Create arrowhead marker
    svg.append('defs').append('marker')
      .attr('id', 'arrowhead')
      .attr('viewBox', '-0 -5 10 10')
      .attr('refX', 25)
      .attr('refY', 0)
      .attr('orient', 'auto')
      .attr('markerWidth', 6)
      .attr('markerHeight', 6)
      .attr('xoverflow', 'visible')
      .append('svg:path')
      .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
      .attr('fill', '#999')
      .style('stroke', 'none')

    // Define drag behavior
    const dragBehavior = d3.drag<SVGGElement, Node>()
      .on('start', dragstarted)
      .on('drag', dragged)
      .on('end', dragended)

    // Create nodes
    const nodes = container.append('g')
      .selectAll('g')
      .data(graphData.nodes)
      .enter()
      .append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        event.stopPropagation()
        console.log('Node group clicked:', d)
        setSelectedNode(d)
      })
      .call(dragBehavior)

    // Add circles for nodes
    nodes.append('circle')
      .attr('r', 30)
      .attr('fill', d => colorScale(d.group.toString()))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        event.stopPropagation()
        console.log('Node clicked:', d)
        setSelectedNode(d)
      })
      .on('mouseover', function(event, d) {
        d3.select(this)
          .attr('stroke-width', 4)
          .style('filter', 'brightness(1.1)')
      })
      .on('mouseout', function(event, d) {
        d3.select(this)
          .attr('stroke-width', 2)
          .style('filter', 'none')
      })

    // Add labels
    nodes.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .style('font-size', '10px')
      .style('fill', '#fff')
      .style('font-weight', 'bold')
      .text(d => d.decisionCode.substring(0, 8))

    // Add condition indicators
    nodes.append('circle')
      .attr('r', 8)
      .attr('cx', 25)
      .attr('cy', -25)
      .attr('fill', d => (d.conditions && d.conditions.length > 0) ? '#ff6b6b' : '#51cf66')
      .attr('stroke', '#fff')
      .attr('stroke-width', 1)

    nodes.append('text')
      .attr('x', 25)
      .attr('y', -25)
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .style('font-size', '8px')
      .style('fill', '#fff')
      .style('font-weight', 'bold')
      .text(d => d.conditions ? d.conditions.length : 0)

    // Update positions on simulation tick
    simulation.on('tick', () => {
      links
        .attr('x1', d => {
          const source = d.source as Node
          return source && typeof source.x === 'number' ? source.x : 0
        })
        .attr('y1', d => {
          const source = d.source as Node
          return source && typeof source.y === 'number' ? source.y : 0
        })
        .attr('x2', d => {
          const target = d.target as Node
          return target && typeof target.x === 'number' ? target.x : 0
        })
        .attr('y2', d => {
          const target = d.target as Node
          return target && typeof target.y === 'number' ? target.y : 0
        })

      nodes.attr('transform', d => {
        const x = typeof d.x === 'number' ? d.x : 0
        const y = typeof d.y === 'number' ? d.y : 0
        return `translate(${x},${y})`
      })
    })
  }

  if (loading) {
    return (
      <Panel>
        <PanelContent>
          <Loader />
        </PanelContent>
      </Panel>
    )
  }

  if (error) {
    return (
      <Panel>
        <PanelContent>
          <div className="error-message">
            <h4>Error loading graph</h4>
            <p>{error}</p>
            <button onClick={fetchGraphData} className="btn btn-primary">
              Retry
            </button>
          </div>
        </PanelContent>
      </Panel>
    )
  }

  if (!loading && (!graphData || !graphData.nodes || graphData.nodes.length === 0)) {
    return (
      <Panel>
        <PanelTitle>
          <h3>Decision Process Flow Graph</h3>
        </PanelTitle>
        <PanelContent>
          <div className="empty-state" style={{ textAlign: 'center', padding: '2rem' }}>
            <h4>No decision processes found</h4>
            <p>There are no decision processes to display in the graph.</p>
            {processSlug && (
              <p>Try selecting "All Processes" or check if the process "{processSlug}" exists.</p>
            )}
          </div>
        </PanelContent>
      </Panel>
    )
  }

  return (
    <div className="decision-process-graph">
      <Panel>
        <PanelTitle>
          <h3>Decision Process Flow Graph</h3>
          {graphData && (
            <div className="graph-metadata">
              <span>Instance: {graphData.metadata.currentInstanceName}</span>
              <span>Processes: {graphData.metadata.totalProcesses}</span>
              <span>Nodes: {graphData.metadata.totalNodes}</span>
              <span>Edges: {graphData.metadata.totalEdges}</span>
              {selectedNode && <span style={{color: 'red'}}>Selected: {selectedNode.decisionCode}</span>}
            </div>
          )}
        </PanelTitle>
        <PanelContent>
          <div className="graph-container">
            <svg
              ref={svgRef}
              width={width}
              height={height}
              style={{ border: '1px solid #ddd' }}
            />
            
            {selectedNode && (
              <div className="node-details">
                <h4>{selectedNode.decisionCode}</h4>
                <p><strong>Process:</strong> {selectedNode.processSlug}</p>
                <p><strong>Operator:</strong> {selectedNode.operatorCode}</p>
                <p><strong>Instance:</strong> {selectedNode.isDefault ? 'Default (All Instances)' : `Instance ${selectedNode.instanceId}`}</p>
                {selectedNode.description && (
                  <p><strong>Description:</strong> {selectedNode.description}</p>
                )}

                {selectedNode.conditions && selectedNode.conditions.length > 0 && (
                  <div className="conditions">
                    <h5>Conditions ({selectedNode.conditions.length})</h5>
                    <ul>
                      {selectedNode.conditions.map(condition => (
                        <li key={condition.id}>
                          <strong>{condition.code}</strong>
                          {condition.negate && <span className="negate"> (NOT)</span>}
                          {condition.parameters && (
                            <div className="parameters">
                              {JSON.stringify(condition.parameters)}
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <button
                  onClick={() => setSelectedNode(null)}
                  className="btn btn-sm btn-secondary"
                >
                  Close
                </button>
              </div>
            )}
          </div>
        </PanelContent>
      </Panel>
    </div>
  )
}

export default DecisionProcessGraph
