import React, { useCallback, useState } from 'react';

import { CellClickedEvent } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import { useParams } from 'react-router-dom';

import trans from '../../trans';
import { IAccountPayment } from '../../types/account';
import { Col, Radio } from '../../ui';
import AgGridLoadingOverlay from '../AgGrid/LoadingOverlay';
import { ExpenseRequestActions } from '../ExpenseRequestAccountingPage/ExpenseRequestActions';
import Pagination from '../SettingsPage/CompanyPage/Pagination/Pagination';
import { Row } from '../ui/AccordionTable';
import Button from '../ui/ButtonComponent';

import { useGridOptions } from './hooks/useGridOptions';
import { useMyCardExport } from './hooks/useMyCardExport';

function AccountsPaymentsPageTable({ data, paginator, setPage, refresh, loading = false }) {
  const [selectedEntries, setSelectedEntries] = useState<IAccountPayment[]>([]);
  const [action, setAction] = useState<string>();
  const params = useParams<{ statementId: string }>();
  const { exportOptions, doExport } = useMyCardExport();
  const gridOptions = useGridOptions();

  const applyAction = useCallback(() => {
    return doExport(
      [params.statementId],
      selectedEntries.map((e) => e.id),
      action,
    ).then(refresh);
  }, [action, doExport, params.statementId, selectedEntries]);

  const onCellClicked = useCallback((event: CellClickedEvent<IAccountPayment>) => {
    if (event.column.getId() === 'id') {
      event.node.setSelected(!event.node.isSelected());

      return;
    }
  }, []);

  return (
    <>
      <div className={'ag-theme-alpine'}>
        <AgGridReact
          gridOptions={gridOptions}
          rowData={data}
          rowHeight={58}
          headerHeight={40}
          onCellClicked={onCellClicked}
          onSelectionChanged={(e) => {
            setSelectedEntries(e.api.getSelectedRows());
          }}
          loadingOverlayComponent={AgGridLoadingOverlay}
          loading={loading}
        />
      </div>

      {selectedEntries.length > 0 && (
        <Row className='notification-bar'>
          <Col sm={12} is_pull_end>
            <div className='notification-bar__row'>
              <ExpenseRequestActions hideNotApplicable>
                {Object.entries(exportOptions).map(([accept, format]) => (
                  <div key={format} className='react-select__option'>
                    <Radio
                      name='status'
                      disabled={!selectedEntries.length}
                      value={accept}
                      label={`${trans('global.export_to', { format })} (${selectedEntries.length})`}
                      onClick={() => {
                        setAction(accept);
                      }}
                    />
                  </div>
                ))}
              </ExpenseRequestActions>

              <Button
                primary
                xs
                disabled={!selectedEntries.length || !action}
                onClick={applyAction}
              >
                {trans('global.perform')}
              </Button>
            </div>
          </Col>
        </Row>
      )}

      <Pagination
        page={paginator.page}
        total={paginator.total}
        perPage={paginator.per_page}
        changePage={(page) => setPage(page)}
      />
    </>
  );
}

export default AccountsPaymentsPageTable;
