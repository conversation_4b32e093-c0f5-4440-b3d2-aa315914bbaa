import React from 'react';

import { Link, useParams } from 'react-router-dom';

import { BreadCrumbs } from '../../containers';
import { RouteManager } from '../../containers/RouteManager';
import { getRouteByName } from '../../routes';
import trans from '../../trans';
import AccountStatementsPageFilters from '../AccountStatementsPageFilters';
import AccountsStatementsPageTable from '../AccountStatementsPageTable';
import { Section } from '../ui/Section';
import { SectionHeader } from '../ui/SectionHeader';

import useAccountStatements from './hooks/useAccountStatements';

function AccountStatementsPage() {
  const { data, paginator, loading, setPage, setFilter, filters } = useAccountStatements();
  const params = useParams();

  return (
    <div>
      <BreadCrumbs>
        <Link to={getRouteByName('main', 'dashboard')}>{trans('global.dashboard')}</Link>
        <Link to={getRouteByName('main', 'my-card-accounts')}>
          {trans('main-menu.my-card-accounts')}
        </Link>
        <Link to={getRouteByName('main', 'my-card-accounts-statements', params)}>
          {trans('statements.statements_page_title')}
        </Link>
      </BreadCrumbs>

      <Section className={'my-cards account-page'} noBorder>
        <SectionHeader
          className={'my-cards__header'}
          caption={`${trans('statements.statements_page_title')}`}
        ></SectionHeader>

        <RouteManager>
          {() => (
            <>
              <AccountStatementsPageFilters filters={filters} setFilter={setFilter} />

              <AccountsStatementsPageTable
                data={data}
                paginator={paginator}
                setPage={setPage}
                loading={loading}
              />
            </>
          )}
        </RouteManager>
      </Section>
    </div>
  );
}

export default AccountStatementsPage;
