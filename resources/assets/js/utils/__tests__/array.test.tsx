import { toArray } from '../array'

describe('utils/array.tsx', () => {
  describe('toArray', () => {
    it('should wrap scalar into array', () => {
      expect(toArray('item')).toStrictEqual(['item'])
    })

    it('should do nothing if pass array as an argument', () => {
      const arr = ['item']

      expect(toArray(arr)).toEqual(arr)
      expect(toArray(arr)).toStrictEqual(arr)
    })

    it('should return empty array if null or undefined', () => {
      expect(toArray(null)).toStrictEqual([])
      expect(toArray(undefined)).toStrictEqual([])
    })
  })
})
