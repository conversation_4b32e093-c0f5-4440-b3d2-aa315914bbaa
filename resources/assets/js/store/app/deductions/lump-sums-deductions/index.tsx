import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import APIClient from '../../../../services/APIClient'
import { createDeductions } from '../createDeductions'

export const MODULE_MOUNT_POINT = 'lump-sum-deductions'

const fetchMethod = (...args) => {
  return APIClient.getAccommodationDriveLumpSumsWidget(...args)
}

const changeMethod = (...args) => {
  return APIClient.updateAccommodationDriveLumpSums(...args)
}

const validateMethod = (...args) => {
  return APIClient.validateAccommodationDriveLumpSums(...args)
}

const deductions = createDeductions(MODULE_MOUNT_POINT, fetchMethod, changeMethod, validateMethod)

const {
  selectors: { getDeductions, isInitialized },
  creators: { reset, set, destroy, fetch, change, validate },
  reducer,
} = deductions

export const isLoading = deductions.selectors.isLoading

const lumpSumsDeductions =
  (resetOnMount = false, fetchOnMount = false, destroyOnUnmount = false) =>
  (Component) => {
    class LumpSumsDeductionsHOC extends React.Component<any, any> {
      constructor(props) {
        super(props)
      }

      componentDidMount() {
        const { deductions } = this.props
        if (resetOnMount) {
          deductions.actions.reset()
        }

        if (fetchOnMount) {
          deductions.actions.fetch()
        }
      }

      componentWillUnmount() {
        const { deductions } = this.props
        if (destroyOnUnmount) {
          deductions.actions.destroy()
        }
      }

      render() {
        return <Component<any, any> {...this.props} />
      }
    }

    const mapStateToProps = (state) => ({
      deductions: getDeductions(state),
      isLoading: isLoading(state),
      isInitialized: isInitialized(state),
    })

    const mapDispatchToProps = (dispatch, props) => {
      return bindActionCreators(
        {
          reset,
          set,
          destroy,
          fetch: fetch(props.request),
          change: change(props.request),
          validate: validate(props.request),
        },
        dispatch,
      )
    }

    const mergedProps = (selectors, actions, own) => {
      return {
        ...own,
        deductions: {
          selectors,
          actions,
        },
      }
    }

    return connect(mapStateToProps, mapDispatchToProps, mergedProps)(LumpSumsDeductionsHOC)
  }

export { reducer }
export { lumpSumsDeductions }
export default lumpSumsDeductions
