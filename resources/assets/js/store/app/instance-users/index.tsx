import APIClient from '../../../services/APIClient'
import { createModule } from '../../modules/list/index'

const fetchMethod = ({ filters }, source) => {
  return APIClient.getInstanceUsers(filters, source)
}

const module = createModule({
  namespace: 'instance-users',
  mountPoint: 'instance-users',
  paginationByAPI: true,
  fetchMethod,
})

export const { reducer, MOUNT_POINT, actions, selectors } = module
