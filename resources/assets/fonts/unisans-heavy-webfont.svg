<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Fri Jun  6 20:19:54 2014
 By <PERSON><PERSON>sey,,,
Copyright (c) 2014 by (c) 2009 Designed by <PERSON><PERSON><PERSON><PERSON> (c) Fontfabric, Inc. All rights reserved.
</metadata>
<defs>
<font id="UniSansHeavyCAPS" horiz-adv-x="588" >
  <font-face 
    font-family="Uni Sans Heavy CAPS"
    font-weight="900"
    font-variant="small-caps"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 5 0 0 0 0 0 0 0"
    ascent="756"
    descent="-244"
    x-height="708"
    cap-height="708"
    bbox="-57 -254 1085 997"
    underline-thickness="50"
    underline-position="-200"
    unicode-range="U+0020-FB02"
  />
<missing-glyph horiz-adv-x="601" 
d="M540 700v-700h-529v700h529zM206 562l70 -104q10 15 31.5 48t37.5 56h-139zM229 349l-8 14l-80 121v-262zM335 363l-8 -14l88 -127v262zM205 139h139q-4 5 -69 102z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="833" 
d="M237 538v-127h273v-167h-273v-244h-181v708h493v-170h-312zM777 708v-708h-179v708h179z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1100" 
d="M237 538v-127h273v-167h-273v-244h-181v708h493v-170h-312zM617 0v708h180v-531h288v-177h-468z" />
    <glyph glyph-name=".notdef" horiz-adv-x="601" 
d="M540 700v-700h-529v700h529zM206 562l70 -104q10 15 31.5 48t37.5 56h-139zM229 349l-8 14l-80 121v-262zM335 363l-8 -14l88 -127v262zM205 139h139q-4 5 -69 102z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="182" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="291" 
d="M53 220l-10 492h204l-10 -492h-184zM47 89q0 42 29 71.5t70 29.5q42 0 71.5 -29.5t29.5 -71.5q0 -41 -29.5 -70t-71.5 -29q-41 0 -70 29t-29 70z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="462" 
d="M220 709l-21 -290h-152v290h173zM425 709l-20 -290h-153v290h173z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="647" 
d="M503 315h66l-43 -146h-67l-50 -170h-158l49 170h-42l-49 -170h-163l50 170h-74l43 146h74l9 33h-74l45 152h74l47 161h158l-47 -161h42l47 161h165l-48 -161h66l-44 -152h-67zM308 361l-17 -59h59l16 59h-58z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="570" 
d="M225 -65v64q-123 5 -201 139l128 88q31 -43 73 -49v97q-191 45 -189 221q4 80 57 132.5t132 69.5v59h129v-58q54 -7 94 -34t79 -85l-120 -76q-17 29 -53 36v-112q44 -11 76.5 -26t60.5 -40.5t42 -66.5t11 -96q-6 -77 -59 -128t-131 -70v-65h-129zM194 494v-1
q-2 -45 38 -59v114q-38 -21 -38 -54zM386 217v1q2 45 -38 59v-114q38 21 38 54z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="740" 
d="M55 7l492 760l129 -86l-492 -757zM325 541q0 -81 -42.5 -129t-108.5 -48q-71 0 -109.5 46t-38.5 133q0 79 41.5 127t106.5 48q73 0 112 -46t39 -131zM206 541q0 60 -33 60q-35 0 -35 -59t35 -59q33 0 33 58zM715 171q0 -81 -42.5 -129t-108.5 -48q-71 0 -109.5 46
t-38.5 133q0 79 41.5 127t106.5 48q73 0 112 -46t39 -131zM596 171q0 60 -33 60q-35 0 -35 -59t35 -59q33 0 33 58z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="682" 
d="M522 349l100 -128l-66 -54l133 -166h-210l-48 57q-53 -47 -123.5 -58t-132 10.5t-104 78.5t-42.5 136q0 115 106 178q-44 46 -50.5 103t18 103t76 76.5t114.5 28.5q79 -2 135.5 -53t56.5 -131q0 -103 -86 -163l59 -73zM290 573q-19 0 -33 -13.5t-14 -32.5q0 -17 37 -62
q49 33 49 65q0 17 -11.5 30t-27.5 13zM327 178l-87 102q-31 -16 -38 -48t11 -54q19 -27 54.5 -27t59.5 27z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="257" 
d="M218 708l-15 -290h-155v290h170z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="322" 
d="M190 795l118 -77q-122 -185 -120.5 -376t123.5 -379l-127 -85q-76 107 -115.5 229.5t-39.5 239.5q0 121 41.5 240t119.5 208z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="316" 
d="M12 707l114 76q79 -90 120 -203.5t41 -232.5q0 -118 -39.5 -239.5t-115.5 -227.5l-120 81q118 189 120 379.5t-120 366.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="477" 
d="M406 649l47 -159l-112 -14l81 -78l-129 -99l-54 101l-52 -101l-134 97l81 84l-110 13l46 158l102 -48l-15 107h164l-19 -106z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="566" 
d="M202 406v170h169v-170h168v-170h-168v-171h-169v171h-175v170h175z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="287" 
d="M251 106l-100 -255h-173l85 255h188z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="404" 
d="M358 375v-170h-312v170h312z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="257" 
d="M29 95q0 41 29 70t70 29t70.5 -29t29.5 -70q0 -42 -29.5 -71.5t-70.5 -29.5t-70 29.5t-29 71.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="490" 
d="M471 709l-294 -779l-158 57l295 777z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="596" 
d="M298 -10q-64 0 -120 22t-96 73t-40 122v293q0 105 77.5 161.5t178.5 56.5q102 0 179 -56.5t77 -161.5v-293q0 -71 -39.5 -122t-96 -73t-120.5 -22zM298 561q-34 0 -60 -18t-26 -48v-290q0 -29 25.5 -46t60.5 -17q34 0 60 17t26 46v290q0 30 -26.5 48t-59.5 18z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="374" 
d="M323 0h-161v475l-68 -47l-94 82l188 198h135v-708z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="558" 
d="M165 484l-134 73q42 80 108.5 120.5t142.5 40.5q111 0 174 -64.5t63 -153.5q0 -116 -88 -198l-156 -148l83 17h160v-170h-456l-45 93l288 324q47 53 21 100q-20 37 -64 36q-59 0 -97 -70z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="540" 
d="M16 118l116 107q37 -60 114 -60q37 0 63 16.5t26 44.5q0 29 -20 48.5t-57 19.5h-110v143h106q33 0 46 32.5t-4 54.5q-25 36 -77 31t-86 -44l-104 92q43 54 103 84.5t120 30.5q163 0 213 -124q24 -50 12.5 -117t-62.5 -99q50 -25 72.5 -71.5t22.5 -94.5q0 -99 -77 -160.5
t-180 -61.5q-72 0 -135.5 32t-101.5 96z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="574" 
d="M543 299v-145h-66v-154h-171v154h-249l-57 105l330 449h147v-409h66zM194 297h125l2 181z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="541" 
d="M28 121l127 100q32 -39 76 -47.5t76.5 13.5t34.5 62q3 51 -23.5 78t-69.5 27q-71 0 -115 -59h-82v413h420v-157h-277v-80q88 28 159.5 9.5t117 -81t45.5 -146.5q0 -108 -68.5 -185.5t-198.5 -77.5q-64 0 -122.5 32.5t-99.5 98.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="584" 
d="M528 587l-125 -72q-31 50 -98 50q-38 0 -65.5 -19.5t-27.5 -51.5v-73q50 35 112 39t114 -19t87.5 -72.5t35.5 -113.5v-42q0 -104 -76 -163.5t-182 -59.5q-107 0 -184.5 59.5t-77.5 163.5v282q0 101 79.5 162t184.5 61q73 0 132.5 -33t90.5 -98zM390 207v53
q0 35 -44.5 49.5t-89 0t-44.5 -49.5v-53q0 -38 44.5 -53.5t89 0t44.5 53.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="529" 
d="M22 708h457l48 -96l-273 -612h-192l230 538h-270v170z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="600" 
d="M308 -10h-14q-123 0 -193.5 58.5t-70.5 164.5q0 94 95 165q-62 41 -62 127q0 58 23 102t59.5 67t74 33.5t74.5 10.5h14q97 0 164 -54.5t67 -165.5q0 -27 -16.5 -63.5t-44.5 -55.5q45 -23 69 -68.5t23 -87.5q-2 -111 -67.5 -172t-194.5 -61zM309 572h-15q-44 -1 -61 -36
t2 -69.5t65 -34.5q31 0 52 14t25.5 34t0 40t-23 35t-45.5 17zM293 157h17q56 0 76.5 40t-2 78t-71.5 34h-23q-47 1 -69.5 -36t-2.5 -76t75 -40z" />
    <glyph glyph-name="nine" unicode="9" 
d="M44 125l127 84q36 -59 112 -59q38 0 65.5 19.5t27.5 48.5v73q-50 -36 -112 -40t-114 19.5t-87.5 73t-35.5 113.5v42q0 104 75.5 161.5t182.5 57.5t184 -57.5t78 -161.5v-282q0 -104 -78.5 -165.5t-183.5 -61.5q-78 0 -143.5 34t-97.5 101zM198 505v-53q0 -35 44.5 -49.5
t89 0t44.5 49.5v53q0 35 -44.5 49.5t-89 0t-44.5 -49.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="277" 
d="M39 393q0 41 29.5 69.5t70.5 28.5t70 -28.5t29 -69.5q0 -42 -29 -71.5t-70 -29.5t-70.5 29.5t-29.5 71.5zM39 106q0 43 29 71.5t71 28.5t70.5 -28.5t28.5 -71.5q0 -41 -29 -70.5t-70 -29.5t-70.5 29.5t-29.5 70.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="293" 
d="M45 382q0 42 28 70t72 28q42 0 70 -28t28 -70q0 -43 -28 -72t-70 -29q-44 0 -72 29t-28 72zM255 127l-92 -255h-156l61 255h187z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="445" 
d="M391 383l-161 -91q75 -40 161 -87v-199l-366 232v109l366 235v-199z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="528" 
d="M481 474v-155h-434v155h434zM481 271v-156h-434v156h434z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="445" 
d="M420 347v-109l-367 -232v199l169 87l-169 91v199z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="491" 
d="M123 497l-102 87q35 67 92.5 102t117 32.5t113 -27.5t87.5 -78t34 -119q0 -71 -44 -128t-128 -69l-10 -117h-150l-10 228q64 -27 115 -3.5t51 78.5q0 35 -32.5 52.5t-73.5 7.5t-60 -46zM117 65q0 39 26 64.5t65 25.5q38 0 64 -25.5t26 -64.5q0 -38 -26 -64.5t-64 -26.5
t-64.5 26.5t-26.5 64.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="759" 
d="M500 538l72 -18q-24 34 -72.5 52t-95.5 18q-77 0 -135 -42t-86 -103.5t-28 -129.5q0 -120 84 -182.5t209 -24.5l27 -96q-67 -21 -125 -21q-137 0 -230 98t-86 252q7 92 51 174.5t126.5 137.5t183.5 55q54 0 105 -13q65 -19 121 -61.5t83 -93.5q45 -87 25.5 -197
t-92.5 -173q-24 -22 -60.5 -36.5t-72.5 -14.5q-43 0 -73 21q-31 -19 -71 -19q-78 0 -127.5 55t-49.5 138q0 96 57 169.5t137 73.5q56 0 105 -40zM589 495l-47 -256q37 17 57 62t19 94t-20 82zM375 244q29 0 45 16l32 147q-53 56 -99 17q-37 -30 -40 -90q-2 -45 22 -74
q15 -16 40 -16z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="648" 
d="M404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="624" 
d="M570 492q-1 -29 -19.5 -63t-53.5 -52q65 -33 87 -88t13 -106q-29 -183 -252 -183h-289v708h284q104 0 168.5 -58.5t61.5 -157.5zM236 431h104q41 0 58.5 31.5t0.5 63t-59 31.5h-104v-126zM236 157h104q51 0 71.5 36.5t-0.5 72.5t-71 36h-104v-145z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="582" 
d="M417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="636" 
d="M56 708h265q74 0 134.5 -24t100.5 -79t40 -134v-238q0 -115 -79.5 -174.5t-195.5 -58.5h-265v708zM236 168h91q44 0 66.5 20.5t22.5 56.5v221q0 35 -22.5 55t-66.5 20h-91v-373z" />
    <glyph glyph-name="E" unicode="E" 
d="M237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="576" 
d="M237 538v-127h273v-167h-273v-244h-181v708h493v-170h-312z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="624" 
d="M565 567l-150 -69q-14 34 -47 49.5t-66 11.5t-58.5 -26t-25.5 -55v-243q0 -39 32.5 -61.5t71 -22.5t71 22.5t32.5 61.5v26h-113v150h285v-176q0 -116 -81.5 -180.5t-196.5 -64.5q-114 0 -197 65t-83 180v243q0 113 83 176.5t197 63.5q49 0 94 -13.5t87 -48.5t65 -89z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="676" 
d="M236 267v-267h-180v708h180v-268h204v268h180v-708h-180v267h-204z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="291" 
d="M235 708v-708h-179v708h179z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="587" 
d="M3 208l169 35q7 -40 36 -60t65 -20q34 0 60 19t26 49v315h-161v162h338v-477q0 -110 -81 -175.5t-189 -65.5q-99 0 -174 56t-89 162z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="640" 
d="M236 446l192 262h223l-280 -342l279 -366h-227l-181 253l-6 -8v-245h-180v708h180v-262z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="539" 
d="M56 0v708h180v-531h288v-177h-468z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="769" 
d="M56 0v708h179l149 -346l150 346h179v-708h-180l1 204l11 143l-6 1l-93 -243h-123l-95 243l-5 -1l12 -143v-204h-179z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="676" 
d="M56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283h-179z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="665" 
d="M39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="618" 
d="M56 0v708h283q122 0 190.5 -74t68.5 -176t-68.5 -176.5t-189.5 -74.5h-105v-207h-179zM235 366h104q31 0 52 18t26 44t0 51.5t-26 43.5t-52 18h-104v-175z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="665" 
d="M39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -69 -36 -125l55 -56l-99 -89l-58 60q-70 -33 -155 -33q-119 0 -206.5 64t-87.5 179zM218 475v-242q0 -47 47 -71t92 -5l-58 60l99 86l49 -50v222q0 39 -36 60.5t-78.5 21t-78.5 -22t-36 -59.5z
" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="646" 
d="M433 0l-164 225h-32v-225h-181v708h283q117 0 188.5 -57t74.5 -176q0 -188 -139 -232l193 -243h-223zM237 379h105q53 0 75 40.5t0 81t-75 40.5h-105v-162z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="564" 
d="M22 118l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5q3 -46 94 -66q43 -8 75 -17t67 -27.5t56.5 -43
t34.5 -64t11 -89.5q-3 -65 -46.5 -115t-105 -73.5t-128.5 -23.5q-65 0 -129 33t-112 95z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="667" 
d="M233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="656" 
d="M361 362l112 347h192l-255 -708h-164l-255 708h192l111 -346l32 -168h3z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="915" 
d="M644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="656" 
d="M327 466l124 242h208l-198 -355l198 -353h-208l-124 241l-121 -241h-209l199 353l-199 355h209z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="655" 
d="M417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429v-279z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="605" 
d="M36 708h515l38 -60l-305 -485l288 2v-165h-525l-33 66l306 476h-284v166z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="328" 
d="M221 544v-479h69v-165h-240v810h240v-166h-69z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="503" 
d="M325 -58l-306 769l161 56l304 -771z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="328" 
d="M105 63v484h-67v163h240v-810h-240v163h67z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="652" 
d="M371 707l254 -303h-207l-97 115l-95 -115h-200l247 303h98z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="593" 
d="M548 34v-169h-504v169h504z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="406" 
d="M306 756h-155l-131 166h186z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="648" 
d="M404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="624" 
d="M570 492q-1 -29 -19.5 -63t-53.5 -52q65 -33 87 -88t13 -106q-29 -183 -252 -183h-289v708h284q104 0 168.5 -58.5t61.5 -157.5zM236 431h104q41 0 58.5 31.5t0.5 63t-59 31.5h-104v-126zM236 157h104q51 0 71.5 36.5t-0.5 72.5t-71 36h-104v-145z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="582" 
d="M417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="636" 
d="M56 708h265q74 0 134.5 -24t100.5 -79t40 -134v-238q0 -115 -79.5 -174.5t-195.5 -58.5h-265v708zM236 168h91q44 0 66.5 20.5t22.5 56.5v221q0 35 -22.5 55t-66.5 20h-91v-373z" />
    <glyph glyph-name="e" unicode="e" 
d="M237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="576" 
d="M237 538v-127h273v-167h-273v-244h-181v708h493v-170h-312z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="624" 
d="M565 567l-150 -69q-14 34 -47 49.5t-66 11.5t-58.5 -26t-25.5 -55v-243q0 -39 32.5 -61.5t71 -22.5t71 22.5t32.5 61.5v26h-113v150h285v-176q0 -116 -81.5 -180.5t-196.5 -64.5q-114 0 -197 65t-83 180v243q0 113 83 176.5t197 63.5q49 0 94 -13.5t87 -48.5t65 -89z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="676" 
d="M236 267v-267h-180v708h180v-268h204v268h180v-708h-180v267h-204z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="291" 
d="M235 708v-708h-179v708h179z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="587" 
d="M3 208l169 35q7 -40 36 -60t65 -20q34 0 60 19t26 49v315h-161v162h338v-477q0 -110 -81 -175.5t-189 -65.5q-99 0 -174 56t-89 162z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="640" 
d="M236 446l192 262h223l-280 -342l279 -366h-227l-181 253l-6 -8v-245h-180v708h180v-262z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="539" 
d="M56 0v708h180v-531h288v-177h-468z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="769" 
d="M56 0v708h179l149 -346l150 346h179v-708h-180l1 204l11 143l-6 1l-93 -243h-123l-95 243l-5 -1l12 -143v-204h-179z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="676" 
d="M56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283h-179z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="665" 
d="M39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="618" 
d="M56 0v708h283q122 0 190.5 -74t68.5 -176t-68.5 -176.5t-189.5 -74.5h-105v-207h-179zM235 366h104q31 0 52 18t26 44t0 51.5t-26 43.5t-52 18h-104v-175z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="665" 
d="M39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -69 -36 -125l55 -56l-99 -89l-58 60q-70 -33 -155 -33q-119 0 -206.5 64t-87.5 179zM218 475v-242q0 -47 47 -71t92 -5l-58 60l99 86l49 -50v222q0 39 -36 60.5t-78.5 21t-78.5 -22t-36 -59.5z
" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="646" 
d="M433 0l-164 225h-32v-225h-181v708h283q117 0 188.5 -57t74.5 -176q0 -188 -139 -232l193 -243h-223zM237 379h105q53 0 75 40.5t0 81t-75 40.5h-105v-162z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="564" 
d="M22 118l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5q3 -46 94 -66q43 -8 75 -17t67 -27.5t56.5 -43
t34.5 -64t11 -89.5q-3 -65 -46.5 -115t-105 -73.5t-128.5 -23.5q-65 0 -129 33t-112 95z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="667" 
d="M233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="656" 
d="M361 362l112 347h192l-255 -708h-164l-255 708h192l111 -346l32 -168h3z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="915" 
d="M644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="656" 
d="M327 466l124 242h208l-198 -355l198 -353h-208l-124 241l-121 -241h-209l199 353l-199 355h209z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="655" 
d="M417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429v-279z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="605" 
d="M36 708h515l38 -60l-305 -485l288 2v-165h-525l-33 66l306 476h-284v166z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="351" 
d="M254 547v-129q0 -34 -20.5 -70t-51.5 -48q34 -12 53 -46t19 -73v-118h59v-163h-90q-79 0 -109.5 38.5t-30.5 115.5v111q0 53 -54 53v163q54 0 54 52v122q0 75 33.5 115t114.5 40h82v-163h-59z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="269" 
d="M219 -135h-169v859h169v-859z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="344" 
d="M39 708h69q95 0 123.5 -37t28.5 -118v-120q0 -52 55 -52v-163q-55 0 -55 -53v-119q0 -78 -32.5 -116t-121.5 -38h-67v163h51v126q-2 37 21.5 72.5t55.5 47.5q-31 15 -55.5 48.5t-21.5 68.5v127h-51v163z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="489" 
d="M362 391l102 -85q-72 -123 -161 -123q-24 0 -50.5 14t-42.5 26t-36 7.5t-38 -35.5l-111 93q78 114 156 114q23 0 46 -11.5t38.5 -23t31.5 -18.5t32 2.5t33 39.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="182" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="268" 
d="M233 416q0 -43 -28.5 -71.5t-71.5 -28.5t-70.5 28t-27.5 72q0 42 27.5 70t70.5 28t71.5 -28.5t28.5 -69.5zM219 291v-475h-171v475h171z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="478" 
d="M318 587v-73q97 -29 126 -132l-142 -44q-4 18 -15 29.5t-24.5 13.5t-27 -1t-22 -14.5t-8.5 -27.5v-158q0 -26 21.5 -38t47 -2t34.5 39l146 -33q-34 -109 -136 -142v-73h-128v70q-66 16 -108 62.5t-42 119.5v151q0 73 42 119.5t108 62.5v71h128z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="530" 
d="M501 627l-99 -95q-31 34 -73.5 21t-42.5 -60v-70h168v-153h-168v-112h206v-158h-455v158h78v112h-78v153h78v75q0 38 11 73.5t34.5 67.5t67 51.5t102.5 19.5q42 0 88.5 -20.5t82.5 -62.5z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="616" 
d="M34 171l46 49q-24 50 -24 111q0 54 24 111l-48 47l114 122l53 -53q51 23 108 23q54 0 109 -25l54 54l113 -121l-49 -48q25 -52 25 -110q0 -59 -24 -112l48 -46l-113 -121l-53 53q-47 -24 -110 -24q-59 0 -108 23l-54 -51zM208 330q0 -41 29 -70t70 -29q40 0 69 29t29 70
q0 42 -29 71.5t-69 29.5q-41 0 -70 -29.5t-29 -71.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="712" 
d="M563 361l-23 -108h-95v-31h118l-23 -108h-95v-114h-179v114h-109l22 108h87v31h-109l22 108h40l-200 347h208l128 -270l128 270h210l-201 -347h71z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="274" 
d="M222 313h-170v347h170v-347zM222 -110h-170v377h170v-377z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="475" 
d="M36 37l117 78q18 -26 45.5 -35.5t48 -1.5t20.5 27q0 16 -24 32t-58.5 32t-69.5 36.5t-59 53t-24 72.5q0 71 50 106q-32 38 -32 87q0 84 59 134.5t139 50.5q98 0 169 -106l-118 -71q-13 22 -32 26.5t-32.5 -6.5t-13.5 -28q0 -18 23 -32.5t55.5 -28t65.5 -31t56 -50
t23 -77.5q0 -61 -48 -100q38 -47 38 -106q0 -73 -59.5 -127t-141.5 -54q-55 0 -108 31t-89 88zM209 305l80 -38q24 25 0.5 49.5t-94.5 53.5q-25 -15 -19.5 -34.5t33.5 -30.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="510" 
d="M50 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM291 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M30 351q0 148 106 253.5t254 105.5t253 -105.5t105 -253.5t-105 -253.5t-253 -105.5q-149 0 -254.5 105.5t-105.5 253.5zM133 351q0 -107 75 -182t182 -75q106 0 180.5 75t74.5 182t-74.5 181.5t-180.5 74.5q-107 0 -182 -74.5t-75 -181.5zM198 404v-111q0 -80 57 -123
t135 -43q64 0 118.5 31t72.5 96l-96 36q-7 -26 -27.5 -42.5t-45.5 -19t-49 2.5t-41 21.5t-20 39.5v113q3 23 20 39.5t41 21.5t49 2.5t45.5 -19t27.5 -42.5l94 36q-18 65 -71.5 96t-117.5 31q-78 0 -135 -43t-57 -123z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="373" 
d="M94 574l-56 52q38 59 108.5 73.5t127 -22t56.5 -110.5v-233q-54 -15 -102 -19t-95 3.5t-74.5 40t-27.5 87.5q0 42 20 70.5t49.5 38.5t62.5 12.5t59 -8.5q-2 39 -49.5 42t-78.5 -27zM215 405v66q-21 11 -48 3t-28 -31q0 -28 24.5 -36.5t51.5 -1.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="613" 
d="M179 524l145 -86l-103 -151l104 -153l-147 -86l-159 239zM437 524l145 -86l-104 -153l106 -150l-147 -87l-160 239z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="577" 
d="M40 260v169h487v-301h-168v132h-319z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="407" 
d="M360 332v-171h-313v171h313z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="778" 
d="M30 351q0 148 106 253.5t254 105.5t253 -105.5t105 -253.5t-105 -253.5t-253 -105.5q-149 0 -254.5 105.5t-105.5 253.5zM137 351q0 -105 74.5 -180t180.5 -75q104 0 178.5 75t74.5 180t-74.5 179.5t-178.5 74.5q-106 0 -180.5 -74t-74.5 -180zM244 169v358h162
q57 0 93 -30.5t43 -69.5t-11.5 -78.5t-59.5 -55.5l86 -124h-116l-78 117h-21v-117h-98zM342 366h61q25 0 35 19t-0.5 38t-35.5 19h-60v-76z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="397" 
d="M50 758v134h297v-134h-297z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="377" 
d="M189 392q-68 0 -116.5 48.5t-48.5 116.5q0 69 48 116.5t117 47.5q68 0 116 -47.5t48 -116.5q0 -67 -48.5 -116t-115.5 -49zM189 507q22 0 36 14t14 34q0 21 -14.5 36.5t-35.5 15.5t-35.5 -15.5t-14.5 -36.5q0 -20 14 -34t36 -14z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="539" 
d="M183 520v132h170v-132h141v-170h-141v-131h-170v131h-138v170h138zM495 181v-168h-451v168h451z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="398" 
d="M126 532l-92 79q55 87 154 87q72 0 117 -44.5t45 -107.5q0 -69 -52 -113l-59 -56h119v-131h-301l-28 76l155 176q35 40 16 62q-16 18 -44 3q-18 -11 -30 -31z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="373" 
d="M131 532h50q19 0 22 21q3 25 -19 33l-15 2q-33 -2 -51 -25l-76 64q33 35 60 51.5t64 18.5q42 2 78.5 -17t56 -49t15 -68.5t-38.5 -71.5q39 -18 54.5 -52t7 -69t-33.5 -63q-38 -42 -96 -53t-111.5 13.5t-78.5 76.5l91 57q11 -20 35 -26.5t43.5 3t19.5 29.5q0 9 -7.5 17.5
t-18.5 7.5h-51v100z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="386" 
d="M386 922l-131 -166h-155l100 166h186z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="528" 
d="M336 -1l-6 38q-26 -38 -58.5 -37.5t-53.5 34.5v-206h-171v688h171v-310q0 -31 22.5 -44.5t45 -1t22.5 44.5v311h172l2 -518z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="661" 
d="M228 -83v301q-97 13 -151 81t-54 155q0 98 64.5 168.5t180.5 70.5h336v-776h-168v614h-40v-614h-168z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="264" 
d="M33 303q0 43 28 72t72 29q42 0 70 -29.5t28 -71.5t-28 -70t-70 -28q-44 0 -72 28t-28 70z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="385" 
d="M293 40l-32 -71q38 -10 58.5 -36.5t18 -57.5t-17.5 -60.5t-48.5 -49t-75.5 -19.5q-84 0 -146 51l32 86q23 -16 46 -24.5t41.5 -5.5t23.5 21t-16.5 33.5t-59.5 18.5l51 114h125z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="290" 
d="M57 500l-50 70l126 137h107v-441h-134v262z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="410" 
d="M205 298q-69 0 -120.5 39.5t-51.5 109.5v120q0 70 51.5 109.5t120.5 39.5t120.5 -39.5t51.5 -109.5v-120q0 -70 -51.5 -109.5t-120.5 -39.5zM168 567v-120q0 -21 18.5 -30t37 -0.5t18.5 30.5v119q0 22 -18.5 31.5t-37 0.5t-18.5 -31z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="603" 
d="M177 49l-148 85l105 153l-103 151l144 87l161 -238zM425 48l-145 87l103 152l-104 152l147 85l159 -237z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="830" 
d="M659 649l-422 -694l-117 70l422 694zM254 272v441h-107l-126 -137l50 -70l49 28v-262h134zM761 468h-113l-240 -291l58 -90h161v-87h134v87h36v117h-36v264zM639 200h-84l82 94l4 -1z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="857" 
d="M666 649l-423 -694l-115 71l422 693zM254 272v441h-107l-126 -137l50 -70l49 28v-262h134zM507 356l91 -76q22 38 54 35q23 -2 24 -23q1 -20 -21 -45l-153 -170l28 -74h291v127h-112l59 54q51 43 51 109q0 61 -44.5 104t-115.5 43q-98 0 -152 -84z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="860" 
d="M717 649l-422 -694l-113 71l422 693zM140 438h54q12 1 19.5 -7.5t7.5 -18.5q0 -21 -20.5 -30.5t-46 -3t-36.5 27.5l-95 -58q26 -54 82 -79t117 -14t101 55q26 29 34.5 65t-7.5 70.5t-57 53.5q36 33 41 73t-15.5 70.5t-59 50.5t-82.5 18q-39 -2 -67.5 -19t-62.5 -53
l80 -66q19 24 53 26q30 1 35 -25q4 -22 -13 -31q-4 -2 -9 -2h-53v-103zM690 452l-232 -281l56 -87h163v-84h118v84h35v113h-35v255h-105zM600 193l79 91l4 -1l-2 -90h-81z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="487" 
d="M372 435q0 -39 -26 -64.5t-65 -25.5q-38 0 -64 25.5t-26 64.5q0 38 26 64.5t64 26.5t64.5 -26.5t26.5 -64.5zM468 -84l-102 87q-19 -36 -60 -46t-73.5 7.5t-32.5 52.5q0 55 51 78.5t115 -3.5l-10 224h-150l-10 -113q-84 -12 -128 -69t-44 -128q0 -66 34 -119t87.5 -78
t113 -27.5t117 32.5t92.5 102z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="648" 
d="M426 756h-155l-131 166h186zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="648" 
d="M507 922l-131 -166h-155l100 166h186zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="648" 
d="M382 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="648" 
d="M219 749l-87 49q23 121 127 118q18 0 43 -11t41.5 -20t34 -4t28.5 31l91 -52q-14 -56 -40.5 -85t-63.5 -28q-25 0 -54.5 11t-48 19.5t-38 3.5t-33.5 -32zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="648" 
d="M119 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM360 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708
h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="648" 
d="M465 0l-46 142h-189l-44 -142h-192l240 688q-40 37 -40 93q0 53 38 91.5t93 38.5q53 0 90.5 -40t37.5 -91q0 -55 -41 -93l245 -687h-192zM325 819q-17 0 -27.5 -11.5t-10.5 -28.5q0 -13 10.5 -24t27.5 -11q15 0 25.5 9.5t10.5 25.5t-10.5 28t-25.5 12zM327 508h-5
l-52 -207h106z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="834" 
d="M794 532h-245v-93h220v-173h-220v-95h245v-171h-415v192h-108l-69 -192h-215l302 708h505v-176zM316 345h72l4 180h-8z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="582" 
d="M417 221l156 -72q-60 -119 -187 -151l-13 -29q38 -10 58.5 -36.5t18 -57.5t-17.5 -60.5t-48.5 -49t-75.5 -19.5q-84 0 -146 51l32 86q23 -16 46 -24.5t41.5 -5.5t23.5 21t-16.5 33.5t-59.5 18.5l31 69q-95 15 -158 77t-63 160v244q0 77 41.5 133t104.5 82.5t136 26.5
q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" 
d="M411 756h-155l-131 166h186zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" 
d="M492 922l-131 -166h-155l100 166h186zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" 
d="M367 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" 
d="M104 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM345 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168
h-312z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="291" 
d="M248 756h-155l-131 166h186zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="291" 
d="M329 922l-131 -166h-155l100 166h186zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="291" 
d="M204 924l140 -168h-162l-36 50l-34 -50h-169l144 168h117zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="291" 
d="M-43 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM168 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="642" 
d="M62 428v280h286q114 0 184 -58t70 -176v-241q0 -117 -70 -175t-184 -58h-286v285h-48v143h48zM243 176h105q74 0 74 57v241q0 58 -74 58h-105v-104h83v-143h-83v-109z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="676" 
d="M238 745l-87 49q23 121 127 118q18 0 43 -11t41.5 -20t34 -4t28.5 31l91 -52q-14 -56 -40.5 -85t-63.5 -28q-25 0 -54.5 11t-48 19.5t-38 3.5t-33.5 -32zM56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283
h-179z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="665" 
d="M434 758h-155l-131 166h186zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5
t-33.5 -58.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="665" 
d="M515 924l-131 -166h-155l100 166h186zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5
t-33.5 -58.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="665" 
d="M390 926l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5
t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="665" 
d="M230 747l-87 49q23 121 127 118q18 0 43 -11t41.5 -20t34 -4t28.5 31l91 -52q-14 -56 -40.5 -85t-63.5 -28q-25 0 -54.5 11t-48 19.5t-38 3.5t-33.5 -32zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64
t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="665" 
d="M127 836q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM368 836q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64
t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="536" 
d="M268 412l122 122l119 -122l-119 -119l119 -120l-120 -120l-120 120q-20 -20 -62.5 -62t-60.5 -61l-117 121l120 120l-124 125l120 119z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="665" 
d="M574 756l-39 -99q91 -68 91 -182v-242q0 -115 -87 -179t-206 -64q-25 0 -47 3l-39 -96l-158 56l42 98q-92 67 -92 182v242q0 115 86.5 179t206.5 64q25 0 49 -3l36 93zM447 233v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5v-242q0 -37 34 -57t81 -20
q46 0 80 20.5t34 56.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="667" 
d="M437 756h-155l-131 166h186zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="667" 
d="M518 922l-131 -166h-155l100 166h186zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="667" 
d="M393 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="667" 
d="M130 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM371 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5
t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="655" 
d="M507 922l-131 -166h-155l100 166h186zM417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429v-279z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="556" 
d="M237 602h10q143 0 217.5 -60.5t74.5 -179.5q0 -66 -25.5 -114t-70 -73t-95.5 -36.5t-111 -11.5v-126h-181v714h181v-113zM237 439v-154q84 -10 116 39q25 38 0 76q-33 48 -116 39z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="576" 
d="M48 0v507q-1 99 69.5 155t166.5 56q94 0 161.5 -54t67.5 -152q0 -100 -74 -149q58 -21 86 -71t25 -105.5t-32 -100.5q-28 -46 -82 -71t-115 -25q-35 0 -72 9v158q43 -22 80 1.5t37 63.5q0 28 -20.5 44t-51.5 16h-45v152q63 -13 86 32q19 36 -3 68q-18 25 -50 26
q-27 0 -46 -16t-19 -44v-500h-169z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="648" 
d="M426 756h-155l-131 166h186zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="648" 
d="M507 922l-131 -166h-155l100 166h186zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="648" 
d="M382 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="648" 
d="M219 749l-87 49q23 121 127 118q18 0 43 -11t41.5 -20t34 -4t28.5 31l91 -52q-14 -56 -40.5 -85t-63.5 -28q-25 0 -54.5 11t-48 19.5t-38 3.5t-33.5 -32zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="648" 
d="M119 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM360 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708
h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="648" 
d="M465 0l-46 142h-189l-44 -142h-192l240 688q-40 37 -40 93q0 53 38 91.5t93 38.5q53 0 90.5 -40t37.5 -91q0 -55 -41 -93l245 -687h-192zM325 819q-17 0 -27.5 -11.5t-10.5 -28.5q0 -13 10.5 -24t27.5 -11q15 0 25.5 9.5t10.5 25.5t-10.5 28t-25.5 12zM327 508h-5
l-52 -207h106z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="834" 
d="M794 532h-245v-93h220v-173h-220v-95h245v-171h-415v192h-108l-69 -192h-215l302 708h505v-176zM316 345h72l4 180h-8z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="582" 
d="M417 221l156 -72q-60 -119 -187 -151l-13 -29q38 -10 58.5 -36.5t18 -57.5t-17.5 -60.5t-48.5 -49t-75.5 -19.5q-84 0 -146 51l32 86q23 -16 46 -24.5t41.5 -5.5t23.5 21t-16.5 33.5t-59.5 18.5l31 69q-95 15 -158 77t-63 160v244q0 77 41.5 133t104.5 82.5t136 26.5
q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" 
d="M411 756h-155l-131 166h186zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" 
d="M492 922l-131 -166h-155l100 166h186zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" 
d="M367 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" 
d="M104 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM345 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168
h-312z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="291" 
d="M248 756h-155l-131 166h186zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="291" 
d="M329 922l-131 -166h-155l100 166h186zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="291" 
d="M204 924l140 -168h-162l-36 50l-34 -50h-169l144 168h117zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="291" 
d="M-43 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM168 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="642" 
d="M62 428v280h286q114 0 184 -58t70 -176v-241q0 -117 -70 -175t-184 -58h-286v285h-48v143h48zM243 176h105q74 0 74 57v241q0 58 -74 58h-105v-104h83v-143h-83v-109z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="676" 
d="M238 745l-87 49q23 121 127 118q18 0 43 -11t41.5 -20t34 -4t28.5 31l91 -52q-14 -56 -40.5 -85t-63.5 -28q-25 0 -54.5 11t-48 19.5t-38 3.5t-33.5 -32zM56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283
h-179z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="665" 
d="M434 758h-155l-131 166h186zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5
t-33.5 -58.5z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="665" 
d="M515 924l-131 -166h-155l100 166h186zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5
t-33.5 -58.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="665" 
d="M390 926l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5
t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="665" 
d="M230 747l-87 49q23 121 127 118q18 0 43 -11t41.5 -20t34 -4t28.5 31l91 -52q-14 -56 -40.5 -85t-63.5 -28q-25 0 -54.5 11t-48 19.5t-38 3.5t-33.5 -32zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64
t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="665" 
d="M127 836q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM368 836q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64
t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="509" 
d="M28 374h454v-152h-454v152zM163 98q0 39 27 64.5t67 25.5t67.5 -25.5t27.5 -64.5t-27.5 -65t-67.5 -26t-67 26t-27 65zM163 499q0 39 27 64.5t67 25.5t67.5 -25.5t27.5 -64.5t-27.5 -65t-67.5 -26t-67 26t-27 65z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="665" 
d="M574 756l-39 -99q91 -68 91 -182v-242q0 -115 -87 -179t-206 -64q-25 0 -47 3l-39 -96l-158 56l42 98q-92 67 -92 182v242q0 115 86.5 179t206.5 64q25 0 49 -3l36 93zM447 233v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5v-242q0 -37 34 -57t81 -20
q46 0 80 20.5t34 56.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="667" 
d="M437 756h-155l-131 166h186zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="667" 
d="M518 922l-131 -166h-155l100 166h186zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="667" 
d="M393 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="667" 
d="M130 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM371 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5
t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="655" 
d="M507 922l-131 -166h-155l100 166h186zM417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429v-279z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="556" 
d="M237 602h10q143 0 217.5 -60.5t74.5 -179.5q0 -66 -25.5 -114t-70 -73t-95.5 -36.5t-111 -11.5v-126h-181v714h181v-113zM237 439v-154q84 -10 116 39q25 38 0 76q-33 48 -116 39z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="655" 
d="M119 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM360 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429
v-279z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="648" 
d="M174 758v134h297v-134h-297zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="648" 
d="M174 758v134h297v-134h-297zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="648" 
d="M362 922h135q0 -55 -26 -96t-65 -59t-84 -18t-84 18t-65 59t-26 96h137q0 -24 19.5 -34t39 0t19.5 34zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="648" 
d="M362 922h135q0 -55 -26 -96t-65 -59t-84 -18t-84 18t-65 59t-26 96h137q0 -24 19.5 -34t39 0t19.5 34zM404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="648" 
d="M675 -97l28 -107q-49 -40 -115 -40q-103 0 -125 74t56 170h-54l-46 142h-189l-44 -142h-192l247 708h163l253 -708q-56 -51 -51 -89q3 -23 29 -22q18 0 40 14zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="648" 
d="M675 -97l28 -107q-49 -40 -115 -40q-103 0 -125 74t56 170h-54l-46 142h-189l-44 -142h-192l247 708h163l253 -708q-56 -51 -51 -89q3 -23 29 -22q18 0 40 14zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="582" 
d="M504 922l-131 -166h-155l100 166h186zM417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5
t67 12.5t47.5 51z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="582" 
d="M504 922l-131 -166h-155l100 166h186zM417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5
t67 12.5t47.5 51z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="582" 
d="M222 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59
q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="582" 
d="M222 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59
q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="582" 
d="M112 924h168l41 -52l46 52h167l-150 -168h-122zM417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51
t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="582" 
d="M112 924h168l41 -52l46 52h167l-150 -168h-122zM417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51
t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="636" 
d="M80 924h168l41 -52l46 52h167l-150 -168h-122zM56 708h265q74 0 134.5 -24t100.5 -79t40 -134v-238q0 -115 -79.5 -174.5t-195.5 -58.5h-265v708zM236 168h91q44 0 66.5 20.5t22.5 56.5v221q0 35 -22.5 55t-66.5 20h-91v-373z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="636" 
d="M80 924h168l41 -52l46 52h167l-150 -168h-122zM56 708h265q74 0 134.5 -24t100.5 -79t40 -134v-238q0 -115 -79.5 -174.5t-195.5 -58.5h-265v708zM236 168h91q44 0 66.5 20.5t22.5 56.5v221q0 35 -22.5 55t-66.5 20h-91v-373z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="642" 
d="M62 428v280h286q114 0 184 -58t70 -176v-241q0 -117 -70 -175t-184 -58h-286v285h-48v143h48zM243 176h105q74 0 74 57v241q0 58 -74 58h-105v-104h83v-143h-83v-109z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="642" 
d="M62 428v280h286q114 0 184 -58t70 -176v-241q0 -117 -70 -175t-184 -58h-286v285h-48v143h48zM243 176h105q74 0 74 57v241q0 58 -74 58h-105v-104h83v-143h-83v-109z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" 
d="M159 758v134h297v-134h-297zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="emacron" unicode="&#x113;" 
d="M159 758v134h297v-134h-297zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" 
d="M210 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" 
d="M210 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" 
d="M568 -97l28 -107q-49 -40 -115 -40q-103 0 -125 74t56 170h-356v708h493v-168h-312v-110h287v-155h-287v-106h312v-169q-57 -54 -50 -91q5 -21 29 -20q18 0 40 14z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" 
d="M568 -97l28 -107q-49 -40 -115 -40q-103 0 -125 74t56 170h-356v708h493v-168h-312v-110h287v-155h-287v-106h312v-169q-57 -54 -50 -91q5 -21 29 -20q18 0 40 14z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" 
d="M100 924h168l41 -52l46 52h167l-150 -168h-122zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" 
d="M100 924h168l41 -52l46 52h167l-150 -168h-122zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="624" 
d="M357 922h135q0 -55 -26 -96t-65 -59t-84 -18t-84 18t-65 59t-26 96h137q0 -24 19.5 -34t39 0t19.5 34zM565 567l-150 -69q-14 34 -47 49.5t-66 11.5t-58.5 -26t-25.5 -55v-243q0 -39 32.5 -61.5t71 -22.5t71 22.5t32.5 61.5v26h-113v150h285v-176q0 -116 -81.5 -180.5
t-196.5 -64.5q-114 0 -197 65t-83 180v243q0 113 83 176.5t197 63.5q49 0 94 -13.5t87 -48.5t65 -89z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="624" 
d="M357 922h135q0 -55 -26 -96t-65 -59t-84 -18t-84 18t-65 59t-26 96h137q0 -24 19.5 -34t39 0t19.5 34zM565 567l-150 -69q-14 34 -47 49.5t-66 11.5t-58.5 -26t-25.5 -55v-243q0 -39 32.5 -61.5t71 -22.5t71 22.5t32.5 61.5v26h-113v150h285v-176q0 -116 -81.5 -180.5
t-196.5 -64.5q-114 0 -197 65t-83 180v243q0 113 83 176.5t197 63.5q49 0 94 -13.5t87 -48.5t65 -89z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="624" 
d="M220 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM565 567l-150 -69q-14 34 -47 49.5t-66 11.5t-58.5 -26t-25.5 -55v-243q0 -39 32.5 -61.5t71 -22.5t71 22.5t32.5 61.5v26h-113v150h285v-176q0 -116 -81.5 -180.5
t-196.5 -64.5q-114 0 -197 65t-83 180v243q0 113 83 176.5t197 63.5q49 0 94 -13.5t87 -48.5t65 -89z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="624" 
d="M220 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM565 567l-150 -69q-14 34 -47 49.5t-66 11.5t-58.5 -26t-25.5 -55v-243q0 -39 32.5 -61.5t71 -22.5t71 22.5t32.5 61.5v26h-113v150h285v-176q0 -116 -81.5 -180.5
t-196.5 -64.5q-114 0 -197 65t-83 180v243q0 113 83 176.5t197 63.5q49 0 94 -13.5t87 -48.5t65 -89z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="624" 
d="M565 567l-150 -69q-14 34 -47 49.5t-66 11.5t-58.5 -26t-25.5 -55v-243q0 -39 32.5 -61.5t71 -22.5t71 22.5t32.5 61.5v26h-113v150h285v-176q0 -116 -81.5 -180.5t-196.5 -64.5q-114 0 -197 65t-83 180v243q0 113 83 176.5t197 63.5q49 0 94 -13.5t87 -48.5t65 -89z
M428 -46l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="624" 
d="M565 567l-150 -69q-14 34 -47 49.5t-66 11.5t-58.5 -26t-25.5 -55v-243q0 -39 32.5 -61.5t71 -22.5t71 22.5t32.5 61.5v26h-113v150h285v-176q0 -116 -81.5 -180.5t-196.5 -64.5q-114 0 -197 65t-83 180v243q0 113 83 176.5t197 63.5q49 0 94 -13.5t87 -48.5t65 -89z
M428 -46l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="697" 
d="M631 518v-518h-180v267h-204v-267h-180v518h-59v104h59v86h180v-86h204v86h180v-86h58v-104h-58zM247 518v-78h204v78h-204z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="697" 
d="M631 518v-518h-180v267h-204v-267h-180v518h-59v104h59v86h180v-86h204v86h180v-86h58v-104h-58zM247 518v-78h204v78h-204z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="291" 
d="M-4 758v134h297v-134h-297zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="291" 
d="M-4 758v134h297v-134h-297zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="291" 
d="M252 -97l28 -107q-49 -40 -115 -40q-103 0 -125 74t56 170h-40v708h179v-708q-50 -43 -51 -83q0 -29 28 -28q18 0 40 14z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="291" 
d="M252 -97l28 -107q-49 -40 -115 -40q-103 0 -125 74t56 170h-40v708h179v-708q-50 -43 -51 -83q0 -29 28 -28q18 0 40 14z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="291" 
d="M47 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM235 708v-708h-179v708h179z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="291" 
d="M235 708v-708h-179v708h179z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="878" 
d="M235 708v-708h-179v708h179zM294 208l169 35q7 -40 36 -60t65 -20q34 0 60 19t26 49v315h-161v162h338v-477q0 -110 -81 -175.5t-189 -65.5q-99 0 -174 56t-89 162z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="878" 
d="M235 708v-708h-179v708h179zM294 208l169 35q7 -40 36 -60t65 -20q34 0 60 19t26 49v315h-161v162h338v-477q0 -110 -81 -175.5t-189 -65.5q-99 0 -174 56t-89 162z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="640" 
d="M236 446l192 262h223l-280 -342l279 -366h-227l-181 253l-6 -8v-245h-180v708h180v-262zM437 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="640" 
d="M236 446l192 262h223l-280 -342l279 -366h-227l-181 253l-6 -8v-245h-180v708h180v-262zM437 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="539" 
d="M331 922l-131 -166h-155l100 166h186zM56 0v708h180v-531h288v-177h-468z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="539" 
d="M331 922l-131 -166h-155l100 166h186zM56 0v708h180v-531h288v-177h-468z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="539" 
d="M56 0v708h180v-531h288v-177h-468zM406 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="539" 
d="M56 0v708h180v-531h288v-177h-468zM406 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="549" 
d="M56 0v708h180v-531h288v-177h-468zM544 704l-75 -204h-153l61 204h167z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="549" 
d="M56 0v708h180v-531h288v-177h-468zM544 704l-75 -204h-153l61 204h167z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="548" 
d="M56 0v708h180v-531h288v-177h-468zM293 380q0 41 27.5 69t69.5 28q40 0 67 -28t27 -69q0 -40 -27 -67t-67 -27q-42 0 -69.5 27t-27.5 67z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="548" 
d="M56 0v708h180v-531h288v-177h-468zM293 380q0 41 27.5 69t69.5 28q40 0 67 -28t27 -69q0 -40 -27 -67t-67 -27q-42 0 -69.5 27t-27.5 67z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="570" 
d="M542 182v-182h-455v257l-41 -17l-45 113l86 36v319h181v-242l129 53l47 -112l-176 -73v-152h274z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="570" 
d="M542 182v-182h-455v257l-41 -17l-45 113l86 36v319h181v-242l129 53l47 -112l-176 -73v-152h274z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="676" 
d="M523 922l-131 -166h-155l100 166h186zM56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283h-179z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="676" 
d="M523 922l-131 -166h-155l100 166h186zM56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283h-179z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="676" 
d="M56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283h-179zM455 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="676" 
d="M56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283h-179zM455 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="676" 
d="M131 924h168l41 -52l46 52h167l-150 -168h-122zM56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283h-179z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="676" 
d="M131 924h168l41 -52l46 52h167l-150 -168h-122zM56 0v708h170l171 -302l60 -142l5 1l-10 61q-9 61 -10 61v321h178v-708h-181l-160 279l-62 142l-5 -1l23 -137v-283h-179z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="676" 
d="M620 708v-713q0 -113 -70 -168t-175 -28v147q35 -8 55.5 8.5t20.5 42.5v10l-172 272l-62 142l-4 -1l22 -137v-283h-179v708h170l171 -294l60 -142l3 1l-9 61l-9 61v313h178z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="676" 
d="M620 708v-713q0 -113 -70 -168t-175 -28v147q35 -8 55.5 8.5t20.5 42.5v10l-172 272l-62 142l-4 -1l22 -137v-283h-179v708h170l171 -294l60 -142l3 1l-9 61l-9 61v313h178z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="665" 
d="M182 760v134h297v-134h-297zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5
t-33.5 -58.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="665" 
d="M182 760v134h297v-134h-297zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5
t-33.5 -58.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="665" 
d="M390 916l-111 -158h-141l75 158h177zM622 916l-117 -158h-140l74 158h183zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242
q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="665" 
d="M390 916l-111 -158h-141l75 158h177zM622 916l-117 -158h-140l74 158h183zM39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242
q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="812" 
d="M535 539v-105h212v-163h-212v-103h238v-168h-391v32q-53 -42 -122 -42q-105 0 -161.5 61t-56.5 161v278q0 101 62.5 164.5t149.5 63.5q69 0 128 -35v25h391v-169h-238zM382 171v368q-44 37 -102 20.5t-58 -70.5v-261q0 -43 25 -63.5t61 -20.5q52 0 74 27z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="812" 
d="M535 539v-105h212v-163h-212v-103h238v-168h-391v32q-53 -42 -122 -42q-105 0 -161.5 61t-56.5 161v278q0 101 62.5 164.5t149.5 63.5q69 0 128 -35v25h391v-169h-238zM382 171v368q-44 37 -102 20.5t-58 -70.5v-261q0 -43 25 -63.5t61 -20.5q52 0 74 27z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="646" 
d="M490 922l-131 -166h-155l100 166h186zM433 0l-164 225h-32v-225h-181v708h283q117 0 188.5 -57t74.5 -176q0 -188 -139 -232l193 -243h-223zM237 379h105q53 0 75 40.5t0 81t-75 40.5h-105v-162z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="646" 
d="M490 922l-131 -166h-155l100 166h186zM433 0l-164 225h-32v-225h-181v708h283q117 0 188.5 -57t74.5 -176q0 -188 -139 -232l193 -243h-223zM237 379h105q53 0 75 40.5t0 81t-75 40.5h-105v-162z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="646" 
d="M433 0l-164 225h-32v-225h-181v708h283q117 0 188.5 -57t74.5 -176q0 -188 -139 -232l193 -243h-223zM237 379h105q53 0 75 40.5t0 81t-75 40.5h-105v-162zM443 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="646" 
d="M433 0l-164 225h-32v-225h-181v708h283q117 0 188.5 -57t74.5 -176q0 -188 -139 -232l193 -243h-223zM237 379h105q53 0 75 40.5t0 81t-75 40.5h-105v-162zM443 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="646" 
d="M98 924h168l41 -52l46 52h167l-150 -168h-122zM433 0l-164 225h-32v-225h-181v708h283q117 0 188.5 -57t74.5 -176q0 -188 -139 -232l193 -243h-223zM237 379h105q53 0 75 40.5t0 81t-75 40.5h-105v-162z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="646" 
d="M98 924h168l41 -52l46 52h167l-150 -168h-122zM433 0l-164 225h-32v-225h-181v708h283q117 0 188.5 -57t74.5 -176q0 -188 -139 -232l193 -243h-223zM237 379h105q53 0 75 40.5t0 81t-75 40.5h-105v-162z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="564" 
d="M467 922l-131 -166h-155l100 166h186zM22 118l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5q3 -46 94 -66
q43 -8 75 -17t67 -27.5t56.5 -43t34.5 -64t11 -89.5q-3 -65 -46.5 -115t-105 -73.5t-128.5 -23.5q-65 0 -129 33t-112 95z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="564" 
d="M467 922l-131 -166h-155l100 166h186zM22 118l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5q3 -46 94 -66
q43 -8 75 -17t67 -27.5t56.5 -43t34.5 -64t11 -89.5q-3 -65 -46.5 -115t-105 -73.5t-128.5 -23.5q-65 0 -129 33t-112 95z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="564" 
d="M299 443q43 -8 75 -17t67 -27.5t56.5 -43t34.5 -64t11 -89.5q-4 -76 -58.5 -128.5t-135.5 -72.5l-14 -32q38 -10 58.5 -36.5t18 -57.5t-17.5 -60.5t-48.5 -49t-75.5 -19.5q-84 0 -146 51l32 86q23 -16 46 -24.5t41.5 -5.5t23.5 21t-16.5 33.5t-59.5 18.5l30 67
q-117 20 -199 125l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5q3 -46 94 -66z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="564" 
d="M299 443q43 -8 75 -17t67 -27.5t56.5 -43t34.5 -64t11 -89.5q-4 -76 -58.5 -128.5t-135.5 -72.5l-14 -32q38 -10 58.5 -36.5t18 -57.5t-17.5 -60.5t-48.5 -49t-75.5 -19.5q-84 0 -146 51l32 86q23 -16 46 -24.5t41.5 -5.5t23.5 21t-16.5 33.5t-59.5 18.5l30 67
q-117 20 -199 125l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5q3 -46 94 -66z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="564" 
d="M75 924h168l41 -52l46 52h167l-150 -168h-122zM22 118l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5
q3 -46 94 -66q43 -8 75 -17t67 -27.5t56.5 -43t34.5 -64t11 -89.5q-3 -65 -46.5 -115t-105 -73.5t-128.5 -23.5q-65 0 -129 33t-112 95z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="564" 
d="M75 924h168l41 -52l46 52h167l-150 -168h-122zM22 118l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5
q3 -46 94 -66q43 -8 75 -17t67 -27.5t56.5 -43t34.5 -64t11 -89.5q-3 -65 -46.5 -115t-105 -73.5t-128.5 -23.5q-65 0 -129 33t-112 95z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170zM406 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170zM406 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="585" 
d="M79 924h168l41 -52l46 52h167l-150 -168h-122zM571 539h-188v-539h-181v539h-188v170h557v-170z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="585" 
d="M79 924h168l41 -52l46 52h167l-150 -168h-122zM571 539h-188v-539h-181v539h-188v170h557v-170z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="589" 
d="M385 345h76v-134h-76v-211h-181v211h-80v134h80v194h-188v170h557v-170h-188v-194z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="589" 
d="M385 345h76v-134h-76v-211h-181v211h-80v134h80v194h-188v170h557v-170h-188v-194z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="667" 
d="M185 758v134h297v-134h-297zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="667" 
d="M185 758v134h297v-134h-297zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="667" 
d="M203 851q0 50 39 84.5t94 34.5q54 0 92 -36t38 -83q0 -49 -38.5 -83t-91.5 -34q-55 0 -94 33.5t-39 83.5zM372 850q0 15 -10.5 26t-25.5 11q-17 0 -27.5 -11t-10.5 -26q0 -12 10.5 -22.5t27.5 -10.5q16 0 26 9t10 24zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0
t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="667" 
d="M203 851q0 50 39 84.5t94 34.5q54 0 92 -36t38 -83q0 -49 -38.5 -83t-91.5 -34q-55 0 -94 33.5t-39 83.5zM372 850q0 15 -10.5 26t-25.5 11q-17 0 -27.5 -11t-10.5 -26q0 -12 10.5 -22.5t27.5 -10.5q16 0 26 9t10 24zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0
t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471h181z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="667" 
d="M393 914l-111 -158h-141l75 158h177zM625 914l-117 -158h-140l74 158h183zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471
h181z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="667" 
d="M393 914l-111 -158h-141l75 158h177zM625 914l-117 -158h-140l74 158h183zM233 708v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181v-471q0 -54 -19.5 -98.5t-52.5 -73.5t-75.5 -49.5t-89 -24.5t-93.5 0t-89.5 24.5t-75 49.5t-51.5 73.5t-19 98.5v471
h181z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="667" 
d="M616 708v-471q0 -73 -34.5 -127t-93.5 -85q-52 -38 -57 -91q-3 -38 21 -44q4 -1 8 -1q18 0 40 14l28 -107q-49 -40 -115 -40q-100 0 -125 70t44 162q-114 0 -197.5 65.5t-83.5 183.5v471h181v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="667" 
d="M616 708v-471q0 -73 -34.5 -127t-93.5 -85q-52 -38 -57 -91q-3 -38 21 -44q4 -1 8 -1q18 0 40 14l28 -107q-49 -40 -115 -40q-100 0 -125 70t44 162q-114 0 -197.5 65.5t-83.5 183.5v471h181v-471q0 -32 21 -54.5t50.5 -27.5t59.5 0t51 27.5t21 54.5v471h181z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="915" 
d="M519 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="915" 
d="M519 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="655" 
d="M382 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429v-279z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="655" 
d="M382 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117zM417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429v-279z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="655" 
d="M119 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM360 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429
v-279z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="605" 
d="M494 922l-131 -166h-155l100 166h186zM36 708h515l38 -60l-305 -485l288 2v-165h-525l-33 66l306 476h-284v166z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="605" 
d="M494 922l-131 -166h-155l100 166h186zM36 708h515l38 -60l-305 -485l288 2v-165h-525l-33 66l306 476h-284v166z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="605" 
d="M212 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM36 708h515l38 -60l-305 -485l288 2v-165h-525l-33 66l306 476h-284v166z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="605" 
d="M212 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5zM36 708h515l38 -60l-305 -485l288 2v-165h-525l-33 66l306 476h-284v166z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="605" 
d="M102 924h168l41 -52l46 52h167l-150 -168h-122zM36 708h515l38 -60l-305 -485l288 2v-165h-525l-33 66l306 476h-284v166z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="605" 
d="M102 924h168l41 -52l46 52h167l-150 -168h-122zM36 708h515l38 -60l-305 -485l288 2v-165h-525l-33 66l306 476h-284v166z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="510" 
d="M11 -83l41 123q55 -27 65 29l45 235h-71v146h98l21 96q11 70 57.5 117t107 51t126.5 -44l-56 -108q-53 22 -64 -34l-18 -78h70v-146h-96l-49 -269q-19 -101 -103 -135t-174 17z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="564" 
d="M22 118l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5q3 -46 94 -66q43 -8 75 -17t67 -27.5t56.5 -43
t34.5 -64t11 -89.5q-3 -65 -46.5 -115t-105 -73.5t-128.5 -23.5q-65 0 -129 33t-112 95zM380 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="564" 
d="M22 118l113 106q20 -25 50.5 -42t60.5 -22t57 -2.5t45 16.5t19 36q1 28 -35 46t-86 25q-93 15 -155.5 72.5t-62.5 152.5q0 99 75.5 155.5t180.5 56.5q149 0 242 -113l-117 -90q-36 38 -84 51.5t-83.5 -2t-36.5 -55.5q3 -46 94 -66q43 -8 75 -17t67 -27.5t56.5 -43
t34.5 -64t11 -89.5q-3 -65 -46.5 -115t-105 -73.5t-128.5 -23.5q-65 0 -129 33t-112 95zM380 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170zM406 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170zM406 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="501" 
d="M311 924l150 -168h-162l-46 52l-44 -52h-169l154 168h117z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="441" 
d="M14 924h168l41 -52l46 52h167l-150 -168h-122z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="330" 
d="M50 758v134h297v-134h-297z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="450" 
d="M265 922h135q0 -55 -26 -96t-65 -59t-84 -18t-84 18t-65 59t-26 96h137q0 -24 19.5 -34t39 0t19.5 34z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="296" 
d="M50 851q0 41 28.5 69t70.5 28t69.5 -28t27.5 -69q0 -42 -27.5 -70t-69.5 -28t-70.5 28.5t-28.5 69.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="359" 
d="M48 871q0 53 39 89.5t94 36.5q54 0 92 -38.5t38 -88.5q0 -51 -38.5 -87.5t-91.5 -36.5q-55 0 -94 36t-39 89zM217 869q0 16 -10.5 28t-25.5 12q-17 0 -27.5 -11.5t-10.5 -28.5q0 -13 10.5 -24t27.5 -11q15 0 25.5 9.5t10.5 25.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="380" 
d="M276 11l5 -11q-53 -52 -48 -89q3 -23 29 -22q18 0 40 14l28 -107q-49 -40 -115 -40q-69 0 -103.5 36t-24 95.5t67.5 123.5h121z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="465" 
d="M137 745l-87 49q23 121 127 118q18 0 43 -11t41.5 -20t34 -4t28.5 31l91 -52q-14 -56 -40.5 -85t-63.5 -28q-25 0 -54.5 11t-48 19.5t-38 3.5t-33.5 -32z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="534" 
d="M302 914l-111 -158h-141l75 158h177zM534 914l-117 -158h-140l74 158h183z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M401 -50l-75 -204h-163l61 204h177z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="776" 
d="M40 176h65q-60 78 -60 198q0 108 50.5 186t126 114t164.5 36t166.5 -36.5t129 -114t51.5 -181.5q0 -122 -63 -202h64v-176h-333v141q161 51 161 221q0 74 -45.5 125t-130.5 51q-80 0 -125.5 -54t-45.5 -130q0 -72 40 -131t117 -82v-141h-332v176z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="530" 
d="M337 -1l-6 38q-26 -38 -58.5 -37.5t-53.5 34.5v-206h-171v688h171v-310q0 -31 22.5 -44.5t45 -1t22.5 44.5v311h172l2 -518z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="575" 
d="M531 130l1 -135q-212 -21 -212 174v199h-51v-368h-168v353l-63 -14l-33 126l19 17q65 42 133 42h389l-16 -156h-40v-196q0 -43 41 -42z" />
    <glyph glyph-name="uni0401" unicode="&#x401;" 
d="M104 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM345 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168
h-312z" />
    <glyph glyph-name="uni0410" unicode="&#x410;" horiz-adv-x="648" 
d="M404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="uni0411" unicode="&#x411;" horiz-adv-x="616" 
d="M598 231q0 -106 -68 -168.5t-185 -62.5h-289v708h484v-150h-304v-110h104q142 0 200 -53t58 -164zM236 157h104q81 0 81 83q0 78 -82 78h-103v-161z" />
    <glyph glyph-name="uni0412" unicode="&#x412;" horiz-adv-x="626" 
d="M570 492q-1 -29 -19.5 -63t-53.5 -52q65 -33 87 -88t13 -106q-29 -183 -252 -183h-289v708h284q104 0 168.5 -58.5t61.5 -157.5zM236 431h104q41 0 58.5 31.5t0.5 63t-59 31.5h-104v-126zM236 157h104q51 0 71.5 36.5t-0.5 72.5t-71 36h-104v-145z" />
    <glyph glyph-name="uni0413" unicode="&#x413;" horiz-adv-x="511" 
d="M56 708h441v-170h-261v-538h-180v708z" />
    <glyph glyph-name="uni0414" unicode="&#x414;" horiz-adv-x="746" 
d="M201 -142h-183v303q23 0 41.5 4.5t32 16.5t23.5 22.5t17 33t11.5 37t7.5 45t4.5 46.5t3.5 52.5t3 52.5l17 237h462v-547h86v-303h-180v142h-346v-142zM461 161v377h-120l-15 -215q-6 -98 -30 -162h165z" />
    <glyph glyph-name="uni0415" unicode="&#x415;" 
d="M237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="uni0416" unicode="&#x416;" horiz-adv-x="948" 
d="M384 0v260l-6 8l-163 -268h-223l252 360l-253 348h216l177 -265v265h180v-265l177 265h216l-253 -348l252 -360h-223l-163 268l-6 -8v-260h-180z" />
    <glyph glyph-name="uni0417" unicode="&#x417;" horiz-adv-x="569" 
d="M440 377q101 -51 101 -165q0 -105 -75 -163.5t-209 -58.5q-82 0 -150.5 37t-108.5 102l114 89q63 -71 135 -71q114 0 114 76q0 38 -24.5 59.5t-67.5 21.5h-105v123h101q40 0 62.5 19.5t22.5 54.5q0 31 -23 49t-67 18q-61 0 -129 -50l-94 92q88 108 236 108
q117 0 180.5 -54t63.5 -151q0 -38 -20.5 -78t-56.5 -58z" />
    <glyph glyph-name="uni0418" unicode="&#x418;" horiz-adv-x="685" 
d="M449 0v283l18 133l-5 1l-67 -138l-170 -279h-169v708h180v-321l-15 -106l4 -1l59 127l181 301h164v-708h-180z" />
    <glyph glyph-name="uni0419" unicode="&#x419;" horiz-adv-x="685" 
d="M338 766q-75 0 -128 46t-53 128h139q0 -18 12 -29.5t30 -11.5t30 11.5t12 29.5h139q0 -82 -53 -128t-128 -46zM449 0v283l18 133l-5 1l-67 -138l-170 -279h-169v708h180v-321l-15 -106l4 -1l59 127l181 301h164v-708h-180z" />
    <glyph glyph-name="uni041A" unicode="&#x41a;" horiz-adv-x="640" 
d="M236 446l192 262h223l-280 -348l279 -360h-225l-183 253l-6 -8v-245h-180v708h180v-262z" />
    <glyph glyph-name="uni041B" unicode="&#x41b;" horiz-adv-x="709" 
d="M342 538l-12 -215q-3 -52 -13.5 -99t-34 -92t-57.5 -76t-87.5 -47t-120.5 -9v167q24 -1 43 3t33 14.5t24.5 22t17 32.5t11 37.5t7.5 45t4 46.5t3 51.5t3 51.5l17 237h473v-708h-180v538h-131z" />
    <glyph glyph-name="uni041C" unicode="&#x41c;" horiz-adv-x="769" 
d="M56 0v708h179l149 -346l150 346h179v-708h-180l1 204l11 143l-6 1l-93 -243h-123l-95 243l-5 -1l12 -143v-204h-179z" />
    <glyph glyph-name="uni041D" unicode="&#x41d;" horiz-adv-x="676" 
d="M236 267v-267h-180v708h180v-268h204v268h180v-708h-180v267h-204z" />
    <glyph glyph-name="uni041E" unicode="&#x41e;" horiz-adv-x="665" 
d="M39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="uni041F" unicode="&#x41f;" horiz-adv-x="671" 
d="M236 538v-538h-180v708h559v-708h-180v538h-199z" />
    <glyph glyph-name="uni0420" unicode="&#x420;" horiz-adv-x="618" 
d="M56 0v708h283q122 0 190.5 -74t68.5 -176t-68.5 -176.5t-189.5 -74.5h-105v-207h-179zM235 366h104q31 0 52 18t26 44t0 51.5t-26 43.5t-52 18h-104v-175z" />
    <glyph glyph-name="uni0421" unicode="&#x421;" horiz-adv-x="582" 
d="M417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="uni0422" unicode="&#x422;" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170z" />
    <glyph glyph-name="uni0423" unicode="&#x423;" horiz-adv-x="601" 
d="M246 554l48 -128h5l53 138l64 144h199l-320 -708h-193l104 229l-233 479h203z" />
    <glyph glyph-name="uni0424" unicode="&#x424;" horiz-adv-x="804" 
d="M317 0v58q-122 0 -206.5 58.5t-84.5 176.5v122q0 118 84.5 176.5t206.5 58.5v58h170v-58q122 0 206.5 -58.5t84.5 -176.5v-122q0 -118 -84.5 -176.5t-206.5 -58.5v-58h-170zM206 416v-124q0 -72 111 -72v268q-111 0 -111 -72zM598 292v124q0 72 -111 72v-268
q111 0 111 72z" />
    <glyph glyph-name="uni0425" unicode="&#x425;" horiz-adv-x="656" 
d="M327 466l124 242h208l-198 -355l198 -353h-208l-124 241l-121 -241h-209l199 353l-199 355h209z" />
    <glyph glyph-name="uni0426" unicode="&#x426;" horiz-adv-x="715" 
d="M56 0v708h180v-538h193v538h180v-538h86v-312h-170v142h-469z" />
    <glyph glyph-name="uni0427" unicode="&#x427;" horiz-adv-x="639" 
d="M403 0v237q-19 -16 -54 -28t-75 -12q-100 0 -171 64t-71 179v268h180v-268q0 -71 77 -73q76 0 113 34l1 307h180v-708h-180z" />
    <glyph glyph-name="uni0428" unicode="&#x428;" horiz-adv-x="988" 
d="M56 708h180v-538h168v538h180v-538h168v538h180v-708h-876v708z" />
    <glyph glyph-name="uni0429" unicode="&#x429;" horiz-adv-x="1038" 
d="M752 708h180v-538h86v-312h-170v142h-792v708h180v-538h168v538h180v-538h168v538z" />
    <glyph glyph-name="uni042A" unicode="&#x42a;" horiz-adv-x="714" 
d="M152 538h-135v170h315v-241h106q137 0 196 -58t59 -166q0 -107 -67.5 -175t-182.5 -68h-291v538zM332 157h106q79 0 79 88q0 39 -21 59.5t-58 20.5h-106v-168z" />
    <glyph glyph-name="uni042B" unicode="&#x42b;" horiz-adv-x="915" 
d="M597 243q0 -107 -67.5 -175t-182.5 -68h-291v708h180v-241h106q137 0 196 -58t59 -166zM859 708v-708h-179v708h179zM236 157h106q79 0 79 88q0 39 -21 59.5t-58 20.5h-106v-168z" />
    <glyph glyph-name="uni042C" unicode="&#x42c;" horiz-adv-x="618" 
d="M597 243q0 -107 -67.5 -175t-182.5 -68h-291v708h180v-241h106q137 0 196 -58t59 -166zM236 157h106q79 0 79 88q0 39 -21 59.5t-58 20.5h-106v-168z" />
    <glyph glyph-name="uni042D" unicode="&#x42d;" 
d="M369 233v50h-157v149h157v42q0 35 -29.5 56t-70.5 21q-75 0 -106 -57l-146 59q39 87 106.5 126t142.5 39q73 0 136.5 -27t105 -83.5t41.5 -133.5v-241q0 -114 -83.5 -178.5t-196.5 -64.5q-47 0 -92.5 15.5t-90 52.5t-72.5 92l156 72q25 -62 104 -62q39 1 67 20.5t28 52.5
z" />
    <glyph glyph-name="uni042E" unicode="&#x42e;" horiz-adv-x="975" 
d="M347 439v36q0 115 87 179t207 64q76 0 141.5 -26t109.5 -83t44 -134v-242q0 -115 -87.5 -179t-206.5 -64q-76 0 -141.5 26t-109.5 83t-44 134v42h-112v-275h-180v708h180v-269h112zM527 468v-231q0 -36 34 -56.5t80 -20.5q47 0 81 20t34 57v231q0 38 -34 59t-81 21
q-46 -1 -80 -22t-34 -58z" />
    <glyph glyph-name="uni042F" unicode="&#x42f;" horiz-adv-x="646" 
d="M590 0h-180v238h-29l-190 -238h-223l216 256q-68 24 -106 74t-38 145q3 111 73.5 172t194.5 61h282v-708zM410 538h-105q-41 0 -63 -20t-22 -57q0 -36 21.5 -59t63.5 -23h105v159z" />
    <glyph glyph-name="uni0430" unicode="&#x430;" horiz-adv-x="648" 
d="M404 708l253 -708h-192l-46 142h-189l-44 -142h-192l247 708h163zM270 301h106l-49 207h-5z" />
    <glyph glyph-name="uni0431" unicode="&#x431;" horiz-adv-x="616" 
d="M598 231q0 -106 -68 -168.5t-185 -62.5h-289v708h484v-150h-304v-110h104q142 0 200 -53t58 -164zM236 157h104q81 0 81 83q0 78 -82 78h-103v-161z" />
    <glyph glyph-name="uni0432" unicode="&#x432;" horiz-adv-x="626" 
d="M570 492q-1 -29 -19.5 -63t-53.5 -52q65 -33 87 -88t13 -106q-29 -183 -252 -183h-289v708h284q104 0 168.5 -58.5t61.5 -157.5zM236 431h104q41 0 58.5 31.5t0.5 63t-59 31.5h-104v-126zM236 157h104q51 0 71.5 36.5t-0.5 72.5t-71 36h-104v-145z" />
    <glyph glyph-name="uni0433" unicode="&#x433;" horiz-adv-x="511" 
d="M56 708h441v-170h-261v-538h-180v708z" />
    <glyph glyph-name="uni0434" unicode="&#x434;" horiz-adv-x="721" 
d="M197 -142h-182v303h64l185 547h189l191 -547h62v-303h-182v142h-327v-142zM397 352l-35 156h-4l-31 -158l-53 -189h172z" />
    <glyph glyph-name="uni0435" unicode="&#x435;" 
d="M237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168h-312z" />
    <glyph glyph-name="uni0436" unicode="&#x436;" horiz-adv-x="948" 
d="M384 0v260l-6 8l-163 -268h-223l252 360l-253 348h216l177 -265v265h180v-265l177 265h216l-253 -348l252 -360h-223l-163 268l-6 -8v-260h-180z" />
    <glyph glyph-name="uni0437" unicode="&#x437;" horiz-adv-x="569" 
d="M440 377q101 -51 101 -165q0 -105 -75 -163.5t-209 -58.5q-82 0 -150.5 37t-108.5 102l114 89q63 -71 135 -71q114 0 114 76q0 38 -24.5 59.5t-67.5 21.5h-105v123h101q40 0 62.5 19.5t22.5 54.5q0 31 -23 49t-67 18q-61 0 -129 -50l-94 92q88 108 236 108
q117 0 180.5 -54t63.5 -151q0 -38 -20.5 -78t-56.5 -58z" />
    <glyph glyph-name="uni0438" unicode="&#x438;" horiz-adv-x="685" 
d="M449 0v283l18 133l-5 1l-67 -138l-170 -279h-169v708h180v-321l-15 -106l4 -1l59 127l181 301h164v-708h-180z" />
    <glyph glyph-name="uni0439" unicode="&#x439;" horiz-adv-x="685" 
d="M338 766q-75 0 -128 46t-53 128h139q0 -18 12 -29.5t30 -11.5t30 11.5t12 29.5h139q0 -82 -53 -128t-128 -46zM449 0v283l18 133l-5 1l-67 -138l-170 -279h-169v708h180v-321l-15 -106l4 -1l59 127l181 301h164v-708h-180z" />
    <glyph glyph-name="uni043A" unicode="&#x43a;" horiz-adv-x="640" 
d="M236 446l192 262h223l-280 -348l279 -360h-225l-183 253l-6 -8v-245h-180v708h180v-262z" />
    <glyph glyph-name="uni043B" unicode="&#x43b;" horiz-adv-x="634" 
d="M320 508h-6l-43 -207l-84 -301h-192l238 708h165l244 -708h-192l-89 301z" />
    <glyph glyph-name="uni043C" unicode="&#x43c;" horiz-adv-x="769" 
d="M56 0v708h179l149 -346l150 346h179v-708h-180l1 204l11 143l-6 1l-93 -243h-123l-95 243l-5 -1l12 -143v-204h-179z" />
    <glyph glyph-name="uni043D" unicode="&#x43d;" horiz-adv-x="676" 
d="M236 267v-267h-180v708h180v-268h204v268h180v-708h-180v267h-204z" />
    <glyph glyph-name="uni043E" unicode="&#x43e;" horiz-adv-x="665" 
d="M39 233v242q0 115 86.5 179t206.5 64q119 0 206.5 -64t87.5 -179v-242q0 -115 -87 -179t-206 -64t-206.5 64t-87.5 179zM218 475v-242q0 -37 34 -57t81 -20q46 0 80 20.5t34 56.5v242q0 39 -34.5 59.5t-82.5 20.5q-45 0 -78.5 -21.5t-33.5 -58.5z" />
    <glyph glyph-name="uni043F" unicode="&#x43f;" horiz-adv-x="671" 
d="M236 538v-538h-180v708h559v-708h-180v538h-199z" />
    <glyph glyph-name="uni0440" unicode="&#x440;" horiz-adv-x="618" 
d="M56 0v708h283q122 0 190.5 -74t68.5 -176t-68.5 -176.5t-189.5 -74.5h-105v-207h-179zM235 366h104q31 0 52 18t26 44t0 51.5t-26 43.5t-52 18h-104v-175z" />
    <glyph glyph-name="uni0441" unicode="&#x441;" horiz-adv-x="582" 
d="M417 221l156 -72q-28 -55 -72.5 -92t-90 -52t-92.5 -15q-113 0 -196 64t-83 178v244q0 77 41.5 133t104.5 82.5t136 26.5q75 0 142.5 -38t106.5 -125l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-244q0 -31 25.5 -51t59 -23.5t67 12.5t47.5 51z" />
    <glyph glyph-name="uni0442" unicode="&#x442;" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170z" />
    <glyph glyph-name="uni0443" unicode="&#x443;" horiz-adv-x="601" 
d="M246 554l48 -128h5l53 138l64 144h199l-320 -708h-193l104 229l-233 479h203z" />
    <glyph glyph-name="uni0444" unicode="&#x444;" horiz-adv-x="804" 
d="M317 0v58q-122 0 -206.5 58.5t-84.5 176.5v122q0 118 84.5 176.5t206.5 58.5v58h170v-58q122 0 206.5 -58.5t84.5 -176.5v-122q0 -118 -84.5 -176.5t-206.5 -58.5v-58h-170zM206 416v-124q0 -72 111 -72v268q-111 0 -111 -72zM598 292v124q0 72 -111 72v-268
q111 0 111 72z" />
    <glyph glyph-name="uni0445" unicode="&#x445;" horiz-adv-x="656" 
d="M327 466l124 242h208l-198 -355l198 -353h-208l-124 241l-121 -241h-209l199 353l-199 355h209z" />
    <glyph glyph-name="uni0446" unicode="&#x446;" horiz-adv-x="715" 
d="M56 0v708h180v-538h193v538h180v-538h86v-312h-170v142h-469z" />
    <glyph glyph-name="uni0447" unicode="&#x447;" horiz-adv-x="639" 
d="M403 0v237q-19 -16 -54 -28t-75 -12q-100 0 -171 64t-71 179v268h180v-268q0 -71 77 -73q76 0 113 34l1 307h180v-708h-180z" />
    <glyph glyph-name="uni0448" unicode="&#x448;" horiz-adv-x="988" 
d="M56 708h180v-538h168v538h180v-538h168v538h180v-708h-876v708z" />
    <glyph glyph-name="uni0449" unicode="&#x449;" horiz-adv-x="1038" 
d="M752 708h180v-538h86v-312h-170v142h-792v708h180v-538h168v538h180v-538h168v538z" />
    <glyph glyph-name="uni044A" unicode="&#x44a;" horiz-adv-x="714" 
d="M152 538h-135v170h315v-241h106q137 0 196 -58t59 -166q0 -107 -67.5 -175t-182.5 -68h-291v538zM332 157h106q79 0 79 88q0 39 -21 59.5t-58 20.5h-106v-168z" />
    <glyph glyph-name="uni044B" unicode="&#x44b;" horiz-adv-x="915" 
d="M597 243q0 -107 -67.5 -175t-182.5 -68h-291v708h180v-241h106q137 0 196 -58t59 -166zM859 708v-708h-179v708h179zM236 157h106q79 0 79 88q0 39 -21 59.5t-58 20.5h-106v-168z" />
    <glyph glyph-name="uni044C" unicode="&#x44c;" horiz-adv-x="618" 
d="M597 243q0 -107 -67.5 -175t-182.5 -68h-291v708h180v-241h106q137 0 196 -58t59 -166zM236 157h106q79 0 79 88q0 39 -21 59.5t-58 20.5h-106v-168z" />
    <glyph glyph-name="uni044D" unicode="&#x44d;" 
d="M369 233v50h-157v149h157v42q0 35 -29.5 56t-70.5 21q-75 0 -106 -57l-146 59q39 87 106.5 126t142.5 39q73 0 136.5 -27t105 -83.5t41.5 -133.5v-241q0 -114 -83.5 -178.5t-196.5 -64.5q-47 0 -92.5 15.5t-90 52.5t-72.5 92l156 72q25 -62 104 -62q39 1 67 20.5t28 52.5
z" />
    <glyph glyph-name="uni044E" unicode="&#x44e;" horiz-adv-x="975" 
d="M347 439v36q0 115 87 179t207 64q76 0 141.5 -26t109.5 -83t44 -134v-242q0 -115 -87.5 -179t-206.5 -64q-76 0 -141.5 26t-109.5 83t-44 134v42h-112v-275h-180v708h180v-269h112zM527 468v-231q0 -36 34 -56.5t80 -20.5q47 0 81 20t34 57v231q0 38 -34 59t-81 21
q-46 -1 -80 -22t-34 -58z" />
    <glyph glyph-name="uni044F" unicode="&#x44f;" horiz-adv-x="646" 
d="M590 0h-180v238h-29l-190 -238h-223l216 256q-68 24 -106 74t-38 145q3 111 73.5 172t194.5 61h282v-708zM410 538h-105q-41 0 -63 -20t-22 -57q0 -36 21.5 -59t63.5 -23h105v159z" />
    <glyph glyph-name="uni0451" unicode="&#x451;" 
d="M104 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM345 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM237 540v-110h287v-155h-287v-106h312v-169h-493v708h493v-168
h-312z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="915" 
d="M563 756h-155l-131 166h186zM644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="915" 
d="M563 756h-155l-131 166h186zM644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="915" 
d="M644 922l-131 -166h-155l100 166h186zM644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="915" 
d="M644 922l-131 -166h-155l100 166h186zM644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="915" 
d="M256 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM497 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3
l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="915" 
d="M256 834q0 36 24.5 60.5t59.5 24.5t59.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-59.5 -24t-59.5 24t-24.5 59zM497 834q0 36 24 60.5t60 24.5t60.5 -24.5t24.5 -60.5q0 -35 -24.5 -59t-60.5 -24t-60 24t-24 59zM644 367l86 341h185l-204 -708h-166l-63 196l-25 162h-3
l-27 -161l-56 -197h-166l-205 708h186l84 -339l18 -150h5l17 149l79 248h136l73 -249l23 -148h3z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="655" 
d="M426 756h-155l-131 166h186zM417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429v-279z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="655" 
d="M426 756h-155l-131 166h186zM417 0h-179v279l-247 429h208l128 -260l128 260h210l-248 -429v-279z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="571" 
d="M525 374v-169h-479v169h479z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="868" 
d="M822 374v-169h-776v169h776z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="310" 
d="M23 492l113 217h157l-68 -217h-202z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="299" 
d="M289 706l-103 -206h-157l58 206h202z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="295" 
d="M260 115l-117 -219h-152l74 219h195z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="544" 
d="M23 492l113 217h157l-68 -217h-202zM257 492l113 217h157l-68 -217h-202z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="530" 
d="M289 706l-103 -206h-157l58 206h202zM520 706l-103 -206h-157l58 206h202z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="518" 
d="M259 104l-117 -208h-152l74 208h195zM483 104l-117 -208h-152l73 208h196z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="511" 
d="M25 348v170h145v190h171v-190h145v-170h-145v-444h-171v444h-145z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="547" 
d="M43 69v166h145v141h-145v165h145v167h171v-167h145v-165h-145v-141h145v-166h-145v-165h-171v165h-145z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="420" 
d="M48 331q0 67 47.5 114.5t115.5 47.5q67 0 114 -47.5t47 -114.5q0 -68 -47 -115.5t-114 -47.5q-68 0 -115.5 47.5t-47.5 115.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="786" 
d="M29 95q0 41 29 70t72 29q41 0 70 -29t29 -70q0 -42 -29 -71.5t-70 -29.5q-43 0 -72 29.5t-29 71.5zM294 95q0 41 28.5 70t69.5 29q43 0 72 -29t29 -70q0 -42 -29 -71.5t-72 -29.5q-41 0 -69.5 29.5t-28.5 71.5zM557 95q0 41 28.5 70t70.5 29q43 0 71.5 -28.5t28.5 -70.5
t-28.5 -71.5t-71.5 -29.5q-42 0 -70.5 29.5t-28.5 71.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1072" 
d="M55 7l492 760l129 -86l-492 -757zM325 541q0 -81 -42.5 -129t-108.5 -48q-71 0 -109.5 46t-38.5 133q0 79 41.5 127t106.5 48q73 0 112 -46t39 -131zM206 541q0 60 -33 60q-35 0 -35 -59t35 -59q33 0 33 58zM715 171q0 -81 -42.5 -129t-108.5 -48q-71 0 -109.5 46
t-38.5 133q0 79 41.5 127t106.5 48q73 0 112 -46t39 -131zM1049 171q0 -81 -42.5 -129t-108.5 -48q-71 0 -109.5 46t-38.5 133q0 79 41.5 127t106.5 48q73 0 112 -46t39 -131zM596 171q0 60 -33 60q-35 0 -35 -59t35 -59q33 0 33 58zM930 171q0 60 -33 60q-35 0 -35 -59
t35 -59q33 0 33 58z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="352" 
d="M179 525l144 -87l-103 -150q16 -24 52.5 -78.5t50.5 -76.5l-145 -85l-159 239z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="352" 
d="M176 49l-148 84l103 154l-102 151l145 86l159 -237z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="583" 
d="M589 685l-454 -762l-141 87l454 761z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="645" 
d="M87 470v4q0 114 82.5 179t196.5 65q75 0 142.5 -39t106.5 -126l-146 -59q-23 42 -73.5 53.5t-91.5 -10t-41 -63.5v-4h178l-23 -108h-155v-31h178l-23 -108h-154q6 -37 47 -55.5t87 -4t64 58.5l156 -72q-28 -55 -72.5 -92t-90 -52.5t-92.5 -15.5q-110 0 -191 61.5
t-85 171.5h-62l22 108h40v31h-62l22 108h40z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" horiz-adv-x="450" 
d="M340 173l87 -100q-46 -56 -113.5 -73.5t-125.5 12t-76 99.5l-37 -28l-59 136l81 69v185q0 130 48 186t119 56q73 0 111.5 -50.5t38.5 -131.5q0 -149 -151 -312v-7q0 -48 23.5 -59t53.5 18zM235 401v-5q41 86 41 133q0 54 -17.5 51.5t-27.5 -51.5q-10 -56 4 -128z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="863" 
d="M117 289v258h-88v162h343v-162h-90v-258h-165zM401 291v418h137l69 -132l71 132h133v-418h-149l3 119l-42 -73h-25l-45 74l2 -120h-154z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="774" 
d="M414 0l-1 143q68 27 106.5 88.5t38.5 130.5q0 74 -44.5 125t-128.5 51q-80 0 -123.5 -53.5t-43.5 -130.5q0 -67 37 -126t103 -85v-143h-319v176h79q-74 77 -74 198q0 108 50.5 186t126 114t164.5 36t166.5 -36.5t129 -114t51.5 -181.5q0 -58 -20.5 -113.5t-55.5 -88.5h77
v-176h-319z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="798" 
d="M271 260v-31q0 -9 0.5 -22.5t0.5 -23.5q56 -50 130 -50q54 0 108.5 22t88.5 71l167 -1q-69 -119 -167.5 -175.5t-197.5 -56.5q-153 0 -261.5 98.5t-108.5 239.5q0 140 109 240t261 100q154 0 263 -100t109 -240v-71h-502zM533 401v76q-61 51 -131 51q-73 0 -131 -51v-76
h262z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="506" 
d="M133 515l-76 114q96 62 171 62q101 0 173.5 -83.5t72.5 -248.5q0 -163 -66.5 -264t-185.5 -101q-94 0 -144.5 65.5t-52.5 160.5q-3 110 63.5 181t151.5 71q32 0 65 -12q-2 46 -30.5 67.5t-67 17t-74.5 -29.5zM223 153h8q14 -1 29 15t27 42q17 38 19 96q-63 47 -99 -9
q-27 -43 -19 -98q6 -46 35 -46z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="585" 
d="M223 675h139l199 -567v-108h-537v107zM290 442l-73 -273h145z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="632" 
d="M527 -53h-169v594h-85v-594h-170v594h-76v168h579v-168h-79v-594z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="547" 
d="M27 52l189 260q-28 41 -88.5 136t-90.5 139v119h468v-147h-242q21 -32 75.5 -119t82.5 -128l-166 -223h262v-154h-490v117z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="605" 
d="M558 379v-168h-511v168h511z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="563" 
d="M575 702l-445 -751l-141 87l445 750z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="264" 
d="M33 303q0 43 28 72t72 29q42 0 70 -29.5t28 -71.5t-28 -70t-70 -28q-44 0 -72 28t-28 70z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="541" 
d="M215 -116l-108 340l-56 -15l-53 145l217 83l56 -283l94 605h174l-184 -875h-140z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="671" 
d="M643 285v-1q0 -78 -53 -128t-123 -50q-83 0 -131 61q-55 -61 -135 -61q-72 0 -122 50.5t-50 124.5q0 78 50 129t127 51q78 0 133 -67q59 70 134 67q76 -3 123 -51t47 -125zM205 324q-30 0 -42 -20t0 -40.5t41 -20.5q23 0 62 41q-40 40 -61 40zM469 324q-21 0 -61 -40
q39 -41 62 -41q29 0 41 20.5t0 40.5t-42 20z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="383" 
d="M381 742l-34 -98q-24 16 -44 4.5t-21 -49.5l5 -546q0 -83 -46 -128.5t-114 -45.5q-59 0 -127 43l43 106q25 -18 46.5 -11t21.5 36q0 63 -9 550q0 85 45.5 132.5t112.5 47.5q65 0 121 -41z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="497" 
d="M373 496l90 -74q-59 -109 -146 -109q-43 0 -104 27q-60 30 -101 -16l-82 75q32 52 69 81t74 32q22 3 52.5 -9.5t53 -26t48.5 -11.5t46 31zM369 298l97 -72q-62 -113 -150 -113q-49 0 -102 32q-27 17 -39 17t-50 -39l-91 78q58 93 105 109.5t125 -19.5v1q7 -2 19 -6.5
t15.5 -6t11.5 -3t11.5 -0.5t11.5 3.5t16 7t20 11.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="528" 
d="M38 114v142h139l22 42h-161v142h236l50 96l121 -55l-22 -41h66v-142h-141l-22 -42h163v-142h-238l-49 -94l-121 51l22 43h-65z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="489" 
d="M432 477l-145 -77q1 0 145 -72v-148h16v-167h-412v167h325l-321 168v105l392 207v-183z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="489" 
d="M449 449v-104l-309 -162h313v-165h-412v165h16v142l115 58l29 14l-144 75v185z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="556" 
d="M216 -24l-196 355l201 357h117l198 -355l-200 -357h-120zM193 331l87 -156l83 153l-86 160z" />
    <glyph glyph-name="i.loclTRK" horiz-adv-x="291" 
d="M235 708v-708h-179v708h179z" />
    <glyph glyph-name="uni0414.loclBGR" horiz-adv-x="721" 
d="M197 -142h-182v303h64l185 547h189l191 -547h62v-303h-182v142h-327v-142zM397 352l-35 156h-4l-31 -158l-53 -189h172z" />
    <glyph glyph-name="uni041B.loclBGR" horiz-adv-x="634" 
d="M320 508h-6l-43 -207l-84 -301h-192l238 708h165l244 -708h-192l-89 301z" />
    <glyph glyph-name="uni0432.loclBGR" horiz-adv-x="626" 
d="M570 492q-1 -29 -19.5 -63t-53.5 -52q65 -33 87 -88t13 -106q-29 -183 -252 -183h-289v708h284q104 0 168.5 -58.5t61.5 -157.5zM236 431h104q41 0 58.5 31.5t0.5 63t-59 31.5h-104v-126zM236 157h104q51 0 71.5 36.5t-0.5 72.5t-71 36h-104v-145z" />
    <glyph glyph-name="uni0433.loclBGR" horiz-adv-x="511" 
d="M56 708h441v-170h-261v-538h-180v708z" />
    <glyph glyph-name="uni0434.loclBGR" horiz-adv-x="721" 
d="M197 -142h-182v303h64l185 547h189l191 -547h62v-303h-182v142h-327v-142zM397 352l-35 156h-4l-31 -158l-53 -189h172z" />
    <glyph glyph-name="uni0436.loclBGR" horiz-adv-x="948" 
d="M384 0v260l-6 8l-163 -268h-223l252 360l-253 348h216l177 -265v265h180v-265l177 265h216l-253 -348l252 -360h-223l-163 268l-6 -8v-260h-180z" />
    <glyph glyph-name="uni0438.loclBGR" horiz-adv-x="685" 
d="M449 0v283l18 133l-5 1l-67 -138l-170 -279h-169v708h180v-321l-15 -106l4 -1l59 127l181 301h164v-708h-180z" />
    <glyph glyph-name="uni0439.loclBGR" horiz-adv-x="685" 
d="M338 766q-75 0 -128 46t-53 128h139q0 -18 12 -29.5t30 -11.5t30 11.5t12 29.5h139q0 -82 -53 -128t-128 -46zM449 0v283l18 133l-5 1l-67 -138l-170 -279h-169v708h180v-321l-15 -106l4 -1l59 127l181 301h164v-708h-180z" />
    <glyph glyph-name="uni043A.loclBGR" horiz-adv-x="640" 
d="M236 446l192 262h223l-280 -348l279 -360h-225l-183 253l-6 -8v-245h-180v708h180v-262z" />
    <glyph glyph-name="uni043B.loclBGR" horiz-adv-x="634" 
d="M320 508h-6l-43 -207l-84 -301h-192l238 708h165l244 -708h-192l-89 301z" />
    <glyph glyph-name="uni043F.loclBGR" horiz-adv-x="671" 
d="M236 538v-538h-180v708h559v-708h-180v538h-199z" />
    <glyph glyph-name="uni0442.loclBGR" horiz-adv-x="585" 
d="M571 539h-188v-539h-181v539h-188v170h557v-170z" />
    <glyph glyph-name="uni0446.loclBGR" horiz-adv-x="715" 
d="M56 0v708h180v-538h193v538h180v-538h86v-312h-170v142h-469z" />
    <glyph glyph-name="uni0448.loclBGR" horiz-adv-x="988" 
d="M56 708h180v-538h168v538h180v-538h168v538h180v-708h-876v708z" />
    <glyph glyph-name="uni0449.loclBGR" horiz-adv-x="1038" 
d="M752 708h180v-538h86v-312h-170v142h-792v708h180v-538h168v538h180v-538h168v538z" />
    <glyph glyph-name="uni044E.loclBGR" horiz-adv-x="975" 
d="M347 439v36q0 115 87 179t207 64q76 0 141.5 -26t109.5 -83t44 -134v-242q0 -115 -87.5 -179t-206.5 -64q-76 0 -141.5 26t-109.5 83t-44 134v42h-112v-275h-180v708h180v-269h112zM527 468v-231q0 -36 34 -56.5t80 -20.5q47 0 81 20t34 57v231q0 38 -34 59t-81 21
q-46 -1 -80 -22t-34 -58z" />
    <glyph glyph-name="at.001" horiz-adv-x="750" 
d="M469 538l72 -18q-24 34 -72.5 52t-95.5 18q-77 0 -135 -42t-86 -103.5t-28 -129.5q0 -120 84 -182.5t209 -24.5l27 -96q-68 -21 -125 -21q-137 0 -230 98t-86 252q7 92 51 174.5t126.5 137.5t183.5 55q54 0 105 -13q65 -19 121 -61.5t83 -93.5q45 -87 25.5 -197
t-92.5 -173q-24 -22 -60.5 -36.5t-72.5 -14.5q-43 0 -73 21q-31 -19 -71 -19q-78 0 -127.5 55t-49.5 138q0 96 57 169.5t137 73.5q56 0 105 -40zM558 495l-47 -256q37 17 57 62t19 94t-20 82zM344 244q29 0 45 16l32 147q-53 56 -99 17q-37 -30 -40 -90q-2 -45 22 -74
q15 -16 40 -16z" />
    <hkern u1="&#x20;" u2="&#x1ef3;" k="33" />
    <hkern u1="&#x20;" u2="&#x1ef2;" k="33" />
    <hkern u1="&#x20;" u2="&#x1e85;" k="26" />
    <hkern u1="&#x20;" u2="&#x1e84;" k="26" />
    <hkern u1="&#x20;" u2="&#x1e83;" k="26" />
    <hkern u1="&#x20;" u2="&#x1e82;" k="26" />
    <hkern u1="&#x20;" u2="&#x1e81;" k="26" />
    <hkern u1="&#x20;" u2="&#x1e80;" k="26" />
    <hkern u1="&#x20;" u2="&#x21b;" k="21" />
    <hkern u1="&#x20;" u2="&#x21a;" k="21" />
    <hkern u1="&#x20;" u2="&#x178;" k="33" />
    <hkern u1="&#x20;" u2="&#x177;" k="33" />
    <hkern u1="&#x20;" u2="&#x176;" k="33" />
    <hkern u1="&#x20;" u2="&#x175;" k="26" />
    <hkern u1="&#x20;" u2="&#x174;" k="26" />
    <hkern u1="&#x20;" u2="&#x167;" k="21" />
    <hkern u1="&#x20;" u2="&#x166;" k="21" />
    <hkern u1="&#x20;" u2="&#x165;" k="21" />
    <hkern u1="&#x20;" u2="&#x164;" k="21" />
    <hkern u1="&#x20;" u2="&#x163;" k="21" />
    <hkern u1="&#x20;" u2="&#x162;" k="21" />
    <hkern u1="&#x20;" u2="&#x105;" k="26" />
    <hkern u1="&#x20;" u2="&#x104;" k="26" />
    <hkern u1="&#x20;" u2="&#x103;" k="26" />
    <hkern u1="&#x20;" u2="&#x102;" k="26" />
    <hkern u1="&#x20;" u2="&#x101;" k="26" />
    <hkern u1="&#x20;" u2="&#x100;" k="26" />
    <hkern u1="&#x20;" u2="&#xff;" k="33" />
    <hkern u1="&#x20;" u2="&#xfd;" k="33" />
    <hkern u1="&#x20;" u2="&#xe6;" k="28" />
    <hkern u1="&#x20;" u2="&#xe5;" k="26" />
    <hkern u1="&#x20;" u2="&#xe4;" k="26" />
    <hkern u1="&#x20;" u2="&#xe3;" k="26" />
    <hkern u1="&#x20;" u2="&#xe2;" k="26" />
    <hkern u1="&#x20;" u2="&#xe1;" k="26" />
    <hkern u1="&#x20;" u2="&#xe0;" k="26" />
    <hkern u1="&#x20;" u2="&#xdd;" k="33" />
    <hkern u1="&#x20;" u2="&#xc6;" k="28" />
    <hkern u1="&#x20;" u2="&#xc5;" k="26" />
    <hkern u1="&#x20;" u2="&#xc4;" k="26" />
    <hkern u1="&#x20;" u2="&#xc3;" k="26" />
    <hkern u1="&#x20;" u2="&#xc2;" k="26" />
    <hkern u1="&#x20;" u2="&#xc1;" k="26" />
    <hkern u1="&#x20;" u2="&#xc0;" k="26" />
    <hkern u1="&#x20;" u2="y" k="33" />
    <hkern u1="&#x20;" u2="x" k="10" />
    <hkern u1="&#x20;" u2="w" k="26" />
    <hkern u1="&#x20;" u2="v" k="29" />
    <hkern u1="&#x20;" u2="t" k="21" />
    <hkern u1="&#x20;" u2="j" k="21" />
    <hkern u1="&#x20;" u2="a" k="26" />
    <hkern u1="&#x20;" u2="Y" k="33" />
    <hkern u1="&#x20;" u2="X" k="10" />
    <hkern u1="&#x20;" u2="W" k="26" />
    <hkern u1="&#x20;" u2="V" k="29" />
    <hkern u1="&#x20;" u2="T" k="21" />
    <hkern u1="&#x20;" u2="J" k="21" />
    <hkern u1="&#x20;" u2="A" k="26" />
    <hkern u1="&#x20;" u2="&#x142;" k="31" />
    <hkern u1="&#x20;" u2="&#x141;" k="31" />
    <hkern u1="&#x20;" u2="&#x127;" k="10" />
    <hkern u1="&#x20;" u2="&#x126;" k="10" />
    <hkern u1="&#x20;" u2="\" k="10" />
    <hkern u1="&#x22;" u2="&#x2206;" k="32" />
    <hkern u1="&#x22;" u2="&#x41b;" k="60" />
    <hkern u1="&#x22;" u2="&#x414;" k="60" />
    <hkern u1="&#x22;" u2="&#x3a9;" k="8" />
    <hkern u1="&#x22;" u2="_" k="102" />
    <hkern u1="&#x22;" u2="&#x34;" k="29" />
    <hkern u1="&#x22;" u2="&#x2f;" k="49" />
    <hkern u1="&#x22;" u2="&#x26;" k="14" />
    <hkern u1="&#x26;" u2="&#x201d;" k="54" />
    <hkern u1="&#x26;" u2="&#x2019;" k="54" />
    <hkern u1="&#x26;" u2="&#x1ef3;" k="77" />
    <hkern u1="&#x26;" u2="&#x1ef2;" k="77" />
    <hkern u1="&#x26;" u2="&#x1e85;" k="53" />
    <hkern u1="&#x26;" u2="&#x1e84;" k="53" />
    <hkern u1="&#x26;" u2="&#x1e83;" k="53" />
    <hkern u1="&#x26;" u2="&#x1e82;" k="53" />
    <hkern u1="&#x26;" u2="&#x1e81;" k="53" />
    <hkern u1="&#x26;" u2="&#x1e80;" k="53" />
    <hkern u1="&#x26;" u2="&#x21b;" k="57" />
    <hkern u1="&#x26;" u2="&#x21a;" k="57" />
    <hkern u1="&#x26;" u2="&#x178;" k="77" />
    <hkern u1="&#x26;" u2="&#x177;" k="77" />
    <hkern u1="&#x26;" u2="&#x176;" k="77" />
    <hkern u1="&#x26;" u2="&#x175;" k="53" />
    <hkern u1="&#x26;" u2="&#x174;" k="53" />
    <hkern u1="&#x26;" u2="&#x173;" k="16" />
    <hkern u1="&#x26;" u2="&#x172;" k="16" />
    <hkern u1="&#x26;" u2="&#x171;" k="16" />
    <hkern u1="&#x26;" u2="&#x170;" k="16" />
    <hkern u1="&#x26;" u2="&#x16f;" k="16" />
    <hkern u1="&#x26;" u2="&#x16e;" k="16" />
    <hkern u1="&#x26;" u2="&#x16b;" k="16" />
    <hkern u1="&#x26;" u2="&#x16a;" k="16" />
    <hkern u1="&#x26;" u2="&#x167;" k="57" />
    <hkern u1="&#x26;" u2="&#x166;" k="57" />
    <hkern u1="&#x26;" u2="&#x165;" k="57" />
    <hkern u1="&#x26;" u2="&#x164;" k="57" />
    <hkern u1="&#x26;" u2="&#x163;" k="57" />
    <hkern u1="&#x26;" u2="&#x162;" k="57" />
    <hkern u1="&#x26;" u2="&#x153;" k="13" />
    <hkern u1="&#x26;" u2="&#x152;" k="13" />
    <hkern u1="&#x26;" u2="&#x151;" k="13" />
    <hkern u1="&#x26;" u2="&#x150;" k="13" />
    <hkern u1="&#x26;" u2="&#x14d;" k="13" />
    <hkern u1="&#x26;" u2="&#x14c;" k="13" />
    <hkern u1="&#x26;" u2="&#x123;" k="13" />
    <hkern u1="&#x26;" u2="&#x122;" k="13" />
    <hkern u1="&#x26;" u2="&#x121;" k="13" />
    <hkern u1="&#x26;" u2="&#x120;" k="13" />
    <hkern u1="&#x26;" u2="&#x11f;" k="13" />
    <hkern u1="&#x26;" u2="&#x11e;" k="13" />
    <hkern u1="&#x26;" u2="&#x10d;" k="13" />
    <hkern u1="&#x26;" u2="&#x10c;" k="13" />
    <hkern u1="&#x26;" u2="&#x10b;" k="13" />
    <hkern u1="&#x26;" u2="&#x10a;" k="13" />
    <hkern u1="&#x26;" u2="&#x107;" k="13" />
    <hkern u1="&#x26;" u2="&#x106;" k="13" />
    <hkern u1="&#x26;" u2="&#x105;" k="-32" />
    <hkern u1="&#x26;" u2="&#x104;" k="-32" />
    <hkern u1="&#x26;" u2="&#x103;" k="-32" />
    <hkern u1="&#x26;" u2="&#x102;" k="-32" />
    <hkern u1="&#x26;" u2="&#x101;" k="-32" />
    <hkern u1="&#x26;" u2="&#x100;" k="-32" />
    <hkern u1="&#x26;" u2="&#xff;" k="77" />
    <hkern u1="&#x26;" u2="&#xfd;" k="77" />
    <hkern u1="&#x26;" u2="&#xfc;" k="16" />
    <hkern u1="&#x26;" u2="&#xfb;" k="16" />
    <hkern u1="&#x26;" u2="&#xfa;" k="16" />
    <hkern u1="&#x26;" u2="&#xf9;" k="16" />
    <hkern u1="&#x26;" u2="&#xf8;" k="13" />
    <hkern u1="&#x26;" u2="&#xf6;" k="13" />
    <hkern u1="&#x26;" u2="&#xf5;" k="13" />
    <hkern u1="&#x26;" u2="&#xf4;" k="13" />
    <hkern u1="&#x26;" u2="&#xf3;" k="13" />
    <hkern u1="&#x26;" u2="&#xf2;" k="13" />
    <hkern u1="&#x26;" u2="&#xe7;" k="13" />
    <hkern u1="&#x26;" u2="&#xe6;" k="-40" />
    <hkern u1="&#x26;" u2="&#xe5;" k="-32" />
    <hkern u1="&#x26;" u2="&#xe4;" k="-32" />
    <hkern u1="&#x26;" u2="&#xe3;" k="-32" />
    <hkern u1="&#x26;" u2="&#xe2;" k="-32" />
    <hkern u1="&#x26;" u2="&#xe1;" k="-32" />
    <hkern u1="&#x26;" u2="&#xe0;" k="-32" />
    <hkern u1="&#x26;" u2="&#xdd;" k="77" />
    <hkern u1="&#x26;" u2="&#xdc;" k="16" />
    <hkern u1="&#x26;" u2="&#xdb;" k="16" />
    <hkern u1="&#x26;" u2="&#xda;" k="16" />
    <hkern u1="&#x26;" u2="&#xd9;" k="16" />
    <hkern u1="&#x26;" u2="&#xd8;" k="13" />
    <hkern u1="&#x26;" u2="&#xd6;" k="13" />
    <hkern u1="&#x26;" u2="&#xd5;" k="13" />
    <hkern u1="&#x26;" u2="&#xd4;" k="13" />
    <hkern u1="&#x26;" u2="&#xd3;" k="13" />
    <hkern u1="&#x26;" u2="&#xd2;" k="13" />
    <hkern u1="&#x26;" u2="&#xc7;" k="13" />
    <hkern u1="&#x26;" u2="&#xc6;" k="-40" />
    <hkern u1="&#x26;" u2="&#xc5;" k="-32" />
    <hkern u1="&#x26;" u2="&#xc4;" k="-32" />
    <hkern u1="&#x26;" u2="&#xc3;" k="-32" />
    <hkern u1="&#x26;" u2="&#xc2;" k="-32" />
    <hkern u1="&#x26;" u2="&#xc1;" k="-32" />
    <hkern u1="&#x26;" u2="&#xc0;" k="-32" />
    <hkern u1="&#x26;" u2="y" k="77" />
    <hkern u1="&#x26;" u2="x" k="-30" />
    <hkern u1="&#x26;" u2="w" k="53" />
    <hkern u1="&#x26;" u2="v" k="66" />
    <hkern u1="&#x26;" u2="u" k="16" />
    <hkern u1="&#x26;" u2="t" k="57" />
    <hkern u1="&#x26;" u2="q" k="13" />
    <hkern u1="&#x26;" u2="o" k="13" />
    <hkern u1="&#x26;" u2="g" k="13" />
    <hkern u1="&#x26;" u2="c" k="13" />
    <hkern u1="&#x26;" u2="a" k="-32" />
    <hkern u1="&#x26;" u2="Y" k="77" />
    <hkern u1="&#x26;" u2="X" k="-30" />
    <hkern u1="&#x26;" u2="W" k="53" />
    <hkern u1="&#x26;" u2="V" k="66" />
    <hkern u1="&#x26;" u2="U" k="16" />
    <hkern u1="&#x26;" u2="T" k="57" />
    <hkern u1="&#x26;" u2="Q" k="13" />
    <hkern u1="&#x26;" u2="O" k="13" />
    <hkern u1="&#x26;" u2="G" k="13" />
    <hkern u1="&#x26;" u2="C" k="13" />
    <hkern u1="&#x26;" u2="A" k="-32" />
    <hkern u1="&#x26;" u2="&#x27;" k="53" />
    <hkern u1="&#x26;" u2="&#x22;" k="53" />
    <hkern u1="&#x26;" u2="\" k="69" />
    <hkern u1="&#x27;" u2="&#x2206;" k="32" />
    <hkern u1="&#x27;" u2="&#x41b;" k="60" />
    <hkern u1="&#x27;" u2="&#x414;" k="60" />
    <hkern u1="&#x27;" u2="&#x3a9;" k="8" />
    <hkern u1="&#x27;" u2="_" k="102" />
    <hkern u1="&#x27;" u2="&#x34;" k="29" />
    <hkern u1="&#x27;" u2="&#x2f;" k="49" />
    <hkern u1="&#x27;" u2="&#x26;" k="14" />
    <hkern u1="&#x28;" g2="uni0436.loclBGR" k="-10" />
    <hkern u1="&#x28;" u2="&#x1ef3;" k="-9" />
    <hkern u1="&#x28;" u2="&#x1ef2;" k="-9" />
    <hkern u1="&#x28;" u2="&#x44f;" k="-19" />
    <hkern u1="&#x28;" u2="&#x444;" k="26" />
    <hkern u1="&#x28;" u2="&#x443;" k="-28" />
    <hkern u1="&#x28;" u2="&#x441;" k="21" />
    <hkern u1="&#x28;" u2="&#x43e;" k="21" />
    <hkern u1="&#x28;" u2="&#x436;" k="-10" />
    <hkern u1="&#x28;" u2="&#x42f;" k="-19" />
    <hkern u1="&#x28;" u2="&#x424;" k="26" />
    <hkern u1="&#x28;" u2="&#x423;" k="-28" />
    <hkern u1="&#x28;" u2="&#x421;" k="21" />
    <hkern u1="&#x28;" u2="&#x41e;" k="21" />
    <hkern u1="&#x28;" u2="&#x416;" k="-10" />
    <hkern u1="&#x28;" u2="&#x219;" k="10" />
    <hkern u1="&#x28;" u2="&#x218;" k="10" />
    <hkern u1="&#x28;" u2="&#x178;" k="-9" />
    <hkern u1="&#x28;" u2="&#x177;" k="-9" />
    <hkern u1="&#x28;" u2="&#x176;" k="-9" />
    <hkern u1="&#x28;" u2="&#x161;" k="10" />
    <hkern u1="&#x28;" u2="&#x160;" k="10" />
    <hkern u1="&#x28;" u2="&#x15f;" k="10" />
    <hkern u1="&#x28;" u2="&#x15e;" k="10" />
    <hkern u1="&#x28;" u2="&#x15b;" k="10" />
    <hkern u1="&#x28;" u2="&#x15a;" k="10" />
    <hkern u1="&#x28;" u2="&#x153;" k="21" />
    <hkern u1="&#x28;" u2="&#x152;" k="21" />
    <hkern u1="&#x28;" u2="&#x151;" k="21" />
    <hkern u1="&#x28;" u2="&#x150;" k="21" />
    <hkern u1="&#x28;" u2="&#x14d;" k="21" />
    <hkern u1="&#x28;" u2="&#x14c;" k="21" />
    <hkern u1="&#x28;" u2="&#x123;" k="21" />
    <hkern u1="&#x28;" u2="&#x122;" k="21" />
    <hkern u1="&#x28;" u2="&#x121;" k="21" />
    <hkern u1="&#x28;" u2="&#x120;" k="21" />
    <hkern u1="&#x28;" u2="&#x11f;" k="21" />
    <hkern u1="&#x28;" u2="&#x11e;" k="21" />
    <hkern u1="&#x28;" u2="&#x10d;" k="21" />
    <hkern u1="&#x28;" u2="&#x10c;" k="21" />
    <hkern u1="&#x28;" u2="&#x10b;" k="21" />
    <hkern u1="&#x28;" u2="&#x10a;" k="21" />
    <hkern u1="&#x28;" u2="&#x107;" k="21" />
    <hkern u1="&#x28;" u2="&#x106;" k="21" />
    <hkern u1="&#x28;" u2="&#xff;" k="-9" />
    <hkern u1="&#x28;" u2="&#xfd;" k="-9" />
    <hkern u1="&#x28;" u2="&#xf8;" k="21" />
    <hkern u1="&#x28;" u2="&#xf6;" k="21" />
    <hkern u1="&#x28;" u2="&#xf5;" k="21" />
    <hkern u1="&#x28;" u2="&#xf4;" k="21" />
    <hkern u1="&#x28;" u2="&#xf3;" k="21" />
    <hkern u1="&#x28;" u2="&#xf2;" k="21" />
    <hkern u1="&#x28;" u2="&#xe7;" k="21" />
    <hkern u1="&#x28;" u2="&#xdd;" k="-9" />
    <hkern u1="&#x28;" u2="&#xd8;" k="21" />
    <hkern u1="&#x28;" u2="&#xd6;" k="21" />
    <hkern u1="&#x28;" u2="&#xd5;" k="21" />
    <hkern u1="&#x28;" u2="&#xd4;" k="21" />
    <hkern u1="&#x28;" u2="&#xd3;" k="21" />
    <hkern u1="&#x28;" u2="&#xd2;" k="21" />
    <hkern u1="&#x28;" u2="&#xc7;" k="21" />
    <hkern u1="&#x28;" u2="y" k="-9" />
    <hkern u1="&#x28;" u2="v" k="-9" />
    <hkern u1="&#x28;" u2="s" k="10" />
    <hkern u1="&#x28;" u2="q" k="21" />
    <hkern u1="&#x28;" u2="o" k="21" />
    <hkern u1="&#x28;" u2="j" k="19" />
    <hkern u1="&#x28;" u2="g" k="21" />
    <hkern u1="&#x28;" u2="c" k="21" />
    <hkern u1="&#x28;" u2="Y" k="-9" />
    <hkern u1="&#x28;" u2="V" k="-9" />
    <hkern u1="&#x28;" u2="S" k="10" />
    <hkern u1="&#x28;" u2="Q" k="21" />
    <hkern u1="&#x28;" u2="O" k="21" />
    <hkern u1="&#x28;" u2="J" k="19" />
    <hkern u1="&#x28;" u2="G" k="21" />
    <hkern u1="&#x28;" u2="C" k="21" />
    <hkern u1="&#x28;" u2="&#x3c0;" k="23" />
    <hkern u1="&#x28;" u2="&#xee;" k="-41" />
    <hkern u1="&#x28;" u2="&#xce;" k="-41" />
    <hkern u1="&#x28;" u2="&#x7b;" k="15" />
    <hkern u1="&#x28;" u2="&#x39;" k="12" />
    <hkern u1="&#x28;" u2="&#x38;" k="19" />
    <hkern u1="&#x28;" u2="&#x36;" k="20" />
    <hkern u1="&#x28;" u2="&#x34;" k="29" />
    <hkern u1="&#x28;" u2="&#x31;" k="20" />
    <hkern u1="&#x28;" u2="&#x30;" k="18" />
    <hkern u1="&#x28;" u2="&#x28;" k="16" />
    <hkern u1="&#x29;" u2="&#x29;" k="16" />
    <hkern u1="&#x2a;" g2="uni043B.loclBGR" k="51" />
    <hkern u1="&#x2a;" g2="uni0436.loclBGR" k="8" />
    <hkern u1="&#x2a;" g2="uni0434.loclBGR" k="64" />
    <hkern u1="&#x2a;" g2="uni041B.loclBGR" k="51" />
    <hkern u1="&#x2a;" g2="uni0414.loclBGR" k="64" />
    <hkern u1="&#x2a;" u2="&#x43b;" k="51" />
    <hkern u1="&#x2a;" u2="&#x436;" k="8" />
    <hkern u1="&#x2a;" u2="&#x434;" k="64" />
    <hkern u1="&#x2a;" u2="&#x430;" k="51" />
    <hkern u1="&#x2a;" u2="&#x416;" k="8" />
    <hkern u1="&#x2a;" u2="&#x410;" k="51" />
    <hkern u1="&#x2a;" u2="&#x105;" k="51" />
    <hkern u1="&#x2a;" u2="&#x104;" k="51" />
    <hkern u1="&#x2a;" u2="&#x103;" k="51" />
    <hkern u1="&#x2a;" u2="&#x102;" k="51" />
    <hkern u1="&#x2a;" u2="&#x101;" k="51" />
    <hkern u1="&#x2a;" u2="&#x100;" k="51" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="64" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="51" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="51" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="51" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="51" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="51" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="51" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="64" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="51" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="51" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="51" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="51" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="51" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="51" />
    <hkern u1="&#x2a;" u2="j" k="71" />
    <hkern u1="&#x2a;" u2="a" k="51" />
    <hkern u1="&#x2a;" u2="J" k="71" />
    <hkern u1="&#x2a;" u2="A" k="51" />
    <hkern u1="&#x2a;" u2="&#x2206;" k="27" />
    <hkern u1="&#x2a;" u2="&#x41b;" k="54" />
    <hkern u1="&#x2a;" u2="&#x414;" k="54" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="43" />
    <hkern u1="&#x2c;" u2="&#x3c0;" k="18" />
    <hkern u1="&#x2c;" u2="\" k="64" />
    <hkern u1="&#x2c;" u2="&#x37;" k="18" />
    <hkern u1="&#x2c;" u2="&#x31;" k="43" />
    <hkern u1="&#x2d;" u2="&#x41b;" k="19" />
    <hkern u1="&#x2d;" u2="&#x414;" k="22" />
    <hkern u1="&#x2d;" u2="&#x167;" k="45" />
    <hkern u1="&#x2d;" u2="&#x166;" k="45" />
    <hkern u1="&#x2d;" u2="\" k="40" />
    <hkern u1="&#x2d;" u2="&#x37;" k="25" />
    <hkern u1="&#x2d;" u2="&#x33;" k="11" />
    <hkern u1="&#x2d;" u2="&#x32;" k="14" />
    <hkern u1="&#x2d;" u2="&#x31;" k="15" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="21" />
    <hkern u1="&#x2e;" u2="&#x3c0;" k="18" />
    <hkern u1="&#x2e;" u2="\" k="64" />
    <hkern u1="&#x2e;" u2="&#x37;" k="18" />
    <hkern u1="&#x2e;" u2="&#x31;" k="43" />
    <hkern u1="&#x2f;" g2="uni043B.loclBGR" k="65" />
    <hkern u1="&#x2f;" g2="uni0436.loclBGR" k="-12" />
    <hkern u1="&#x2f;" g2="uni0434.loclBGR" k="68" />
    <hkern u1="&#x2f;" g2="uni041B.loclBGR" k="65" />
    <hkern u1="&#x2f;" g2="uni0414.loclBGR" k="68" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="29" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="44" />
    <hkern u1="&#x2f;" u2="&#x2026;" k="61" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="61" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="61" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="37" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="37" />
    <hkern u1="&#x2f;" u2="&#x1ef3;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x1ef2;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x44f;" k="14" />
    <hkern u1="&#x2f;" u2="&#x445;" k="-6" />
    <hkern u1="&#x2f;" u2="&#x444;" k="17" />
    <hkern u1="&#x2f;" u2="&#x443;" k="-30" />
    <hkern u1="&#x2f;" u2="&#x441;" k="11" />
    <hkern u1="&#x2f;" u2="&#x43e;" k="11" />
    <hkern u1="&#x2f;" u2="&#x43b;" k="65" />
    <hkern u1="&#x2f;" u2="&#x436;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x434;" k="68" />
    <hkern u1="&#x2f;" u2="&#x430;" k="65" />
    <hkern u1="&#x2f;" u2="&#x42f;" k="14" />
    <hkern u1="&#x2f;" u2="&#x425;" k="-6" />
    <hkern u1="&#x2f;" u2="&#x424;" k="17" />
    <hkern u1="&#x2f;" u2="&#x423;" k="-30" />
    <hkern u1="&#x2f;" u2="&#x421;" k="11" />
    <hkern u1="&#x2f;" u2="&#x41e;" k="11" />
    <hkern u1="&#x2f;" u2="&#x416;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x410;" k="65" />
    <hkern u1="&#x2f;" u2="&#x178;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x177;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x176;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x153;" k="11" />
    <hkern u1="&#x2f;" u2="&#x152;" k="11" />
    <hkern u1="&#x2f;" u2="&#x151;" k="11" />
    <hkern u1="&#x2f;" u2="&#x150;" k="11" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="11" />
    <hkern u1="&#x2f;" u2="&#x14c;" k="11" />
    <hkern u1="&#x2f;" u2="&#x123;" k="11" />
    <hkern u1="&#x2f;" u2="&#x122;" k="11" />
    <hkern u1="&#x2f;" u2="&#x121;" k="11" />
    <hkern u1="&#x2f;" u2="&#x120;" k="11" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="11" />
    <hkern u1="&#x2f;" u2="&#x11e;" k="11" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="11" />
    <hkern u1="&#x2f;" u2="&#x10c;" k="11" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="11" />
    <hkern u1="&#x2f;" u2="&#x10a;" k="11" />
    <hkern u1="&#x2f;" u2="&#x107;" k="11" />
    <hkern u1="&#x2f;" u2="&#x106;" k="11" />
    <hkern u1="&#x2f;" u2="&#x105;" k="65" />
    <hkern u1="&#x2f;" u2="&#x104;" k="65" />
    <hkern u1="&#x2f;" u2="&#x103;" k="65" />
    <hkern u1="&#x2f;" u2="&#x102;" k="65" />
    <hkern u1="&#x2f;" u2="&#x101;" k="65" />
    <hkern u1="&#x2f;" u2="&#x100;" k="65" />
    <hkern u1="&#x2f;" u2="&#xff;" k="-12" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="-12" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="11" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="11" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="11" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="11" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="11" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="11" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="11" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="72" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="65" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="65" />
    <hkern u1="&#x2f;" u2="&#xdd;" k="-12" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="11" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="11" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="72" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="65" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="29" />
    <hkern u1="&#x2f;" u2="&#xad;" k="37" />
    <hkern u1="&#x2f;" u2="&#xab;" k="44" />
    <hkern u1="&#x2f;" u2="y" k="-12" />
    <hkern u1="&#x2f;" u2="x" k="-6" />
    <hkern u1="&#x2f;" u2="v" k="-12" />
    <hkern u1="&#x2f;" u2="q" k="11" />
    <hkern u1="&#x2f;" u2="o" k="11" />
    <hkern u1="&#x2f;" u2="j" k="53" />
    <hkern u1="&#x2f;" u2="g" k="11" />
    <hkern u1="&#x2f;" u2="c" k="11" />
    <hkern u1="&#x2f;" u2="a" k="65" />
    <hkern u1="&#x2f;" u2="Y" k="-12" />
    <hkern u1="&#x2f;" u2="X" k="-6" />
    <hkern u1="&#x2f;" u2="V" k="-12" />
    <hkern u1="&#x2f;" u2="Q" k="11" />
    <hkern u1="&#x2f;" u2="O" k="11" />
    <hkern u1="&#x2f;" u2="J" k="53" />
    <hkern u1="&#x2f;" u2="G" k="11" />
    <hkern u1="&#x2f;" u2="C" k="11" />
    <hkern u1="&#x2f;" u2="A" k="65" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="27" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="27" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="61" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="37" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="61" />
    <hkern u1="&#x2f;" u2="&#x2206;" k="55" />
    <hkern u1="&#x2f;" u2="&#x2126;" k="26" />
    <hkern u1="&#x2f;" u2="&#x41b;" k="52" />
    <hkern u1="&#x2f;" u2="&#x414;" k="52" />
    <hkern u1="&#x2f;" u2="&#x3c0;" k="17" />
    <hkern u1="&#x2f;" u2="&#x3bc;" k="27" />
    <hkern u1="&#x2f;" u2="&#x3a9;" k="25" />
    <hkern u1="&#x2f;" u2="&#xee;" k="-28" />
    <hkern u1="&#x2f;" u2="&#xdf;" k="15" />
    <hkern u1="&#x2f;" u2="&#xce;" k="-28" />
    <hkern u1="&#x2f;" u2="&#xae;" k="24" />
    <hkern u1="&#x2f;" u2="&#xa9;" k="24" />
    <hkern u1="&#x2f;" u2="_" k="55" />
    <hkern u1="&#x2f;" u2="&#x40;" k="28" />
    <hkern u1="&#x2f;" u2="&#x38;" k="18" />
    <hkern u1="&#x2f;" u2="&#x34;" k="46" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="242" />
    <hkern u1="&#x2f;" u2="&#x26;" k="25" />
    <hkern u1="&#x30;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x30;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x30;" u2="&#x178;" k="20" />
    <hkern u1="&#x30;" u2="&#x177;" k="20" />
    <hkern u1="&#x30;" u2="&#x176;" k="20" />
    <hkern u1="&#x30;" u2="&#x105;" k="11" />
    <hkern u1="&#x30;" u2="&#x104;" k="11" />
    <hkern u1="&#x30;" u2="&#x103;" k="11" />
    <hkern u1="&#x30;" u2="&#x102;" k="11" />
    <hkern u1="&#x30;" u2="&#x101;" k="11" />
    <hkern u1="&#x30;" u2="&#x100;" k="11" />
    <hkern u1="&#x30;" u2="&#xff;" k="20" />
    <hkern u1="&#x30;" u2="&#xfd;" k="20" />
    <hkern u1="&#x30;" u2="&#xe5;" k="11" />
    <hkern u1="&#x30;" u2="&#xe4;" k="11" />
    <hkern u1="&#x30;" u2="&#xe3;" k="11" />
    <hkern u1="&#x30;" u2="&#xe2;" k="11" />
    <hkern u1="&#x30;" u2="&#xe1;" k="11" />
    <hkern u1="&#x30;" u2="&#xe0;" k="11" />
    <hkern u1="&#x30;" u2="&#xdd;" k="20" />
    <hkern u1="&#x30;" u2="&#xc5;" k="11" />
    <hkern u1="&#x30;" u2="&#xc4;" k="11" />
    <hkern u1="&#x30;" u2="&#xc3;" k="11" />
    <hkern u1="&#x30;" u2="&#xc2;" k="11" />
    <hkern u1="&#x30;" u2="&#xc1;" k="11" />
    <hkern u1="&#x30;" u2="&#xc0;" k="11" />
    <hkern u1="&#x30;" u2="y" k="20" />
    <hkern u1="&#x30;" u2="v" k="11" />
    <hkern u1="&#x30;" u2="a" k="11" />
    <hkern u1="&#x30;" u2="Y" k="20" />
    <hkern u1="&#x30;" u2="V" k="11" />
    <hkern u1="&#x30;" u2="A" k="11" />
    <hkern u1="&#x30;" u2="&#x2044;" k="16" />
    <hkern u1="&#x30;" u2="&#x2f;" k="10" />
    <hkern u1="&#x30;" u2="&#x29;" k="16" />
    <hkern u1="&#x32;" u2="&#x1ef3;" k="18" />
    <hkern u1="&#x32;" u2="&#x1ef2;" k="18" />
    <hkern u1="&#x32;" u2="&#x178;" k="18" />
    <hkern u1="&#x32;" u2="&#x177;" k="18" />
    <hkern u1="&#x32;" u2="&#x176;" k="18" />
    <hkern u1="&#x32;" u2="&#xff;" k="18" />
    <hkern u1="&#x32;" u2="&#xfd;" k="18" />
    <hkern u1="&#x32;" u2="&#xdd;" k="18" />
    <hkern u1="&#x32;" u2="y" k="18" />
    <hkern u1="&#x32;" u2="v" k="11" />
    <hkern u1="&#x32;" u2="Y" k="18" />
    <hkern u1="&#x32;" u2="V" k="11" />
    <hkern u1="&#x32;" u2="&#xb7;" k="10" />
    <hkern u1="&#x33;" u2="&#x1ef3;" k="25" />
    <hkern u1="&#x33;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x33;" u2="&#x1e85;" k="12" />
    <hkern u1="&#x33;" u2="&#x1e84;" k="12" />
    <hkern u1="&#x33;" u2="&#x1e83;" k="12" />
    <hkern u1="&#x33;" u2="&#x1e82;" k="12" />
    <hkern u1="&#x33;" u2="&#x1e81;" k="12" />
    <hkern u1="&#x33;" u2="&#x1e80;" k="12" />
    <hkern u1="&#x33;" u2="&#x178;" k="25" />
    <hkern u1="&#x33;" u2="&#x177;" k="25" />
    <hkern u1="&#x33;" u2="&#x176;" k="25" />
    <hkern u1="&#x33;" u2="&#x175;" k="12" />
    <hkern u1="&#x33;" u2="&#x174;" k="12" />
    <hkern u1="&#x33;" u2="&#x105;" k="10" />
    <hkern u1="&#x33;" u2="&#x104;" k="10" />
    <hkern u1="&#x33;" u2="&#x103;" k="10" />
    <hkern u1="&#x33;" u2="&#x102;" k="10" />
    <hkern u1="&#x33;" u2="&#x101;" k="10" />
    <hkern u1="&#x33;" u2="&#x100;" k="10" />
    <hkern u1="&#x33;" u2="&#xff;" k="25" />
    <hkern u1="&#x33;" u2="&#xfd;" k="25" />
    <hkern u1="&#x33;" u2="&#xe5;" k="10" />
    <hkern u1="&#x33;" u2="&#xe4;" k="10" />
    <hkern u1="&#x33;" u2="&#xe3;" k="10" />
    <hkern u1="&#x33;" u2="&#xe2;" k="10" />
    <hkern u1="&#x33;" u2="&#xe1;" k="10" />
    <hkern u1="&#x33;" u2="&#xe0;" k="10" />
    <hkern u1="&#x33;" u2="&#xdd;" k="25" />
    <hkern u1="&#x33;" u2="&#xc5;" k="10" />
    <hkern u1="&#x33;" u2="&#xc4;" k="10" />
    <hkern u1="&#x33;" u2="&#xc3;" k="10" />
    <hkern u1="&#x33;" u2="&#xc2;" k="10" />
    <hkern u1="&#x33;" u2="&#xc1;" k="10" />
    <hkern u1="&#x33;" u2="&#xc0;" k="10" />
    <hkern u1="&#x33;" u2="y" k="25" />
    <hkern u1="&#x33;" u2="w" k="12" />
    <hkern u1="&#x33;" u2="v" k="15" />
    <hkern u1="&#x33;" u2="a" k="10" />
    <hkern u1="&#x33;" u2="Y" k="25" />
    <hkern u1="&#x33;" u2="W" k="12" />
    <hkern u1="&#x33;" u2="V" k="15" />
    <hkern u1="&#x33;" u2="A" k="10" />
    <hkern u1="&#x33;" u2="&#x2044;" k="11" />
    <hkern u1="&#x33;" u2="\" k="17" />
    <hkern u1="&#x33;" u2="&#x29;" k="18" />
    <hkern u1="&#x34;" u2="&#x1ef3;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ef2;" k="19" />
    <hkern u1="&#x34;" u2="&#x1e85;" k="13" />
    <hkern u1="&#x34;" u2="&#x1e84;" k="13" />
    <hkern u1="&#x34;" u2="&#x1e83;" k="13" />
    <hkern u1="&#x34;" u2="&#x1e82;" k="13" />
    <hkern u1="&#x34;" u2="&#x1e81;" k="13" />
    <hkern u1="&#x34;" u2="&#x1e80;" k="13" />
    <hkern u1="&#x34;" u2="&#x21b;" k="12" />
    <hkern u1="&#x34;" u2="&#x21a;" k="12" />
    <hkern u1="&#x34;" u2="&#x178;" k="19" />
    <hkern u1="&#x34;" u2="&#x177;" k="19" />
    <hkern u1="&#x34;" u2="&#x176;" k="19" />
    <hkern u1="&#x34;" u2="&#x175;" k="13" />
    <hkern u1="&#x34;" u2="&#x174;" k="13" />
    <hkern u1="&#x34;" u2="&#x167;" k="12" />
    <hkern u1="&#x34;" u2="&#x166;" k="12" />
    <hkern u1="&#x34;" u2="&#x165;" k="12" />
    <hkern u1="&#x34;" u2="&#x164;" k="12" />
    <hkern u1="&#x34;" u2="&#x163;" k="12" />
    <hkern u1="&#x34;" u2="&#x162;" k="12" />
    <hkern u1="&#x34;" u2="&#xff;" k="19" />
    <hkern u1="&#x34;" u2="&#xfd;" k="19" />
    <hkern u1="&#x34;" u2="&#xdd;" k="19" />
    <hkern u1="&#x34;" u2="y" k="19" />
    <hkern u1="&#x34;" u2="w" k="13" />
    <hkern u1="&#x34;" u2="v" k="14" />
    <hkern u1="&#x34;" u2="t" k="12" />
    <hkern u1="&#x34;" u2="Y" k="19" />
    <hkern u1="&#x34;" u2="W" k="13" />
    <hkern u1="&#x34;" u2="V" k="14" />
    <hkern u1="&#x34;" u2="T" k="12" />
    <hkern u1="&#x34;" u2="&#x27;" k="14" />
    <hkern u1="&#x34;" u2="&#x22;" k="14" />
    <hkern u1="&#x34;" u2="&#xb0;" k="13" />
    <hkern u1="&#x34;" u2="\" k="14" />
    <hkern u1="&#x34;" u2="&#x31;" k="11" />
    <hkern u1="&#x34;" u2="&#x29;" k="12" />
    <hkern u1="&#x35;" u2="&#x105;" k="10" />
    <hkern u1="&#x35;" u2="&#x104;" k="10" />
    <hkern u1="&#x35;" u2="&#x103;" k="10" />
    <hkern u1="&#x35;" u2="&#x102;" k="10" />
    <hkern u1="&#x35;" u2="&#x101;" k="10" />
    <hkern u1="&#x35;" u2="&#x100;" k="10" />
    <hkern u1="&#x35;" u2="&#xe5;" k="10" />
    <hkern u1="&#x35;" u2="&#xe4;" k="10" />
    <hkern u1="&#x35;" u2="&#xe3;" k="10" />
    <hkern u1="&#x35;" u2="&#xe2;" k="10" />
    <hkern u1="&#x35;" u2="&#xe1;" k="10" />
    <hkern u1="&#x35;" u2="&#xe0;" k="10" />
    <hkern u1="&#x35;" u2="&#xc5;" k="10" />
    <hkern u1="&#x35;" u2="&#xc4;" k="10" />
    <hkern u1="&#x35;" u2="&#xc3;" k="10" />
    <hkern u1="&#x35;" u2="&#xc2;" k="10" />
    <hkern u1="&#x35;" u2="&#xc1;" k="10" />
    <hkern u1="&#x35;" u2="&#xc0;" k="10" />
    <hkern u1="&#x35;" u2="a" k="10" />
    <hkern u1="&#x35;" u2="A" k="10" />
    <hkern u1="&#x35;" u2="&#x2044;" k="12" />
    <hkern u1="&#x35;" u2="&#x31;" k="10" />
    <hkern u1="&#x36;" u2="&#x1ef3;" k="19" />
    <hkern u1="&#x36;" u2="&#x1ef2;" k="19" />
    <hkern u1="&#x36;" u2="&#x178;" k="19" />
    <hkern u1="&#x36;" u2="&#x177;" k="19" />
    <hkern u1="&#x36;" u2="&#x176;" k="19" />
    <hkern u1="&#x36;" u2="&#xff;" k="19" />
    <hkern u1="&#x36;" u2="&#xfd;" k="19" />
    <hkern u1="&#x36;" u2="&#xdd;" k="19" />
    <hkern u1="&#x36;" u2="y" k="19" />
    <hkern u1="&#x36;" u2="v" k="12" />
    <hkern u1="&#x36;" u2="Y" k="19" />
    <hkern u1="&#x36;" u2="V" k="12" />
    <hkern u1="&#x36;" u2="\" k="11" />
    <hkern u1="&#x36;" u2="&#x31;" k="15" />
    <hkern u1="&#x36;" u2="&#x29;" k="12" />
    <hkern u1="&#x37;" u2="&#x2026;" k="59" />
    <hkern u1="&#x37;" u2="&#x201e;" k="59" />
    <hkern u1="&#x37;" u2="&#x201a;" k="59" />
    <hkern u1="&#x37;" u2="&#x2014;" k="29" />
    <hkern u1="&#x37;" u2="&#x2013;" k="29" />
    <hkern u1="&#x37;" u2="&#x21b;" k="-6" />
    <hkern u1="&#x37;" u2="&#x21a;" k="-6" />
    <hkern u1="&#x37;" u2="&#x167;" k="-6" />
    <hkern u1="&#x37;" u2="&#x166;" k="-6" />
    <hkern u1="&#x37;" u2="&#x165;" k="-6" />
    <hkern u1="&#x37;" u2="&#x164;" k="-6" />
    <hkern u1="&#x37;" u2="&#x163;" k="-6" />
    <hkern u1="&#x37;" u2="&#x162;" k="-6" />
    <hkern u1="&#x37;" u2="&#x105;" k="58" />
    <hkern u1="&#x37;" u2="&#x104;" k="58" />
    <hkern u1="&#x37;" u2="&#x103;" k="58" />
    <hkern u1="&#x37;" u2="&#x102;" k="58" />
    <hkern u1="&#x37;" u2="&#x101;" k="58" />
    <hkern u1="&#x37;" u2="&#x100;" k="58" />
    <hkern u1="&#x37;" u2="&#xe5;" k="58" />
    <hkern u1="&#x37;" u2="&#xe4;" k="58" />
    <hkern u1="&#x37;" u2="&#xe3;" k="58" />
    <hkern u1="&#x37;" u2="&#xe2;" k="58" />
    <hkern u1="&#x37;" u2="&#xe1;" k="58" />
    <hkern u1="&#x37;" u2="&#xe0;" k="58" />
    <hkern u1="&#x37;" u2="&#xc5;" k="58" />
    <hkern u1="&#x37;" u2="&#xc4;" k="58" />
    <hkern u1="&#x37;" u2="&#xc3;" k="58" />
    <hkern u1="&#x37;" u2="&#xc2;" k="58" />
    <hkern u1="&#x37;" u2="&#xc1;" k="58" />
    <hkern u1="&#x37;" u2="&#xc0;" k="58" />
    <hkern u1="&#x37;" u2="&#xad;" k="29" />
    <hkern u1="&#x37;" u2="t" k="-6" />
    <hkern u1="&#x37;" u2="j" k="50" />
    <hkern u1="&#x37;" u2="a" k="58" />
    <hkern u1="&#x37;" u2="T" k="-6" />
    <hkern u1="&#x37;" u2="J" k="50" />
    <hkern u1="&#x37;" u2="A" k="58" />
    <hkern u1="&#x37;" u2="&#x2e;" k="59" />
    <hkern u1="&#x37;" u2="&#x2d;" k="29" />
    <hkern u1="&#x37;" u2="&#x2c;" k="59" />
    <hkern u1="&#x37;" u2="&#x2212;" k="28" />
    <hkern u1="&#x37;" u2="&#x2044;" k="79" />
    <hkern u1="&#x37;" u2="&#xf7;" k="26" />
    <hkern u1="&#x37;" u2="&#xd7;" k="22" />
    <hkern u1="&#x37;" u2="&#xb7;" k="29" />
    <hkern u1="&#x37;" u2="&#xa2;" k="27" />
    <hkern u1="&#x37;" u2="&#x3d;" k="14" />
    <hkern u1="&#x37;" u2="&#x34;" k="38" />
    <hkern u1="&#x37;" u2="&#x2f;" k="63" />
    <hkern u1="&#x37;" u2="&#x2b;" k="19" />
    <hkern u1="&#x37;" u2="&#x23;" k="33" />
    <hkern u1="&#x38;" u2="&#x1ef3;" k="27" />
    <hkern u1="&#x38;" u2="&#x1ef2;" k="27" />
    <hkern u1="&#x38;" u2="&#x1e85;" k="12" />
    <hkern u1="&#x38;" u2="&#x1e84;" k="12" />
    <hkern u1="&#x38;" u2="&#x1e83;" k="12" />
    <hkern u1="&#x38;" u2="&#x1e82;" k="12" />
    <hkern u1="&#x38;" u2="&#x1e81;" k="12" />
    <hkern u1="&#x38;" u2="&#x1e80;" k="12" />
    <hkern u1="&#x38;" u2="&#x178;" k="27" />
    <hkern u1="&#x38;" u2="&#x177;" k="27" />
    <hkern u1="&#x38;" u2="&#x176;" k="27" />
    <hkern u1="&#x38;" u2="&#x175;" k="12" />
    <hkern u1="&#x38;" u2="&#x174;" k="12" />
    <hkern u1="&#x38;" u2="&#x105;" k="10" />
    <hkern u1="&#x38;" u2="&#x104;" k="10" />
    <hkern u1="&#x38;" u2="&#x103;" k="10" />
    <hkern u1="&#x38;" u2="&#x102;" k="10" />
    <hkern u1="&#x38;" u2="&#x101;" k="10" />
    <hkern u1="&#x38;" u2="&#x100;" k="10" />
    <hkern u1="&#x38;" u2="&#xff;" k="27" />
    <hkern u1="&#x38;" u2="&#xfd;" k="27" />
    <hkern u1="&#x38;" u2="&#xe5;" k="10" />
    <hkern u1="&#x38;" u2="&#xe4;" k="10" />
    <hkern u1="&#x38;" u2="&#xe3;" k="10" />
    <hkern u1="&#x38;" u2="&#xe2;" k="10" />
    <hkern u1="&#x38;" u2="&#xe1;" k="10" />
    <hkern u1="&#x38;" u2="&#xe0;" k="10" />
    <hkern u1="&#x38;" u2="&#xdd;" k="27" />
    <hkern u1="&#x38;" u2="&#xc5;" k="10" />
    <hkern u1="&#x38;" u2="&#xc4;" k="10" />
    <hkern u1="&#x38;" u2="&#xc3;" k="10" />
    <hkern u1="&#x38;" u2="&#xc2;" k="10" />
    <hkern u1="&#x38;" u2="&#xc1;" k="10" />
    <hkern u1="&#x38;" u2="&#xc0;" k="10" />
    <hkern u1="&#x38;" u2="y" k="27" />
    <hkern u1="&#x38;" u2="w" k="12" />
    <hkern u1="&#x38;" u2="v" k="15" />
    <hkern u1="&#x38;" u2="a" k="10" />
    <hkern u1="&#x38;" u2="Y" k="27" />
    <hkern u1="&#x38;" u2="W" k="12" />
    <hkern u1="&#x38;" u2="V" k="15" />
    <hkern u1="&#x38;" u2="A" k="10" />
    <hkern u1="&#x38;" u2="\" k="19" />
    <hkern u1="&#x38;" u2="&#x29;" k="18" />
    <hkern u1="&#x39;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x39;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x39;" u2="&#x178;" k="20" />
    <hkern u1="&#x39;" u2="&#x177;" k="20" />
    <hkern u1="&#x39;" u2="&#x176;" k="20" />
    <hkern u1="&#x39;" u2="&#x105;" k="11" />
    <hkern u1="&#x39;" u2="&#x104;" k="11" />
    <hkern u1="&#x39;" u2="&#x103;" k="11" />
    <hkern u1="&#x39;" u2="&#x102;" k="11" />
    <hkern u1="&#x39;" u2="&#x101;" k="11" />
    <hkern u1="&#x39;" u2="&#x100;" k="11" />
    <hkern u1="&#x39;" u2="&#xff;" k="20" />
    <hkern u1="&#x39;" u2="&#xfd;" k="20" />
    <hkern u1="&#x39;" u2="&#xe5;" k="11" />
    <hkern u1="&#x39;" u2="&#xe4;" k="11" />
    <hkern u1="&#x39;" u2="&#xe3;" k="11" />
    <hkern u1="&#x39;" u2="&#xe2;" k="11" />
    <hkern u1="&#x39;" u2="&#xe1;" k="11" />
    <hkern u1="&#x39;" u2="&#xe0;" k="11" />
    <hkern u1="&#x39;" u2="&#xdd;" k="20" />
    <hkern u1="&#x39;" u2="&#xc5;" k="11" />
    <hkern u1="&#x39;" u2="&#xc4;" k="11" />
    <hkern u1="&#x39;" u2="&#xc3;" k="11" />
    <hkern u1="&#x39;" u2="&#xc2;" k="11" />
    <hkern u1="&#x39;" u2="&#xc1;" k="11" />
    <hkern u1="&#x39;" u2="&#xc0;" k="11" />
    <hkern u1="&#x39;" u2="y" k="20" />
    <hkern u1="&#x39;" u2="v" k="11" />
    <hkern u1="&#x39;" u2="a" k="11" />
    <hkern u1="&#x39;" u2="Y" k="20" />
    <hkern u1="&#x39;" u2="V" k="11" />
    <hkern u1="&#x39;" u2="A" k="11" />
    <hkern u1="&#x39;" u2="&#x2044;" k="19" />
    <hkern u1="&#x39;" u2="&#x2f;" k="11" />
    <hkern u1="&#x39;" u2="&#x29;" k="17" />
    <hkern u1="&#x3a;" u2="&#x167;" k="37" />
    <hkern u1="&#x3a;" u2="&#x166;" k="37" />
    <hkern u1="&#x3a;" u2="\" k="30" />
    <hkern u1="&#x3b;" u2="&#x167;" k="37" />
    <hkern u1="&#x3b;" u2="&#x166;" k="37" />
    <hkern u1="&#x3b;" u2="\" k="30" />
    <hkern u1="&#x40;" u2="&#x1ef3;" k="27" />
    <hkern u1="&#x40;" u2="&#x1ef2;" k="27" />
    <hkern u1="&#x40;" u2="&#x178;" k="27" />
    <hkern u1="&#x40;" u2="&#x177;" k="27" />
    <hkern u1="&#x40;" u2="&#x176;" k="27" />
    <hkern u1="&#x40;" u2="&#x105;" k="25" />
    <hkern u1="&#x40;" u2="&#x104;" k="25" />
    <hkern u1="&#x40;" u2="&#x103;" k="25" />
    <hkern u1="&#x40;" u2="&#x102;" k="25" />
    <hkern u1="&#x40;" u2="&#x101;" k="25" />
    <hkern u1="&#x40;" u2="&#x100;" k="25" />
    <hkern u1="&#x40;" u2="&#xff;" k="27" />
    <hkern u1="&#x40;" u2="&#xfd;" k="27" />
    <hkern u1="&#x40;" u2="&#xe5;" k="25" />
    <hkern u1="&#x40;" u2="&#xe4;" k="25" />
    <hkern u1="&#x40;" u2="&#xe3;" k="25" />
    <hkern u1="&#x40;" u2="&#xe2;" k="25" />
    <hkern u1="&#x40;" u2="&#xe1;" k="25" />
    <hkern u1="&#x40;" u2="&#xe0;" k="25" />
    <hkern u1="&#x40;" u2="&#xdd;" k="27" />
    <hkern u1="&#x40;" u2="&#xc5;" k="25" />
    <hkern u1="&#x40;" u2="&#xc4;" k="25" />
    <hkern u1="&#x40;" u2="&#xc3;" k="25" />
    <hkern u1="&#x40;" u2="&#xc2;" k="25" />
    <hkern u1="&#x40;" u2="&#xc1;" k="25" />
    <hkern u1="&#x40;" u2="&#xc0;" k="25" />
    <hkern u1="&#x40;" u2="y" k="27" />
    <hkern u1="&#x40;" u2="x" k="20" />
    <hkern u1="&#x40;" u2="v" k="11" />
    <hkern u1="&#x40;" u2="a" k="25" />
    <hkern u1="&#x40;" u2="Y" k="27" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="V" k="11" />
    <hkern u1="&#x40;" u2="A" k="25" />
    <hkern u1="&#x40;" u2="\" k="12" />
    <hkern u1="&#x40;" u2="&#x2f;" k="30" />
    <hkern u1="A" u2="&#x2122;" k="60" />
    <hkern u1="A" u2="&#xba;" k="48" />
    <hkern u1="A" u2="&#xae;" k="19" />
    <hkern u1="A" u2="&#xaa;" k="47" />
    <hkern u1="A" u2="&#xa9;" k="19" />
    <hkern u1="A" u2="\" k="68" />
    <hkern u1="A" u2="&#x40;" k="15" />
    <hkern u1="A" u2="&#x3f;" k="18" />
    <hkern u1="A" u2="&#x39;" k="10" />
    <hkern u1="A" u2="&#x38;" k="10" />
    <hkern u1="A" u2="&#x36;" k="11" />
    <hkern u1="A" u2="&#x34;" k="11" />
    <hkern u1="A" u2="&#x31;" k="40" />
    <hkern u1="A" u2="&#x30;" k="11" />
    <hkern u1="A" u2="&#x2f;" k="-7" />
    <hkern u1="A" u2="&#x2a;" k="51" />
    <hkern u1="A" u2="&#x26;" k="10" />
    <hkern u1="A" u2="&#x20;" k="27" />
    <hkern u1="B" u2="_" k="17" />
    <hkern u1="B" u2="\" k="18" />
    <hkern u1="B" u2="&#x29;" k="18" />
    <hkern u1="D" u2="_" k="30" />
    <hkern u1="D" u2="\" k="12" />
    <hkern u1="D" u2="&#x2f;" k="12" />
    <hkern u1="D" u2="&#x29;" k="19" />
    <hkern u1="F" u2="&#xdf;" k="5" />
    <hkern u1="F" u2="_" k="104" />
    <hkern u1="F" u2="&#x2f;" k="43" />
    <hkern u1="F" u2="&#x20;" k="17" />
    <hkern u1="G" u2="_" k="18" />
    <hkern u1="G" u2="\" k="14" />
    <hkern u1="G" u2="&#x29;" k="17" />
    <hkern u1="J" u2="_" k="38" />
    <hkern u1="J" u2="&#x2f;" k="17" />
    <hkern u1="K" u2="&#x2122;" k="-27" />
    <hkern u1="K" u2="&#xae;" k="35" />
    <hkern u1="K" u2="&#xa9;" k="35" />
    <hkern u1="K" u2="\" k="-14" />
    <hkern u1="K" u2="&#x40;" k="30" />
    <hkern u1="K" u2="&#x37;" k="-11" />
    <hkern u1="K" u2="&#x36;" k="10" />
    <hkern u1="K" u2="&#x34;" k="35" />
    <hkern u1="K" u2="&#x31;" k="21" />
    <hkern u1="K" u2="&#x2f;" k="-8" />
    <hkern u1="K" u2="&#x29;" k="-19" />
    <hkern u1="K" u2="&#x26;" k="11" />
    <hkern u1="K" u2="&#x20;" k="14" />
    <hkern u1="L" u2="&#x2122;" k="96" />
    <hkern u1="L" u2="&#xba;" k="100" />
    <hkern u1="L" u2="&#xb7;" k="102" />
    <hkern u1="L" u2="&#xaa;" k="99" />
    <hkern u1="L" u2="\" k="72" />
    <hkern u1="L" u2="&#x3f;" k="18" />
    <hkern u1="L" u2="&#x31;" k="45" />
    <hkern u1="L" u2="&#x2a;" k="105" />
    <hkern u1="L" u2="&#x20;" k="21" />
    <hkern u1="O" u2="_" k="29" />
    <hkern u1="O" u2="\" k="12" />
    <hkern u1="O" u2="&#x2f;" k="12" />
    <hkern u1="O" u2="&#x29;" k="19" />
    <hkern u1="P" u2="_" k="122" />
    <hkern u1="P" u2="&#x2f;" k="49" />
    <hkern u1="P" u2="&#x29;" k="24" />
    <hkern u1="P" u2="&#x26;" k="10" />
    <hkern u1="P" u2="&#x20;" k="22" />
    <hkern u1="Q" u2="&#xe6;" k="16" />
    <hkern u1="Q" u2="&#xc6;" k="16" />
    <hkern u1="Q" u2="_" k="29" />
    <hkern u1="Q" u2="\" k="12" />
    <hkern u1="Q" u2="&#x2f;" k="12" />
    <hkern u1="Q" u2="&#x29;" k="19" />
    <hkern u1="R" u2="\" k="17" />
    <hkern u1="R" u2="&#x34;" k="19" />
    <hkern u1="R" u2="&#x2f;" k="-8" />
    <hkern u1="S" u2="_" k="12" />
    <hkern u1="S" u2="&#x31;" k="11" />
    <hkern u1="T" u2="&#xdf;" k="7" />
    <hkern u1="T" u2="&#xae;" k="11" />
    <hkern u1="T" u2="&#xa9;" k="11" />
    <hkern u1="T" u2="_" k="61" />
    <hkern u1="T" u2="&#x40;" k="18" />
    <hkern u1="T" u2="&#x34;" k="56" />
    <hkern u1="T" u2="&#x2f;" k="60" />
    <hkern u1="T" u2="&#x26;" k="11" />
    <hkern u1="T" u2="&#x20;" k="21" />
    <hkern u1="U" u2="_" k="39" />
    <hkern u1="U" u2="&#x2f;" k="17" />
    <hkern u1="V" u2="&#x2122;" k="-24" />
    <hkern u1="V" u2="&#xdf;" k="16" />
    <hkern u1="V" u2="&#xae;" k="21" />
    <hkern u1="V" u2="&#xa9;" k="21" />
    <hkern u1="V" u2="_" k="77" />
    <hkern u1="V" u2="\" k="-11" />
    <hkern u1="V" u2="&#x40;" k="26" />
    <hkern u1="V" u2="&#x38;" k="15" />
    <hkern u1="V" u2="&#x37;" k="-9" />
    <hkern u1="V" u2="&#x36;" k="11" />
    <hkern u1="V" u2="&#x34;" k="41" />
    <hkern u1="V" u2="&#x32;" k="10" />
    <hkern u1="V" u2="&#x30;" k="11" />
    <hkern u1="V" u2="&#x2f;" k="68" />
    <hkern u1="V" u2="&#x29;" k="-16" />
    <hkern u1="V" u2="&#x26;" k="23" />
    <hkern u1="V" u2="&#x20;" k="29" />
    <hkern u1="W" u2="&#x2122;" k="-15" />
    <hkern u1="W" u2="&#xdf;" k="10" />
    <hkern u1="W" u2="&#xae;" k="15" />
    <hkern u1="W" u2="&#xa9;" k="15" />
    <hkern u1="W" u2="_" k="60" />
    <hkern u1="W" u2="&#x40;" k="18" />
    <hkern u1="W" u2="&#x38;" k="12" />
    <hkern u1="W" u2="&#x34;" k="29" />
    <hkern u1="W" u2="&#x2f;" k="56" />
    <hkern u1="W" u2="&#x29;" k="-8" />
    <hkern u1="W" u2="&#x26;" k="18" />
    <hkern u1="W" u2="&#x20;" k="25" />
    <hkern u1="X" u2="&#x2122;" k="-18" />
    <hkern u1="X" u2="&#xae;" k="26" />
    <hkern u1="X" u2="&#xa9;" k="26" />
    <hkern u1="X" u2="\" k="-5" />
    <hkern u1="X" u2="&#x40;" k="23" />
    <hkern u1="X" u2="&#x34;" k="24" />
    <hkern u1="X" u2="&#x31;" k="10" />
    <hkern u1="X" u2="&#x29;" k="-10" />
    <hkern u1="X" u2="&#x20;" k="10" />
    <hkern u1="Y" u2="&#x2122;" k="-25" />
    <hkern u1="Y" u2="&#xdf;" k="36" />
    <hkern u1="Y" u2="&#xae;" k="42" />
    <hkern u1="Y" u2="&#xa9;" k="42" />
    <hkern u1="Y" u2="_" k="79" />
    <hkern u1="Y" u2="\" k="-13" />
    <hkern u1="Y" u2="&#x40;" k="47" />
    <hkern u1="Y" u2="&#x39;" k="15" />
    <hkern u1="Y" u2="&#x38;" k="26" />
    <hkern u1="Y" u2="&#x37;" k="-10" />
    <hkern u1="Y" u2="&#x36;" k="20" />
    <hkern u1="Y" u2="&#x34;" k="75" />
    <hkern u1="Y" u2="&#x32;" k="15" />
    <hkern u1="Y" u2="&#x31;" k="16" />
    <hkern u1="Y" u2="&#x30;" k="19" />
    <hkern u1="Y" u2="&#x2f;" k="79" />
    <hkern u1="Y" u2="&#x29;" k="-19" />
    <hkern u1="Y" u2="&#x26;" k="36" />
    <hkern u1="Y" u2="&#x20;" k="34" />
    <hkern u1="Z" u2="&#xae;" k="12" />
    <hkern u1="Z" u2="&#xa9;" k="12" />
    <hkern u1="[" u2="&#x44f;" k="-16" />
    <hkern u1="[" u2="&#x443;" k="-10" />
    <hkern u1="[" u2="&#x42f;" k="-16" />
    <hkern u1="[" u2="&#x423;" k="-10" />
    <hkern u1="[" u2="j" k="11" />
    <hkern u1="[" u2="J" k="11" />
    <hkern u1="[" u2="&#x34;" k="19" />
    <hkern u1="\" g2="uni0442.loclBGR" k="61" />
    <hkern u1="\" g2="uni043B.loclBGR" k="-7" />
    <hkern u1="\" g2="uni0436.loclBGR" k="-10" />
    <hkern u1="\" g2="uni041B.loclBGR" k="-7" />
    <hkern u1="\" u2="&#x2039;" k="28" />
    <hkern u1="\" u2="&#x201d;" k="54" />
    <hkern u1="\" u2="&#x2019;" k="54" />
    <hkern u1="\" u2="&#x2014;" k="21" />
    <hkern u1="\" u2="&#x2013;" k="21" />
    <hkern u1="\" u2="&#x1ef3;" k="80" />
    <hkern u1="\" u2="&#x1ef2;" k="80" />
    <hkern u1="\" u2="&#x1e85;" k="56" />
    <hkern u1="\" u2="&#x1e84;" k="56" />
    <hkern u1="\" u2="&#x1e83;" k="56" />
    <hkern u1="\" u2="&#x1e82;" k="56" />
    <hkern u1="\" u2="&#x1e81;" k="56" />
    <hkern u1="\" u2="&#x1e80;" k="56" />
    <hkern u1="\" u2="&#x44f;" k="-33" />
    <hkern u1="\" u2="&#x44a;" k="41" />
    <hkern u1="\" u2="&#x447;" k="49" />
    <hkern u1="\" u2="&#x444;" k="18" />
    <hkern u1="\" u2="&#x443;" k="24" />
    <hkern u1="\" u2="&#x442;" k="61" />
    <hkern u1="\" u2="&#x441;" k="13" />
    <hkern u1="\" u2="&#x43e;" k="13" />
    <hkern u1="\" u2="&#x43b;" k="-7" />
    <hkern u1="\" u2="&#x436;" k="-10" />
    <hkern u1="\" u2="&#x430;" k="-7" />
    <hkern u1="\" u2="&#x42f;" k="-33" />
    <hkern u1="\" u2="&#x42a;" k="41" />
    <hkern u1="\" u2="&#x427;" k="49" />
    <hkern u1="\" u2="&#x424;" k="18" />
    <hkern u1="\" u2="&#x423;" k="24" />
    <hkern u1="\" u2="&#x422;" k="61" />
    <hkern u1="\" u2="&#x421;" k="13" />
    <hkern u1="\" u2="&#x41e;" k="13" />
    <hkern u1="\" u2="&#x416;" k="-10" />
    <hkern u1="\" u2="&#x410;" k="-7" />
    <hkern u1="\" u2="&#x21b;" k="61" />
    <hkern u1="\" u2="&#x21a;" k="61" />
    <hkern u1="\" u2="&#x178;" k="80" />
    <hkern u1="\" u2="&#x177;" k="80" />
    <hkern u1="\" u2="&#x176;" k="80" />
    <hkern u1="\" u2="&#x175;" k="56" />
    <hkern u1="\" u2="&#x174;" k="56" />
    <hkern u1="\" u2="&#x173;" k="16" />
    <hkern u1="\" u2="&#x172;" k="16" />
    <hkern u1="\" u2="&#x171;" k="16" />
    <hkern u1="\" u2="&#x170;" k="16" />
    <hkern u1="\" u2="&#x16f;" k="16" />
    <hkern u1="\" u2="&#x16e;" k="16" />
    <hkern u1="\" u2="&#x16b;" k="16" />
    <hkern u1="\" u2="&#x16a;" k="16" />
    <hkern u1="\" u2="&#x167;" k="61" />
    <hkern u1="\" u2="&#x166;" k="61" />
    <hkern u1="\" u2="&#x165;" k="61" />
    <hkern u1="\" u2="&#x164;" k="61" />
    <hkern u1="\" u2="&#x163;" k="61" />
    <hkern u1="\" u2="&#x162;" k="61" />
    <hkern u1="\" u2="&#x153;" k="13" />
    <hkern u1="\" u2="&#x152;" k="13" />
    <hkern u1="\" u2="&#x151;" k="13" />
    <hkern u1="\" u2="&#x150;" k="13" />
    <hkern u1="\" u2="&#x14d;" k="13" />
    <hkern u1="\" u2="&#x14c;" k="13" />
    <hkern u1="\" u2="&#x123;" k="13" />
    <hkern u1="\" u2="&#x122;" k="13" />
    <hkern u1="\" u2="&#x121;" k="13" />
    <hkern u1="\" u2="&#x120;" k="13" />
    <hkern u1="\" u2="&#x11f;" k="13" />
    <hkern u1="\" u2="&#x11e;" k="13" />
    <hkern u1="\" u2="&#x10d;" k="13" />
    <hkern u1="\" u2="&#x10c;" k="13" />
    <hkern u1="\" u2="&#x10b;" k="13" />
    <hkern u1="\" u2="&#x10a;" k="13" />
    <hkern u1="\" u2="&#x107;" k="13" />
    <hkern u1="\" u2="&#x106;" k="13" />
    <hkern u1="\" u2="&#x105;" k="-7" />
    <hkern u1="\" u2="&#x104;" k="-7" />
    <hkern u1="\" u2="&#x103;" k="-7" />
    <hkern u1="\" u2="&#x102;" k="-7" />
    <hkern u1="\" u2="&#x101;" k="-7" />
    <hkern u1="\" u2="&#x100;" k="-7" />
    <hkern u1="\" u2="&#xff;" k="80" />
    <hkern u1="\" u2="&#xfd;" k="80" />
    <hkern u1="\" u2="&#xfc;" k="16" />
    <hkern u1="\" u2="&#xfb;" k="16" />
    <hkern u1="\" u2="&#xfa;" k="16" />
    <hkern u1="\" u2="&#xf9;" k="16" />
    <hkern u1="\" u2="&#xf8;" k="13" />
    <hkern u1="\" u2="&#xf6;" k="13" />
    <hkern u1="\" u2="&#xf5;" k="13" />
    <hkern u1="\" u2="&#xf4;" k="13" />
    <hkern u1="\" u2="&#xf3;" k="13" />
    <hkern u1="\" u2="&#xf2;" k="13" />
    <hkern u1="\" u2="&#xe7;" k="13" />
    <hkern u1="\" u2="&#xe6;" k="-14" />
    <hkern u1="\" u2="&#xe5;" k="-7" />
    <hkern u1="\" u2="&#xe4;" k="-7" />
    <hkern u1="\" u2="&#xe3;" k="-7" />
    <hkern u1="\" u2="&#xe2;" k="-7" />
    <hkern u1="\" u2="&#xe1;" k="-7" />
    <hkern u1="\" u2="&#xe0;" k="-7" />
    <hkern u1="\" u2="&#xdd;" k="80" />
    <hkern u1="\" u2="&#xdc;" k="16" />
    <hkern u1="\" u2="&#xdb;" k="16" />
    <hkern u1="\" u2="&#xda;" k="16" />
    <hkern u1="\" u2="&#xd9;" k="16" />
    <hkern u1="\" u2="&#xd8;" k="13" />
    <hkern u1="\" u2="&#xd6;" k="13" />
    <hkern u1="\" u2="&#xd5;" k="13" />
    <hkern u1="\" u2="&#xd4;" k="13" />
    <hkern u1="\" u2="&#xd3;" k="13" />
    <hkern u1="\" u2="&#xd2;" k="13" />
    <hkern u1="\" u2="&#xc7;" k="13" />
    <hkern u1="\" u2="&#xc6;" k="-14" />
    <hkern u1="\" u2="&#xc5;" k="-7" />
    <hkern u1="\" u2="&#xc4;" k="-7" />
    <hkern u1="\" u2="&#xc3;" k="-7" />
    <hkern u1="\" u2="&#xc2;" k="-7" />
    <hkern u1="\" u2="&#xc1;" k="-7" />
    <hkern u1="\" u2="&#xc0;" k="-7" />
    <hkern u1="\" u2="&#xad;" k="21" />
    <hkern u1="\" u2="&#xab;" k="28" />
    <hkern u1="\" u2="y" k="80" />
    <hkern u1="\" u2="w" k="56" />
    <hkern u1="\" u2="v" k="68" />
    <hkern u1="\" u2="u" k="16" />
    <hkern u1="\" u2="t" k="61" />
    <hkern u1="\" u2="q" k="13" />
    <hkern u1="\" u2="o" k="13" />
    <hkern u1="\" u2="g" k="13" />
    <hkern u1="\" u2="c" k="13" />
    <hkern u1="\" u2="a" k="-7" />
    <hkern u1="\" u2="Y" k="80" />
    <hkern u1="\" u2="W" k="56" />
    <hkern u1="\" u2="V" k="68" />
    <hkern u1="\" u2="U" k="16" />
    <hkern u1="\" u2="T" k="61" />
    <hkern u1="\" u2="Q" k="13" />
    <hkern u1="\" u2="O" k="13" />
    <hkern u1="\" u2="G" k="13" />
    <hkern u1="\" u2="C" k="13" />
    <hkern u1="\" u2="A" k="-7" />
    <hkern u1="\" u2="&#x2d;" k="21" />
    <hkern u1="\" u2="&#x27;" k="48" />
    <hkern u1="\" u2="&#x22;" k="48" />
    <hkern u1="\" u2="&#x3c0;" k="21" />
    <hkern u1="\" u2="&#xae;" k="25" />
    <hkern u1="\" u2="&#xa9;" k="25" />
    <hkern u1="\" u2="\" k="252" />
    <hkern u1="\" u2="&#x40;" k="20" />
    <hkern u1="\" u2="&#x36;" k="10" />
    <hkern u1="\" u2="&#x31;" k="45" />
    <hkern u1="\" u2="&#x2a;" k="43" />
    <hkern u1="\" u2="&#x20;" k="10" />
    <hkern u1="_" u2="&#x201d;" k="114" />
    <hkern u1="_" u2="&#x2019;" k="114" />
    <hkern u1="_" u2="&#x1ef3;" k="79" />
    <hkern u1="_" u2="&#x1ef2;" k="79" />
    <hkern u1="_" u2="&#x1e85;" k="60" />
    <hkern u1="_" u2="&#x1e84;" k="60" />
    <hkern u1="_" u2="&#x1e83;" k="60" />
    <hkern u1="_" u2="&#x1e82;" k="60" />
    <hkern u1="_" u2="&#x1e81;" k="60" />
    <hkern u1="_" u2="&#x1e80;" k="60" />
    <hkern u1="_" u2="&#x21b;" k="61" />
    <hkern u1="_" u2="&#x21a;" k="61" />
    <hkern u1="_" u2="&#x178;" k="79" />
    <hkern u1="_" u2="&#x177;" k="79" />
    <hkern u1="_" u2="&#x176;" k="79" />
    <hkern u1="_" u2="&#x175;" k="60" />
    <hkern u1="_" u2="&#x174;" k="60" />
    <hkern u1="_" u2="&#x173;" k="38" />
    <hkern u1="_" u2="&#x172;" k="38" />
    <hkern u1="_" u2="&#x171;" k="38" />
    <hkern u1="_" u2="&#x170;" k="38" />
    <hkern u1="_" u2="&#x16f;" k="38" />
    <hkern u1="_" u2="&#x16e;" k="38" />
    <hkern u1="_" u2="&#x16b;" k="38" />
    <hkern u1="_" u2="&#x16a;" k="38" />
    <hkern u1="_" u2="&#x167;" k="61" />
    <hkern u1="_" u2="&#x166;" k="61" />
    <hkern u1="_" u2="&#x165;" k="61" />
    <hkern u1="_" u2="&#x164;" k="61" />
    <hkern u1="_" u2="&#x163;" k="61" />
    <hkern u1="_" u2="&#x162;" k="61" />
    <hkern u1="_" u2="&#x153;" k="29" />
    <hkern u1="_" u2="&#x152;" k="29" />
    <hkern u1="_" u2="&#x151;" k="29" />
    <hkern u1="_" u2="&#x150;" k="29" />
    <hkern u1="_" u2="&#x14d;" k="29" />
    <hkern u1="_" u2="&#x14c;" k="29" />
    <hkern u1="_" u2="&#x123;" k="29" />
    <hkern u1="_" u2="&#x122;" k="29" />
    <hkern u1="_" u2="&#x121;" k="29" />
    <hkern u1="_" u2="&#x120;" k="29" />
    <hkern u1="_" u2="&#x11f;" k="29" />
    <hkern u1="_" u2="&#x11e;" k="29" />
    <hkern u1="_" u2="&#x10d;" k="29" />
    <hkern u1="_" u2="&#x10c;" k="29" />
    <hkern u1="_" u2="&#x10b;" k="29" />
    <hkern u1="_" u2="&#x10a;" k="29" />
    <hkern u1="_" u2="&#x107;" k="29" />
    <hkern u1="_" u2="&#x106;" k="29" />
    <hkern u1="_" u2="&#xff;" k="79" />
    <hkern u1="_" u2="&#xfd;" k="79" />
    <hkern u1="_" u2="&#xfc;" k="38" />
    <hkern u1="_" u2="&#xfb;" k="38" />
    <hkern u1="_" u2="&#xfa;" k="38" />
    <hkern u1="_" u2="&#xf9;" k="38" />
    <hkern u1="_" u2="&#xf8;" k="29" />
    <hkern u1="_" u2="&#xf6;" k="29" />
    <hkern u1="_" u2="&#xf5;" k="29" />
    <hkern u1="_" u2="&#xf4;" k="29" />
    <hkern u1="_" u2="&#xf3;" k="29" />
    <hkern u1="_" u2="&#xf2;" k="29" />
    <hkern u1="_" u2="&#xe7;" k="29" />
    <hkern u1="_" u2="&#xdd;" k="79" />
    <hkern u1="_" u2="&#xdc;" k="38" />
    <hkern u1="_" u2="&#xdb;" k="38" />
    <hkern u1="_" u2="&#xda;" k="38" />
    <hkern u1="_" u2="&#xd9;" k="38" />
    <hkern u1="_" u2="&#xd8;" k="29" />
    <hkern u1="_" u2="&#xd6;" k="29" />
    <hkern u1="_" u2="&#xd5;" k="29" />
    <hkern u1="_" u2="&#xd4;" k="29" />
    <hkern u1="_" u2="&#xd3;" k="29" />
    <hkern u1="_" u2="&#xd2;" k="29" />
    <hkern u1="_" u2="&#xc7;" k="29" />
    <hkern u1="_" u2="y" k="79" />
    <hkern u1="_" u2="w" k="60" />
    <hkern u1="_" u2="v" k="77" />
    <hkern u1="_" u2="u" k="38" />
    <hkern u1="_" u2="t" k="61" />
    <hkern u1="_" u2="q" k="29" />
    <hkern u1="_" u2="o" k="29" />
    <hkern u1="_" u2="j" k="19" />
    <hkern u1="_" u2="g" k="29" />
    <hkern u1="_" u2="c" k="29" />
    <hkern u1="_" u2="Y" k="79" />
    <hkern u1="_" u2="W" k="60" />
    <hkern u1="_" u2="V" k="77" />
    <hkern u1="_" u2="U" k="38" />
    <hkern u1="_" u2="T" k="61" />
    <hkern u1="_" u2="Q" k="29" />
    <hkern u1="_" u2="O" k="29" />
    <hkern u1="_" u2="J" k="19" />
    <hkern u1="_" u2="G" k="29" />
    <hkern u1="_" u2="C" k="29" />
    <hkern u1="_" u2="&#x27;" k="100" />
    <hkern u1="_" u2="&#x22;" k="100" />
    <hkern u1="_" u2="\" k="58" />
    <hkern u1="a" u2="&#x2122;" k="60" />
    <hkern u1="a" u2="&#xba;" k="48" />
    <hkern u1="a" u2="&#xae;" k="19" />
    <hkern u1="a" u2="&#xaa;" k="47" />
    <hkern u1="a" u2="&#xa9;" k="19" />
    <hkern u1="a" u2="\" k="68" />
    <hkern u1="a" u2="&#x40;" k="15" />
    <hkern u1="a" u2="&#x3f;" k="18" />
    <hkern u1="a" u2="&#x39;" k="10" />
    <hkern u1="a" u2="&#x38;" k="10" />
    <hkern u1="a" u2="&#x36;" k="11" />
    <hkern u1="a" u2="&#x34;" k="11" />
    <hkern u1="a" u2="&#x31;" k="40" />
    <hkern u1="a" u2="&#x30;" k="11" />
    <hkern u1="a" u2="&#x2f;" k="-7" />
    <hkern u1="a" u2="&#x2a;" k="51" />
    <hkern u1="a" u2="&#x26;" k="10" />
    <hkern u1="a" u2="&#x20;" k="27" />
    <hkern u1="b" u2="_" k="17" />
    <hkern u1="b" u2="\" k="18" />
    <hkern u1="b" u2="&#x29;" k="18" />
    <hkern u1="d" u2="_" k="30" />
    <hkern u1="d" u2="\" k="12" />
    <hkern u1="d" u2="&#x2f;" k="12" />
    <hkern u1="d" u2="&#x29;" k="19" />
    <hkern u1="f" u2="&#xdf;" k="5" />
    <hkern u1="f" u2="_" k="104" />
    <hkern u1="f" u2="&#x2f;" k="43" />
    <hkern u1="f" u2="&#x20;" k="17" />
    <hkern u1="g" u2="_" k="18" />
    <hkern u1="g" u2="\" k="14" />
    <hkern u1="g" u2="&#x29;" k="17" />
    <hkern u1="j" u2="_" k="38" />
    <hkern u1="j" u2="&#x2f;" k="17" />
    <hkern u1="k" u2="&#x2122;" k="-27" />
    <hkern u1="k" u2="&#xae;" k="35" />
    <hkern u1="k" u2="&#xa9;" k="35" />
    <hkern u1="k" u2="\" k="-14" />
    <hkern u1="k" u2="&#x40;" k="30" />
    <hkern u1="k" u2="&#x37;" k="-11" />
    <hkern u1="k" u2="&#x36;" k="10" />
    <hkern u1="k" u2="&#x34;" k="35" />
    <hkern u1="k" u2="&#x31;" k="21" />
    <hkern u1="k" u2="&#x2f;" k="-8" />
    <hkern u1="k" u2="&#x29;" k="-19" />
    <hkern u1="k" u2="&#x26;" k="11" />
    <hkern u1="k" u2="&#x20;" k="14" />
    <hkern u1="l" u2="&#x2122;" k="96" />
    <hkern u1="l" u2="&#xba;" k="100" />
    <hkern u1="l" u2="&#xb7;" k="102" />
    <hkern u1="l" u2="&#xaa;" k="99" />
    <hkern u1="l" u2="\" k="72" />
    <hkern u1="l" u2="&#x3f;" k="18" />
    <hkern u1="l" u2="&#x31;" k="45" />
    <hkern u1="l" u2="&#x2a;" k="105" />
    <hkern u1="l" u2="&#x20;" k="21" />
    <hkern u1="o" u2="_" k="29" />
    <hkern u1="o" u2="\" k="12" />
    <hkern u1="o" u2="&#x2f;" k="12" />
    <hkern u1="o" u2="&#x29;" k="19" />
    <hkern u1="p" u2="_" k="122" />
    <hkern u1="p" u2="&#x2f;" k="49" />
    <hkern u1="p" u2="&#x29;" k="24" />
    <hkern u1="p" u2="&#x26;" k="10" />
    <hkern u1="p" u2="&#x20;" k="22" />
    <hkern u1="q" u2="&#xe6;" k="16" />
    <hkern u1="q" u2="_" k="29" />
    <hkern u1="q" u2="\" k="12" />
    <hkern u1="q" u2="&#x2f;" k="12" />
    <hkern u1="q" u2="&#x29;" k="19" />
    <hkern u1="r" u2="\" k="17" />
    <hkern u1="r" u2="&#x34;" k="19" />
    <hkern u1="r" u2="&#x2f;" k="-8" />
    <hkern u1="s" u2="_" k="12" />
    <hkern u1="s" u2="&#x31;" k="11" />
    <hkern u1="t" u2="&#xdf;" k="7" />
    <hkern u1="t" u2="&#xae;" k="11" />
    <hkern u1="t" u2="&#xa9;" k="11" />
    <hkern u1="t" u2="_" k="61" />
    <hkern u1="t" u2="&#x40;" k="18" />
    <hkern u1="t" u2="&#x34;" k="56" />
    <hkern u1="t" u2="&#x2f;" k="60" />
    <hkern u1="t" u2="&#x26;" k="11" />
    <hkern u1="t" u2="&#x20;" k="21" />
    <hkern u1="u" u2="_" k="39" />
    <hkern u1="u" u2="&#x2f;" k="17" />
    <hkern u1="v" u2="&#x2122;" k="-24" />
    <hkern u1="v" u2="&#xdf;" k="16" />
    <hkern u1="v" u2="&#xae;" k="21" />
    <hkern u1="v" u2="&#xa9;" k="21" />
    <hkern u1="v" u2="_" k="77" />
    <hkern u1="v" u2="\" k="-11" />
    <hkern u1="v" u2="&#x40;" k="26" />
    <hkern u1="v" u2="&#x38;" k="15" />
    <hkern u1="v" u2="&#x37;" k="-9" />
    <hkern u1="v" u2="&#x36;" k="11" />
    <hkern u1="v" u2="&#x34;" k="41" />
    <hkern u1="v" u2="&#x32;" k="10" />
    <hkern u1="v" u2="&#x30;" k="11" />
    <hkern u1="v" u2="&#x2f;" k="68" />
    <hkern u1="v" u2="&#x29;" k="-16" />
    <hkern u1="v" u2="&#x26;" k="23" />
    <hkern u1="v" u2="&#x20;" k="29" />
    <hkern u1="w" u2="&#x2122;" k="-15" />
    <hkern u1="w" u2="&#xdf;" k="10" />
    <hkern u1="w" u2="&#xae;" k="15" />
    <hkern u1="w" u2="&#xa9;" k="15" />
    <hkern u1="w" u2="_" k="60" />
    <hkern u1="w" u2="&#x40;" k="18" />
    <hkern u1="w" u2="&#x38;" k="12" />
    <hkern u1="w" u2="&#x34;" k="29" />
    <hkern u1="w" u2="&#x2f;" k="56" />
    <hkern u1="w" u2="&#x29;" k="-8" />
    <hkern u1="w" u2="&#x26;" k="18" />
    <hkern u1="w" u2="&#x20;" k="25" />
    <hkern u1="x" u2="&#x2122;" k="-18" />
    <hkern u1="x" u2="&#xae;" k="26" />
    <hkern u1="x" u2="&#xa9;" k="26" />
    <hkern u1="x" u2="\" k="-5" />
    <hkern u1="x" u2="&#x40;" k="23" />
    <hkern u1="x" u2="&#x34;" k="24" />
    <hkern u1="x" u2="&#x31;" k="10" />
    <hkern u1="x" u2="&#x29;" k="-10" />
    <hkern u1="x" u2="&#x20;" k="10" />
    <hkern u1="y" u2="&#x2122;" k="-25" />
    <hkern u1="y" u2="&#xdf;" k="36" />
    <hkern u1="y" u2="&#xae;" k="42" />
    <hkern u1="y" u2="&#xa9;" k="42" />
    <hkern u1="y" u2="_" k="79" />
    <hkern u1="y" u2="\" k="-13" />
    <hkern u1="y" u2="&#x40;" k="47" />
    <hkern u1="y" u2="&#x39;" k="15" />
    <hkern u1="y" u2="&#x38;" k="26" />
    <hkern u1="y" u2="&#x37;" k="-10" />
    <hkern u1="y" u2="&#x36;" k="20" />
    <hkern u1="y" u2="&#x34;" k="75" />
    <hkern u1="y" u2="&#x32;" k="15" />
    <hkern u1="y" u2="&#x31;" k="16" />
    <hkern u1="y" u2="&#x30;" k="19" />
    <hkern u1="y" u2="&#x2f;" k="79" />
    <hkern u1="y" u2="&#x29;" k="-19" />
    <hkern u1="y" u2="&#x26;" k="36" />
    <hkern u1="y" u2="&#x20;" k="34" />
    <hkern u1="z" u2="&#xae;" k="12" />
    <hkern u1="z" u2="&#xa9;" k="12" />
    <hkern u1="&#x7b;" u2="&#x44f;" k="-16" />
    <hkern u1="&#x7b;" u2="&#x444;" k="10" />
    <hkern u1="&#x7b;" u2="&#x443;" k="-10" />
    <hkern u1="&#x7b;" u2="&#x42f;" k="-16" />
    <hkern u1="&#x7b;" u2="&#x424;" k="10" />
    <hkern u1="&#x7b;" u2="&#x423;" k="-10" />
    <hkern u1="&#x7b;" u2="j" k="10" />
    <hkern u1="&#x7b;" u2="J" k="10" />
    <hkern u1="&#x7b;" u2="&#x34;" k="21" />
    <hkern u1="&#x7d;" u2="&#x29;" k="13" />
    <hkern u1="&#xa1;" u2="&#x1ef3;" k="57" />
    <hkern u1="&#xa1;" u2="&#x1ef2;" k="57" />
    <hkern u1="&#xa1;" u2="&#x1e85;" k="23" />
    <hkern u1="&#xa1;" u2="&#x1e84;" k="23" />
    <hkern u1="&#xa1;" u2="&#x1e83;" k="23" />
    <hkern u1="&#xa1;" u2="&#x1e82;" k="23" />
    <hkern u1="&#xa1;" u2="&#x1e81;" k="23" />
    <hkern u1="&#xa1;" u2="&#x1e80;" k="23" />
    <hkern u1="&#xa1;" u2="&#x21b;" k="47" />
    <hkern u1="&#xa1;" u2="&#x21a;" k="47" />
    <hkern u1="&#xa1;" u2="&#x178;" k="57" />
    <hkern u1="&#xa1;" u2="&#x177;" k="57" />
    <hkern u1="&#xa1;" u2="&#x176;" k="57" />
    <hkern u1="&#xa1;" u2="&#x175;" k="23" />
    <hkern u1="&#xa1;" u2="&#x174;" k="23" />
    <hkern u1="&#xa1;" u2="&#x165;" k="47" />
    <hkern u1="&#xa1;" u2="&#x164;" k="47" />
    <hkern u1="&#xa1;" u2="&#x163;" k="47" />
    <hkern u1="&#xa1;" u2="&#x162;" k="47" />
    <hkern u1="&#xa1;" u2="&#xff;" k="57" />
    <hkern u1="&#xa1;" u2="&#xfd;" k="57" />
    <hkern u1="&#xa1;" u2="&#xdd;" k="57" />
    <hkern u1="&#xa1;" u2="y" k="57" />
    <hkern u1="&#xa1;" u2="w" k="23" />
    <hkern u1="&#xa1;" u2="v" k="31" />
    <hkern u1="&#xa1;" u2="t" k="47" />
    <hkern u1="&#xa1;" u2="Y" k="57" />
    <hkern u1="&#xa1;" u2="W" k="23" />
    <hkern u1="&#xa1;" u2="V" k="31" />
    <hkern u1="&#xa1;" u2="T" k="47" />
    <hkern u1="&#xa1;" u2="&#x167;" k="41" />
    <hkern u1="&#xa1;" u2="&#x166;" k="41" />
    <hkern u1="&#xa3;" u2="&#x31;" k="13" />
    <hkern u1="&#xa9;" u2="&#x1ef3;" k="42" />
    <hkern u1="&#xa9;" u2="&#x1ef2;" k="42" />
    <hkern u1="&#xa9;" u2="&#x1e85;" k="16" />
    <hkern u1="&#xa9;" u2="&#x1e84;" k="16" />
    <hkern u1="&#xa9;" u2="&#x1e83;" k="16" />
    <hkern u1="&#xa9;" u2="&#x1e82;" k="16" />
    <hkern u1="&#xa9;" u2="&#x1e81;" k="16" />
    <hkern u1="&#xa9;" u2="&#x1e80;" k="16" />
    <hkern u1="&#xa9;" u2="&#x21b;" k="12" />
    <hkern u1="&#xa9;" u2="&#x21a;" k="12" />
    <hkern u1="&#xa9;" u2="&#x17e;" k="14" />
    <hkern u1="&#xa9;" u2="&#x17d;" k="14" />
    <hkern u1="&#xa9;" u2="&#x17c;" k="14" />
    <hkern u1="&#xa9;" u2="&#x17b;" k="14" />
    <hkern u1="&#xa9;" u2="&#x17a;" k="14" />
    <hkern u1="&#xa9;" u2="&#x179;" k="14" />
    <hkern u1="&#xa9;" u2="&#x178;" k="42" />
    <hkern u1="&#xa9;" u2="&#x177;" k="42" />
    <hkern u1="&#xa9;" u2="&#x176;" k="42" />
    <hkern u1="&#xa9;" u2="&#x175;" k="16" />
    <hkern u1="&#xa9;" u2="&#x174;" k="16" />
    <hkern u1="&#xa9;" u2="&#x167;" k="12" />
    <hkern u1="&#xa9;" u2="&#x166;" k="12" />
    <hkern u1="&#xa9;" u2="&#x165;" k="12" />
    <hkern u1="&#xa9;" u2="&#x164;" k="12" />
    <hkern u1="&#xa9;" u2="&#x163;" k="12" />
    <hkern u1="&#xa9;" u2="&#x162;" k="12" />
    <hkern u1="&#xa9;" u2="&#x105;" k="21" />
    <hkern u1="&#xa9;" u2="&#x104;" k="21" />
    <hkern u1="&#xa9;" u2="&#x103;" k="21" />
    <hkern u1="&#xa9;" u2="&#x102;" k="21" />
    <hkern u1="&#xa9;" u2="&#x101;" k="21" />
    <hkern u1="&#xa9;" u2="&#x100;" k="21" />
    <hkern u1="&#xa9;" u2="&#xff;" k="42" />
    <hkern u1="&#xa9;" u2="&#xfd;" k="42" />
    <hkern u1="&#xa9;" u2="&#xe6;" k="26" />
    <hkern u1="&#xa9;" u2="&#xe5;" k="21" />
    <hkern u1="&#xa9;" u2="&#xe4;" k="21" />
    <hkern u1="&#xa9;" u2="&#xe3;" k="21" />
    <hkern u1="&#xa9;" u2="&#xe2;" k="21" />
    <hkern u1="&#xa9;" u2="&#xe1;" k="21" />
    <hkern u1="&#xa9;" u2="&#xe0;" k="21" />
    <hkern u1="&#xa9;" u2="&#xdd;" k="42" />
    <hkern u1="&#xa9;" u2="&#xc6;" k="26" />
    <hkern u1="&#xa9;" u2="&#xc5;" k="21" />
    <hkern u1="&#xa9;" u2="&#xc4;" k="21" />
    <hkern u1="&#xa9;" u2="&#xc3;" k="21" />
    <hkern u1="&#xa9;" u2="&#xc2;" k="21" />
    <hkern u1="&#xa9;" u2="&#xc1;" k="21" />
    <hkern u1="&#xa9;" u2="&#xc0;" k="21" />
    <hkern u1="&#xa9;" u2="z" k="14" />
    <hkern u1="&#xa9;" u2="y" k="42" />
    <hkern u1="&#xa9;" u2="x" k="26" />
    <hkern u1="&#xa9;" u2="w" k="16" />
    <hkern u1="&#xa9;" u2="v" k="23" />
    <hkern u1="&#xa9;" u2="t" k="12" />
    <hkern u1="&#xa9;" u2="a" k="21" />
    <hkern u1="&#xa9;" u2="Z" k="14" />
    <hkern u1="&#xa9;" u2="Y" k="42" />
    <hkern u1="&#xa9;" u2="X" k="26" />
    <hkern u1="&#xa9;" u2="W" k="16" />
    <hkern u1="&#xa9;" u2="V" k="23" />
    <hkern u1="&#xa9;" u2="T" k="12" />
    <hkern u1="&#xa9;" u2="A" k="21" />
    <hkern u1="&#xa9;" u2="\" k="27" />
    <hkern u1="&#xa9;" u2="&#x2f;" k="25" />
    <hkern u1="&#xab;" u2="\" k="32" />
    <hkern u1="&#xad;" u2="&#x41b;" k="19" />
    <hkern u1="&#xad;" u2="&#x414;" k="22" />
    <hkern u1="&#xad;" u2="&#x167;" k="45" />
    <hkern u1="&#xad;" u2="&#x166;" k="45" />
    <hkern u1="&#xad;" u2="\" k="40" />
    <hkern u1="&#xad;" u2="&#x37;" k="25" />
    <hkern u1="&#xad;" u2="&#x33;" k="11" />
    <hkern u1="&#xad;" u2="&#x32;" k="14" />
    <hkern u1="&#xad;" u2="&#x31;" k="15" />
    <hkern u1="&#xad;" u2="&#x2f;" k="21" />
    <hkern u1="&#xae;" u2="&#x1ef3;" k="42" />
    <hkern u1="&#xae;" u2="&#x1ef2;" k="42" />
    <hkern u1="&#xae;" u2="&#x1e85;" k="16" />
    <hkern u1="&#xae;" u2="&#x1e84;" k="16" />
    <hkern u1="&#xae;" u2="&#x1e83;" k="16" />
    <hkern u1="&#xae;" u2="&#x1e82;" k="16" />
    <hkern u1="&#xae;" u2="&#x1e81;" k="16" />
    <hkern u1="&#xae;" u2="&#x1e80;" k="16" />
    <hkern u1="&#xae;" u2="&#x21b;" k="12" />
    <hkern u1="&#xae;" u2="&#x21a;" k="12" />
    <hkern u1="&#xae;" u2="&#x17e;" k="14" />
    <hkern u1="&#xae;" u2="&#x17d;" k="14" />
    <hkern u1="&#xae;" u2="&#x17c;" k="14" />
    <hkern u1="&#xae;" u2="&#x17b;" k="14" />
    <hkern u1="&#xae;" u2="&#x17a;" k="14" />
    <hkern u1="&#xae;" u2="&#x179;" k="14" />
    <hkern u1="&#xae;" u2="&#x178;" k="42" />
    <hkern u1="&#xae;" u2="&#x177;" k="42" />
    <hkern u1="&#xae;" u2="&#x176;" k="42" />
    <hkern u1="&#xae;" u2="&#x175;" k="16" />
    <hkern u1="&#xae;" u2="&#x174;" k="16" />
    <hkern u1="&#xae;" u2="&#x167;" k="12" />
    <hkern u1="&#xae;" u2="&#x166;" k="12" />
    <hkern u1="&#xae;" u2="&#x165;" k="12" />
    <hkern u1="&#xae;" u2="&#x164;" k="12" />
    <hkern u1="&#xae;" u2="&#x163;" k="12" />
    <hkern u1="&#xae;" u2="&#x162;" k="12" />
    <hkern u1="&#xae;" u2="&#x105;" k="21" />
    <hkern u1="&#xae;" u2="&#x104;" k="21" />
    <hkern u1="&#xae;" u2="&#x103;" k="21" />
    <hkern u1="&#xae;" u2="&#x102;" k="21" />
    <hkern u1="&#xae;" u2="&#x101;" k="21" />
    <hkern u1="&#xae;" u2="&#x100;" k="21" />
    <hkern u1="&#xae;" u2="&#xff;" k="42" />
    <hkern u1="&#xae;" u2="&#xfd;" k="42" />
    <hkern u1="&#xae;" u2="&#xe6;" k="26" />
    <hkern u1="&#xae;" u2="&#xe5;" k="21" />
    <hkern u1="&#xae;" u2="&#xe4;" k="21" />
    <hkern u1="&#xae;" u2="&#xe3;" k="21" />
    <hkern u1="&#xae;" u2="&#xe2;" k="21" />
    <hkern u1="&#xae;" u2="&#xe1;" k="21" />
    <hkern u1="&#xae;" u2="&#xe0;" k="21" />
    <hkern u1="&#xae;" u2="&#xdd;" k="42" />
    <hkern u1="&#xae;" u2="&#xc6;" k="26" />
    <hkern u1="&#xae;" u2="&#xc5;" k="21" />
    <hkern u1="&#xae;" u2="&#xc4;" k="21" />
    <hkern u1="&#xae;" u2="&#xc3;" k="21" />
    <hkern u1="&#xae;" u2="&#xc2;" k="21" />
    <hkern u1="&#xae;" u2="&#xc1;" k="21" />
    <hkern u1="&#xae;" u2="&#xc0;" k="21" />
    <hkern u1="&#xae;" u2="z" k="14" />
    <hkern u1="&#xae;" u2="y" k="42" />
    <hkern u1="&#xae;" u2="x" k="26" />
    <hkern u1="&#xae;" u2="w" k="16" />
    <hkern u1="&#xae;" u2="v" k="23" />
    <hkern u1="&#xae;" u2="t" k="12" />
    <hkern u1="&#xae;" u2="a" k="21" />
    <hkern u1="&#xae;" u2="Z" k="14" />
    <hkern u1="&#xae;" u2="Y" k="42" />
    <hkern u1="&#xae;" u2="X" k="26" />
    <hkern u1="&#xae;" u2="W" k="16" />
    <hkern u1="&#xae;" u2="V" k="23" />
    <hkern u1="&#xae;" u2="T" k="12" />
    <hkern u1="&#xae;" u2="A" k="21" />
    <hkern u1="&#xae;" u2="\" k="27" />
    <hkern u1="&#xae;" u2="&#x2f;" k="25" />
    <hkern u1="&#xb0;" u2="&#x34;" k="36" />
    <hkern u1="&#xb7;" u2="&#x37;" k="25" />
    <hkern u1="&#xb7;" u2="&#x33;" k="16" />
    <hkern u1="&#xb7;" u2="&#x32;" k="18" />
    <hkern u1="&#xb7;" u2="&#x31;" k="15" />
    <hkern u1="&#xbb;" u2="&#x41b;" k="20" />
    <hkern u1="&#xbb;" u2="&#x414;" k="22" />
    <hkern u1="&#xbb;" u2="&#x167;" k="49" />
    <hkern u1="&#xbb;" u2="&#x166;" k="49" />
    <hkern u1="&#xbb;" u2="\" k="47" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="27" />
    <hkern u1="&#xbf;" g2="i.loclTRK" k="20" />
    <hkern u1="&#xbf;" g2="fl" k="20" />
    <hkern u1="&#xbf;" g2="fi" k="20" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="78" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="78" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="52" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="52" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="52" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="52" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="52" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="52" />
    <hkern u1="&#xbf;" u2="&#x21b;" k="61" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="61" />
    <hkern u1="&#xbf;" u2="&#x219;" k="21" />
    <hkern u1="&#xbf;" u2="&#x218;" k="21" />
    <hkern u1="&#xbf;" u2="&#x17e;" k="19" />
    <hkern u1="&#xbf;" u2="&#x17d;" k="19" />
    <hkern u1="&#xbf;" u2="&#x17c;" k="19" />
    <hkern u1="&#xbf;" u2="&#x17b;" k="19" />
    <hkern u1="&#xbf;" u2="&#x17a;" k="19" />
    <hkern u1="&#xbf;" u2="&#x179;" k="19" />
    <hkern u1="&#xbf;" u2="&#x178;" k="78" />
    <hkern u1="&#xbf;" u2="&#x177;" k="78" />
    <hkern u1="&#xbf;" u2="&#x176;" k="78" />
    <hkern u1="&#xbf;" u2="&#x175;" k="52" />
    <hkern u1="&#xbf;" u2="&#x174;" k="52" />
    <hkern u1="&#xbf;" u2="&#x173;" k="29" />
    <hkern u1="&#xbf;" u2="&#x172;" k="29" />
    <hkern u1="&#xbf;" u2="&#x171;" k="29" />
    <hkern u1="&#xbf;" u2="&#x170;" k="29" />
    <hkern u1="&#xbf;" u2="&#x16f;" k="29" />
    <hkern u1="&#xbf;" u2="&#x16e;" k="29" />
    <hkern u1="&#xbf;" u2="&#x16b;" k="29" />
    <hkern u1="&#xbf;" u2="&#x16a;" k="29" />
    <hkern u1="&#xbf;" u2="&#x167;" k="61" />
    <hkern u1="&#xbf;" u2="&#x166;" k="61" />
    <hkern u1="&#xbf;" u2="&#x165;" k="61" />
    <hkern u1="&#xbf;" u2="&#x164;" k="61" />
    <hkern u1="&#xbf;" u2="&#x163;" k="61" />
    <hkern u1="&#xbf;" u2="&#x162;" k="61" />
    <hkern u1="&#xbf;" u2="&#x161;" k="21" />
    <hkern u1="&#xbf;" u2="&#x160;" k="21" />
    <hkern u1="&#xbf;" u2="&#x15f;" k="21" />
    <hkern u1="&#xbf;" u2="&#x15e;" k="21" />
    <hkern u1="&#xbf;" u2="&#x15b;" k="21" />
    <hkern u1="&#xbf;" u2="&#x15a;" k="21" />
    <hkern u1="&#xbf;" u2="&#x159;" k="20" />
    <hkern u1="&#xbf;" u2="&#x158;" k="20" />
    <hkern u1="&#xbf;" u2="&#x157;" k="20" />
    <hkern u1="&#xbf;" u2="&#x156;" k="20" />
    <hkern u1="&#xbf;" u2="&#x155;" k="20" />
    <hkern u1="&#xbf;" u2="&#x154;" k="20" />
    <hkern u1="&#xbf;" u2="&#x153;" k="26" />
    <hkern u1="&#xbf;" u2="&#x152;" k="26" />
    <hkern u1="&#xbf;" u2="&#x151;" k="26" />
    <hkern u1="&#xbf;" u2="&#x150;" k="26" />
    <hkern u1="&#xbf;" u2="&#x14d;" k="26" />
    <hkern u1="&#xbf;" u2="&#x14c;" k="26" />
    <hkern u1="&#xbf;" u2="&#x14b;" k="20" />
    <hkern u1="&#xbf;" u2="&#x14a;" k="20" />
    <hkern u1="&#xbf;" u2="&#x148;" k="20" />
    <hkern u1="&#xbf;" u2="&#x147;" k="20" />
    <hkern u1="&#xbf;" u2="&#x146;" k="20" />
    <hkern u1="&#xbf;" u2="&#x145;" k="20" />
    <hkern u1="&#xbf;" u2="&#x144;" k="20" />
    <hkern u1="&#xbf;" u2="&#x143;" k="20" />
    <hkern u1="&#xbf;" u2="&#x142;" k="20" />
    <hkern u1="&#xbf;" u2="&#x141;" k="20" />
    <hkern u1="&#xbf;" u2="&#x140;" k="20" />
    <hkern u1="&#xbf;" u2="&#x13e;" k="20" />
    <hkern u1="&#xbf;" u2="&#x13d;" k="20" />
    <hkern u1="&#xbf;" u2="&#x13c;" k="20" />
    <hkern u1="&#xbf;" u2="&#x13b;" k="20" />
    <hkern u1="&#xbf;" u2="&#x13a;" k="20" />
    <hkern u1="&#xbf;" u2="&#x139;" k="20" />
    <hkern u1="&#xbf;" u2="&#x137;" k="20" />
    <hkern u1="&#xbf;" u2="&#x136;" k="20" />
    <hkern u1="&#xbf;" u2="&#x133;" k="20" />
    <hkern u1="&#xbf;" u2="&#x132;" k="20" />
    <hkern u1="&#xbf;" u2="&#x131;" k="20" />
    <hkern u1="&#xbf;" u2="&#x130;" k="20" />
    <hkern u1="&#xbf;" u2="&#x12f;" k="20" />
    <hkern u1="&#xbf;" u2="&#x12e;" k="20" />
    <hkern u1="&#xbf;" u2="&#x12b;" k="20" />
    <hkern u1="&#xbf;" u2="&#x12a;" k="20" />
    <hkern u1="&#xbf;" u2="&#x127;" k="20" />
    <hkern u1="&#xbf;" u2="&#x126;" k="20" />
    <hkern u1="&#xbf;" u2="&#x123;" k="26" />
    <hkern u1="&#xbf;" u2="&#x122;" k="26" />
    <hkern u1="&#xbf;" u2="&#x121;" k="26" />
    <hkern u1="&#xbf;" u2="&#x120;" k="26" />
    <hkern u1="&#xbf;" u2="&#x11f;" k="26" />
    <hkern u1="&#xbf;" u2="&#x11e;" k="26" />
    <hkern u1="&#xbf;" u2="&#x11b;" k="20" />
    <hkern u1="&#xbf;" u2="&#x11a;" k="20" />
    <hkern u1="&#xbf;" u2="&#x119;" k="20" />
    <hkern u1="&#xbf;" u2="&#x118;" k="20" />
    <hkern u1="&#xbf;" u2="&#x117;" k="20" />
    <hkern u1="&#xbf;" u2="&#x116;" k="20" />
    <hkern u1="&#xbf;" u2="&#x113;" k="20" />
    <hkern u1="&#xbf;" u2="&#x112;" k="20" />
    <hkern u1="&#xbf;" u2="&#x111;" k="20" />
    <hkern u1="&#xbf;" u2="&#x110;" k="20" />
    <hkern u1="&#xbf;" u2="&#x10f;" k="20" />
    <hkern u1="&#xbf;" u2="&#x10e;" k="20" />
    <hkern u1="&#xbf;" u2="&#x10d;" k="26" />
    <hkern u1="&#xbf;" u2="&#x10c;" k="26" />
    <hkern u1="&#xbf;" u2="&#x10b;" k="26" />
    <hkern u1="&#xbf;" u2="&#x10a;" k="26" />
    <hkern u1="&#xbf;" u2="&#x107;" k="26" />
    <hkern u1="&#xbf;" u2="&#x106;" k="26" />
    <hkern u1="&#xbf;" u2="&#x105;" k="11" />
    <hkern u1="&#xbf;" u2="&#x104;" k="11" />
    <hkern u1="&#xbf;" u2="&#x103;" k="11" />
    <hkern u1="&#xbf;" u2="&#x102;" k="11" />
    <hkern u1="&#xbf;" u2="&#x101;" k="11" />
    <hkern u1="&#xbf;" u2="&#x100;" k="11" />
    <hkern u1="&#xbf;" u2="&#xff;" k="78" />
    <hkern u1="&#xbf;" u2="&#xfe;" k="20" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="78" />
    <hkern u1="&#xbf;" u2="&#xfc;" k="29" />
    <hkern u1="&#xbf;" u2="&#xfb;" k="29" />
    <hkern u1="&#xbf;" u2="&#xfa;" k="29" />
    <hkern u1="&#xbf;" u2="&#xf9;" k="29" />
    <hkern u1="&#xbf;" u2="&#xf8;" k="26" />
    <hkern u1="&#xbf;" u2="&#xf6;" k="26" />
    <hkern u1="&#xbf;" u2="&#xf5;" k="26" />
    <hkern u1="&#xbf;" u2="&#xf4;" k="26" />
    <hkern u1="&#xbf;" u2="&#xf3;" k="26" />
    <hkern u1="&#xbf;" u2="&#xf2;" k="26" />
    <hkern u1="&#xbf;" u2="&#xf1;" k="20" />
    <hkern u1="&#xbf;" u2="&#xf0;" k="20" />
    <hkern u1="&#xbf;" u2="&#xef;" k="20" />
    <hkern u1="&#xbf;" u2="&#xee;" k="20" />
    <hkern u1="&#xbf;" u2="&#xed;" k="20" />
    <hkern u1="&#xbf;" u2="&#xec;" k="20" />
    <hkern u1="&#xbf;" u2="&#xeb;" k="20" />
    <hkern u1="&#xbf;" u2="&#xea;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe9;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe8;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe7;" k="26" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="10" />
    <hkern u1="&#xbf;" u2="&#xe5;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe4;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe3;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe2;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe1;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe0;" k="11" />
    <hkern u1="&#xbf;" u2="&#xde;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="78" />
    <hkern u1="&#xbf;" u2="&#xdc;" k="29" />
    <hkern u1="&#xbf;" u2="&#xdb;" k="29" />
    <hkern u1="&#xbf;" u2="&#xda;" k="29" />
    <hkern u1="&#xbf;" u2="&#xd9;" k="29" />
    <hkern u1="&#xbf;" u2="&#xd8;" k="26" />
    <hkern u1="&#xbf;" u2="&#xd6;" k="26" />
    <hkern u1="&#xbf;" u2="&#xd5;" k="26" />
    <hkern u1="&#xbf;" u2="&#xd4;" k="26" />
    <hkern u1="&#xbf;" u2="&#xd3;" k="26" />
    <hkern u1="&#xbf;" u2="&#xd2;" k="26" />
    <hkern u1="&#xbf;" u2="&#xd1;" k="20" />
    <hkern u1="&#xbf;" u2="&#xd0;" k="20" />
    <hkern u1="&#xbf;" u2="&#xcf;" k="20" />
    <hkern u1="&#xbf;" u2="&#xce;" k="20" />
    <hkern u1="&#xbf;" u2="&#xcd;" k="20" />
    <hkern u1="&#xbf;" u2="&#xcc;" k="20" />
    <hkern u1="&#xbf;" u2="&#xcb;" k="20" />
    <hkern u1="&#xbf;" u2="&#xca;" k="20" />
    <hkern u1="&#xbf;" u2="&#xc9;" k="20" />
    <hkern u1="&#xbf;" u2="&#xc8;" k="20" />
    <hkern u1="&#xbf;" u2="&#xc7;" k="26" />
    <hkern u1="&#xbf;" u2="&#xc6;" k="10" />
    <hkern u1="&#xbf;" u2="&#xc5;" k="11" />
    <hkern u1="&#xbf;" u2="&#xc4;" k="11" />
    <hkern u1="&#xbf;" u2="&#xc3;" k="11" />
    <hkern u1="&#xbf;" u2="&#xc2;" k="11" />
    <hkern u1="&#xbf;" u2="&#xc1;" k="11" />
    <hkern u1="&#xbf;" u2="&#xc0;" k="11" />
    <hkern u1="&#xbf;" u2="z" k="19" />
    <hkern u1="&#xbf;" u2="y" k="78" />
    <hkern u1="&#xbf;" u2="x" k="15" />
    <hkern u1="&#xbf;" u2="w" k="52" />
    <hkern u1="&#xbf;" u2="v" k="59" />
    <hkern u1="&#xbf;" u2="u" k="29" />
    <hkern u1="&#xbf;" u2="t" k="61" />
    <hkern u1="&#xbf;" u2="s" k="21" />
    <hkern u1="&#xbf;" u2="r" k="20" />
    <hkern u1="&#xbf;" u2="q" k="26" />
    <hkern u1="&#xbf;" u2="p" k="20" />
    <hkern u1="&#xbf;" u2="o" k="26" />
    <hkern u1="&#xbf;" u2="n" k="20" />
    <hkern u1="&#xbf;" u2="m" k="20" />
    <hkern u1="&#xbf;" u2="l" k="20" />
    <hkern u1="&#xbf;" u2="k" k="20" />
    <hkern u1="&#xbf;" u2="j" k="27" />
    <hkern u1="&#xbf;" u2="i" k="20" />
    <hkern u1="&#xbf;" u2="h" k="20" />
    <hkern u1="&#xbf;" u2="g" k="26" />
    <hkern u1="&#xbf;" u2="f" k="20" />
    <hkern u1="&#xbf;" u2="e" k="20" />
    <hkern u1="&#xbf;" u2="d" k="20" />
    <hkern u1="&#xbf;" u2="c" k="26" />
    <hkern u1="&#xbf;" u2="b" k="20" />
    <hkern u1="&#xbf;" u2="a" k="11" />
    <hkern u1="&#xbf;" u2="Z" k="19" />
    <hkern u1="&#xbf;" u2="Y" k="78" />
    <hkern u1="&#xbf;" u2="X" k="15" />
    <hkern u1="&#xbf;" u2="W" k="52" />
    <hkern u1="&#xbf;" u2="V" k="59" />
    <hkern u1="&#xbf;" u2="U" k="29" />
    <hkern u1="&#xbf;" u2="T" k="61" />
    <hkern u1="&#xbf;" u2="S" k="21" />
    <hkern u1="&#xbf;" u2="R" k="20" />
    <hkern u1="&#xbf;" u2="Q" k="26" />
    <hkern u1="&#xbf;" u2="P" k="20" />
    <hkern u1="&#xbf;" u2="O" k="26" />
    <hkern u1="&#xbf;" u2="N" k="20" />
    <hkern u1="&#xbf;" u2="M" k="20" />
    <hkern u1="&#xbf;" u2="L" k="20" />
    <hkern u1="&#xbf;" u2="K" k="20" />
    <hkern u1="&#xbf;" u2="J" k="27" />
    <hkern u1="&#xbf;" u2="I" k="20" />
    <hkern u1="&#xbf;" u2="H" k="20" />
    <hkern u1="&#xbf;" u2="G" k="26" />
    <hkern u1="&#xbf;" u2="F" k="20" />
    <hkern u1="&#xbf;" u2="E" k="20" />
    <hkern u1="&#xbf;" u2="D" k="20" />
    <hkern u1="&#xbf;" u2="C" k="26" />
    <hkern u1="&#xbf;" u2="B" k="20" />
    <hkern u1="&#xbf;" u2="A" k="11" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="17" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="60" />
    <hkern u1="&#xc0;" u2="&#xba;" k="48" />
    <hkern u1="&#xc0;" u2="&#xae;" k="19" />
    <hkern u1="&#xc0;" u2="&#xaa;" k="47" />
    <hkern u1="&#xc0;" u2="&#xa9;" k="19" />
    <hkern u1="&#xc0;" u2="\" k="68" />
    <hkern u1="&#xc0;" u2="&#x40;" k="15" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="18" />
    <hkern u1="&#xc0;" u2="&#x39;" k="10" />
    <hkern u1="&#xc0;" u2="&#x38;" k="10" />
    <hkern u1="&#xc0;" u2="&#x36;" k="11" />
    <hkern u1="&#xc0;" u2="&#x34;" k="11" />
    <hkern u1="&#xc0;" u2="&#x31;" k="40" />
    <hkern u1="&#xc0;" u2="&#x30;" k="11" />
    <hkern u1="&#xc0;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="51" />
    <hkern u1="&#xc0;" u2="&#x26;" k="10" />
    <hkern u1="&#xc0;" u2="&#x20;" k="27" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="60" />
    <hkern u1="&#xc1;" u2="&#xba;" k="48" />
    <hkern u1="&#xc1;" u2="&#xae;" k="19" />
    <hkern u1="&#xc1;" u2="&#xaa;" k="47" />
    <hkern u1="&#xc1;" u2="&#xa9;" k="19" />
    <hkern u1="&#xc1;" u2="\" k="68" />
    <hkern u1="&#xc1;" u2="&#x40;" k="15" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="18" />
    <hkern u1="&#xc1;" u2="&#x39;" k="10" />
    <hkern u1="&#xc1;" u2="&#x38;" k="10" />
    <hkern u1="&#xc1;" u2="&#x36;" k="11" />
    <hkern u1="&#xc1;" u2="&#x34;" k="11" />
    <hkern u1="&#xc1;" u2="&#x31;" k="40" />
    <hkern u1="&#xc1;" u2="&#x30;" k="11" />
    <hkern u1="&#xc1;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="51" />
    <hkern u1="&#xc1;" u2="&#x26;" k="10" />
    <hkern u1="&#xc1;" u2="&#x20;" k="27" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="60" />
    <hkern u1="&#xc2;" u2="&#xba;" k="48" />
    <hkern u1="&#xc2;" u2="&#xae;" k="19" />
    <hkern u1="&#xc2;" u2="&#xaa;" k="47" />
    <hkern u1="&#xc2;" u2="&#xa9;" k="19" />
    <hkern u1="&#xc2;" u2="\" k="68" />
    <hkern u1="&#xc2;" u2="&#x40;" k="15" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="18" />
    <hkern u1="&#xc2;" u2="&#x39;" k="10" />
    <hkern u1="&#xc2;" u2="&#x38;" k="10" />
    <hkern u1="&#xc2;" u2="&#x36;" k="11" />
    <hkern u1="&#xc2;" u2="&#x34;" k="11" />
    <hkern u1="&#xc2;" u2="&#x31;" k="40" />
    <hkern u1="&#xc2;" u2="&#x30;" k="11" />
    <hkern u1="&#xc2;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="51" />
    <hkern u1="&#xc2;" u2="&#x26;" k="10" />
    <hkern u1="&#xc2;" u2="&#x20;" k="27" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="60" />
    <hkern u1="&#xc3;" u2="&#xba;" k="48" />
    <hkern u1="&#xc3;" u2="&#xae;" k="19" />
    <hkern u1="&#xc3;" u2="&#xaa;" k="47" />
    <hkern u1="&#xc3;" u2="&#xa9;" k="19" />
    <hkern u1="&#xc3;" u2="\" k="68" />
    <hkern u1="&#xc3;" u2="&#x40;" k="15" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="18" />
    <hkern u1="&#xc3;" u2="&#x39;" k="10" />
    <hkern u1="&#xc3;" u2="&#x38;" k="10" />
    <hkern u1="&#xc3;" u2="&#x36;" k="11" />
    <hkern u1="&#xc3;" u2="&#x34;" k="11" />
    <hkern u1="&#xc3;" u2="&#x31;" k="40" />
    <hkern u1="&#xc3;" u2="&#x30;" k="11" />
    <hkern u1="&#xc3;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="51" />
    <hkern u1="&#xc3;" u2="&#x26;" k="10" />
    <hkern u1="&#xc3;" u2="&#x20;" k="27" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="60" />
    <hkern u1="&#xc4;" u2="&#xba;" k="48" />
    <hkern u1="&#xc4;" u2="&#xae;" k="19" />
    <hkern u1="&#xc4;" u2="&#xaa;" k="47" />
    <hkern u1="&#xc4;" u2="&#xa9;" k="19" />
    <hkern u1="&#xc4;" u2="\" k="68" />
    <hkern u1="&#xc4;" u2="&#x40;" k="15" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="18" />
    <hkern u1="&#xc4;" u2="&#x39;" k="10" />
    <hkern u1="&#xc4;" u2="&#x38;" k="10" />
    <hkern u1="&#xc4;" u2="&#x36;" k="11" />
    <hkern u1="&#xc4;" u2="&#x34;" k="11" />
    <hkern u1="&#xc4;" u2="&#x31;" k="40" />
    <hkern u1="&#xc4;" u2="&#x30;" k="11" />
    <hkern u1="&#xc4;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="51" />
    <hkern u1="&#xc4;" u2="&#x26;" k="10" />
    <hkern u1="&#xc4;" u2="&#x20;" k="27" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="60" />
    <hkern u1="&#xc5;" u2="&#xba;" k="48" />
    <hkern u1="&#xc5;" u2="&#xae;" k="19" />
    <hkern u1="&#xc5;" u2="&#xaa;" k="47" />
    <hkern u1="&#xc5;" u2="&#xa9;" k="19" />
    <hkern u1="&#xc5;" u2="\" k="68" />
    <hkern u1="&#xc5;" u2="&#x40;" k="15" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="18" />
    <hkern u1="&#xc5;" u2="&#x39;" k="10" />
    <hkern u1="&#xc5;" u2="&#x38;" k="10" />
    <hkern u1="&#xc5;" u2="&#x36;" k="11" />
    <hkern u1="&#xc5;" u2="&#x34;" k="11" />
    <hkern u1="&#xc5;" u2="&#x31;" k="40" />
    <hkern u1="&#xc5;" u2="&#x30;" k="11" />
    <hkern u1="&#xc5;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="51" />
    <hkern u1="&#xc5;" u2="&#x26;" k="10" />
    <hkern u1="&#xc5;" u2="&#x20;" k="27" />
    <hkern u1="&#xce;" u2="\" k="-25" />
    <hkern u1="&#xce;" u2="&#x29;" k="-30" />
    <hkern u1="&#xd0;" u2="_" k="30" />
    <hkern u1="&#xd0;" u2="\" k="12" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="12" />
    <hkern u1="&#xd0;" u2="&#x29;" k="19" />
    <hkern u1="&#xd2;" u2="_" k="29" />
    <hkern u1="&#xd2;" u2="\" k="12" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="12" />
    <hkern u1="&#xd2;" u2="&#x29;" k="19" />
    <hkern u1="&#xd3;" u2="_" k="29" />
    <hkern u1="&#xd3;" u2="\" k="12" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="12" />
    <hkern u1="&#xd3;" u2="&#x29;" k="19" />
    <hkern u1="&#xd4;" u2="_" k="29" />
    <hkern u1="&#xd4;" u2="\" k="12" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="12" />
    <hkern u1="&#xd4;" u2="&#x29;" k="19" />
    <hkern u1="&#xd5;" u2="_" k="29" />
    <hkern u1="&#xd5;" u2="\" k="12" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="12" />
    <hkern u1="&#xd5;" u2="&#x29;" k="19" />
    <hkern u1="&#xd6;" u2="_" k="29" />
    <hkern u1="&#xd6;" u2="\" k="12" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="12" />
    <hkern u1="&#xd6;" u2="&#x29;" k="19" />
    <hkern u1="&#xd8;" u2="_" k="29" />
    <hkern u1="&#xd8;" u2="\" k="12" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="12" />
    <hkern u1="&#xd8;" u2="&#x29;" k="19" />
    <hkern u1="&#xd9;" u2="_" k="39" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="17" />
    <hkern u1="&#xda;" u2="_" k="39" />
    <hkern u1="&#xda;" u2="&#x2f;" k="17" />
    <hkern u1="&#xdb;" u2="_" k="39" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="17" />
    <hkern u1="&#xdc;" u2="_" k="39" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="17" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-25" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="36" />
    <hkern u1="&#xdd;" u2="&#xae;" k="42" />
    <hkern u1="&#xdd;" u2="&#xa9;" k="42" />
    <hkern u1="&#xdd;" u2="_" k="79" />
    <hkern u1="&#xdd;" u2="\" k="-13" />
    <hkern u1="&#xdd;" u2="&#x40;" k="47" />
    <hkern u1="&#xdd;" u2="&#x39;" k="15" />
    <hkern u1="&#xdd;" u2="&#x38;" k="26" />
    <hkern u1="&#xdd;" u2="&#x37;" k="-10" />
    <hkern u1="&#xdd;" u2="&#x36;" k="20" />
    <hkern u1="&#xdd;" u2="&#x34;" k="75" />
    <hkern u1="&#xdd;" u2="&#x32;" k="15" />
    <hkern u1="&#xdd;" u2="&#x31;" k="16" />
    <hkern u1="&#xdd;" u2="&#x30;" k="19" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="79" />
    <hkern u1="&#xdd;" u2="&#x29;" k="-19" />
    <hkern u1="&#xdd;" u2="&#x26;" k="36" />
    <hkern u1="&#xdd;" u2="&#x20;" k="34" />
    <hkern u1="&#xde;" u2="&#x2122;" k="10" />
    <hkern u1="&#xde;" u2="_" k="40" />
    <hkern u1="&#xde;" u2="]" k="12" />
    <hkern u1="&#xde;" u2="\" k="21" />
    <hkern u1="&#xde;" u2="&#x2f;" k="25" />
    <hkern u1="&#xde;" u2="&#x29;" k="28" />
    <hkern u1="&#xdf;" u2="&#x201d;" k="9" />
    <hkern u1="&#xdf;" u2="&#x201c;" k="9" />
    <hkern u1="&#xdf;" u2="&#x2019;" k="9" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="9" />
    <hkern u1="&#xdf;" u2="&#x27;" k="10" />
    <hkern u1="&#xdf;" u2="&#x22;" k="10" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="8" />
    <hkern u1="&#xdf;" u2="&#xe4;" k="7" />
    <hkern u1="&#xdf;" u2="&#xe2;" k="7" />
    <hkern u1="&#xdf;" u2="y" k="50" />
    <hkern u1="&#xdf;" u2="x" k="15" />
    <hkern u1="&#xdf;" u2="w" k="22" />
    <hkern u1="&#xdf;" u2="v" k="28" />
    <hkern u1="&#xdf;" u2="t" k="18" />
    <hkern u1="&#xdf;" u2="a" k="7" />
    <hkern u1="&#xdf;" u2="\" k="24" />
    <hkern u1="&#xdf;" u2="&#x29;" k="18" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="60" />
    <hkern u1="&#xe0;" u2="&#xba;" k="48" />
    <hkern u1="&#xe0;" u2="&#xae;" k="19" />
    <hkern u1="&#xe0;" u2="&#xaa;" k="47" />
    <hkern u1="&#xe0;" u2="&#xa9;" k="19" />
    <hkern u1="&#xe0;" u2="\" k="68" />
    <hkern u1="&#xe0;" u2="&#x40;" k="15" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="18" />
    <hkern u1="&#xe0;" u2="&#x39;" k="10" />
    <hkern u1="&#xe0;" u2="&#x38;" k="10" />
    <hkern u1="&#xe0;" u2="&#x36;" k="11" />
    <hkern u1="&#xe0;" u2="&#x34;" k="11" />
    <hkern u1="&#xe0;" u2="&#x31;" k="40" />
    <hkern u1="&#xe0;" u2="&#x30;" k="11" />
    <hkern u1="&#xe0;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="51" />
    <hkern u1="&#xe0;" u2="&#x26;" k="10" />
    <hkern u1="&#xe0;" u2="&#x20;" k="27" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="60" />
    <hkern u1="&#xe1;" u2="&#xba;" k="48" />
    <hkern u1="&#xe1;" u2="&#xae;" k="19" />
    <hkern u1="&#xe1;" u2="&#xaa;" k="47" />
    <hkern u1="&#xe1;" u2="&#xa9;" k="19" />
    <hkern u1="&#xe1;" u2="\" k="68" />
    <hkern u1="&#xe1;" u2="&#x40;" k="15" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="18" />
    <hkern u1="&#xe1;" u2="&#x39;" k="10" />
    <hkern u1="&#xe1;" u2="&#x38;" k="10" />
    <hkern u1="&#xe1;" u2="&#x36;" k="11" />
    <hkern u1="&#xe1;" u2="&#x34;" k="11" />
    <hkern u1="&#xe1;" u2="&#x31;" k="40" />
    <hkern u1="&#xe1;" u2="&#x30;" k="11" />
    <hkern u1="&#xe1;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="51" />
    <hkern u1="&#xe1;" u2="&#x26;" k="10" />
    <hkern u1="&#xe1;" u2="&#x20;" k="27" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="60" />
    <hkern u1="&#xe2;" u2="&#xba;" k="48" />
    <hkern u1="&#xe2;" u2="&#xae;" k="19" />
    <hkern u1="&#xe2;" u2="&#xaa;" k="47" />
    <hkern u1="&#xe2;" u2="&#xa9;" k="19" />
    <hkern u1="&#xe2;" u2="\" k="68" />
    <hkern u1="&#xe2;" u2="&#x40;" k="15" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="18" />
    <hkern u1="&#xe2;" u2="&#x39;" k="10" />
    <hkern u1="&#xe2;" u2="&#x38;" k="10" />
    <hkern u1="&#xe2;" u2="&#x36;" k="11" />
    <hkern u1="&#xe2;" u2="&#x34;" k="11" />
    <hkern u1="&#xe2;" u2="&#x31;" k="40" />
    <hkern u1="&#xe2;" u2="&#x30;" k="11" />
    <hkern u1="&#xe2;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="51" />
    <hkern u1="&#xe2;" u2="&#x26;" k="10" />
    <hkern u1="&#xe2;" u2="&#x20;" k="27" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="60" />
    <hkern u1="&#xe3;" u2="&#xba;" k="48" />
    <hkern u1="&#xe3;" u2="&#xae;" k="19" />
    <hkern u1="&#xe3;" u2="&#xaa;" k="47" />
    <hkern u1="&#xe3;" u2="&#xa9;" k="19" />
    <hkern u1="&#xe3;" u2="\" k="68" />
    <hkern u1="&#xe3;" u2="&#x40;" k="15" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="18" />
    <hkern u1="&#xe3;" u2="&#x39;" k="10" />
    <hkern u1="&#xe3;" u2="&#x38;" k="10" />
    <hkern u1="&#xe3;" u2="&#x36;" k="11" />
    <hkern u1="&#xe3;" u2="&#x34;" k="11" />
    <hkern u1="&#xe3;" u2="&#x31;" k="40" />
    <hkern u1="&#xe3;" u2="&#x30;" k="11" />
    <hkern u1="&#xe3;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="51" />
    <hkern u1="&#xe3;" u2="&#x26;" k="10" />
    <hkern u1="&#xe3;" u2="&#x20;" k="27" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="60" />
    <hkern u1="&#xe4;" u2="&#xba;" k="48" />
    <hkern u1="&#xe4;" u2="&#xae;" k="19" />
    <hkern u1="&#xe4;" u2="&#xaa;" k="47" />
    <hkern u1="&#xe4;" u2="&#xa9;" k="19" />
    <hkern u1="&#xe4;" u2="\" k="68" />
    <hkern u1="&#xe4;" u2="&#x40;" k="15" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="18" />
    <hkern u1="&#xe4;" u2="&#x39;" k="10" />
    <hkern u1="&#xe4;" u2="&#x38;" k="10" />
    <hkern u1="&#xe4;" u2="&#x36;" k="11" />
    <hkern u1="&#xe4;" u2="&#x34;" k="11" />
    <hkern u1="&#xe4;" u2="&#x31;" k="40" />
    <hkern u1="&#xe4;" u2="&#x30;" k="11" />
    <hkern u1="&#xe4;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="51" />
    <hkern u1="&#xe4;" u2="&#x26;" k="10" />
    <hkern u1="&#xe4;" u2="&#x20;" k="27" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="60" />
    <hkern u1="&#xe5;" u2="&#xba;" k="48" />
    <hkern u1="&#xe5;" u2="&#xae;" k="19" />
    <hkern u1="&#xe5;" u2="&#xaa;" k="47" />
    <hkern u1="&#xe5;" u2="&#xa9;" k="19" />
    <hkern u1="&#xe5;" u2="\" k="68" />
    <hkern u1="&#xe5;" u2="&#x40;" k="15" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="18" />
    <hkern u1="&#xe5;" u2="&#x39;" k="10" />
    <hkern u1="&#xe5;" u2="&#x38;" k="10" />
    <hkern u1="&#xe5;" u2="&#x36;" k="11" />
    <hkern u1="&#xe5;" u2="&#x34;" k="11" />
    <hkern u1="&#xe5;" u2="&#x31;" k="40" />
    <hkern u1="&#xe5;" u2="&#x30;" k="11" />
    <hkern u1="&#xe5;" u2="&#x2f;" k="-7" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="51" />
    <hkern u1="&#xe5;" u2="&#x26;" k="10" />
    <hkern u1="&#xe5;" u2="&#x20;" k="27" />
    <hkern u1="&#xee;" u2="\" k="-25" />
    <hkern u1="&#xee;" u2="&#x29;" k="-30" />
    <hkern u1="&#xf0;" u2="_" k="30" />
    <hkern u1="&#xf0;" u2="\" k="12" />
    <hkern u1="&#xf0;" u2="&#x2f;" k="12" />
    <hkern u1="&#xf0;" u2="&#x29;" k="19" />
    <hkern u1="&#xf2;" u2="_" k="29" />
    <hkern u1="&#xf2;" u2="\" k="12" />
    <hkern u1="&#xf2;" u2="&#x2f;" k="12" />
    <hkern u1="&#xf2;" u2="&#x29;" k="19" />
    <hkern u1="&#xf3;" u2="_" k="29" />
    <hkern u1="&#xf3;" u2="\" k="12" />
    <hkern u1="&#xf3;" u2="&#x2f;" k="12" />
    <hkern u1="&#xf3;" u2="&#x29;" k="19" />
    <hkern u1="&#xf4;" u2="_" k="29" />
    <hkern u1="&#xf4;" u2="\" k="12" />
    <hkern u1="&#xf4;" u2="&#x2f;" k="12" />
    <hkern u1="&#xf4;" u2="&#x29;" k="19" />
    <hkern u1="&#xf5;" u2="_" k="29" />
    <hkern u1="&#xf5;" u2="\" k="12" />
    <hkern u1="&#xf5;" u2="&#x2f;" k="12" />
    <hkern u1="&#xf5;" u2="&#x29;" k="19" />
    <hkern u1="&#xf6;" u2="_" k="29" />
    <hkern u1="&#xf6;" u2="\" k="12" />
    <hkern u1="&#xf6;" u2="&#x2f;" k="12" />
    <hkern u1="&#xf6;" u2="&#x29;" k="19" />
    <hkern u1="&#xf7;" u2="&#x37;" k="32" />
    <hkern u1="&#xf7;" u2="&#x33;" k="18" />
    <hkern u1="&#xf7;" u2="&#x32;" k="22" />
    <hkern u1="&#xf7;" u2="&#x31;" k="22" />
    <hkern u1="&#xf8;" u2="_" k="29" />
    <hkern u1="&#xf8;" u2="\" k="12" />
    <hkern u1="&#xf8;" u2="&#x2f;" k="12" />
    <hkern u1="&#xf8;" u2="&#x29;" k="19" />
    <hkern u1="&#xf9;" u2="_" k="39" />
    <hkern u1="&#xf9;" u2="&#x2f;" k="17" />
    <hkern u1="&#xfa;" u2="_" k="39" />
    <hkern u1="&#xfa;" u2="&#x2f;" k="17" />
    <hkern u1="&#xfb;" u2="_" k="39" />
    <hkern u1="&#xfb;" u2="&#x2f;" k="17" />
    <hkern u1="&#xfc;" u2="_" k="39" />
    <hkern u1="&#xfc;" u2="&#x2f;" k="17" />
    <hkern u1="&#xfd;" u2="&#x2122;" k="-25" />
    <hkern u1="&#xfd;" u2="&#xdf;" k="36" />
    <hkern u1="&#xfd;" u2="&#xae;" k="42" />
    <hkern u1="&#xfd;" u2="&#xa9;" k="42" />
    <hkern u1="&#xfd;" u2="_" k="79" />
    <hkern u1="&#xfd;" u2="\" k="-13" />
    <hkern u1="&#xfd;" u2="&#x40;" k="47" />
    <hkern u1="&#xfd;" u2="&#x39;" k="15" />
    <hkern u1="&#xfd;" u2="&#x38;" k="26" />
    <hkern u1="&#xfd;" u2="&#x37;" k="-10" />
    <hkern u1="&#xfd;" u2="&#x36;" k="20" />
    <hkern u1="&#xfd;" u2="&#x34;" k="75" />
    <hkern u1="&#xfd;" u2="&#x32;" k="15" />
    <hkern u1="&#xfd;" u2="&#x31;" k="16" />
    <hkern u1="&#xfd;" u2="&#x30;" k="19" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="79" />
    <hkern u1="&#xfd;" u2="&#x29;" k="-19" />
    <hkern u1="&#xfd;" u2="&#x26;" k="36" />
    <hkern u1="&#xfd;" u2="&#x20;" k="34" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="10" />
    <hkern u1="&#xfe;" u2="_" k="40" />
    <hkern u1="&#xfe;" u2="]" k="12" />
    <hkern u1="&#xfe;" u2="\" k="21" />
    <hkern u1="&#xfe;" u2="&#x2f;" k="25" />
    <hkern u1="&#xfe;" u2="&#x29;" k="28" />
    <hkern u1="&#xff;" u2="&#x2122;" k="-25" />
    <hkern u1="&#xff;" u2="&#xdf;" k="36" />
    <hkern u1="&#xff;" u2="&#xae;" k="42" />
    <hkern u1="&#xff;" u2="&#xa9;" k="42" />
    <hkern u1="&#xff;" u2="_" k="79" />
    <hkern u1="&#xff;" u2="\" k="-13" />
    <hkern u1="&#xff;" u2="&#x40;" k="47" />
    <hkern u1="&#xff;" u2="&#x39;" k="15" />
    <hkern u1="&#xff;" u2="&#x38;" k="26" />
    <hkern u1="&#xff;" u2="&#x37;" k="-10" />
    <hkern u1="&#xff;" u2="&#x36;" k="20" />
    <hkern u1="&#xff;" u2="&#x34;" k="75" />
    <hkern u1="&#xff;" u2="&#x32;" k="15" />
    <hkern u1="&#xff;" u2="&#x31;" k="16" />
    <hkern u1="&#xff;" u2="&#x30;" k="19" />
    <hkern u1="&#xff;" u2="&#x2f;" k="79" />
    <hkern u1="&#xff;" u2="&#x29;" k="-19" />
    <hkern u1="&#xff;" u2="&#x26;" k="36" />
    <hkern u1="&#xff;" u2="&#x20;" k="34" />
    <hkern u1="&#x100;" u2="&#x2122;" k="60" />
    <hkern u1="&#x100;" u2="&#xba;" k="48" />
    <hkern u1="&#x100;" u2="&#xae;" k="19" />
    <hkern u1="&#x100;" u2="&#xaa;" k="47" />
    <hkern u1="&#x100;" u2="&#xa9;" k="19" />
    <hkern u1="&#x100;" u2="\" k="68" />
    <hkern u1="&#x100;" u2="&#x40;" k="15" />
    <hkern u1="&#x100;" u2="&#x3f;" k="18" />
    <hkern u1="&#x100;" u2="&#x39;" k="10" />
    <hkern u1="&#x100;" u2="&#x38;" k="10" />
    <hkern u1="&#x100;" u2="&#x36;" k="11" />
    <hkern u1="&#x100;" u2="&#x34;" k="11" />
    <hkern u1="&#x100;" u2="&#x31;" k="40" />
    <hkern u1="&#x100;" u2="&#x30;" k="11" />
    <hkern u1="&#x100;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x100;" u2="&#x2a;" k="51" />
    <hkern u1="&#x100;" u2="&#x26;" k="10" />
    <hkern u1="&#x100;" u2="&#x20;" k="27" />
    <hkern u1="&#x101;" u2="&#x2122;" k="60" />
    <hkern u1="&#x101;" u2="&#xba;" k="48" />
    <hkern u1="&#x101;" u2="&#xae;" k="19" />
    <hkern u1="&#x101;" u2="&#xaa;" k="47" />
    <hkern u1="&#x101;" u2="&#xa9;" k="19" />
    <hkern u1="&#x101;" u2="\" k="68" />
    <hkern u1="&#x101;" u2="&#x40;" k="15" />
    <hkern u1="&#x101;" u2="&#x3f;" k="18" />
    <hkern u1="&#x101;" u2="&#x39;" k="10" />
    <hkern u1="&#x101;" u2="&#x38;" k="10" />
    <hkern u1="&#x101;" u2="&#x36;" k="11" />
    <hkern u1="&#x101;" u2="&#x34;" k="11" />
    <hkern u1="&#x101;" u2="&#x31;" k="40" />
    <hkern u1="&#x101;" u2="&#x30;" k="11" />
    <hkern u1="&#x101;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x101;" u2="&#x2a;" k="51" />
    <hkern u1="&#x101;" u2="&#x26;" k="10" />
    <hkern u1="&#x101;" u2="&#x20;" k="27" />
    <hkern u1="&#x102;" u2="&#x2122;" k="60" />
    <hkern u1="&#x102;" u2="&#xba;" k="48" />
    <hkern u1="&#x102;" u2="&#xae;" k="19" />
    <hkern u1="&#x102;" u2="&#xaa;" k="47" />
    <hkern u1="&#x102;" u2="&#xa9;" k="19" />
    <hkern u1="&#x102;" u2="\" k="68" />
    <hkern u1="&#x102;" u2="&#x40;" k="15" />
    <hkern u1="&#x102;" u2="&#x3f;" k="18" />
    <hkern u1="&#x102;" u2="&#x39;" k="10" />
    <hkern u1="&#x102;" u2="&#x38;" k="10" />
    <hkern u1="&#x102;" u2="&#x36;" k="11" />
    <hkern u1="&#x102;" u2="&#x34;" k="11" />
    <hkern u1="&#x102;" u2="&#x31;" k="40" />
    <hkern u1="&#x102;" u2="&#x30;" k="11" />
    <hkern u1="&#x102;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x102;" u2="&#x2a;" k="51" />
    <hkern u1="&#x102;" u2="&#x26;" k="10" />
    <hkern u1="&#x102;" u2="&#x20;" k="27" />
    <hkern u1="&#x103;" u2="&#x2122;" k="60" />
    <hkern u1="&#x103;" u2="&#xba;" k="48" />
    <hkern u1="&#x103;" u2="&#xae;" k="19" />
    <hkern u1="&#x103;" u2="&#xaa;" k="47" />
    <hkern u1="&#x103;" u2="&#xa9;" k="19" />
    <hkern u1="&#x103;" u2="\" k="68" />
    <hkern u1="&#x103;" u2="&#x40;" k="15" />
    <hkern u1="&#x103;" u2="&#x3f;" k="18" />
    <hkern u1="&#x103;" u2="&#x39;" k="10" />
    <hkern u1="&#x103;" u2="&#x38;" k="10" />
    <hkern u1="&#x103;" u2="&#x36;" k="11" />
    <hkern u1="&#x103;" u2="&#x34;" k="11" />
    <hkern u1="&#x103;" u2="&#x31;" k="40" />
    <hkern u1="&#x103;" u2="&#x30;" k="11" />
    <hkern u1="&#x103;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x103;" u2="&#x2a;" k="51" />
    <hkern u1="&#x103;" u2="&#x26;" k="10" />
    <hkern u1="&#x103;" u2="&#x20;" k="27" />
    <hkern u1="&#x104;" u2="&#x2122;" k="60" />
    <hkern u1="&#x104;" u2="&#x201e;" k="-59" />
    <hkern u1="&#x104;" u2="&#x201a;" k="-58" />
    <hkern u1="&#x104;" u2="&#x12f;" k="-30" />
    <hkern u1="&#x104;" u2="&#x12e;" k="-30" />
    <hkern u1="&#x104;" u2="&#xba;" k="48" />
    <hkern u1="&#x104;" u2="&#xae;" k="19" />
    <hkern u1="&#x104;" u2="&#xaa;" k="47" />
    <hkern u1="&#x104;" u2="&#xa9;" k="19" />
    <hkern u1="&#x104;" u2="&#x7d;" k="-13" />
    <hkern u1="&#x104;" u2="&#x7c;" k="-9" />
    <hkern u1="&#x104;" u2="_" k="-13" />
    <hkern u1="&#x104;" u2="]" k="-11" />
    <hkern u1="&#x104;" u2="\" k="68" />
    <hkern u1="&#x104;" u2="&#x40;" k="15" />
    <hkern u1="&#x104;" u2="&#x3f;" k="18" />
    <hkern u1="&#x104;" u2="&#x3b;" k="-47" />
    <hkern u1="&#x104;" u2="&#x39;" k="10" />
    <hkern u1="&#x104;" u2="&#x38;" k="10" />
    <hkern u1="&#x104;" u2="&#x36;" k="11" />
    <hkern u1="&#x104;" u2="&#x34;" k="11" />
    <hkern u1="&#x104;" u2="&#x31;" k="40" />
    <hkern u1="&#x104;" u2="&#x30;" k="11" />
    <hkern u1="&#x104;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-82" />
    <hkern u1="&#x104;" u2="&#x2a;" k="51" />
    <hkern u1="&#x104;" u2="&#x26;" k="10" />
    <hkern u1="&#x104;" u2="&#x20;" k="27" />
    <hkern u1="&#x105;" u2="&#x2122;" k="60" />
    <hkern u1="&#x105;" u2="&#x201e;" k="-59" />
    <hkern u1="&#x105;" u2="&#x201a;" k="-58" />
    <hkern u1="&#x105;" u2="&#x12f;" k="-30" />
    <hkern u1="&#x105;" u2="&#xba;" k="48" />
    <hkern u1="&#x105;" u2="&#xae;" k="19" />
    <hkern u1="&#x105;" u2="&#xaa;" k="47" />
    <hkern u1="&#x105;" u2="&#xa9;" k="19" />
    <hkern u1="&#x105;" u2="&#x7d;" k="-13" />
    <hkern u1="&#x105;" u2="&#x7c;" k="-9" />
    <hkern u1="&#x105;" u2="_" k="-13" />
    <hkern u1="&#x105;" u2="]" k="-11" />
    <hkern u1="&#x105;" u2="\" k="68" />
    <hkern u1="&#x105;" u2="&#x40;" k="15" />
    <hkern u1="&#x105;" u2="&#x3f;" k="18" />
    <hkern u1="&#x105;" u2="&#x3b;" k="-47" />
    <hkern u1="&#x105;" u2="&#x39;" k="10" />
    <hkern u1="&#x105;" u2="&#x38;" k="10" />
    <hkern u1="&#x105;" u2="&#x36;" k="11" />
    <hkern u1="&#x105;" u2="&#x34;" k="11" />
    <hkern u1="&#x105;" u2="&#x31;" k="40" />
    <hkern u1="&#x105;" u2="&#x30;" k="11" />
    <hkern u1="&#x105;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x105;" u2="&#x2c;" k="-82" />
    <hkern u1="&#x105;" u2="&#x2a;" k="51" />
    <hkern u1="&#x105;" u2="&#x26;" k="10" />
    <hkern u1="&#x105;" u2="&#x20;" k="27" />
    <hkern u1="&#x10e;" u2="_" k="30" />
    <hkern u1="&#x10e;" u2="\" k="12" />
    <hkern u1="&#x10e;" u2="&#x2f;" k="12" />
    <hkern u1="&#x10e;" u2="&#x29;" k="19" />
    <hkern u1="&#x10f;" u2="_" k="30" />
    <hkern u1="&#x10f;" u2="\" k="12" />
    <hkern u1="&#x10f;" u2="&#x2f;" k="12" />
    <hkern u1="&#x10f;" u2="&#x29;" k="19" />
    <hkern u1="&#x110;" u2="_" k="30" />
    <hkern u1="&#x110;" u2="\" k="12" />
    <hkern u1="&#x110;" u2="&#x2f;" k="12" />
    <hkern u1="&#x110;" u2="&#x29;" k="19" />
    <hkern u1="&#x111;" u2="_" k="30" />
    <hkern u1="&#x111;" u2="\" k="12" />
    <hkern u1="&#x111;" u2="&#x2f;" k="12" />
    <hkern u1="&#x111;" u2="&#x29;" k="19" />
    <hkern u1="&#x118;" u2="&#x201e;" k="-12" />
    <hkern u1="&#x118;" u2="&#x201a;" k="-11" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-35" />
    <hkern u1="&#x119;" u2="&#x201e;" k="-12" />
    <hkern u1="&#x119;" u2="&#x201a;" k="-11" />
    <hkern u1="&#x119;" u2="&#x2c;" k="-35" />
    <hkern u1="&#x11e;" u2="_" k="18" />
    <hkern u1="&#x11e;" u2="\" k="14" />
    <hkern u1="&#x11e;" u2="&#x29;" k="17" />
    <hkern u1="&#x11f;" u2="_" k="18" />
    <hkern u1="&#x11f;" u2="\" k="14" />
    <hkern u1="&#x11f;" u2="&#x29;" k="17" />
    <hkern u1="&#x120;" u2="_" k="18" />
    <hkern u1="&#x120;" u2="\" k="14" />
    <hkern u1="&#x120;" u2="&#x29;" k="17" />
    <hkern u1="&#x121;" u2="_" k="18" />
    <hkern u1="&#x121;" u2="\" k="14" />
    <hkern u1="&#x121;" u2="&#x29;" k="17" />
    <hkern u1="&#x122;" u2="_" k="18" />
    <hkern u1="&#x122;" u2="\" k="14" />
    <hkern u1="&#x122;" u2="&#x29;" k="17" />
    <hkern u1="&#x123;" u2="_" k="18" />
    <hkern u1="&#x123;" u2="\" k="14" />
    <hkern u1="&#x123;" u2="&#x29;" k="17" />
    <hkern u1="&#x126;" u2="&#x2122;" k="-7" />
    <hkern u1="&#x126;" u2="&#x20;" k="10" />
    <hkern u1="&#x127;" u2="&#x2122;" k="-7" />
    <hkern u1="&#x127;" u2="&#x20;" k="10" />
    <hkern u1="&#x12e;" u2="&#x2c;" k="-15" />
    <hkern u1="&#x12f;" u2="&#x2c;" k="-15" />
    <hkern u1="&#x132;" u2="_" k="38" />
    <hkern u1="&#x132;" u2="&#x2f;" k="17" />
    <hkern u1="&#x133;" u2="_" k="38" />
    <hkern u1="&#x133;" u2="&#x2f;" k="17" />
    <hkern u1="&#x136;" u2="&#x2122;" k="-27" />
    <hkern u1="&#x136;" u2="&#xae;" k="35" />
    <hkern u1="&#x136;" u2="&#xa9;" k="35" />
    <hkern u1="&#x136;" u2="\" k="-14" />
    <hkern u1="&#x136;" u2="&#x40;" k="30" />
    <hkern u1="&#x136;" u2="&#x37;" k="-11" />
    <hkern u1="&#x136;" u2="&#x36;" k="10" />
    <hkern u1="&#x136;" u2="&#x34;" k="35" />
    <hkern u1="&#x136;" u2="&#x31;" k="21" />
    <hkern u1="&#x136;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x136;" u2="&#x29;" k="-19" />
    <hkern u1="&#x136;" u2="&#x26;" k="11" />
    <hkern u1="&#x136;" u2="&#x20;" k="14" />
    <hkern u1="&#x137;" u2="&#x2122;" k="-27" />
    <hkern u1="&#x137;" u2="&#xae;" k="35" />
    <hkern u1="&#x137;" u2="&#xa9;" k="35" />
    <hkern u1="&#x137;" u2="\" k="-14" />
    <hkern u1="&#x137;" u2="&#x40;" k="30" />
    <hkern u1="&#x137;" u2="&#x37;" k="-11" />
    <hkern u1="&#x137;" u2="&#x36;" k="10" />
    <hkern u1="&#x137;" u2="&#x34;" k="35" />
    <hkern u1="&#x137;" u2="&#x31;" k="21" />
    <hkern u1="&#x137;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x137;" u2="&#x29;" k="-19" />
    <hkern u1="&#x137;" u2="&#x26;" k="11" />
    <hkern u1="&#x137;" u2="&#x20;" k="14" />
    <hkern u1="&#x139;" u2="&#x2122;" k="96" />
    <hkern u1="&#x139;" u2="&#xba;" k="100" />
    <hkern u1="&#x139;" u2="&#xb7;" k="102" />
    <hkern u1="&#x139;" u2="&#xaa;" k="99" />
    <hkern u1="&#x139;" u2="\" k="72" />
    <hkern u1="&#x139;" u2="&#x3f;" k="18" />
    <hkern u1="&#x139;" u2="&#x31;" k="45" />
    <hkern u1="&#x139;" u2="&#x2a;" k="105" />
    <hkern u1="&#x139;" u2="&#x20;" k="21" />
    <hkern u1="&#x13a;" u2="&#x2122;" k="96" />
    <hkern u1="&#x13a;" u2="&#xba;" k="100" />
    <hkern u1="&#x13a;" u2="&#xb7;" k="102" />
    <hkern u1="&#x13a;" u2="&#xaa;" k="99" />
    <hkern u1="&#x13a;" u2="\" k="72" />
    <hkern u1="&#x13a;" u2="&#x3f;" k="18" />
    <hkern u1="&#x13a;" u2="&#x31;" k="45" />
    <hkern u1="&#x13a;" u2="&#x2a;" k="105" />
    <hkern u1="&#x13a;" u2="&#x20;" k="21" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="96" />
    <hkern u1="&#x13b;" u2="&#xba;" k="100" />
    <hkern u1="&#x13b;" u2="&#xb7;" k="102" />
    <hkern u1="&#x13b;" u2="&#xaa;" k="99" />
    <hkern u1="&#x13b;" u2="\" k="72" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="18" />
    <hkern u1="&#x13b;" u2="&#x31;" k="45" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="105" />
    <hkern u1="&#x13b;" u2="&#x20;" k="21" />
    <hkern u1="&#x13c;" u2="&#x2122;" k="96" />
    <hkern u1="&#x13c;" u2="&#xba;" k="100" />
    <hkern u1="&#x13c;" u2="&#xb7;" k="102" />
    <hkern u1="&#x13c;" u2="&#xaa;" k="99" />
    <hkern u1="&#x13c;" u2="\" k="72" />
    <hkern u1="&#x13c;" u2="&#x3f;" k="18" />
    <hkern u1="&#x13c;" u2="&#x31;" k="45" />
    <hkern u1="&#x13c;" u2="&#x2a;" k="105" />
    <hkern u1="&#x13c;" u2="&#x20;" k="21" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="70" />
    <hkern u1="&#x13d;" u2="&#x201c;" k="85" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="70" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="85" />
    <hkern u1="&#x13d;" u2="&#x1ef3;" k="-25" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="-25" />
    <hkern u1="&#x13d;" u2="&#x1e85;" k="-17" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="-17" />
    <hkern u1="&#x13d;" u2="&#x1e83;" k="-17" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="-17" />
    <hkern u1="&#x13d;" u2="&#x1e81;" k="-17" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="-17" />
    <hkern u1="&#x13d;" u2="&#x21b;" k="-3" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="-3" />
    <hkern u1="&#x13d;" u2="&#x178;" k="-25" />
    <hkern u1="&#x13d;" u2="&#x177;" k="-25" />
    <hkern u1="&#x13d;" u2="&#x176;" k="-25" />
    <hkern u1="&#x13d;" u2="&#x175;" k="-17" />
    <hkern u1="&#x13d;" u2="&#x174;" k="-17" />
    <hkern u1="&#x13d;" u2="&#x167;" k="-3" />
    <hkern u1="&#x13d;" u2="&#x166;" k="-3" />
    <hkern u1="&#x13d;" u2="&#x165;" k="-3" />
    <hkern u1="&#x13d;" u2="&#x164;" k="-3" />
    <hkern u1="&#x13d;" u2="&#x163;" k="-3" />
    <hkern u1="&#x13d;" u2="&#x162;" k="-3" />
    <hkern u1="&#x13d;" u2="&#xff;" k="-25" />
    <hkern u1="&#x13d;" u2="&#xfd;" k="-25" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x13d;" u2="y" k="-25" />
    <hkern u1="&#x13d;" u2="x" k="-20" />
    <hkern u1="&#x13d;" u2="w" k="-17" />
    <hkern u1="&#x13d;" u2="v" k="-24" />
    <hkern u1="&#x13d;" u2="t" k="-3" />
    <hkern u1="&#x13d;" u2="Y" k="-25" />
    <hkern u1="&#x13d;" u2="X" k="-20" />
    <hkern u1="&#x13d;" u2="W" k="-17" />
    <hkern u1="&#x13d;" u2="V" k="-24" />
    <hkern u1="&#x13d;" u2="T" k="-3" />
    <hkern u1="&#x13d;" u2="&#x27;" k="32" />
    <hkern u1="&#x13d;" u2="&#x22;" k="32" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="-11" />
    <hkern u1="&#x13d;" u2="&#xba;" k="56" />
    <hkern u1="&#x13d;" u2="&#xb7;" k="102" />
    <hkern u1="&#x13d;" u2="&#xaa;" k="50" />
    <hkern u1="&#x13d;" u2="\" k="5" />
    <hkern u1="&#x13d;" u2="&#x3f;" k="18" />
    <hkern u1="&#x13d;" u2="&#x31;" k="45" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="50" />
    <hkern u1="&#x13d;" u2="&#x20;" k="31" />
    <hkern u1="&#x13e;" u2="&#x201d;" k="70" />
    <hkern u1="&#x13e;" u2="&#x201c;" k="85" />
    <hkern u1="&#x13e;" u2="&#x2019;" k="70" />
    <hkern u1="&#x13e;" u2="&#x2018;" k="85" />
    <hkern u1="&#x13e;" u2="&#x27;" k="32" />
    <hkern u1="&#x13e;" u2="&#x22;" k="32" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-11" />
    <hkern u1="&#x13e;" u2="&#x165;" k="-3" />
    <hkern u1="&#x13e;" u2="&#xfd;" k="-25" />
    <hkern u1="&#x13e;" u2="&#xba;" k="56" />
    <hkern u1="&#x13e;" u2="&#xb7;" k="102" />
    <hkern u1="&#x13e;" u2="&#xaa;" k="50" />
    <hkern u1="&#x13e;" u2="y" k="-25" />
    <hkern u1="&#x13e;" u2="x" k="-20" />
    <hkern u1="&#x13e;" u2="w" k="-17" />
    <hkern u1="&#x13e;" u2="v" k="-24" />
    <hkern u1="&#x13e;" u2="t" k="-3" />
    <hkern u1="&#x13e;" u2="\" k="5" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="18" />
    <hkern u1="&#x13e;" u2="&#x31;" k="45" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="50" />
    <hkern u1="&#x13e;" u2="&#x20;" k="31" />
    <hkern u1="&#x141;" u2="&#x2122;" k="96" />
    <hkern u1="&#x141;" u2="&#xba;" k="100" />
    <hkern u1="&#x141;" u2="&#xb7;" k="102" />
    <hkern u1="&#x141;" u2="&#xaa;" k="99" />
    <hkern u1="&#x141;" u2="\" k="72" />
    <hkern u1="&#x141;" u2="&#x3f;" k="18" />
    <hkern u1="&#x141;" u2="&#x31;" k="45" />
    <hkern u1="&#x141;" u2="&#x2a;" k="105" />
    <hkern u1="&#x141;" u2="&#x20;" k="21" />
    <hkern u1="&#x142;" u2="&#x2122;" k="96" />
    <hkern u1="&#x142;" u2="&#xba;" k="100" />
    <hkern u1="&#x142;" u2="&#xb7;" k="102" />
    <hkern u1="&#x142;" u2="&#xaa;" k="99" />
    <hkern u1="&#x142;" u2="\" k="72" />
    <hkern u1="&#x142;" u2="&#x3f;" k="18" />
    <hkern u1="&#x142;" u2="&#x31;" k="45" />
    <hkern u1="&#x142;" u2="&#x2a;" k="105" />
    <hkern u1="&#x142;" u2="&#x20;" k="21" />
    <hkern u1="&#x14c;" u2="_" k="29" />
    <hkern u1="&#x14c;" u2="\" k="12" />
    <hkern u1="&#x14c;" u2="&#x2f;" k="12" />
    <hkern u1="&#x14c;" u2="&#x29;" k="19" />
    <hkern u1="&#x14d;" u2="_" k="29" />
    <hkern u1="&#x14d;" u2="\" k="12" />
    <hkern u1="&#x14d;" u2="&#x2f;" k="12" />
    <hkern u1="&#x14d;" u2="&#x29;" k="19" />
    <hkern u1="&#x150;" u2="_" k="29" />
    <hkern u1="&#x150;" u2="\" k="12" />
    <hkern u1="&#x150;" u2="&#x2f;" k="12" />
    <hkern u1="&#x150;" u2="&#x29;" k="19" />
    <hkern u1="&#x151;" u2="_" k="29" />
    <hkern u1="&#x151;" u2="\" k="12" />
    <hkern u1="&#x151;" u2="&#x2f;" k="12" />
    <hkern u1="&#x151;" u2="&#x29;" k="19" />
    <hkern u1="&#x154;" u2="\" k="17" />
    <hkern u1="&#x154;" u2="&#x34;" k="19" />
    <hkern u1="&#x154;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x155;" u2="\" k="17" />
    <hkern u1="&#x155;" u2="&#x34;" k="19" />
    <hkern u1="&#x155;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x156;" u2="\" k="17" />
    <hkern u1="&#x156;" u2="&#x34;" k="19" />
    <hkern u1="&#x156;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x157;" u2="\" k="17" />
    <hkern u1="&#x157;" u2="&#x34;" k="19" />
    <hkern u1="&#x157;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x158;" u2="\" k="17" />
    <hkern u1="&#x158;" u2="&#x34;" k="19" />
    <hkern u1="&#x158;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x159;" u2="\" k="17" />
    <hkern u1="&#x159;" u2="&#x34;" k="19" />
    <hkern u1="&#x159;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x15a;" u2="_" k="12" />
    <hkern u1="&#x15a;" u2="&#x31;" k="11" />
    <hkern u1="&#x15b;" u2="_" k="12" />
    <hkern u1="&#x15b;" u2="&#x31;" k="11" />
    <hkern u1="&#x15e;" u2="_" k="12" />
    <hkern u1="&#x15e;" u2="&#x31;" k="11" />
    <hkern u1="&#x15f;" u2="_" k="12" />
    <hkern u1="&#x15f;" u2="&#x31;" k="11" />
    <hkern u1="&#x160;" u2="_" k="12" />
    <hkern u1="&#x160;" u2="&#x31;" k="11" />
    <hkern u1="&#x161;" u2="_" k="12" />
    <hkern u1="&#x161;" u2="&#x31;" k="11" />
    <hkern u1="&#x162;" u2="&#xdf;" k="7" />
    <hkern u1="&#x162;" u2="&#xae;" k="11" />
    <hkern u1="&#x162;" u2="&#xa9;" k="11" />
    <hkern u1="&#x162;" u2="_" k="61" />
    <hkern u1="&#x162;" u2="&#x40;" k="18" />
    <hkern u1="&#x162;" u2="&#x34;" k="56" />
    <hkern u1="&#x162;" u2="&#x2f;" k="60" />
    <hkern u1="&#x162;" u2="&#x26;" k="11" />
    <hkern u1="&#x162;" u2="&#x20;" k="21" />
    <hkern u1="&#x163;" u2="&#xdf;" k="7" />
    <hkern u1="&#x163;" u2="&#xae;" k="11" />
    <hkern u1="&#x163;" u2="&#xa9;" k="11" />
    <hkern u1="&#x163;" u2="_" k="61" />
    <hkern u1="&#x163;" u2="&#x40;" k="18" />
    <hkern u1="&#x163;" u2="&#x34;" k="56" />
    <hkern u1="&#x163;" u2="&#x2f;" k="60" />
    <hkern u1="&#x163;" u2="&#x26;" k="11" />
    <hkern u1="&#x163;" u2="&#x20;" k="21" />
    <hkern u1="&#x164;" u2="&#xdf;" k="7" />
    <hkern u1="&#x164;" u2="&#xae;" k="11" />
    <hkern u1="&#x164;" u2="&#xa9;" k="11" />
    <hkern u1="&#x164;" u2="_" k="61" />
    <hkern u1="&#x164;" u2="&#x40;" k="18" />
    <hkern u1="&#x164;" u2="&#x34;" k="56" />
    <hkern u1="&#x164;" u2="&#x2f;" k="60" />
    <hkern u1="&#x164;" u2="&#x26;" k="11" />
    <hkern u1="&#x164;" u2="&#x20;" k="21" />
    <hkern u1="&#x165;" u2="&#xdf;" k="7" />
    <hkern u1="&#x165;" u2="&#xae;" k="11" />
    <hkern u1="&#x165;" u2="&#xa9;" k="11" />
    <hkern u1="&#x165;" u2="_" k="61" />
    <hkern u1="&#x165;" u2="&#x40;" k="18" />
    <hkern u1="&#x165;" u2="&#x34;" k="56" />
    <hkern u1="&#x165;" u2="&#x2f;" k="60" />
    <hkern u1="&#x165;" u2="&#x26;" k="11" />
    <hkern u1="&#x165;" u2="&#x20;" k="21" />
    <hkern u1="&#x166;" u2="&#x2039;" k="50" />
    <hkern u1="&#x166;" u2="&#x2014;" k="46" />
    <hkern u1="&#x166;" u2="&#x2013;" k="46" />
    <hkern u1="&#x166;" u2="&#xad;" k="46" />
    <hkern u1="&#x166;" u2="&#xab;" k="50" />
    <hkern u1="&#x166;" u2="j" k="77" />
    <hkern u1="&#x166;" u2="J" k="77" />
    <hkern u1="&#x166;" u2="&#x2d;" k="46" />
    <hkern u1="&#x166;" u2="&#xdf;" k="7" />
    <hkern u1="&#x166;" u2="&#xae;" k="11" />
    <hkern u1="&#x166;" u2="&#xa9;" k="11" />
    <hkern u1="&#x166;" u2="_" k="61" />
    <hkern u1="&#x166;" u2="&#x40;" k="18" />
    <hkern u1="&#x166;" u2="&#x34;" k="56" />
    <hkern u1="&#x166;" u2="&#x2f;" k="60" />
    <hkern u1="&#x166;" u2="&#x26;" k="11" />
    <hkern u1="&#x166;" u2="&#x20;" k="21" />
    <hkern u1="&#x167;" u2="&#x2039;" k="50" />
    <hkern u1="&#x167;" u2="&#x2014;" k="46" />
    <hkern u1="&#x167;" u2="&#x2013;" k="46" />
    <hkern u1="&#x167;" u2="&#xad;" k="46" />
    <hkern u1="&#x167;" u2="&#xab;" k="50" />
    <hkern u1="&#x167;" u2="&#x2d;" k="46" />
    <hkern u1="&#x167;" u2="&#xdf;" k="7" />
    <hkern u1="&#x167;" u2="&#xae;" k="11" />
    <hkern u1="&#x167;" u2="&#xa9;" k="11" />
    <hkern u1="&#x167;" u2="j" k="77" />
    <hkern u1="&#x167;" u2="_" k="61" />
    <hkern u1="&#x167;" u2="&#x40;" k="18" />
    <hkern u1="&#x167;" u2="&#x34;" k="56" />
    <hkern u1="&#x167;" u2="&#x2f;" k="60" />
    <hkern u1="&#x167;" u2="&#x26;" k="11" />
    <hkern u1="&#x167;" u2="&#x20;" k="21" />
    <hkern u1="&#x16a;" u2="_" k="39" />
    <hkern u1="&#x16a;" u2="&#x2f;" k="17" />
    <hkern u1="&#x16b;" u2="_" k="39" />
    <hkern u1="&#x16b;" u2="&#x2f;" k="17" />
    <hkern u1="&#x16e;" u2="_" k="39" />
    <hkern u1="&#x16e;" u2="&#x2f;" k="17" />
    <hkern u1="&#x16f;" u2="_" k="39" />
    <hkern u1="&#x16f;" u2="&#x2f;" k="17" />
    <hkern u1="&#x170;" u2="_" k="39" />
    <hkern u1="&#x170;" u2="&#x2f;" k="17" />
    <hkern u1="&#x171;" u2="_" k="39" />
    <hkern u1="&#x171;" u2="&#x2f;" k="17" />
    <hkern u1="&#x172;" u2="_" k="39" />
    <hkern u1="&#x172;" u2="&#x2f;" k="17" />
    <hkern u1="&#x173;" u2="_" k="39" />
    <hkern u1="&#x173;" u2="&#x2f;" k="17" />
    <hkern u1="&#x174;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x174;" u2="&#xdf;" k="10" />
    <hkern u1="&#x174;" u2="&#xae;" k="15" />
    <hkern u1="&#x174;" u2="&#xa9;" k="15" />
    <hkern u1="&#x174;" u2="_" k="60" />
    <hkern u1="&#x174;" u2="&#x40;" k="18" />
    <hkern u1="&#x174;" u2="&#x38;" k="12" />
    <hkern u1="&#x174;" u2="&#x34;" k="29" />
    <hkern u1="&#x174;" u2="&#x2f;" k="56" />
    <hkern u1="&#x174;" u2="&#x29;" k="-8" />
    <hkern u1="&#x174;" u2="&#x26;" k="18" />
    <hkern u1="&#x174;" u2="&#x20;" k="25" />
    <hkern u1="&#x175;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x175;" u2="&#xdf;" k="10" />
    <hkern u1="&#x175;" u2="&#xae;" k="15" />
    <hkern u1="&#x175;" u2="&#xa9;" k="15" />
    <hkern u1="&#x175;" u2="_" k="60" />
    <hkern u1="&#x175;" u2="&#x40;" k="18" />
    <hkern u1="&#x175;" u2="&#x38;" k="12" />
    <hkern u1="&#x175;" u2="&#x34;" k="29" />
    <hkern u1="&#x175;" u2="&#x2f;" k="56" />
    <hkern u1="&#x175;" u2="&#x29;" k="-8" />
    <hkern u1="&#x175;" u2="&#x26;" k="18" />
    <hkern u1="&#x175;" u2="&#x20;" k="25" />
    <hkern u1="&#x176;" u2="&#x2122;" k="-25" />
    <hkern u1="&#x176;" u2="&#xdf;" k="36" />
    <hkern u1="&#x176;" u2="&#xae;" k="42" />
    <hkern u1="&#x176;" u2="&#xa9;" k="42" />
    <hkern u1="&#x176;" u2="_" k="79" />
    <hkern u1="&#x176;" u2="\" k="-13" />
    <hkern u1="&#x176;" u2="&#x40;" k="47" />
    <hkern u1="&#x176;" u2="&#x39;" k="15" />
    <hkern u1="&#x176;" u2="&#x38;" k="26" />
    <hkern u1="&#x176;" u2="&#x37;" k="-10" />
    <hkern u1="&#x176;" u2="&#x36;" k="20" />
    <hkern u1="&#x176;" u2="&#x34;" k="75" />
    <hkern u1="&#x176;" u2="&#x32;" k="15" />
    <hkern u1="&#x176;" u2="&#x31;" k="16" />
    <hkern u1="&#x176;" u2="&#x30;" k="19" />
    <hkern u1="&#x176;" u2="&#x2f;" k="79" />
    <hkern u1="&#x176;" u2="&#x29;" k="-19" />
    <hkern u1="&#x176;" u2="&#x26;" k="36" />
    <hkern u1="&#x176;" u2="&#x20;" k="34" />
    <hkern u1="&#x177;" u2="&#x2122;" k="-25" />
    <hkern u1="&#x177;" u2="&#xdf;" k="36" />
    <hkern u1="&#x177;" u2="&#xae;" k="42" />
    <hkern u1="&#x177;" u2="&#xa9;" k="42" />
    <hkern u1="&#x177;" u2="_" k="79" />
    <hkern u1="&#x177;" u2="\" k="-13" />
    <hkern u1="&#x177;" u2="&#x40;" k="47" />
    <hkern u1="&#x177;" u2="&#x39;" k="15" />
    <hkern u1="&#x177;" u2="&#x38;" k="26" />
    <hkern u1="&#x177;" u2="&#x37;" k="-10" />
    <hkern u1="&#x177;" u2="&#x36;" k="20" />
    <hkern u1="&#x177;" u2="&#x34;" k="75" />
    <hkern u1="&#x177;" u2="&#x32;" k="15" />
    <hkern u1="&#x177;" u2="&#x31;" k="16" />
    <hkern u1="&#x177;" u2="&#x30;" k="19" />
    <hkern u1="&#x177;" u2="&#x2f;" k="79" />
    <hkern u1="&#x177;" u2="&#x29;" k="-19" />
    <hkern u1="&#x177;" u2="&#x26;" k="36" />
    <hkern u1="&#x177;" u2="&#x20;" k="34" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-25" />
    <hkern u1="&#x178;" u2="&#xdf;" k="36" />
    <hkern u1="&#x178;" u2="&#xae;" k="42" />
    <hkern u1="&#x178;" u2="&#xa9;" k="42" />
    <hkern u1="&#x178;" u2="_" k="79" />
    <hkern u1="&#x178;" u2="\" k="-13" />
    <hkern u1="&#x178;" u2="&#x40;" k="47" />
    <hkern u1="&#x178;" u2="&#x39;" k="15" />
    <hkern u1="&#x178;" u2="&#x38;" k="26" />
    <hkern u1="&#x178;" u2="&#x37;" k="-10" />
    <hkern u1="&#x178;" u2="&#x36;" k="20" />
    <hkern u1="&#x178;" u2="&#x34;" k="75" />
    <hkern u1="&#x178;" u2="&#x32;" k="15" />
    <hkern u1="&#x178;" u2="&#x31;" k="16" />
    <hkern u1="&#x178;" u2="&#x30;" k="19" />
    <hkern u1="&#x178;" u2="&#x2f;" k="79" />
    <hkern u1="&#x178;" u2="&#x29;" k="-19" />
    <hkern u1="&#x178;" u2="&#x26;" k="36" />
    <hkern u1="&#x178;" u2="&#x20;" k="34" />
    <hkern u1="&#x179;" u2="&#xae;" k="12" />
    <hkern u1="&#x179;" u2="&#xa9;" k="12" />
    <hkern u1="&#x17a;" u2="&#xae;" k="12" />
    <hkern u1="&#x17a;" u2="&#xa9;" k="12" />
    <hkern u1="&#x17b;" u2="&#xae;" k="12" />
    <hkern u1="&#x17b;" u2="&#xa9;" k="12" />
    <hkern u1="&#x17c;" u2="&#xae;" k="12" />
    <hkern u1="&#x17c;" u2="&#xa9;" k="12" />
    <hkern u1="&#x17d;" u2="&#xae;" k="12" />
    <hkern u1="&#x17d;" u2="&#xa9;" k="12" />
    <hkern u1="&#x17e;" u2="&#xae;" k="12" />
    <hkern u1="&#x17e;" u2="&#xa9;" k="12" />
    <hkern u1="&#x218;" u2="_" k="12" />
    <hkern u1="&#x218;" u2="&#x31;" k="11" />
    <hkern u1="&#x219;" u2="_" k="12" />
    <hkern u1="&#x219;" u2="&#x31;" k="11" />
    <hkern u1="&#x21a;" u2="&#xdf;" k="7" />
    <hkern u1="&#x21a;" u2="&#xae;" k="11" />
    <hkern u1="&#x21a;" u2="&#xa9;" k="11" />
    <hkern u1="&#x21a;" u2="_" k="61" />
    <hkern u1="&#x21a;" u2="&#x40;" k="18" />
    <hkern u1="&#x21a;" u2="&#x34;" k="56" />
    <hkern u1="&#x21a;" u2="&#x2f;" k="60" />
    <hkern u1="&#x21a;" u2="&#x26;" k="11" />
    <hkern u1="&#x21a;" u2="&#x20;" k="21" />
    <hkern u1="&#x21b;" u2="&#xdf;" k="7" />
    <hkern u1="&#x21b;" u2="&#xae;" k="11" />
    <hkern u1="&#x21b;" u2="&#xa9;" k="11" />
    <hkern u1="&#x21b;" u2="_" k="61" />
    <hkern u1="&#x21b;" u2="&#x40;" k="18" />
    <hkern u1="&#x21b;" u2="&#x34;" k="56" />
    <hkern u1="&#x21b;" u2="&#x2f;" k="60" />
    <hkern u1="&#x21b;" u2="&#x26;" k="11" />
    <hkern u1="&#x21b;" u2="&#x20;" k="21" />
    <hkern u1="&#x3a9;" u2="&#x201d;" k="8" />
    <hkern u1="&#x3a9;" u2="&#x2019;" k="8" />
    <hkern u1="&#x3a9;" u2="\" k="26" />
    <hkern u1="&#x3bc;" u2="\" k="30" />
    <hkern u1="&#x3c0;" u2="\" k="23" />
    <hkern u1="&#x410;" u2="\" k="68" />
    <hkern u1="&#x410;" u2="&#x3f;" k="18" />
    <hkern u1="&#x410;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x410;" u2="&#x2a;" k="51" />
    <hkern u1="&#x412;" u2="&#x414;" k="7" />
    <hkern u1="&#x412;" u2="\" k="17" />
    <hkern u1="&#x412;" u2="&#x29;" k="17" />
    <hkern u1="&#x413;" u2="&#x41b;" k="87" />
    <hkern u1="&#x413;" u2="&#x414;" k="84" />
    <hkern u1="&#x413;" u2="&#x2f;" k="70" />
    <hkern u1="&#x414;" u2="&#x201e;" k="-10" />
    <hkern u1="&#x414;" u2="&#x201a;" k="-9" />
    <hkern u1="&#x414;" u2="\" k="19" />
    <hkern u1="&#x414;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x414;" u2="&#x2a;" k="21" />
    <hkern u1="&#x416;" u2="\" k="-12" />
    <hkern u1="&#x416;" u2="&#x2f;" k="-6" />
    <hkern u1="&#x416;" u2="&#x29;" k="-17" />
    <hkern u1="&#x417;" u2="&#x414;" k="7" />
    <hkern u1="&#x417;" u2="\" k="11" />
    <hkern u1="&#x417;" u2="&#x29;" k="15" />
    <hkern u1="&#x41a;" u2="\" k="-14" />
    <hkern u1="&#x41a;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x41a;" u2="&#x29;" k="-19" />
    <hkern u1="&#x41e;" u2="&#x41b;" k="9" />
    <hkern u1="&#x41e;" u2="&#x414;" k="12" />
    <hkern u1="&#x41e;" u2="\" k="12" />
    <hkern u1="&#x41e;" u2="&#x2f;" k="12" />
    <hkern u1="&#x41e;" u2="&#x29;" k="19" />
    <hkern u1="&#x420;" u2="&#x41b;" k="75" />
    <hkern u1="&#x420;" u2="&#x414;" k="74" />
    <hkern u1="&#x420;" u2="&#x2f;" k="49" />
    <hkern u1="&#x420;" u2="&#x29;" k="24" />
    <hkern u1="&#x422;" u2="&#x41b;" k="75" />
    <hkern u1="&#x422;" u2="&#x414;" k="75" />
    <hkern u1="&#x422;" u2="&#x2f;" k="60" />
    <hkern u1="&#x423;" u2="&#x41b;" k="80" />
    <hkern u1="&#x423;" u2="&#x414;" k="85" />
    <hkern u1="&#x423;" u2="\" k="-16" />
    <hkern u1="&#x423;" u2="&#x2f;" k="76" />
    <hkern u1="&#x423;" u2="&#x29;" k="-21" />
    <hkern u1="&#x424;" u2="&#x41b;" k="16" />
    <hkern u1="&#x424;" u2="&#x414;" k="19" />
    <hkern u1="&#x424;" u2="\" k="18" />
    <hkern u1="&#x424;" u2="&#x2f;" k="19" />
    <hkern u1="&#x424;" u2="&#x29;" k="24" />
    <hkern u1="&#x425;" u2="\" k="-5" />
    <hkern u1="&#x425;" u2="&#x29;" k="-10" />
    <hkern u1="&#x426;" u2="&#x201e;" k="-10" />
    <hkern u1="&#x426;" u2="&#x201a;" k="-9" />
    <hkern u1="&#x426;" u2="\" k="19" />
    <hkern u1="&#x426;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x426;" u2="&#x2a;" k="21" />
    <hkern u1="&#x429;" u2="&#x201e;" k="-10" />
    <hkern u1="&#x429;" u2="&#x201a;" k="-9" />
    <hkern u1="&#x429;" u2="\" k="19" />
    <hkern u1="&#x429;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x429;" u2="&#x2a;" k="21" />
    <hkern u1="&#x42a;" u2="&#x7d;" k="17" />
    <hkern u1="&#x42a;" u2="]" k="18" />
    <hkern u1="&#x42a;" u2="\" k="51" />
    <hkern u1="&#x42a;" u2="&#x3f;" k="27" />
    <hkern u1="&#x42a;" u2="&#x2a;" k="17" />
    <hkern u1="&#x42a;" u2="&#x29;" k="28" />
    <hkern u1="&#x42c;" u2="&#x7d;" k="17" />
    <hkern u1="&#x42c;" u2="]" k="18" />
    <hkern u1="&#x42c;" u2="\" k="51" />
    <hkern u1="&#x42c;" u2="&#x3f;" k="27" />
    <hkern u1="&#x42c;" u2="&#x2a;" k="17" />
    <hkern u1="&#x42c;" u2="&#x29;" k="28" />
    <hkern u1="&#x42d;" u2="&#x41b;" k="9" />
    <hkern u1="&#x42d;" u2="&#x414;" k="12" />
    <hkern u1="&#x42d;" u2="\" k="12" />
    <hkern u1="&#x42d;" u2="&#x2f;" k="12" />
    <hkern u1="&#x42d;" u2="&#x29;" k="19" />
    <hkern u1="&#x42e;" u2="&#x41b;" k="9" />
    <hkern u1="&#x42e;" u2="&#x414;" k="12" />
    <hkern u1="&#x42e;" u2="\" k="12" />
    <hkern u1="&#x42e;" u2="&#x2f;" k="12" />
    <hkern u1="&#x42e;" u2="&#x29;" k="19" />
    <hkern u1="&#x430;" u2="\" k="68" />
    <hkern u1="&#x430;" u2="&#x3f;" k="18" />
    <hkern u1="&#x430;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x430;" u2="&#x2a;" k="51" />
    <hkern u1="&#x432;" u2="&#x414;" k="7" />
    <hkern u1="&#x432;" u2="\" k="17" />
    <hkern u1="&#x432;" u2="&#x29;" k="17" />
    <hkern u1="&#x433;" u2="&#x41b;" k="87" />
    <hkern u1="&#x433;" u2="&#x414;" k="84" />
    <hkern u1="&#x433;" u2="&#x2f;" k="70" />
    <hkern u1="&#x434;" g2="uni0442.loclBGR" k="81" />
    <hkern u1="&#x434;" g2="uni0436.loclBGR" k="-5" />
    <hkern u1="&#x434;" u2="&#x201e;" k="-14" />
    <hkern u1="&#x434;" u2="&#x201a;" k="-13" />
    <hkern u1="&#x434;" u2="&#x44f;" k="-28" />
    <hkern u1="&#x434;" u2="&#x44a;" k="66" />
    <hkern u1="&#x434;" u2="&#x447;" k="80" />
    <hkern u1="&#x434;" u2="&#x444;" k="21" />
    <hkern u1="&#x434;" u2="&#x443;" k="51" />
    <hkern u1="&#x434;" u2="&#x442;" k="81" />
    <hkern u1="&#x434;" u2="&#x441;" k="19" />
    <hkern u1="&#x434;" u2="&#x43e;" k="19" />
    <hkern u1="&#x434;" u2="&#x436;" k="-5" />
    <hkern u1="&#x434;" u2="\" k="72" />
    <hkern u1="&#x434;" u2="&#x3f;" k="20" />
    <hkern u1="&#x434;" u2="&#x2c;" k="-24" />
    <hkern u1="&#x434;" u2="&#x2a;" k="63" />
    <hkern u1="&#x436;" u2="\" k="-12" />
    <hkern u1="&#x436;" u2="&#x2f;" k="-6" />
    <hkern u1="&#x436;" u2="&#x29;" k="-17" />
    <hkern u1="&#x437;" u2="&#x414;" k="7" />
    <hkern u1="&#x437;" u2="\" k="11" />
    <hkern u1="&#x437;" u2="&#x29;" k="15" />
    <hkern u1="&#x43a;" u2="\" k="-14" />
    <hkern u1="&#x43a;" u2="&#x2f;" k="-8" />
    <hkern u1="&#x43a;" u2="&#x29;" k="-19" />
    <hkern u1="&#x43b;" u2="\" k="68" />
    <hkern u1="&#x43b;" u2="&#x3f;" k="18" />
    <hkern u1="&#x43b;" u2="&#x2f;" k="-7" />
    <hkern u1="&#x43b;" u2="&#x2a;" k="51" />
    <hkern u1="&#x43e;" u2="&#x41b;" k="9" />
    <hkern u1="&#x43e;" u2="&#x414;" k="12" />
    <hkern u1="&#x43e;" u2="\" k="12" />
    <hkern u1="&#x43e;" u2="&#x2f;" k="12" />
    <hkern u1="&#x43e;" u2="&#x29;" k="19" />
    <hkern u1="&#x440;" u2="&#x41b;" k="75" />
    <hkern u1="&#x440;" u2="&#x414;" k="74" />
    <hkern u1="&#x440;" u2="&#x2f;" k="49" />
    <hkern u1="&#x440;" u2="&#x29;" k="24" />
    <hkern u1="&#x442;" u2="&#x41b;" k="75" />
    <hkern u1="&#x442;" u2="&#x414;" k="75" />
    <hkern u1="&#x442;" u2="&#x2f;" k="60" />
    <hkern u1="&#x443;" u2="&#x41b;" k="80" />
    <hkern u1="&#x443;" u2="&#x414;" k="85" />
    <hkern u1="&#x443;" u2="\" k="-16" />
    <hkern u1="&#x443;" u2="&#x2f;" k="76" />
    <hkern u1="&#x443;" u2="&#x29;" k="-21" />
    <hkern u1="&#x444;" u2="&#x41b;" k="16" />
    <hkern u1="&#x444;" u2="&#x414;" k="19" />
    <hkern u1="&#x444;" u2="\" k="18" />
    <hkern u1="&#x444;" u2="&#x2f;" k="19" />
    <hkern u1="&#x444;" u2="&#x29;" k="24" />
    <hkern u1="&#x445;" u2="\" k="-5" />
    <hkern u1="&#x445;" u2="&#x29;" k="-10" />
    <hkern u1="&#x446;" u2="&#x201e;" k="-10" />
    <hkern u1="&#x446;" u2="&#x201a;" k="-9" />
    <hkern u1="&#x446;" u2="\" k="19" />
    <hkern u1="&#x446;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x446;" u2="&#x2a;" k="21" />
    <hkern u1="&#x449;" u2="&#x201e;" k="-10" />
    <hkern u1="&#x449;" u2="&#x201a;" k="-9" />
    <hkern u1="&#x449;" u2="\" k="19" />
    <hkern u1="&#x449;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x449;" u2="&#x2a;" k="21" />
    <hkern u1="&#x44a;" u2="&#x7d;" k="17" />
    <hkern u1="&#x44a;" u2="]" k="18" />
    <hkern u1="&#x44a;" u2="\" k="51" />
    <hkern u1="&#x44a;" u2="&#x3f;" k="27" />
    <hkern u1="&#x44a;" u2="&#x2a;" k="17" />
    <hkern u1="&#x44a;" u2="&#x29;" k="28" />
    <hkern u1="&#x44c;" u2="&#x7d;" k="17" />
    <hkern u1="&#x44c;" u2="]" k="18" />
    <hkern u1="&#x44c;" u2="\" k="51" />
    <hkern u1="&#x44c;" u2="&#x3f;" k="27" />
    <hkern u1="&#x44c;" u2="&#x2a;" k="17" />
    <hkern u1="&#x44c;" u2="&#x29;" k="28" />
    <hkern u1="&#x44d;" u2="&#x41b;" k="9" />
    <hkern u1="&#x44d;" u2="&#x414;" k="12" />
    <hkern u1="&#x44d;" u2="\" k="12" />
    <hkern u1="&#x44d;" u2="&#x2f;" k="12" />
    <hkern u1="&#x44d;" u2="&#x29;" k="19" />
    <hkern u1="&#x44e;" u2="&#x41b;" k="9" />
    <hkern u1="&#x44e;" u2="&#x414;" k="12" />
    <hkern u1="&#x44e;" u2="\" k="12" />
    <hkern u1="&#x44e;" u2="&#x2f;" k="12" />
    <hkern u1="&#x44e;" u2="&#x29;" k="19" />
    <hkern u1="&#x1e80;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x1e80;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1e80;" u2="&#xae;" k="15" />
    <hkern u1="&#x1e80;" u2="&#xa9;" k="15" />
    <hkern u1="&#x1e80;" u2="_" k="60" />
    <hkern u1="&#x1e80;" u2="&#x40;" k="18" />
    <hkern u1="&#x1e80;" u2="&#x38;" k="12" />
    <hkern u1="&#x1e80;" u2="&#x34;" k="29" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="56" />
    <hkern u1="&#x1e80;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e80;" u2="&#x26;" k="18" />
    <hkern u1="&#x1e80;" u2="&#x20;" k="25" />
    <hkern u1="&#x1e81;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x1e81;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1e81;" u2="&#xae;" k="15" />
    <hkern u1="&#x1e81;" u2="&#xa9;" k="15" />
    <hkern u1="&#x1e81;" u2="_" k="60" />
    <hkern u1="&#x1e81;" u2="&#x40;" k="18" />
    <hkern u1="&#x1e81;" u2="&#x38;" k="12" />
    <hkern u1="&#x1e81;" u2="&#x34;" k="29" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="56" />
    <hkern u1="&#x1e81;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e81;" u2="&#x26;" k="18" />
    <hkern u1="&#x1e81;" u2="&#x20;" k="25" />
    <hkern u1="&#x1e82;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x1e82;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1e82;" u2="&#xae;" k="15" />
    <hkern u1="&#x1e82;" u2="&#xa9;" k="15" />
    <hkern u1="&#x1e82;" u2="_" k="60" />
    <hkern u1="&#x1e82;" u2="&#x40;" k="18" />
    <hkern u1="&#x1e82;" u2="&#x38;" k="12" />
    <hkern u1="&#x1e82;" u2="&#x34;" k="29" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="56" />
    <hkern u1="&#x1e82;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e82;" u2="&#x26;" k="18" />
    <hkern u1="&#x1e82;" u2="&#x20;" k="25" />
    <hkern u1="&#x1e83;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x1e83;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1e83;" u2="&#xae;" k="15" />
    <hkern u1="&#x1e83;" u2="&#xa9;" k="15" />
    <hkern u1="&#x1e83;" u2="_" k="60" />
    <hkern u1="&#x1e83;" u2="&#x40;" k="18" />
    <hkern u1="&#x1e83;" u2="&#x38;" k="12" />
    <hkern u1="&#x1e83;" u2="&#x34;" k="29" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="56" />
    <hkern u1="&#x1e83;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e83;" u2="&#x26;" k="18" />
    <hkern u1="&#x1e83;" u2="&#x20;" k="25" />
    <hkern u1="&#x1e84;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x1e84;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1e84;" u2="&#xae;" k="15" />
    <hkern u1="&#x1e84;" u2="&#xa9;" k="15" />
    <hkern u1="&#x1e84;" u2="_" k="60" />
    <hkern u1="&#x1e84;" u2="&#x40;" k="18" />
    <hkern u1="&#x1e84;" u2="&#x38;" k="12" />
    <hkern u1="&#x1e84;" u2="&#x34;" k="29" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="56" />
    <hkern u1="&#x1e84;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e84;" u2="&#x26;" k="18" />
    <hkern u1="&#x1e84;" u2="&#x20;" k="25" />
    <hkern u1="&#x1e85;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x1e85;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1e85;" u2="&#xae;" k="15" />
    <hkern u1="&#x1e85;" u2="&#xa9;" k="15" />
    <hkern u1="&#x1e85;" u2="_" k="60" />
    <hkern u1="&#x1e85;" u2="&#x40;" k="18" />
    <hkern u1="&#x1e85;" u2="&#x38;" k="12" />
    <hkern u1="&#x1e85;" u2="&#x34;" k="29" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="56" />
    <hkern u1="&#x1e85;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e85;" u2="&#x26;" k="18" />
    <hkern u1="&#x1e85;" u2="&#x20;" k="25" />
    <hkern u1="&#x1ef2;" u2="&#x2122;" k="-25" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="36" />
    <hkern u1="&#x1ef2;" u2="&#xae;" k="42" />
    <hkern u1="&#x1ef2;" u2="&#xa9;" k="42" />
    <hkern u1="&#x1ef2;" u2="_" k="79" />
    <hkern u1="&#x1ef2;" u2="\" k="-13" />
    <hkern u1="&#x1ef2;" u2="&#x40;" k="47" />
    <hkern u1="&#x1ef2;" u2="&#x39;" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x38;" k="26" />
    <hkern u1="&#x1ef2;" u2="&#x37;" k="-10" />
    <hkern u1="&#x1ef2;" u2="&#x36;" k="20" />
    <hkern u1="&#x1ef2;" u2="&#x34;" k="75" />
    <hkern u1="&#x1ef2;" u2="&#x32;" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x31;" k="16" />
    <hkern u1="&#x1ef2;" u2="&#x30;" k="19" />
    <hkern u1="&#x1ef2;" u2="&#x2f;" k="79" />
    <hkern u1="&#x1ef2;" u2="&#x29;" k="-19" />
    <hkern u1="&#x1ef2;" u2="&#x26;" k="36" />
    <hkern u1="&#x1ef2;" u2="&#x20;" k="34" />
    <hkern u1="&#x1ef3;" u2="&#x2122;" k="-25" />
    <hkern u1="&#x1ef3;" u2="&#xdf;" k="36" />
    <hkern u1="&#x1ef3;" u2="&#xae;" k="42" />
    <hkern u1="&#x1ef3;" u2="&#xa9;" k="42" />
    <hkern u1="&#x1ef3;" u2="_" k="79" />
    <hkern u1="&#x1ef3;" u2="\" k="-13" />
    <hkern u1="&#x1ef3;" u2="&#x40;" k="47" />
    <hkern u1="&#x1ef3;" u2="&#x39;" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x38;" k="26" />
    <hkern u1="&#x1ef3;" u2="&#x37;" k="-10" />
    <hkern u1="&#x1ef3;" u2="&#x36;" k="20" />
    <hkern u1="&#x1ef3;" u2="&#x34;" k="75" />
    <hkern u1="&#x1ef3;" u2="&#x32;" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x31;" k="16" />
    <hkern u1="&#x1ef3;" u2="&#x30;" k="19" />
    <hkern u1="&#x1ef3;" u2="&#x2f;" k="79" />
    <hkern u1="&#x1ef3;" u2="&#x29;" k="-19" />
    <hkern u1="&#x1ef3;" u2="&#x26;" k="36" />
    <hkern u1="&#x1ef3;" u2="&#x20;" k="34" />
    <hkern u1="&#x2013;" u2="&#x41b;" k="19" />
    <hkern u1="&#x2013;" u2="&#x414;" k="22" />
    <hkern u1="&#x2013;" u2="&#x167;" k="45" />
    <hkern u1="&#x2013;" u2="&#x166;" k="45" />
    <hkern u1="&#x2013;" u2="\" k="40" />
    <hkern u1="&#x2013;" u2="&#x37;" k="25" />
    <hkern u1="&#x2013;" u2="&#x33;" k="11" />
    <hkern u1="&#x2013;" u2="&#x32;" k="14" />
    <hkern u1="&#x2013;" u2="&#x31;" k="15" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="21" />
    <hkern u1="&#x2014;" u2="&#x41b;" k="19" />
    <hkern u1="&#x2014;" u2="&#x414;" k="22" />
    <hkern u1="&#x2014;" u2="&#x167;" k="45" />
    <hkern u1="&#x2014;" u2="&#x166;" k="45" />
    <hkern u1="&#x2014;" u2="\" k="40" />
    <hkern u1="&#x2014;" u2="&#x37;" k="25" />
    <hkern u1="&#x2014;" u2="&#x33;" k="11" />
    <hkern u1="&#x2014;" u2="&#x32;" k="14" />
    <hkern u1="&#x2014;" u2="&#x31;" k="15" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="21" />
    <hkern u1="&#x2018;" u2="&#x2206;" k="48" />
    <hkern u1="&#x2018;" u2="&#x41b;" k="65" />
    <hkern u1="&#x2018;" u2="&#x414;" k="65" />
    <hkern u1="&#x2018;" u2="&#x3c0;" k="13" />
    <hkern u1="&#x2018;" u2="&#x3bc;" k="15" />
    <hkern u1="&#x2018;" u2="&#x3a9;" k="27" />
    <hkern u1="&#x2018;" u2="&#xdf;" k="10" />
    <hkern u1="&#x2019;" u2="&#x41b;" k="65" />
    <hkern u1="&#x2019;" u2="&#x414;" k="65" />
    <hkern u1="&#x2019;" u2="&#xdf;" k="14" />
    <hkern u1="&#x2019;" u2="&#xae;" k="25" />
    <hkern u1="&#x2019;" u2="&#xa9;" k="25" />
    <hkern u1="&#x2019;" u2="_" k="135" />
    <hkern u1="&#x2019;" u2="&#x40;" k="30" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="63" />
    <hkern u1="&#x2019;" u2="&#x26;" k="22" />
    <hkern u1="&#x2019;" u2="&#x20;" k="11" />
    <hkern u1="&#x201a;" u2="&#x3c0;" k="18" />
    <hkern u1="&#x201a;" u2="\" k="64" />
    <hkern u1="&#x201a;" u2="&#x37;" k="18" />
    <hkern u1="&#x201a;" u2="&#x31;" k="43" />
    <hkern u1="&#x201c;" u2="&#x2206;" k="48" />
    <hkern u1="&#x201c;" u2="&#x41b;" k="65" />
    <hkern u1="&#x201c;" u2="&#x414;" k="65" />
    <hkern u1="&#x201c;" u2="&#x3c0;" k="13" />
    <hkern u1="&#x201c;" u2="&#x3bc;" k="15" />
    <hkern u1="&#x201c;" u2="&#x3a9;" k="27" />
    <hkern u1="&#x201c;" u2="&#xdf;" k="10" />
    <hkern u1="&#x201d;" u2="&#x41b;" k="65" />
    <hkern u1="&#x201d;" u2="&#x414;" k="65" />
    <hkern u1="&#x201d;" u2="&#xdf;" k="14" />
    <hkern u1="&#x201d;" u2="&#xae;" k="25" />
    <hkern u1="&#x201d;" u2="&#xa9;" k="25" />
    <hkern u1="&#x201d;" u2="_" k="135" />
    <hkern u1="&#x201d;" u2="&#x40;" k="30" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="63" />
    <hkern u1="&#x201d;" u2="&#x26;" k="22" />
    <hkern u1="&#x201d;" u2="&#x20;" k="11" />
    <hkern u1="&#x201e;" u2="&#x3c0;" k="18" />
    <hkern u1="&#x201e;" u2="\" k="64" />
    <hkern u1="&#x201e;" u2="&#x37;" k="18" />
    <hkern u1="&#x201e;" u2="&#x31;" k="43" />
    <hkern u1="&#x2039;" u2="\" k="32" />
    <hkern u1="&#x203a;" u2="&#x41b;" k="20" />
    <hkern u1="&#x203a;" u2="&#x414;" k="22" />
    <hkern u1="&#x203a;" u2="&#x167;" k="49" />
    <hkern u1="&#x203a;" u2="&#x166;" k="49" />
    <hkern u1="&#x203a;" u2="\" k="47" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="27" />
    <hkern u1="&#x2044;" u2="&#x38;" k="21" />
    <hkern u1="&#x2044;" u2="&#x37;" k="-5" />
    <hkern u1="&#x2044;" u2="&#x36;" k="13" />
    <hkern u1="&#x2044;" u2="&#x34;" k="71" />
    <hkern u1="&#x2044;" u2="&#x30;" k="11" />
    <hkern u1="&#x2206;" u2="&#x201d;" k="37" />
    <hkern u1="&#x2206;" u2="&#x2019;" k="37" />
    <hkern u1="&#x2206;" u2="&#x27;" k="31" />
    <hkern u1="&#x2206;" u2="&#x22;" k="31" />
    <hkern u1="&#x2206;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x2206;" u2="\" k="58" />
    <hkern u1="&#x2206;" u2="&#x3f;" k="17" />
    <hkern u1="&#x2206;" u2="&#x2a;" k="26" />
    <hkern u1="&#x2212;" u2="&#x37;" k="24" />
    <hkern u1="&#x2212;" u2="&#x33;" k="13" />
    <hkern u1="&#x2212;" u2="&#x32;" k="15" />
    <hkern u1="&#x2212;" u2="&#x31;" k="14" />
    <hkern g1="fl" u2="&#x2122;" k="96" />
    <hkern g1="fl" u2="&#xba;" k="100" />
    <hkern g1="fl" u2="&#xb7;" k="102" />
    <hkern g1="fl" u2="&#xaa;" k="99" />
    <hkern g1="fl" u2="\" k="72" />
    <hkern g1="fl" u2="&#x3f;" k="18" />
    <hkern g1="fl" u2="&#x31;" k="45" />
    <hkern g1="fl" u2="&#x2a;" k="105" />
    <hkern g1="fl" u2="&#x20;" k="21" />
    <hkern g1="uni0414.loclBGR" u2="&#x42f;" k="-28" />
    <hkern g1="uni0414.loclBGR" u2="&#x42a;" k="66" />
    <hkern g1="uni0414.loclBGR" u2="&#x427;" k="80" />
    <hkern g1="uni0414.loclBGR" u2="&#x424;" k="21" />
    <hkern g1="uni0414.loclBGR" u2="&#x423;" k="51" />
    <hkern g1="uni0414.loclBGR" u2="&#x422;" k="81" />
    <hkern g1="uni0414.loclBGR" u2="&#x421;" k="19" />
    <hkern g1="uni0414.loclBGR" u2="&#x41e;" k="19" />
    <hkern g1="uni0414.loclBGR" u2="&#x416;" k="-5" />
    <hkern g1="uni0414.loclBGR" g2="uni0442.loclBGR" k="81" />
    <hkern g1="uni0414.loclBGR" g2="uni0436.loclBGR" k="-5" />
    <hkern g1="uni0414.loclBGR" u2="&#x201e;" k="-14" />
    <hkern g1="uni0414.loclBGR" u2="&#x201a;" k="-13" />
    <hkern g1="uni0414.loclBGR" u2="&#x44f;" k="-28" />
    <hkern g1="uni0414.loclBGR" u2="&#x44a;" k="66" />
    <hkern g1="uni0414.loclBGR" u2="&#x447;" k="80" />
    <hkern g1="uni0414.loclBGR" u2="&#x444;" k="21" />
    <hkern g1="uni0414.loclBGR" u2="&#x443;" k="51" />
    <hkern g1="uni0414.loclBGR" u2="&#x442;" k="81" />
    <hkern g1="uni0414.loclBGR" u2="&#x441;" k="19" />
    <hkern g1="uni0414.loclBGR" u2="&#x43e;" k="19" />
    <hkern g1="uni0414.loclBGR" u2="&#x436;" k="-5" />
    <hkern g1="uni0414.loclBGR" u2="\" k="72" />
    <hkern g1="uni0414.loclBGR" u2="&#x3f;" k="20" />
    <hkern g1="uni0414.loclBGR" u2="&#x2c;" k="-24" />
    <hkern g1="uni0414.loclBGR" u2="&#x2a;" k="63" />
    <hkern g1="uni041B.loclBGR" u2="\" k="68" />
    <hkern g1="uni041B.loclBGR" u2="&#x3f;" k="18" />
    <hkern g1="uni041B.loclBGR" u2="&#x2f;" k="-7" />
    <hkern g1="uni041B.loclBGR" u2="&#x2a;" k="51" />
    <hkern g1="uni0432.loclBGR" u2="&#x414;" k="7" />
    <hkern g1="uni0432.loclBGR" u2="\" k="17" />
    <hkern g1="uni0432.loclBGR" u2="&#x29;" k="17" />
    <hkern g1="uni0433.loclBGR" u2="&#x41b;" k="87" />
    <hkern g1="uni0433.loclBGR" u2="&#x414;" k="84" />
    <hkern g1="uni0433.loclBGR" u2="&#x2f;" k="70" />
    <hkern g1="uni0434.loclBGR" g2="uni0442.loclBGR" k="81" />
    <hkern g1="uni0434.loclBGR" g2="uni0436.loclBGR" k="-5" />
    <hkern g1="uni0434.loclBGR" u2="&#x201e;" k="-14" />
    <hkern g1="uni0434.loclBGR" u2="&#x201a;" k="-13" />
    <hkern g1="uni0434.loclBGR" u2="&#x44f;" k="-28" />
    <hkern g1="uni0434.loclBGR" u2="&#x44a;" k="66" />
    <hkern g1="uni0434.loclBGR" u2="&#x447;" k="80" />
    <hkern g1="uni0434.loclBGR" u2="&#x444;" k="21" />
    <hkern g1="uni0434.loclBGR" u2="&#x443;" k="51" />
    <hkern g1="uni0434.loclBGR" u2="&#x442;" k="81" />
    <hkern g1="uni0434.loclBGR" u2="&#x441;" k="19" />
    <hkern g1="uni0434.loclBGR" u2="&#x43e;" k="19" />
    <hkern g1="uni0434.loclBGR" u2="&#x436;" k="-5" />
    <hkern g1="uni0434.loclBGR" u2="\" k="72" />
    <hkern g1="uni0434.loclBGR" u2="&#x3f;" k="20" />
    <hkern g1="uni0434.loclBGR" u2="&#x2c;" k="-24" />
    <hkern g1="uni0434.loclBGR" u2="&#x2a;" k="63" />
    <hkern g1="uni0436.loclBGR" u2="\" k="-12" />
    <hkern g1="uni0436.loclBGR" u2="&#x2f;" k="-6" />
    <hkern g1="uni0436.loclBGR" u2="&#x29;" k="-17" />
    <hkern g1="uni043A.loclBGR" u2="\" k="-14" />
    <hkern g1="uni043A.loclBGR" u2="&#x2f;" k="-8" />
    <hkern g1="uni043A.loclBGR" u2="&#x29;" k="-19" />
    <hkern g1="uni043B.loclBGR" u2="\" k="68" />
    <hkern g1="uni043B.loclBGR" u2="&#x3f;" k="18" />
    <hkern g1="uni043B.loclBGR" u2="&#x2f;" k="-7" />
    <hkern g1="uni043B.loclBGR" u2="&#x2a;" k="51" />
    <hkern g1="uni0442.loclBGR" u2="&#x41b;" k="75" />
    <hkern g1="uni0442.loclBGR" u2="&#x414;" k="75" />
    <hkern g1="uni0442.loclBGR" u2="&#x2f;" k="60" />
    <hkern g1="uni0446.loclBGR" u2="&#x201e;" k="-10" />
    <hkern g1="uni0446.loclBGR" u2="&#x201a;" k="-9" />
    <hkern g1="uni0446.loclBGR" u2="\" k="19" />
    <hkern g1="uni0446.loclBGR" u2="&#x2c;" k="-20" />
    <hkern g1="uni0446.loclBGR" u2="&#x2a;" k="21" />
    <hkern g1="uni0449.loclBGR" u2="&#x201e;" k="-10" />
    <hkern g1="uni0449.loclBGR" u2="&#x201a;" k="-9" />
    <hkern g1="uni0449.loclBGR" u2="\" k="19" />
    <hkern g1="uni0449.loclBGR" u2="&#x2c;" k="-20" />
    <hkern g1="uni0449.loclBGR" u2="&#x2a;" k="21" />
    <hkern g1="uni044E.loclBGR" u2="&#x41b;" k="9" />
    <hkern g1="uni044E.loclBGR" u2="&#x414;" k="12" />
    <hkern g1="uni044E.loclBGR" u2="\" k="12" />
    <hkern g1="uni044E.loclBGR" u2="&#x2f;" k="12" />
    <hkern g1="uni044E.loclBGR" u2="&#x29;" k="19" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="98" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	k="11" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="19" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek"
	k="20" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="hyphen,uni00AD,endash,emdash"
	k="15" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="V,v"
	k="74" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="76" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="quoteleft,quotedblleft"
	k="63" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="quoteright,quotedblright"
	k="66" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="quotedbl,quotesingle"
	k="59" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="63" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	g2="J,j"
	k="8" />
    <hkern g1="B,b"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="42" />
    <hkern g1="B,b"
	g2="V,v"
	k="24" />
    <hkern g1="B,b"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="13" />
    <hkern g1="B,b"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="19" />
    <hkern g1="B,b"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="14" />
    <hkern g1="B,b"
	g2="X,x"
	k="26" />
    <hkern g1="B,b"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="5" />
    <hkern g1="B,b"
	g2="AE,ae"
	k="15" />
    <hkern g1="C,c,Ccedilla,ccedilla,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="13" />
    <hkern g1="C,c,Ccedilla,ccedilla,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron"
	g2="V,v"
	k="5" />
    <hkern g1="C,c,Ccedilla,ccedilla,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="5" />
    <hkern g1="C,c,Ccedilla,ccedilla,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron"
	g2="X,x"
	k="9" />
    <hkern g1="C,c,Ccedilla,ccedilla,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron"
	g2="AE,ae"
	k="6" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="38" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="V,v"
	k="20" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="6" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="15" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="19" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="X,x"
	k="28" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="6" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="AE,ae"
	k="22" />
    <hkern g1="D,d,Eth,eth,Dcaron,dcaron,Dcroat,dcroat"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="8" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="5" />
    <hkern g1="F,f"
	g2="hyphen,uni00AD,endash,emdash"
	k="12" />
    <hkern g1="F,f"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="F,f"
	g2="J,j"
	k="38" />
    <hkern g1="F,f"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="60" />
    <hkern g1="F,f"
	g2="AE,ae"
	k="74" />
    <hkern g1="F,f"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="54" />
    <hkern g1="F,f"
	g2="colon,semicolon"
	k="12" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="39" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="V,v"
	k="22" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="14" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="quoteleft,quotedblleft"
	k="15" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="quoteright,quotedblright"
	k="13" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="16" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="14" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="X,x"
	k="26" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="6" />
    <hkern g1="G,g,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent"
	g2="AE,ae"
	k="17" />
    <hkern g1="J,j,IJ,ij"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="20" />
    <hkern g1="J,j,IJ,ij"
	g2="AE,ae"
	k="25" />
    <hkern g1="J,j,IJ,ij"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="12" />
    <hkern g1="K,k,Kcommaaccent,kcommaaccent"
	g2="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	k="28" />
    <hkern g1="K,k,Kcommaaccent,kcommaaccent"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="42" />
    <hkern g1="K,k,Kcommaaccent,kcommaaccent"
	g2="hyphen,uni00AD,endash,emdash"
	k="33" />
    <hkern g1="K,k,Kcommaaccent,kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="36" />
    <hkern g1="K,k,Kcommaaccent,kcommaaccent"
	g2="quoteleft,quotedblleft"
	k="15" />
    <hkern g1="K,k,Kcommaaccent,kcommaaccent"
	g2="J,j"
	k="29" />
    <hkern g1="K,k,Kcommaaccent,kcommaaccent"
	g2="guillemotright,guilsinglright"
	k="13" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="110" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="6" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek"
	k="10" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="hyphen,uni00AD,endash,emdash"
	k="16" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="V,v"
	k="95" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="guillemotleft,guilsinglleft"
	k="21" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="100" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="quoteleft,quotedblleft"
	k="110" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="quoteright,quotedblright"
	k="111" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="quotedbl,quotesingle"
	k="109" />
    <hkern g1="L,l,Lacute,lacute,Lcommaaccent,lcommaaccent,Lcaron,lcaron,Lslash,lslash,fl"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="85" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="37" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="V,v"
	k="20" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="6" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="15" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="19" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="X,x"
	k="27" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="6" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="AE,ae"
	k="23" />
    <hkern g1="O,Q,o,q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="P,p"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="23" />
    <hkern g1="P,p"
	g2="hyphen,uni00AD,endash,emdash"
	k="9" />
    <hkern g1="P,p"
	g2="V,v"
	k="15" />
    <hkern g1="P,p"
	g2="guillemotleft,guilsinglleft"
	k="11" />
    <hkern g1="P,p"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="15" />
    <hkern g1="P,p"
	g2="J,j"
	k="81" />
    <hkern g1="P,p"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="61" />
    <hkern g1="P,p"
	g2="X,x"
	k="38" />
    <hkern g1="P,p"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="6" />
    <hkern g1="P,p"
	g2="AE,ae"
	k="79" />
    <hkern g1="P,p"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="71" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="41" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="6" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek"
	k="5" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="hyphen,uni00AD,endash,emdash"
	k="9" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="V,v"
	k="23" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="13" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="18" />
    <hkern g1="R,r,Racute,racute,Rcommaaccent,rcommaaccent,Rcaron,rcaron"
	g2="J,j"
	k="22" />
    <hkern g1="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="23" />
    <hkern g1="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	g2="V,v"
	k="14" />
    <hkern g1="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	g2="quoteleft,quotedblleft"
	k="14" />
    <hkern g1="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="11" />
    <hkern g1="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="12" />
    <hkern g1="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	g2="X,x"
	k="19" />
    <hkern g1="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	g2="AE,ae"
	k="14" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="6" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="hyphen,uni00AD,endash,emdash"
	k="55" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="guillemotleft,guilsinglleft"
	k="59" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="J,j"
	k="96" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="76" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="AE,ae"
	k="82" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="56" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="colon,semicolon"
	k="43" />
    <hkern g1="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	g2="guillemotright,guilsinglright"
	k="51" />
    <hkern g1="Thorn,thorn"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="55" />
    <hkern g1="Thorn,thorn"
	g2="V,v"
	k="26" />
    <hkern g1="Thorn,thorn"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="20" />
    <hkern g1="Thorn,thorn"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="18" />
    <hkern g1="Thorn,thorn"
	g2="J,j"
	k="6" />
    <hkern g1="Thorn,thorn"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="30" />
    <hkern g1="Thorn,thorn"
	g2="X,x"
	k="51" />
    <hkern g1="Thorn,thorn"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="19" />
    <hkern g1="Thorn,thorn"
	g2="AE,ae"
	k="39" />
    <hkern g1="Thorn,thorn"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="21" />
    <hkern g1="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek"
	g2="AE,ae"
	k="26" />
    <hkern g1="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="12" />
    <hkern g1="V,v"
	g2="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	k="13" />
    <hkern g1="V,v"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="19" />
    <hkern g1="V,v"
	g2="hyphen,uni00AD,endash,emdash"
	k="35" />
    <hkern g1="V,v"
	g2="guillemotleft,guilsinglleft"
	k="45" />
    <hkern g1="V,v"
	g2="J,j"
	k="83" />
    <hkern g1="V,v"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="75" />
    <hkern g1="V,v"
	g2="AE,ae"
	k="87" />
    <hkern g1="V,v"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="V,v"
	g2="colon,semicolon"
	k="26" />
    <hkern g1="V,v"
	g2="guillemotright,guilsinglright"
	k="30" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	k="10" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="14" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="hyphen,uni00AD,endash,emdash"
	k="26" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="34" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="J,j"
	k="61" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="63" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="AE,ae"
	k="74" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="48" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	g2="guillemotright,guilsinglright"
	k="21" />
    <hkern g1="X,x"
	g2="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	k="16" />
    <hkern g1="X,x"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="27" />
    <hkern g1="X,x"
	g2="hyphen,uni00AD,endash,emdash"
	k="27" />
    <hkern g1="X,x"
	g2="guillemotleft,guilsinglleft"
	k="32" />
    <hkern g1="X,x"
	g2="quoteleft,quotedblleft"
	k="11" />
    <hkern g1="X,x"
	g2="J,j"
	k="19" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	k="23" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="36" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="hyphen,uni00AD,endash,emdash"
	k="61" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="75" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="quoteleft,quotedblleft"
	k="17" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="J,j"
	k="107" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="99" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="AE,ae"
	k="114" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="72" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="colon,semicolon"
	k="48" />
    <hkern g1="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	g2="guillemotright,guilsinglright"
	k="58" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="6" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="hyphen,uni00AD,endash,emdash"
	k="9" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="quoteleft,quotedblleft"
	k="8" />
    <hkern g1="colon,semicolon"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="48" />
    <hkern g1="colon,semicolon"
	g2="V,v"
	k="26" />
    <hkern g1="colon,semicolon"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="43" />
    <hkern g1="colon,semicolon"
	g2="quoteright,quotedblright"
	k="14" />
    <hkern g1="colon,semicolon"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="uni042A,uni044A"
	k="30" />
    <hkern g1="colon,semicolon"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="43" />
    <hkern g1="colon,semicolon"
	g2="uni0423,uni0443"
	k="23" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="58" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V,v"
	k="30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="51" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="quoteright,quotedblright"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="21" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="uni042A,uni044A"
	k="38" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="51" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="uni0423,uni0443"
	k="30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="74" />
    <hkern g1="guillemotright,guilsinglright"
	g2="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V,v"
	k="44" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="58" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quoteright,quotedblright"
	k="35" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quotedbl,quotesingle"
	k="21" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="33" />
    <hkern g1="guillemotright,guilsinglright"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="17" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X,x"
	k="31" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="AE,ae"
	k="24" />
    <hkern g1="guillemotright,guilsinglright"
	g2="uni042A,uni044A"
	k="46" />
    <hkern g1="guillemotright,guilsinglright"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="58" />
    <hkern g1="guillemotright,guilsinglright"
	g2="uni0423,uni0443"
	k="49" />
    <hkern g1="guillemotright,guilsinglright"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="34" />
    <hkern g1="guillemotright,guilsinglright"
	g2="uni0425,uni0445"
	k="31" />
    <hkern g1="guillemotright,guilsinglright"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="18" />
    <hkern g1="guillemotright,guilsinglright"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="17" />
    <hkern g1="guillemotright,guilsinglright"
	g2="uni042F,uni044F"
	k="14" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="61" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="V,v"
	k="35" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="55" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="quoteright,quotedblright"
	k="54" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="quotedbl,quotesingle"
	k="8" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="26" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="15" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="X,x"
	k="27" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="16" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="AE,ae"
	k="19" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni042A,uni044A"
	k="39" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="55" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni0423,uni0443"
	k="39" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="31" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni0425,uni0445"
	k="27" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="21" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="15" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni042F,uni044F"
	k="9" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="72" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="9" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek"
	k="12" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="V,v"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="T,t,Tcommaaccent,tcommaaccent,Tcaron,tcaron,Tbar,tbar,uni021A,uni021B"
	k="57" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quoteleft,quotedblleft"
	k="101" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quoteright,quotedblright"
	k="105" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="91" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="49" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="uni042A,uni044A"
	k="42" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="57" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="uni0423,uni0443"
	k="35" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="uni0427,uni0447"
	k="66" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="uni0424,uni0444"
	k="13" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="9" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="-13" />
    <hkern g1="quoteleft,quotedblleft"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="12" />
    <hkern g1="quoteleft,quotedblleft"
	g2="V,v"
	k="-13" />
    <hkern g1="quoteleft,quotedblleft"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="-5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J,j"
	k="72" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="76" />
    <hkern g1="quoteleft,quotedblleft"
	g2="X,x"
	k="-7" />
    <hkern g1="quoteleft,quotedblleft"
	g2="AE,ae"
	k="90" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="117" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni0423,uni0443"
	k="-32" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="-14" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni0425,uni0445"
	k="-7" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="87" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="76" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni042F,uni044F"
	k="8" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni0424,uni0444"
	k="18" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="12" />
    <hkern g1="quoteright,quotedblright"
	g2="Y,y,Yacute,yacute,ydieresis,Ycircumflex,ycircumflex,Ydieresis,Ygrave,ygrave"
	k="-20" />
    <hkern g1="quoteright,quotedblright"
	g2="S,s,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,Scommaaccent,scommaaccent"
	k="9" />
    <hkern g1="quoteright,quotedblright"
	g2="C,G,O,Q,c,g,o,q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Cdotaccent,cdotaccent,Ccaron,ccaron,Gbreve,gbreve,Gdotaccent,gdotaccent,Gcommaaccent,gcommaaccent,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe"
	k="19" />
    <hkern g1="quoteright,quotedblright"
	g2="hyphen,uni00AD,endash,emdash"
	k="72" />
    <hkern g1="quoteright,quotedblright"
	g2="V,v"
	k="-20" />
    <hkern g1="quoteright,quotedblright"
	g2="guillemotleft,guilsinglleft"
	k="56" />
    <hkern g1="quoteright,quotedblright"
	g2="W,w,Wcircumflex,wcircumflex,Wgrave,wgrave,Wacute,wacute,Wdieresis,wdieresis"
	k="-12" />
    <hkern g1="quoteright,quotedblright"
	g2="J,j"
	k="72" />
    <hkern g1="quoteright,quotedblright"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="80" />
    <hkern g1="quoteright,quotedblright"
	g2="X,x"
	k="-14" />
    <hkern g1="quoteright,quotedblright"
	g2="AE,ae"
	k="95" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="126" />
    <hkern g1="quoteright,quotedblright"
	g2="colon,semicolon"
	k="41" />
    <hkern g1="quoteright,quotedblright"
	g2="guillemotright,guilsinglright"
	k="41" />
    <hkern g1="quoteright,quotedblright"
	g2="uni0423,uni0443"
	k="-38" />
    <hkern g1="quoteright,quotedblright"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="-21" />
    <hkern g1="quoteright,quotedblright"
	g2="uni0425,uni0445"
	k="-14" />
    <hkern g1="quoteright,quotedblright"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="91" />
    <hkern g1="quoteright,quotedblright"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="80" />
    <hkern g1="quoteright,quotedblright"
	g2="uni042F,uni044F"
	k="15" />
    <hkern g1="quoteright,quotedblright"
	g2="uni0424,uni0444"
	k="27" />
    <hkern g1="quoteright,quotedblright"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="19" />
    <hkern g1="quotedbl,quotesingle"
	g2="hyphen,uni00AD,endash,emdash"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="guillemotleft,guilsinglleft"
	k="23" />
    <hkern g1="quotedbl,quotesingle"
	g2="J,j"
	k="74" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="AE,ae"
	k="71" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="93" />
    <hkern g1="quotedbl,quotesingle"
	g2="uni0423,uni0443"
	k="-9" />
    <hkern g1="quotedbl,quotesingle"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="71" />
    <hkern g1="quotedbl,quotesingle"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="60" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="hyphen,uni00AD,endash,emdash"
	k="15" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="quoteleft,quotedblleft"
	k="63" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="quoteright,quotedblright"
	k="66" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="quotedbl,quotesingle"
	k="59" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="uni042A,uni044A"
	k="62" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="76" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="uni0423,uni0443"
	k="40" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="-5" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="uni0427,uni0447"
	k="62" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="uni0424,uni0444"
	k="22" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="19" />
    <hkern g1="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	g2="uni042D,uni044D"
	k="10" />
    <hkern g1="uni0411,uni0431"
	g2="quoteleft,quotedblleft"
	k="23" />
    <hkern g1="uni0411,uni0431"
	g2="quoteright,quotedblright"
	k="19" />
    <hkern g1="uni0411,uni0431"
	g2="quotedbl,quotesingle"
	k="8" />
    <hkern g1="uni0411,uni0431"
	g2="uni042A,uni044A"
	k="12" />
    <hkern g1="uni0411,uni0431"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="14" />
    <hkern g1="uni0411,uni0431"
	g2="uni0423,uni0443"
	k="14" />
    <hkern g1="uni0411,uni0431"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="13" />
    <hkern g1="uni0411,uni0431"
	g2="uni0425,uni0445"
	k="14" />
    <hkern g1="uni0411,uni0431"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="7" />
    <hkern g1="uni0411,uni0431"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="12" />
    <hkern g1="uni0411,uni0431"
	g2="uni042F,uni044F"
	k="5" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni042A,uni044A"
	k="9" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="11" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni0423,uni0443"
	k="35" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="34" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni0425,uni0445"
	k="25" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="11" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="14" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni042F,uni044F"
	k="5" />
    <hkern g1="uni0412,uni0432,uni0432.loclBGR"
	g2="uni0427,uni0447"
	k="6" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="hyphen,uni00AD,endash,emdash"
	k="58" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="guillemotleft,guilsinglleft"
	k="59" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="75" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="guillemotright,guilsinglright"
	k="47" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="uni0423,uni0443"
	k="-13" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="95" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="90" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="uni042F,uni044F"
	k="12" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="uni0424,uni0444"
	k="11" />
    <hkern g1="uni0413,uni0433,uni0433.loclBGR"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="6" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="hyphen,uni00AD,endash,emdash"
	k="14" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="quoteleft,quotedblleft"
	k="26" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="quoteright,quotedblright"
	k="27" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="quotedbl,quotesingle"
	k="25" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="uni042A,uni044A"
	k="24" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="28" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="uni0423,uni0443"
	k="14" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="uni042F,uni044F"
	k="-24" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="uni0427,uni0447"
	k="28" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="uni0424,uni0444"
	k="11" />
    <hkern g1="uni0414,uni0426,uni0429,uni0446,uni0449,uni0446.loclBGR,uni0449.loclBGR"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="6" />
    <hkern g1="uni0401,uni0415,uni0435,uni0451"
	g2="uni0424,uni0444"
	k="6" />
    <hkern g1="uni0401,uni0415,uni0435,uni0451"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="5" />
    <hkern g1="uni0416,uni0436,uni0436.loclBGR"
	g2="hyphen,uni00AD,endash,emdash"
	k="31" />
    <hkern g1="uni0416,uni0436,uni0436.loclBGR"
	g2="guillemotleft,guilsinglleft"
	k="35" />
    <hkern g1="uni0416,uni0436,uni0436.loclBGR"
	g2="quoteleft,quotedblleft"
	k="15" />
    <hkern g1="uni0416,uni0436,uni0436.loclBGR"
	g2="guillemotright,guilsinglright"
	k="12" />
    <hkern g1="uni0416,uni0436,uni0436.loclBGR"
	g2="uni0424,uni0444"
	k="56" />
    <hkern g1="uni0416,uni0436,uni0436.loclBGR"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="36" />
    <hkern g1="uni0416,uni0436,uni0436.loclBGR"
	g2="uni042D,uni044D"
	k="26" />
    <hkern g1="uni0416,uni0436,uni0436.loclBGR"
	g2="uni0417,uni0437"
	k="13" />
    <hkern g1="uni0417,uni0437"
	g2="uni042A,uni044A"
	k="6" />
    <hkern g1="uni0417,uni0437"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="7" />
    <hkern g1="uni0417,uni0437"
	g2="uni0423,uni0443"
	k="30" />
    <hkern g1="uni0417,uni0437"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="31" />
    <hkern g1="uni0417,uni0437"
	g2="uni0425,uni0445"
	k="23" />
    <hkern g1="uni0417,uni0437"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="10" />
    <hkern g1="uni0417,uni0437"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="13" />
    <hkern g1="uni0417,uni0437"
	g2="uni0427,uni0447"
	k="5" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="hyphen,uni00AD,endash,emdash"
	k="33" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="guillemotleft,guilsinglleft"
	k="36" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="quoteleft,quotedblleft"
	k="15" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="guillemotright,guilsinglright"
	k="13" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="-6" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="uni0424,uni0444"
	k="62" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="42" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="uni042D,uni044D"
	k="26" />
    <hkern g1="uni041A,uni043A,uni043A.loclBGR"
	g2="uni0417,uni0437"
	k="13" />
    <hkern g1="uni041E,uni042D,uni042E,uni043E,uni044D,uni044E,uni044E.loclBGR"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="uni041E,uni042D,uni042E,uni043E,uni044D,uni044E,uni044E.loclBGR"
	g2="uni042A,uni044A"
	k="5" />
    <hkern g1="uni041E,uni042D,uni042E,uni043E,uni044D,uni044E,uni044E.loclBGR"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="6" />
    <hkern g1="uni041E,uni042D,uni042E,uni043E,uni044D,uni044E,uni044E.loclBGR"
	g2="uni0423,uni0443"
	k="31" />
    <hkern g1="uni041E,uni042D,uni042E,uni043E,uni044D,uni044E,uni044E.loclBGR"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="36" />
    <hkern g1="uni041E,uni042D,uni042E,uni043E,uni044D,uni044E,uni044E.loclBGR"
	g2="uni0425,uni0445"
	k="27" />
    <hkern g1="uni041E,uni042D,uni042E,uni043E,uni044D,uni044E,uni044E.loclBGR"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="21" />
    <hkern g1="uni041E,uni042D,uni042E,uni043E,uni044D,uni044E,uni044E.loclBGR"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="19" />
    <hkern g1="uni0420,uni0440"
	g2="hyphen,uni00AD,endash,emdash"
	k="9" />
    <hkern g1="uni0420,uni0440"
	g2="guillemotleft,guilsinglleft"
	k="11" />
    <hkern g1="uni0420,uni0440"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="71" />
    <hkern g1="uni0420,uni0440"
	g2="uni0423,uni0443"
	k="10" />
    <hkern g1="uni0420,uni0440"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="54" />
    <hkern g1="uni0420,uni0440"
	g2="uni0425,uni0445"
	k="38" />
    <hkern g1="uni0420,uni0440"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="82" />
    <hkern g1="uni0420,uni0440"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="61" />
    <hkern g1="uni0420,uni0440"
	g2="uni042D,uni044D"
	k="7" />
    <hkern g1="uni0420,uni0440"
	g2="uni0417,uni0437"
	k="25" />
    <hkern g1="uni0421,uni0441"
	g2="uni0423,uni0443"
	k="14" />
    <hkern g1="uni0421,uni0441"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="20" />
    <hkern g1="uni0421,uni0441"
	g2="uni0425,uni0445"
	k="9" />
    <hkern g1="uni0421,uni0441"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="5" />
    <hkern g1="uni0421,uni0441"
	g2="uni042F,uni044F"
	k="6" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="hyphen,uni00AD,endash,emdash"
	k="55" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="guillemotleft,guilsinglleft"
	k="59" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="56" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="colon,semicolon"
	k="43" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="guillemotright,guilsinglright"
	k="51" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="uni0423,uni0443"
	k="-13" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="81" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="76" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="uni042F,uni044F"
	k="11" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="uni0424,uni0444"
	k="14" />
    <hkern g1="uni0422,uni0442,uni0442.loclBGR"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="6" />
    <hkern g1="uni0423,uni0443"
	g2="hyphen,uni00AD,endash,emdash"
	k="45" />
    <hkern g1="uni0423,uni0443"
	g2="guillemotleft,guilsinglleft"
	k="57" />
    <hkern g1="uni0423,uni0443"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="71" />
    <hkern g1="uni0423,uni0443"
	g2="colon,semicolon"
	k="34" />
    <hkern g1="uni0423,uni0443"
	g2="guillemotright,guilsinglright"
	k="40" />
    <hkern g1="uni0423,uni0443"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="102" />
    <hkern g1="uni0423,uni0443"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="89" />
    <hkern g1="uni0423,uni0443"
	g2="uni042F,uni044F"
	k="27" />
    <hkern g1="uni0423,uni0443"
	g2="uni0424,uni0444"
	k="30" />
    <hkern g1="uni0423,uni0443"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="24" />
    <hkern g1="uni0423,uni0443"
	g2="uni042D,uni044D"
	k="13" />
    <hkern g1="uni0423,uni0443"
	g2="uni0417,uni0437"
	k="15" />
    <hkern g1="uni0424,uni0444"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="13" />
    <hkern g1="uni0424,uni0444"
	g2="uni042A,uni044A"
	k="12" />
    <hkern g1="uni0424,uni0444"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="14" />
    <hkern g1="uni0424,uni0444"
	g2="uni0423,uni0443"
	k="39" />
    <hkern g1="uni0424,uni0444"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="56" />
    <hkern g1="uni0424,uni0444"
	g2="uni0425,uni0445"
	k="39" />
    <hkern g1="uni0424,uni0444"
	g2="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	k="21" />
    <hkern g1="uni0424,uni0444"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="22" />
    <hkern g1="uni0424,uni0444"
	g2="uni042D,uni044D"
	k="5" />
    <hkern g1="uni0424,uni0444"
	g2="uni0417,uni0437"
	k="11" />
    <hkern g1="uni0425,uni0445"
	g2="hyphen,uni00AD,endash,emdash"
	k="27" />
    <hkern g1="uni0425,uni0445"
	g2="guillemotleft,guilsinglleft"
	k="32" />
    <hkern g1="uni0425,uni0445"
	g2="quoteleft,quotedblleft"
	k="11" />
    <hkern g1="uni0425,uni0445"
	g2="uni0424,uni0444"
	k="38" />
    <hkern g1="uni0425,uni0445"
	g2="uni041E,uni0421,uni043E,uni0441"
	k="27" />
    <hkern g1="uni0425,uni0445"
	g2="uni042D,uni044D"
	k="15" />
    <hkern g1="uni0425,uni0445"
	g2="uni0417,uni0437"
	k="12" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="quoteleft,quotedblleft"
	k="64" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="quoteright,quotedblright"
	k="72" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="quotedbl,quotesingle"
	k="27" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni042A,uni044A"
	k="69" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni0422,uni0442,uni0442.loclBGR"
	k="89" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni0423,uni0443"
	k="90" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni0416,uni0436,uni0436.loclBGR"
	k="51" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni0425,uni0445"
	k="38" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni0410,uni0430,uni043B,uni041B.loclBGR,uni043B.loclBGR"
	k="15" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni042F,uni044F"
	k="8" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni0427,uni0447"
	k="10" />
    <hkern g1="uni042A,uni042C,uni044A,uni044C"
	g2="uni042D,uni044D"
	k="5" />
    <hkern g1="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	g2="hyphen,uni00AD,endash,emdash"
	k="21" />
    <hkern g1="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	g2="guillemotleft,guilsinglleft"
	k="21" />
    <hkern g1="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	g2="quoteleft,quotedblleft"
	k="73" />
    <hkern g1="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	g2="quoteright,quotedblright"
	k="77" />
    <hkern g1="uni0434,uni0414.loclBGR,uni0434.loclBGR"
	g2="quotedbl,quotesingle"
	k="70" />
  </font>
</defs></svg>
