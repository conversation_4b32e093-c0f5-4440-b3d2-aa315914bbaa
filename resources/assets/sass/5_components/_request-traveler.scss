.request-traveler {
  display: flex;
  flex-direction: column;
}

.request-traveler-select {
  .react-select__menu {
    width: 200px;
  }

  .icon-other {
    font-size: 24px;
    cursor: pointer;
  }

  .react-select-custom {
    top: -28px;
  }
}
.request-traveler-list {
  margin: 20px 0;
  display: flex;
  flex-direction: column;
}

.request-traveler-avatar {
  margin-right: 15px;

  .user-avatar {
    width: 40px;
    height: 40px;
  }
}

.request-traveler-add {
  font-size: 12px;
  margin-bottom: 10px;
}

.request-traveler-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.request-traveler-item + .request-traveler-item {
  margin-top: 10px;
}

.request-traveler-name {
  margin-right: 30px;
}

.request-traveler-email {
  font-size: 12px;
  color: $color-grey-05;
}

.request-traveler-remove {
  margin-left: auto;
  cursor: pointer;
  font-size: 1.5em;
}
