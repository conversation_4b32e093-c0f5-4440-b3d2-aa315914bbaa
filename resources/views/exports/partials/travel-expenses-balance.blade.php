@if($mealDeductions)
    <div class="main__section-wrapper">
    <div class="main__section-wrapper-header">
        <span class="main__section-wrapper-heading main__section-wrapper-heading--center">{{trans('print-settlement.expense.header')}}</span>
    </div>

    <table class="main__deductions-table">
        <thead>
        <tr>
            <th align="left">{{trans('print-settlement.expense.table.time')}}</th>
            <th>{{trans('print-settlement.expense.table.breakfast')}}</th>
            <th>{{trans('print-settlement.expense.table.dinner')}}</th>
            <th>{{trans('print-settlement.expense.table.supper')}}</th>
        </tr>
        </thead>
        <tbody>
            @component('exports.components.travel-expenses', ['travelExpenses' => $mealDeductions, 'values' => [
                'breakfast',
                'lunch',
                'dinner'
            ]])
            @endcomponent
        </tbody>
    </table>
</div>
@endif

@if($lumpSums)
<div class="main__section-wrapper">
    <div class="main__section-wrapper-header">
        <span class="main__section-wrapper-heading main__section-wrapper-heading--center">{{trans('print-settlement.expense.lump-sums')}}</span>
    </div>

    <table class="main__deductions-table">
        <thead>
        <tr>
            <th align="left">{{trans('print-settlement.expense.table.time')}}</th>
            <th>{{trans('print-settlement.expense.table.accommodation')}}</th>
            <th>{{trans('print-settlement.expense.table.travels')}}</th>
        </tr>
        </thead>
        <tbody>
            @component('exports.components.travel-expenses', ['travelExpenses' => $lumpSums, 'values' => [
                'accommodation',
                'drive',
            ]])
            @endcomponent
        </tbody>
    </table>
</div>
@endif

@if($travelExpensesBalance)
<div class="main__section-wrapper">
    <div class="main__section-wrapper-header">
        <span class="main__section-wrapper-heading main__section-wrapper-heading--center">{{trans('print-settlement.expense.settlement')}}</span>
    </div>

    <table class="main__expense-commute-table">
        <thead>
        <tr>
            <th style="width: 25%;">{{trans('print-settlement.expense.table.country')}}</th>
            <th style="width: 12.5%;">{{trans('print-settlement.expense.table.daily-amount')}}</th>
            <th style="width: 12.5%;">{{trans('print-settlement.expense.table.quantity')}}</th>
            <th style="width: 12.5%;">{{trans('print-settlement.expense.table.amount')}}</th>
            <th style="width: 12.5%;">{{trans('print-settlement.expense.table.subs')}}</th>
            <th style="width: 12.5%;">{{trans('print-settlement.expense.table.rate')}}</th>
            <th style="width: 12.5%;">{{trans('print-settlement.expense.table.converted', ['currencyCode' => $currencyCode])}}</th>
        </tr>
        </thead>
        <tbody>
        @foreach($travelExpensesBalance as $expenses)
            <tr class="main__expense-commute-heading">
                <td colspan="7">{{trans($expenses['name'])}}</td>
            </tr>
            @forelse($expenses['elements'] as $countryExpenses)
                <tr>
                    <td>{{trans($countryExpenses['countryName'])}} ({{$countryExpenses['currency']}})</td>
                    @if(isset($countryExpenses['base']))
                        <td align="right">@amount_format($countryExpenses['base'])</td>
                    @else
                         <td align="right">-</td>
                    @endif

                    @if(isset($countryExpenses['duration']))
                        <td align="right">@amount_format($countryExpenses['duration'])</td>
                    @else
                         <td align="right">-</td>
                    @endif

                    @if(isset($countryExpenses['amount']))
                        <td align="right">@amount_format($countryExpenses['amount'])</td>
                    @else
                         <td align="right">-</td>
                    @endif

                    @if(isset($countryExpenses['deductions']))
                        <td align="right">@amount_format($countryExpenses['deductions'])</td>
                    @else
                         <td align="right">-</td>
                    @endif

                    @if(isset($countryExpenses['exchangeRate']))
                        <td align="right">@exchange_rate_format($countryExpenses['exchangeRate'])</td>
                    @else
                         <td align="right">-</td>
                    @endif

                    @if(isset($countryExpenses['convertedAmount']))
                        <td align="right">@amount_format($countryExpenses['convertedAmount'])</td>
                    @else
                         <td align="right">-</td>
                    @endif
                </tr>
                @empty
                    <tr>
                        <td align="left">-</td>
                        <td align="right">-</td>
                        <td align="right">-</td>
                        <td align="right">-</td>
                        <td align="right">-</td>
                        <td align="right">-</td>
                        <th align="right">-</th>
                    </tr>
            @endforelse
        @endforeach
        <tr>
            <th colspan="7" align="right">{{trans('print-settlement.expense.table.total')}}: @amount_format($travelExpensesAmount) {{$generalInformation['currency']}}</th>
        </tr>
        </tbody>
    </table>
</div>
@endif