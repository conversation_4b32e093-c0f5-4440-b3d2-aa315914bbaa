<section class="main__section-wrapper" style="page-break-inside: avoid;">
    <div class="main__section-wrapper-header">
        <span class="main__section-wrapper-heading main__section-wrapper-heading--center">{{trans('print-settlement.decret.header')}}</span>
    </div>

    @foreach($documentPostings as $key => $document)
        @if(!\App\Vendors\Math::isEqualZero($document->getGrossAmountInInstanceCurrency()))
            <table class="main__accounting-table">
                <thead class="main__accounting-table-header">
                <tr>
                    <th>{{trans('print-settlement.decret.table.row-no')}}</th>
                    <th>{{trans('print-settlement.decret.table.number')}}</th>
                    <th></th>
                    <th>{{trans('print-settlement.decret.table.amount')}}</th>
                    <th>{{trans('print-settlement.decret.table.currency')}}</th>
                    <th>{{trans('print-settlement.decret.table.rate')}}</th>
                    <th>{{trans('print-settlement.decret.table.converted', ['currencyCode' => $currencyCode])}}</th>
                </tr>
                </thead>
                <tbody class="main__accounting-table-body">


                <tr class="main__accounting-table-document-row">
                    <td>{{++$key}}</td>
                    <td>{{$document->getDocumentNumber()}}</td>
                    <td style="white-space: nowrap;"></td>
                    <td>@amount_format($document->getGrossAmountInDocumentCurrency())</td>
                    <td>{{$document->getDocumentCurrencyCode()}}</td>
                    <td>{{intval($document->getExchangeRate()) !== 1 ? $document->getExchangeRate() : '-' }}</td>
                    <td>@amount_format($document->getGrossAmountInInstanceCurrency())</td>
                </tr>
                <tr class="main__accounting-table-document-row">
                  <td></td>
                  <td>
                    @foreach($document->getDetails() as $postingDetail)
                      @if(!empty($postingDetail->getExpenseType()))
                        @if($loop->first)
                          <b>{{trans('print-settlement.decret.table.expense-types')}}</b>
                        @endif
                        <span>@if(!$loop->first), @endif{{trans('expense-type.'.$postingDetail->getExpenseType())}}</span>
                      @endif
                    @endforeach
                  </td>
                  <td colspan="5"></td>
                </tr>
                <tr class="row-accounting_details-table">
                    <td colspan="7">
                        <table class="accounting_details-table">
                            @include('exports.partials.accouting-order-column-headers')
                            <tbody class="accounting_details-table-body">
                            @foreach($document->getDetails() as $postingDetail)
                                @include('exports.partials.accouting-order-column-values')

                                @if($postingDetail->getVatAmount() !== null && !\App\Vendors\Math::isEqualZero($postingDetail->getVatAmount()))
                                    @include('exports.partials.accouting-order-column-vat-line')
                                @endif
                            @endforeach

                            @foreach($document->getReverseTax() as $reverseTax)
                                @include('exports.partials.accouting-order-column-reverse-vat-line')
                            @endforeach

                            @foreach($document->getCreditDetails() as $creditDetail)
                                @if(!\App\Vendors\Math::isEqualZero($creditDetail->getAmount()))
                                    @include('exports.partials.accouting-order-column-summary-line')
                                @endif
                            @endforeach

                            </tbody>
                        </table>
                    </td>
                </tr>

                </tbody>
            </table>
        @endif
    @endforeach
</section>
