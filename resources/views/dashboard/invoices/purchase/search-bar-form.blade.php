{{ Form::open(['method' => 'GET', 'id' => 'search-bar']) }}
<div class="invoices__filters-panel mb-3">
    <div class="invoices__filters-options">

        {{ Form::label('invoice_issue_date', 'Data wystawienia', ['class' => 'mb-0 mr-2']) }}
        <div class="invoices__datepicker-wrapper input-group mr-2">

            @include('dashboard.filter-issue-date')

            <div class="input-group-append">
                        <span class="input-group-text bg-light text-dark border-1" id="calendar-icon-addon text-light">
                            <span class="icon-calendar" aria-hidden="true"></span>
                        </span>
            </div>
        </div>


        {{ Form::select('obt_type', ['' => 'Kategoria produktu' ] + $obtTypes->pluck('name', 'id')->toArray(), $currentFilters->get('obt_type'), ['class' => 'invoices__select selectpicker mr-2', 'data-live-search' => 'true']) }}
        {{ Form::select('issuer', ['' => 'Wystawca' ] + $issuers->pluck('issuer_name', 'issuer_name')->toArray(), $currentFilters->get('issuer'), ['class' => 'invoices__select selectpicker mr-2', 'data-live-search' => 'true']) }}
        {{ Form::search('number', $currentFilters->get('number'), ['class' => 'invoices__input form-control mr-2', 'placeholder' => 'Nr faktury']) }}
        {{ Form::select('payment_batch', ['' => 'Batch' ] + $batches->pluck('name', 'id')->toArray(), $currentFilters->get('payment_batch'), ['class' => 'invoices__select selectpicker mr-2', 'data-live-search' => 'true']) }}

        {{ Form::submit('Szukaj', ['name' => 'submit', 'class' => 'btn btn-primary']) }}

    </div>
</div>
{{ Form::close() }}
