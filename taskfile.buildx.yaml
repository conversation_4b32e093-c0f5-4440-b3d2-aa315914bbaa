version: '3'

vars:
  host: "{{.BUILDX_HOST}}"
  user: "{{.BUILDX_USER}}"
  key_path: "docker/buildx-rsa"
  ssh: ssh://{{.user}}@{{.host}}

tasks:
  create:
    desc: "Establish SSH tunnel to remote Docker host"
    cmds:
      - docker buildx rm -f portainer || true
      - docker context rm -f portainer || true
      - docker -H {{.ssh}} info
      - docker buildx create --name portainer --node local --platform linux/arm64,linux/riscv64,linux/ppc64le,linux/s390x,linux/mips64le,linux/mips64,linux/arm/v7,linux/arm/v6 --driver-opt env.BUILDKIT_STEP_LOG_MAX_SIZE=10000000 --driver-opt env.BUILDKIT_STEP_LOG_MAX_SPEED=10000000
      - docker buildx create --name portainer --append --node portainer --platform linux/amd64,linux/386 {{.ssh}} --driver-opt env.BUILDKIT_STEP_LOG_MAX_SIZE=10000000 --driver-opt env.BUILDKIT_STEP_LOG_MAX_SPEED=10000000
      # - docker context create portainer --docker host=ssh://{{.user}}@{{.host}} default
      # - docker buildx --name portainer --driver remote --docker host={{.ssh}} default
      # - docker buildx use portainer

  use:
    desc: "Use remote Docker host"
    cmds:
      - task docker:buildx:create
      - docker buildx use portainer

  status:
    desc: "Check Docker info on remote server"
    cmds:
      - docker info
