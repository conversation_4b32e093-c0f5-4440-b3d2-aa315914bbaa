<?php

use Illuminate\Database\Seeder;

class PromedicaUsersSeeder extends Seeder
{
    public function run()
    {
        DB::beginTransaction();
        try {
            //DEMO
            $demoInstance = \App\Instance::with(['mpks', 'companies', 'groups'])->where(['domain' => 'demo.mindento.com'])->first();

            if($demoInstance === null)
            {
                throw new Exception('Instance not found');
            }


            $demoCompany = $demoInstance->companies->first();
            $manager1 = factory(App\User::class)->create([
                'instance_id' => $demoInstance->id,
                'mpk_id'      => $demoInstance->mpks->random(1)->first(),
                'company_id'  => $demoCompany,
                'first_name'  => 'Volodymyr',
                'last_name'   => 'Kleban',
                'email'       => '<EMAIL>',
                'phone'       => '+48 ***********',
                'avatar'      => 'https://randomuser.me/api/portraits/women/63.jpg',
                'user_id'     => null,
                'slug'        => 'v.kleban',
                'level'       => 7,
                'grade'       => 1,
                'erp_id'      => 0
            ]);

            $manager2 = factory(App\User::class)->create([
                'instance_id' => $demoInstance->id,
                'mpk_id'      => $demoInstance->mpks->random(1)->first(),
                'company_id'  => $demoCompany,
                'first_name'  => 'Marta',
                'last_name'   => 'Jaworska',
                'email'       => '<EMAIL>',
                'phone'       => '+48 ***********',
                'avatar'      => 'https://randomuser.me/api/portraits/women/63.jpg',
                'user_id'     => $manager1->id,
                'slug'        => 'm.jaworska',
                'level'       => 7,
                'grade'       => 1,
                'erp_id'      => 0
            ]);

            $regular1 = factory(App\User::class)->create([
                'instance_id' => $demoInstance->id,
                'mpk_id'      => $demoInstance->mpks->random(1)->first(),
                'company_id'  => $demoCompany,
                'first_name'  => 'Szymon',
                'last_name'   => 'Stefaniak',
                'email'       => '<EMAIL>',
                'phone'       => '+48 ***********',
                'avatar'      => 'https://randomuser.me/api/portraits/women/63.jpg',
                'user_id'     => $manager1->id,
                'slug'        => 's.stefaniak',
                'level'       => 7,
                'grade'       => 1,
                'erp_id'      => 0
            ]);

            $regular2 = factory(App\User::class)->create([
                'instance_id' => $demoInstance->id,
                'mpk_id'      => $demoInstance->mpks->random(1)->first(),
                'company_id'  => $demoCompany,
                'first_name'  => 'Anna',
                'last_name'   => 'Heropolitańska',
                'email'       => '<EMAIL>',
                'phone'       => '+48 ***********',
                'avatar'      => 'https://randomuser.me/api/portraits/women/63.jpg',
                'user_id'     => $manager2->id,
                'slug'        => 'a.heropolitanska',
                'level'       => 7,
                'grade'       => 1,
                'erp_id'      => 0
            ]);


            $regularGroup = \App\Group::where(['name' => 'Regular', 'instance_id' => $demoInstance->id])->first();
            $regularGroup->users()->attach([
                $regular1->id,
                $regular2->id,

            ]);

            $managerGroup = \App\Group::where(['name' => \App\User::GROUP_NAME_APPROVER, 'instance_id' => $demoInstance->id])->first();
            $managerGroup->users()->attach([
                $manager1->id,
                $manager2->id,
            ]);

            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();

            throw $exception;
        }

    }
}
