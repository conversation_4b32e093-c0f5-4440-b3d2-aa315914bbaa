<?php

class DemoSeeder extends DatabaseSeeder
{
    protected $instance;
    protected $instances;
    protected $company;
    protected $mpks;
    protected $demoInstanceMode = true;

    public function run()
    {
        DB::transaction(function () {
            try {
                $this->flushEventListeners();
                $this->mpks = collect();

//                $this->call(CurrenciesSeeder::class);
//                $this->call(CountriesSeeder::class);
                $this->instances = $this->seedInstances();

                $this->call(DemoAccountingAccountsSeeder::class);
                $this->call(DemoVatNumbersSeeder::class);
                $this->call(GroupsSeeder::class);

                $this->instances->each(function (App\Instance $instance) {

                    $this->createInstanceLocation($instance);
                    $this->seedCompany($instance);
                    $this->seedProviders($instance);
                });

                $this->call(DemoMpksSeeder::class);
                $this->call(UsersDemoSeeds::class);
                $this->call(DemoUserTestingInstanceSeeds::class);
                $this->call(DemoKpiSeeder::class);
                $this->call(DemoRuleSeeder::class);
                $this->call(DemoPermissionsSeeder::class);

                $this->call(DemoDocumentElementGroupSeeder::class);
                $this->call(DemoDocumentElementTypeSeeder::class);

                DB::commit();
            } catch (Throwable $e) {
                DB::rollBack();
                dd($e);
            }
        });


    }

    protected function seedInstances()
    {
        $this->command->getOutput()->writeln("<info>Seeding:</info> Instances");

        $instances = collect();

        $instances->push($this->seedInstance('demo.mindento.com', 'Demo', '**********'));
        $instances->push($this->seedInstance('merigo.mindento.com', 'Merigo', '**********'));
        $instances->push($this->seedInstance('mindento.mindento.com', 'Mindento', '**********'));

        return $instances;
    }

    public function seedInstance($domain, $name, $nip)
    {
        $currency = \App\Currency::where(['code' => 'PLN'])->first();
        $country = \App\Country::where(['country_code' => 'PL'])->first();

        $instance = factory(App\Instance::class)->create([
            'domain' => $domain,
            'name' => $name,
            'currency_id' => $currency,
            'post_code' => '12-345',
            'city' => 'Warszawa',
            'nip' => $nip,
            'country_id' => $country,
            'trip_agent' => '+48 22 101 33 23</br/>Emergency line: 18:00 - 08:00</br>
<a href="mailto:<EMAIL>"><EMAIL></a>',
            'modules' => [
                'compliance' => [
                    'name' => 'amrest'
                ]
            ],
            'demo_mode' => true
        ]);

        return $instance;
    }

    protected function createInstanceLocation($instance)
    {
        $this->command->getOutput()->writeln("<info>Seeding:</info> InstanceLocation for $instance->name");

        factory(\App\Location::class)->create([
            'country' => 'Polska',
            'country_code' => 'PL',
            'city' => "Warszawa",
            'province' => 'mazowieckie',
            'address' => 'ul. Mazowiecka 1',
            'lat' => 51.1117415,
            'long' => 17.0619047,
            'name' => 'Demo',
            'formatted_address' => 'Warszawa, Polska',
            'additional_data' => new \App\LocationAddress(
                'Warszawa',
                '12-123',
                'ulica',
                '7'
            ),
            'instance_id' => $instance->id,
            'column' => 'location',
            'localizable_id' => $instance->id,
            'localizable_type' => \App\Instance::RELATION_NAME,
        ]);
    }

    protected function seedCompany($instance)
    {
        $this->command->getOutput()->writeln("<info>Seeding:</info> Company for $instance->name");

        return factory(\App\Company::class)->create([
            'created_at' => \Carbon\Carbon::now(),
            'instance_id' => $instance->id,
            'name' => "Mindento",
            'nip' => '**********',
            'code' => 'PL10',
        ]);
    }

    protected function seedProviders(\App\Instance $instance): void
    {
        resolve(\Modules\Accounting\Pub\Facades\ProviderFacade::class)->create(new \App\Services\ClearInstance\CreateMindentoProviderDto($instance), $instance->users->first());
    }


    protected function findMpk($code)
    {
        return $this->mpks->filter(function ($mpk) use (&$code) {
            return $mpk->code == $code;
        })->first();
    }
}
