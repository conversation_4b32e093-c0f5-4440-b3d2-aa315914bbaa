<?php


class RequestCase2Seeder extends RequestCaseSeeder
{
    protected static $exchangeRates = [
        '12/01' => 0.1634,
        '15/01' => 0.1633,
        '16/01' => 0.1639,
        '17/01' => 0.1640,
        '18/01' => 0.1640,
    ];

    protected static $locations = [
        'warsaw' => [
            'country' => 'Polska',
            'city' => "Warszawa",
            'province' => 'mazowieckie',
            'address' => 'Nowy Świat',
            'lat' => 52.2304758,
            'long' => 21.0216325,
            'name' => 'warszawa',
            'country_code' => 'PL'
        ],
        'prague' => [
            'country' => 'Czechy',
            'city' => "Praga",
            'province' => 'praga',
            'address' => 'Ulice krecika',
            'lat' => 50.040602,
            'long' => 14.4110441,
            'name' => 'praga',
            'country_code' => 'CZ'
        ],
        'piaseczno' => [
            'country' => 'Polska',
            'city' => "Piaseczno",
            'province' => 'piaseczno',
            'address' => 'Piaseczno',
            'lat' => 52.0729413,
            'long' => 21.022871,
            'name' => 'piaseczno',
            'country_code' => 'PL'
        ],
    ];

    protected static $demo = false;

    public function run()
    {
        foreach(static::$exchangeRates as $day => $rate) {
            $date = explode('/', $day);

            factory(\Modules\ExchangeRate\Priv\Models\ExchangeRateNbp::class)->create([
                'currency_id' => $this->findCurrencyByCode('CZK'),
                'rate' => $rate,
                'effective_date' => \Carbon\Carbon::create(2018, $date[1], $date[0])->startOfDay()
            ]);
        }

        \App\Request::getStatuses()->filter(function($status) {
            return !in_array($status, [\App\Request::STATUS_DELETED, \App\Request::STATUS_TRIP]);
        })->each(function($status, $key) {
            $this->request = factory(\App\Request::class)->create([
                'instance_id' => 1,
                'slug' => 'case2_v'.$key,
                'user_id' => 1,
                'company_id' => 1,
                'mpk_id' => 1,
                'trip_starts' => '2018-01-15 16:00:00',
                'trip_ends' => '2018-01-17 18:30:00',
                'type' => \App\Request::TYPE_TRIP,
                'status' => $status,
            ]);

            $this->createLocation('warsaw', [
                'column' => 'start_location',
                'localizable_id' => $this->request,
                'localizable_type' => \App\Request::RELATION_NAME,
                'additional_data' => 'ZRH'
            ]);

            $this->createLocation('piaseczno', [
                'column' => 'end_location',
                'localizable_id' => $this->request,
                'localizable_type' => \App\Request::RELATION_NAME,
                'additional_data' => 'ZRH'
            ]);

            $this->tripDay15012018();
            $this->tripDay16012018();
            $this->tripDay17012018();
        });
    }

    protected function tripDay15012018()
    {
        $this->planeFromWarsawToPrague();
        $borderCrossing = $this->createBorderCrossing('2018-01-15 20:00:00', 'Czechy');
        $this->hotelInPrague();

        $travelExpenses = factory(App\RequestMealDeduction::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'date' => '2018-01-15 00:00:00',
            'breakfast' => false,
            'lunch' => false,
            'dinner' => false,
            'accomodation' => true,
            'drive' => false
        ]);
    }

    protected  function planeFromWarsawToPrague()
    {
        $planeTrip = factory(App\PlaneTrip::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'flight_class' => \App\PlaneTrip::CLASS_FIRST,
            'arrival_at' => '2018-01-15 16:00:00',
            'return_at' => '2018-01-17 16:30:00',
            'amount_currency_id' => $this->findCurrencyByCode('PLN')->id,
            'amount' => 800,
            'round_trip' => true,
            'access_to' => true,
            'access_from' => false,
        ]);
        $departureLocation = $this->createLocation('warsaw', [
            'column' => 'flight_from',
            'localizable_id' => $planeTrip->id,
            'localizable_type' => \App\PlaneTrip::RELATION_NAME
        ]);

        $destinationLocation = $this->createLocation('prague', [
            'column' => 'flight_to',
            'localizable_id' => $planeTrip->id,
            'localizable_type' => \App\PlaneTrip::RELATION_NAME
        ]);

        $planeDocument = factory(App\Document::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'user_id' => 1,
            'type' => \App\Document::TYPE_ACCOUNTING,
            'provider_id' => null,
            'document_number' => '87t97rt9ewt',
            'currency_id' => $this->findCurrencyByCode('PLN')->id,
            'gross' => 800,
            'exchange_rate' => 1,
            'issue_date' => '2018-01-15 16:00:00',
            'received_date' => '2018-01-15 16:00:00',
            'accounting_date' => '2018-01-15 16:00:00',
            'payment' => \App\Document::PAYMENT_TYPE_SERVICE_CARD,
            'accounting_type' => \App\Document::ACCOUNTING_TYPE_INVOICE
        ]);

        $type = $this->findElementTypeByName('Przeloty');
        $documentElement = $this->createDocumentElement($type, $planeDocument->id, 800);
    }

    protected function hotelInPrague() {
        $installment = factory(App\Installment::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'amount' => 2000,
            'currency_id' => $this->findCurrencyByCode('CZK')->id,
        ]);

        $accommodation = factory(App\Accomodation::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'arrival_at' => '2018-01-15 20:00:00',
            'departure_at' => '2018-01-16 08:00:00',
            'breakfast' => false,
            'amount' => 1700,
            'amount_currency_id' => $this->findCurrencyByCode('CZK')->id,
        ]);

        $location = $this->createLocation('prague', [
            'column' => 'location',
            'localizable_id' => $accommodation->id,
            'localizable_type' => \App\Accomodation::RELATION_NAME
        ]);

        $type = $this->findElementTypeByName('Hotel');
        $document = factory(\App\Document::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'user_id' => 1,
            'type' => \App\Document::TYPE_ACCOUNTING,
            'provider_id' => null,
            'document_number' => '3466433',
            'currency_id' => $this->findCurrencyByCode('CZK')->id,
            'gross' => 1700,
            'issue_date' => '2018-01-15 20:00:00',
            'received_date' => '2018-01-15 20:00:00',
            'accounting_date' => '2018-01-15 20:00:00',
            'exchange_rate' => static::$exchangeRates['12/01'],
            'payment' => \App\Document::PAYMENT_TYPE_OWN,
            'accounting_type' => \App\Document::ACCOUNTING_TYPE_INVOICE
        ]);
        $documentElement = $this->createDocumentElement($type, $document->id, 1700);
    }

    protected function tripDay16012018()
    {
        $travelExpenses = factory(App\RequestMealDeduction::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'date' => '2018-01-16 00:00:00',
            'breakfast' => false,
            'lunch' => false,
            'dinner' => false,
            'accomodation' => false,
            'drive' => false
        ]);
    }

    protected function tripDay17012018()
    {
        $borderCrossing = $this->createBorderCrossing('2018-01-17 17:30:00', 'Polska');
        $this->trainFromWarsawToPiaseczno();
        $travelExpenses = factory(App\RequestMealDeduction::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'date' => '2018-01-17 00:00:00',
            'breakfast' => false,
            'lunch' => false,
            'dinner' => false,
            'accomodation' => false,
            'drive' => true
        ]);
    }

    protected function trainFromWarsawToPiaseczno()
    {
        $train = factory(App\TrainTrip::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'departure_at' => '2018-01-17 18:30:00',
            'amount' => 10,
            'amount_currency_id' => $this->findCurrencyByCode('PLN')->id,
            'access_to' => false,
            'access_from' => false,
        ]);
        $departureLocation = $this->createLocation('warszawa', [
            'column' => 'departure_location',
            'localizable_id' => $train->id,
            'localizable_type' => \App\TrainTrip::RELATION_NAME
        ]);

        $destinationLocation = $this->createLocation('piaseczno', [
            'column' => 'destination_location',
            'localizable_id' => $train->id,
            'localizable_type' => \App\TrainTrip::RELATION_NAME
        ]);

        $document = factory(App\Document::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'user_id' => 1,
            'type' => \App\Document::TYPE_ACCOUNTING,
            'provider_id' => null,
            'document_number' => '2300023',
            'currency_id' => $this->findCurrencyByCode('PLN')->id,
            'gross' => 15,
            'issue_date' => '2018-01-17 18:30:00',
            'received_date' => '2018-01-17 18:30:00',
            'accounting_date' => '2018-01-17 18:30:00',
            'exchange_rate' => 1,
            'payment' => \App\Document::PAYMENT_TYPE_OWN,
            'accounting_type' => \App\Document::ACCOUNTING_TYPE_RECEIPT
        ]);

        $type = $this->findElementTypeByName('Pociąg');
        $documentElement = $this->createDocumentElement($type, $document->id, 15);
    }
}