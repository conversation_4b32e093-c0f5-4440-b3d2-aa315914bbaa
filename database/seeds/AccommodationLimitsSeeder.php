<?php

use Illuminate\Database\Seeder;

class AccommodationLimitsSeeder extends Seeder
{
    public function run()
    {
        $countries = \App\Country::all();
        $currencies = \App\Currency::all();

        $defaultExpenseLimits = collect(config('default-expenses'));

        $defaultExpenseLimits->each(function($expense) use (&$countries, &$currencies) {
            $currency = $currencies->filter(function($item) use (&$expense) {
                return $item->code == $expense['currency'];
            })->first();

            $country = $countries->filter(function($item) use (&$expense) {
                return $item->polish_name == $expense['country'];
            })->first();

            if($currency)
            {
                $attributes = [
                    'country_id' => $country,
                    'currency_id' => $currency,
                    'accommodation_limit' => $expense['accommodation_limit'],
                ];

                if(isset($expense['start_date'])) {
                    $attributes['start_date'] = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $expense['start_date']);
                }

                if(isset($expense['end_date'])) {
                    $attributes['end_date'] = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $expense['end_date']);
                }

                factory(App\AccommodationLimit::class, 1)->create($attributes);
            }
        });
    }
}