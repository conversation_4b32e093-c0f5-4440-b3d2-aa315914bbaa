<?php

use App\Instance;
use Illuminate\Database\Seeder;

class CompaniesSeeder extends Seeder {
    public function run() {
        switch (App::environment()) {
            case 'local':
                $this->seedLocal();
                break;

            case 'testing':
                $this->seedTesting();
                break;
        }
    }

    protected function seedLocal(): void
    {
        $instances = Instance::all();

        $company = factory(App\Company::class)->create([
            'instance_id' => $instances->first()->id,
            'name' => 'Mindento Local Company 1',
            'code' => '111'
        ]);

        $this->createCompanyLocation($company);

        $company = factory(App\Company::class)->create([
            'instance_id' => $instances->last()->id,
            'name' => 'Mindento Local Company 2',
            'code' => '222'
        ]);

        $this->createCompanyLocation($company);
    }

    protected function seedTesting(): void
    {
        Instance::all()->each(function (Instance $instance) {
            foreach(['A', 'B'] as $companyLetter) {
                $company = factory(App\Company::class)->create([
                    'instance_id' => $instance->id,
                    'name' => sprintf('Mindento Testing Company %s%s', $instance->id, $companyLetter),
                    'code' => rand(1, 10000)
                ]);

                $this->createCompanyLocation($company);
            }
        });
    }

    protected function createCompanyLocation(\App\Company $company) {
        factory(\App\Location::class)->create([
            'country' => 'Polska',
            'country_code' => 'PL',
            'city' => "Warszawa",
            'province' => 'mazowieckie',
            'address' => 'Nowy Świat',
            'lat' => 52.2304758,
            'long' => 21.0216325,
            'name' => 'warszawa',
            'formatted_address' => 'Warszawa, Polska',
            'instance_id' => $company->instance_id,
            'column' => 'location',
            'localizable_id' => $company->id,
            'localizable_type' => \App\Company::RELATION_NAME,
            'additional_data' => (new \App\LocationAddress(
                'Warszawa',
                '00-496',
                'Nowy świat',
                5,
                11
            ))->toArray()
        ]);
    }
}
