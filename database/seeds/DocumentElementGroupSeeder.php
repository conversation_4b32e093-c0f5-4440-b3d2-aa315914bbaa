<?php

use Illuminate\Database\Seeder;

class DocumentElementGroup<PERSON>eeder extends Seeder
{
    protected $groupNames;
    protected $instancesCollection;

    public function __construct()
    {
        $this->groupNames = collect(config('vaterval.document_element_group_names'));
        $this->instancesCollection = \App\Instance::with('documentElementTypes')->get();
    }

    public function run()
    {
        $this->instancesCollection->each(function ($instance) {
            $this->groupNames->each(function ($group) use (&$instance) {
                $group['instance_id'] = $instance;
                factory(\Modules\Accounting\Priv\Entities\DocumentElementGroup::class)->create($group);
            });
        });
    }
}