<?php


use Illuminate\Database\Seeder;

class FilledDocumentSeeder extends Seeder
{
    protected $instanceId = 1;

    public function run()
    {
        $country = App\Country::where('name', '=', 'Niemcy')->first();
        $provider =  factory(App\Provider::class)->create([
            'instance_id' => $this->instanceId,
            'country_id' => $country,
            'name' => 'AIR BERLIN GMBH',
            'erp_id' => 213,
            'address' => 'BerlinStrasse 13 Berlin',
            'postcode' => '44WLZ',
            'registry_number' => 'DE-525-25-40-187'
        ]);


        $request = App\Request::with(['user'])->where(['slug' => 'aa'])->first();
        $currency = App\Currency::where(['code'=> 'EUR'])->first();

        $document = factory(\App\Document::class)->create([
            'id' => 737,
            'instance_id' => $this->instanceId,
            'request_id' => $request,
            'type' => \App\Document::getTypes()->random(1)->first(),
            'user_id' => $request->user,
            'status' => \App\Document::STATUS_PROCESSED,
            'provider_id' => $provider,
            'document_number' => 'FV-3422/2355',
            'issue_date' => \Carbon\Carbon::create(2017, 04, 04),
            'received_date' => \Carbon\Carbon::create(2017, 04, 04),
            'accounting_date' => \Carbon\Carbon::create(2017, 04, 04),
            'annotation' => 'Flight service',
            'gross' => 456.00,
            'currency_id' => $currency,
            'exchange_rate' => 4.32,
            'payment' => \App\Document::PAYMENT_TYPE_SERVICE_CARD,
            'accounting_type' => \App\Document::ACCOUNTING_TYPE_INVOICE,
        ]);

        $accomodationType = Modules\Accounting\Priv\Entities\DocumentElementType::with(['vatNumber'])->where(['instance_id' => $this->instanceId, 'name' => 'Hotel'])->first();
        $accomodationElement = factory(App\DocumentElement::class)->create([
            'instance_id' => $this->instanceId,
            'accounting_account_id' => $accomodationType->vatNumber->accounting_account_id,
            'vat_number_id' => $accomodationType->vat_number_id,
            'type_id' => $accomodationType->id,
            'document_id' => $document->id,
            'gross' => rand(1,150),
            'cost_of_earning' => 1,
            'asset' => 0,
            'mpk_id' => $request->mpk_id,
        ]);

        $representationType = Modules\Accounting\Priv\Entities\DocumentElementType::with(['vatNumber'])->where(['instance_id' => $this->instanceId, 'name' => 'Spotkanie wewnętrzne'])->first();
        $representationElement = factory(App\DocumentElement::class)->create([
            'instance_id' => $this->instanceId,
            'accounting_account_id' => $representationType->vatNumber->accounting_account_id,
            'vat_number_id' => $representationType->vat_number_id,
            'type_id' => $representationType->id,
            'document_id' => $document->id,
            'gross' => rand(1,150),
            'cost_of_earning' => 1,
            'asset' => 0,
            'mpk_id' => $request->mpk_id,
        ]);
    }
}