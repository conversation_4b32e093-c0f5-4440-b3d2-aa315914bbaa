<?php

use Illuminate\Database\Migrations\Migration;

class AddNewRegularTripPermission extends Migration
{
    public function up(): void
    {
        $now = \Carbon\Carbon::now();

        $permissions = [];
        $permissionsFile = config('permissions');

        \App\Group::all()->each(function (\App\Group $group) use (&$permissions, $now, $permissionsFile) {
            $permissions[] = [
                'created_at' => $now,
                'updated_at' => $now,
                'group_id' => $group->id,
                'ability' => \App\Permission::REGULAR_TRIP,
                'scope' => null,
                'name' => \Illuminate\Support\Str::random(),
                'can' => $permissionsFile['default_permissions'][$group->name][\App\Permission::REGULAR_TRIP],
                'instance_id' => $group->instance_id
            ];
        });

        \App\Permission::insert($permissions);
    }

    public function down(): void
    {
        \App\Permission::where('ability', \App\Permission::REGULAR_TRIP)->delete();
    }
}
