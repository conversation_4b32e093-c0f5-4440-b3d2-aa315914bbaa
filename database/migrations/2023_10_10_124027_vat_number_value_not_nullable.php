<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use App\DocumentElement;
use App\Document;

class VatNumberValueNotNullable extends Migration
{
    public function up(): void
    {
        $elements = DocumentElement::query()
            ->withTrashed()
            ->select('document_elements.*')
            ->distinct()
            ->join('documents', 'documents.id', '=', 'document_elements.document_id')
            ->join('requests', 'requests.id', '=', 'documents.request_id')
            ->join('document_element_types', 'document_element_types.id', '=', 'document_elements.type_id')
            ->join('vat_numbers', 'vat_numbers.id', '=', 'document_element_types.vat_number_id')
            ->where('requests.status', 'accounting')
            ->where('document_elements.gross', '>', 0)
            ->whereNull('vat_numbers.value')
            ->whereNull('document_elements.net')
            ->whereNull('document_elements.tax')
            ->get();

        $documents = Document::query()
            ->withTrashed()
            ->whereIn('id', $elements->pluck('document_id')->unique()->toArray());

        \Modules\Accounting\Priv\Entities\VatNumber::query()->whereNull('value')->update(['value' => 0]);

        $elements->each(function (DocumentElement $document) {
            $document->recalculateNet();
            $document->recalculateTax();
            $document->saveQuietly();
        });


        $documents->each(function (Document $document) {
            $document->recalculate();
            $document->saveQuietly();
        });

        Schema::table(
            'vat_numbers',
            function (Blueprint $table) {
                $table->decimal('value', 6, 3)->nullable(false)->change();
            }
        );
    }

    public function down(): void
    {
        Schema::table(
            'vat_numbers',
            function (Blueprint $table) {
                $table->decimal('value', 6, 3)->nullable()->change();
            }
        );
    }
}
