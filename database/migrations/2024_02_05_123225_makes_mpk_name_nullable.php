<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MakesMpkNameNullable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('mpks', function (Blueprint $table) {
            $table->string('name')->nullable()->change();
        });

        DB::table('mpks')
            ->whereRaw('name = code')
            ->update(['name' => null]);

        DB::table('mpks')
            ->where('name', '-')
            ->update(['name' => null]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        DB::table('mpks')
            ->whereRaw('name = code')
            ->update(['name' => DB::raw('code')]);

        Schema::table('mpks', function (Blueprint $table) {
            $table->string('name')->nullable(false);
        });
    }
}
