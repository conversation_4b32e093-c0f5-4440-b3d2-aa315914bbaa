<?php
class DocumentChanges extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('document_changes', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->instance();
            $table->document();
            $table->user(true);

            $table->longText('changes');
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('document_changes');
    }
}
