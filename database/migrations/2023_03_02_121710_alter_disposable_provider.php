<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterDisposable<PERSON>rovider extends Migration
{
    public function up()
    {
        Schema::table('provider_disposables', function (Blueprint $table) {
            $table->integer('company_id')->unsigned()->nullable()->change();
            $table->string('erp_id', 100)->after('tax_id');
        });
    }

    public function down()
    {
        Schema::table('provider_disposables', function (Blueprint $table) {
            $table->integer('company_id')->unsigned()->change();
            $table->dropColumn('erp_id');
        });
    }
}
