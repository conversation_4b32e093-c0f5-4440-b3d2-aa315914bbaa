<?php

class Projects extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('projects', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->instance();
            $table->user();

            $table->string('code');
            $table->string('name');
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('projects');
    }
}
