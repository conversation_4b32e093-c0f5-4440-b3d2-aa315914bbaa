<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSlugToCompanies extends Migration
{
    /**
     * @var \App\Services\SlugGeneratorService
     */
    protected $slugGenerator;

    /**
     * ProjectChanges constructor.
     * @param \App\Services\SlugGeneratorService $slugGenerator
     */
    public function __construct()
    {
        $this->slugGenerator = resolve(\App\Services\SlugGeneratorService::class);
    }

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->string('slug', 36)->after('id');
        });

        \App\Company::all()->each(function (\App\Company $company) {
            $company->slug = $this->slugGenerator->generate();

            $company->save();
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->string('slug', 36)->after('id')->comment('')->nullable(false)->change();
            $table->unique('slug', 'companies_slug_index');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->integer('updated_by')->unsigned()->index()->nullable(true)->after('created_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropIndex('companies_slug_index');
            $table->dropColumn('slug');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('updated_by');
        });
    }
}
