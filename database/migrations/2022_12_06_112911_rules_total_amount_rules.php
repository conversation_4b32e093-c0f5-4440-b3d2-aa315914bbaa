<?php

use App\Compliance\Instances\Amrest\Rules\TotalSettledAmountRule;
use App\Compliance\Instances\Amrest\Rules\TotalRequestedAmountRule;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use App\Rule;
use App\Instance;

class RulesTotalAmountRules extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Instance::withTrashed()->get()->each(
            function (Instance $instance) {
                $rules = $this->getRules($instance);

                $rules->each(
                    function ($rule) use ($instance) {
                        $instanceRule = Rule::where('instance_id', $instance->id)
                            ->where('name', $rule['name'])
                            ->first();

                        if (!$instanceRule) {
                            $rule['instance_id'] = $instance;
                            factory(Rule::class)->create($rule);
                        }
                    }
                );
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Rule::where('name', TotalSettledAmountRule::NAME)->delete();
        Rule::where('name', TotalRequestedAmountRule::NAME)->delete();

    }

    protected function getRules(\App\Instance $instance): \Illuminate\Support\Collection
    {
        $parameters = [];
        for($i = 0; $i <= $instance->max_grade_value; $i++) {
            $parameters["grade-{$i}"] = ['maxAmount' => 99999999];
        }

        return collect([
            [
                'level' => Rule::LEVEL_WARNING,
                'name' => TotalSettledAmountRule::NAME,
                'parameters' => $parameters,
            ],
            [
                'level' => Rule::LEVEL_WARNING,
                'name' => TotalRequestedAmountRule::NAME,
                'parameters' => $parameters,
            ],

        ]);
    }
}
