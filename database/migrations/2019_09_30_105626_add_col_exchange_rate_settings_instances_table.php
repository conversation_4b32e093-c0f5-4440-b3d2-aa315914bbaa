<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddColExchangeRateSettingsInstancesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('instances', function (Blueprint $table) {

            $driver = Schema::connection($this->getConnection())->getConnection()->getDriverName();

            if ('sqlite' === $driver) {
                $table->string('exchange_rate_settings')->default('');
            } else {
                $table->text('exchange_rate_settings')->after('accounting_reminder_settings');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('instances', function (Blueprint $table) {
            $table->dropColumn('exchange_rate_settings');
        });
    }
}
