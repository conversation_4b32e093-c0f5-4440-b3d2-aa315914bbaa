<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RegularExpensePermission extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /** @var Collection $groups */
        $groups = \App\Group::where([])->get();
        $groups->each(function(\App\Group $group) {
            $permission = new \App\Permission();
            $permission->group_id = $group->id;
            $permission->ability = \App\Permission::REGULAR_REQUEST_EXPENSE;
            $permission->name = str_random(16);
            $permission->can = true;
            $permission->instance_id = $group->instance_id;
            $permission->save();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        \App\Permission::where('ability', \App\Permission::REGULAR_REQUEST_EXPENSE)->delete();
    }
}
