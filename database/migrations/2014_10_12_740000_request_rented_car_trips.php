<?php

class RequestRentedCarTrips extends \App\Vendors\Migration
{
    public function up()
    {
        $this->getSchema()->create('request_rented_car_trips', function (\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->softDeletes();

            $table->instance();
            $table->request();
            $table->documentElementType();

            $table->dateTime('pickup_at');
            $table->dateTime('return_at');

            //rent cost
            $table->amount(true, 'rent_cost');
            $table->amountCurrency(true, 'rent_cost_currency_id');

            //fuel cost
            $table->amount(true, 'fuel_cost');
            $table->amountCurrency(true, 'fuel_cost_currency_id');

            //other costs
            $table->amount(true, 'other_costs_amount');
            $table->amountCurrency(true, 'other_costs_currency_id');

            //requested cost
            $table->amount(true);
            $table->amountCurrency(true);

            $table->weight();
        });
    }

    public function down()
    {
        $this->getSchema()->dropIfExists('request_rented_car_trips');
    }
}
