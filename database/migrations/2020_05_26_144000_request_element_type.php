<?php

use App\Vendors\Blueprint;
use App\Vendors\Migration;

class RequestElementType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('request_element_type', function (Blueprint $table) {
            $table->increments('id');
            $table->string('slug', 36);
            $table->string('name');
            $table->timestamps();
            $table->unique('slug', 'request_element_type_unique');
        });

        $types = [
            \App\Accomodation::RELATION_NAME,
            \App\BusTrip::RELATION_NAME,
            \App\CompanyCarTrip::FUEL_DOCUMENT_TYPE,
            \App\CompanyCarTrip::OTHER_COSTS_DOCUMENT_TYPE,
            \App\CompanyCarTrip::RELATION_NAME,
            \App\FerryBoatTrip::RELATION_NAME,
            \App\PlaneTrip::RELATION_NAME,
            \App\RentedCarTrip::RELATION_NAME,
            \App\TrainTrip::RELATION_NAME,
        ];

        $slugGeneratorService = resolve(\App\Services\SlugGeneratorService::class);
        foreach ($types as $type) {
            DB::table('request_element_type')->insert([
                'slug' => $slugGeneratorService->generate(),
                'name' => $type,
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now()
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $this->getSchema()->dropIfExists('request_element_type');
    }
}
