<?php

use App\Vendors\Migration;
use App\Vendors\Blueprint;

class AddRequestedLumpSumsColumnToRequestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->table('requests', function (Blueprint $table) {
            $table->string('requested_lump_sums')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $this->getSchema()->table('requests', function (Blueprint $table) {
            $table->dropColumn('requested_lump_sums');
        });
    }
}
