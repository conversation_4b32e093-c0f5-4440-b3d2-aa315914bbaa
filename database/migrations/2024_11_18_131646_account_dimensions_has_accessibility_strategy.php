<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Analytics\Priv\Enums\AccessibilityEnum;

class AccountDimensionsHasAccessibilityStrategy extends Migration
{
    const ALT_INSTANCE_ID = 78;

    public function up(): void
    {
        Schema::table('account_dimensions', static function (Blueprint $table) {
            $table->addColumn('string', 'accessibility', ['null' => true, 'length' => 255])->nullable()->default(null);
        });

        DB::table('account_dimensions')
            ->where(['code' => 'Refaktura', 'instance_id' => self::ALT_INSTANCE_ID])
            ->update(
                [
                    'accessibility' => AccessibilityEnum::EDITABLE_ON_WAITING_FOR_ACCEPTANCE()->getValue(),
                ],
            );
    }

    public function down(): void
    {
        Schema::table('account_dimensions', static function (Blueprint $table) {
            $table->dropColumn('accessibility');
        });
    }
}
