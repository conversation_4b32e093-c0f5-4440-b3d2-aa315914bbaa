<?php

use Illuminate\Database\Migrations\Migration;
use App\Instance;
use App\Company;
use App\Location;

class AddingCompanyAddress extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Instance::all()->each(function (Instance $instance) {
            $instanceLocation = $instance->location;
            $instance->companies->each(function (Company $company) use ($instanceLocation) {
                $companyLocation = $instanceLocation->replicate([
                    'localizable_id',
                    'localizable_type'
                ]);

                $company->location()->save($companyLocation);
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Instance::all()->each(function (Instance $instance) {
            $instance->companies->each(function (Company $company) {
                $company->location()->delete();
            });
        });
    }
}
