<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\TripPlanner\Pub\Enums\RequestElementAcceptanceSourceEnum;

class AddAcceptStatusOnRequestElements extends Migration
{
    private const TABLES = [
        'request_accomodations',
        'request_bus_trips',
        'request_company_car_trips',
        'request_ferry_boats',
        'request_plane_trips',
        'request_private_accomodations',
        'request_private_car_trips',
        'request_rented_car_trips',
        'request_train_trips',
        'request_unrequested_elements',
        'target_points',
    ];

    public function up(): void
    {
        collect(static::TABLES)
            ->each(function (string $table) {
                Schema::table($table, function (Blueprint $table) {
                    $table->enum(
                        'request_element_acceptance_source',
                        RequestElementAcceptanceSourceEnum::toArray()
                    )
                        ->after('request_id')
                        ->default((string)RequestElementAcceptanceSourceEnum::PRIMARY());
                });
            });
    }

    public function down(): void
    {
        collect(static::TABLES)
            ->each(function (string $table) {
                Schema::table($table, function (Blueprint $table) {
                    $table->dropColumn('request_element_acceptance_source');
                });
            });
    }
}
