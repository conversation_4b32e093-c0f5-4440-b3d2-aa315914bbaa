<?php

use App\Instance;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRequestTypeCriterionForAccountDimensionItem extends Migration
{
    public function up(): void
    {
        Schema::table('account_dimension_items', function (Blueprint $table) {
            $table->string('request_type', 20)->nullable();
        });

        $instance = Instance::query()->where('name', 'LIKE', '%Artifex%')->first();
        if ($instance) {
            DB::table('account_dimension_items')
                ->where('code', 'C1.4')
                ->where('name', 'Back Office - HR')
                ->where('instance_id', 88)
                ->update(['request_type' => 'trip']);
        }

    }

    public function down(): void
    {
        Schema::table('account_dimension_items', function (Blueprint $table) {
            $table->dropColumn('request_type');
        });
    }
}
