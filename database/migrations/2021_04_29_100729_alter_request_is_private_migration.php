<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;

class AlterRequestIsPrivateMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \Illuminate\Support\Facades\DB::beginTransaction();

        Schema::table('requests', function (Blueprint $table) {
            $table->boolean('private')->default(false)->after('type');
        });

        \App\Instance::withTrashed()->get()->each(function(\App\Instance $instance) {
            $instance->groups->each(function(\App\Group $group) {
                factory(\App\Permission::class)->create([
                    'ability' => \App\Permission::PRIVATE_REQUEST_ACCEPTOR,
                    'group_id' => $group->id,
                    'can' => 0,
                    'instance_id' => $group->instance_id
                ]);
            });
        });

        $permissions = collect(config('permissions.default_permissions')[\App\User::GROUP_NAME_HR]);
        \App\Instance::withTrashed()->get()->each(function(\App\Instance $instance) use ($permissions) {

            /** @var \App\Group $group */
            $instanceId = $instance->id;
            $group = factory(\App\Group::class)->create([
                'name' => \App\User::GROUP_NAME_HR,
                'slug' => strtolower(\App\User::GROUP_NAME_HR),
                'visible' => 1,
                'instance_id' => $instanceId,
                'type' => \Modules\Users\Pub\Enums\GroupTypeEnum::MANUAL(),
            ]);

            $groupId = $group->id;

            $permissions->each(function ($value, $key) use ($groupId, $instanceId) {
                factory(\App\Permission::class)->create([
                    'ability' => $key,
                    'group_id' => $groupId,
                    'can' => $value,
                    'instance_id' => $instanceId
                ]);
            });
        });

        factory(Feature::class)->create([
            'slug' => FeatureEnum::FEATURE_PRIVATE_TRIP_REQUESTS_ENABLED(),
            'type' => FeatureTypeEnum::SWITCH(),
            'default' => '0',
        ]);

        \Illuminate\Support\Facades\DB::commit();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \Illuminate\Support\Facades\DB::beginTransaction();

        Schema::table('requests', function (Blueprint $table) {
            $table->dropColumn('private');
        });

        \App\Permission::where('ability', \App\Permission::PRIVATE_REQUEST_ACCEPTOR)->delete();
        \App\Group::where('name', \App\User::GROUP_NAME_HR)->delete();
        Feature::where('slug', FeatureEnum::FEATURE_PRIVATE_TRIP_REQUESTS_ENABLED())->delete();

        \Illuminate\Support\Facades\DB::commit();
    }
}
