<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCancelableColumnsToOffersTable extends Migration
{
    public function up(): void
    {
        Schema::table('offers', function (Blueprint $table) {
            $table->boolean('cancellable')->default(false)->after('status');
            $table->dateTime('cancel_date')->nullable()->after('status');
        });
    }

    public function down(): void
    {
        Schema::table('offers', function (Blueprint $table) {
            $table->dropColumn('cancellable');
            $table->dropColumn('cancel_date');
        });
    }
}
