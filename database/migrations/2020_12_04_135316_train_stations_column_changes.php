<?php

declare(strict_types=1);

use App\TraveltechCity;
use App\TraveltechStation;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class TrainStationsColumnChanges extends Migration
{
    public function up(): void
    {
        Schema::table('traveltech_cities', function (Blueprint $table) {
            $table->renameColumn('name', 'name_pl');
            $table->string('name_en')->after('name')->nullable();
        });
        Schema::table('traveltech_stations', function (Blueprint $table) {
            $table->renameColumn('name', 'name_pl');
            $table->string('name_en')->after('name')->nullable();
        });

        TraveltechCity::all()->each(function (TraveltechCity $traveltechCity) {
            $traveltechCity->name_en = \App\Helpers\CsvHelper::removePolishChars($traveltechCity->name_pl);
            $traveltechCity->save();
        });

        TraveltechStation::all()->each(function (TraveltechStation $traveltechStation) {
            $traveltechStation->name_en = \App\Helpers\CsvHelper::removePolishChars($traveltechStation->name_pl);
            $traveltechStation->save();
        });
    }

    public function down(): void
    {
        Schema::table('traveltech_cities', function (Blueprint $table) {
            $table->renameColumn('name_pl', 'name');
            $table->dropColumn('name_en');
        });
        Schema::table('traveltech_stations', function (Blueprint $table) {
            $table->renameColumn('name_pl', 'name');
            $table->dropColumn('name_en');
        });
    }
}
