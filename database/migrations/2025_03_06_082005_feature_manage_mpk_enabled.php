<?php

use App\Instance;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Priv\Entities\FeatureSetting;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;

class FeatureManageMpkEnabled extends Migration
{
    public function up(): void
    {
        $feature = new Feature();
        $feature->slug = FeatureEnum::FEATURE_MANAGE_MPK_ENABLED();
        $feature->type = FeatureTypeEnum::SWITCH();
        $feature->default = '1';
        $feature->save();

        $instances = Instance::query()
            ->where('name', 'LIKE', '%Artifex%')
            ->orWhere('name', 'LIKE', '%HRO Personnel%')
            ->orWhere('name', 'LIKE', '%Lingaro%')
            ->get();

        foreach ($instances as $instance) {
            $featureSetting = new FeatureSetting();
            $featureSetting->feature_id = $feature->id;
            $featureSetting->instance_id = $instance->id;;
            $featureSetting->setting = '0';
            $featureSetting->save();
        }
    }

    public function down(): void
    {
    }
}
