<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DocumentEprVatAtRename extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->timestamp('vat_date')->nullable()->after('received_date');
        });

        \DB::statement("UPDATE documents SET documents.vat_date = documents.erp_vat_at");

        Schema::table('documents', function (Blueprint $table) {
            $table->dropColumn('erp_vat_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->timestamp('erp_vat_at')->nullable()->after('erp_accounted_at');
        });

        \DB::statement("UPDATE documents SET documents.erp_vat_at = documents.vat_date");

        Schema::table('documents', function (Blueprint $table) {
            $table->dropColumn('vat_date');
        });
    }
}
