<?php

use App\Compliance\Instances\Amrest\Rules\WarningLocationAccommodationRule;
use App\Instance;
use App\Rule;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SetupWarningLocationRuleForClg extends Migration
{
    public function up(): void
    {
        $instance = Instance::query()->where('name', 'LIKE', '%Culligan%')->first();

        if ($instance) {
            Rule::updateOrInsert(
                [
                    'instance_id' => $instance->id,
                    'name' => WarningLocationAccommodationRule::NAME,
                ],
                [
                    'level' => Rule::LEVEL_WARNING,
                    'parameters' => json_encode($this->getParameters()),
                ]
            );
        }
    }

    public function down(): void
    {
        //
    }

    private function getParameters(): array
    {
        $parameters = [
            'default' => 500,
            'PL' => [
                'default' => [
                    '1_room' => 300,
                ],
                'groups' => [
                    'group_400' => [
                        'locations' => [
                            [ // Warszawa
                                'northeast' => ["lat" => 52.368, "lng" => 21.271],
                                'southwest' => ["lat" => 52.098, "lng" => 20.851],
                            ],
                        ],
                        '1_room' => 400,
                        '2_room' => 400,
                    ],
                ]
            ],
        ];

        $gradeParameters = [
            'grade-0' => $parameters,
        ];

        return $gradeParameters;
    }
}
