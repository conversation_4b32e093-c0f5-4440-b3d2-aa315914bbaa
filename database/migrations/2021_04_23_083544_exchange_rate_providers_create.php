<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ExchangeRateProvidersCreate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exchange_rate_providers', function (Blueprint $table) {
            $table->char('slug', 3)->primary();
            $table->string('name');
            $table->integer('currency_id')->unsigned()->index()->nullable(false);
            $table->foreign('currency_id')->references('id')->on('currencies')->onDelete('cascade');
            $table->boolean('is_active')->nullable(false);
            $table->timestamps();
        });

        Schema::rename('exchange_rates', 'exchange_rates_nbp');

        Schema::create('exchange_rates_ecb', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamp('effective_date')->nullable(true);
            $table->integer('currency_id')->unsigned()->index()->nullable(false);
            $table->foreign('currency_id')->references('id')->on('currencies')->onDelete('restrict');

            $table->decimal('rate', 14, 8);
            $table->timestamps();
        });


        Schema::table('instances', function (Blueprint $table) {
            $table->char('exchange_rate_provider', 3)->after('currency_id');
        });


        $pln = \App\Currency::where('code', '=', 'PLN')->first();
        if($pln !== null) {
            /** @var \Modules\ExchangeRate\Priv\Models\ExchangeRateProvider $exchangeRateProvider */
            $exchangeRateProvider = factory(\Modules\ExchangeRate\Priv\Models\ExchangeRateProvider::class)->create([
                'slug' => 'nbp',
                'name' => 'NBP',
                'currency_id' => $pln->id,
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now(),
            ]);

            DB::statement('update instances set exchange_rate_provider=?', [$exchangeRateProvider->slug]);
        }


        Schema::table('instances', function (Blueprint $table) {
            $table->foreign('exchange_rate_provider')->references('slug')->on('exchange_rate_providers')->onDelete('restrict');
        });

        /** @var \Modules\ExchangeRate\Priv\Models\ExchangeRateProvider $exchangeRateProvider */
        $eur = \App\Currency::where('code', '=', 'EUR')->first();
        if($eur !== null) {
            $exchangeRateProvider = factory(\Modules\ExchangeRate\Priv\Models\ExchangeRateProvider::class)->create([
                'slug' => 'ecb',
                'name' => 'European Central Bank',
                'currency_id' => $eur->id,
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('instances', function (Blueprint $table) {
            $table->dropForeign('instances_exchange_rate_provider_foreign');
            $table->dropColumn('exchange_rate_provider');
        });

        Schema::rename('exchange_rates_nbp', 'exchange_rates');
        Schema::drop('exchange_rates_ecb');

        Schema::drop('exchange_rate_providers');
    }
}
