<?php

use Illuminate\Database\Migrations\Migration;

class ChangeBoardOfDirectorsGroupNaming extends Migration
{
    private const OLD_GROUP_SLUG = 'boardofdirectors';
    private const OLD_GROUP_NAME = 'BoardOfDirectors';
    private const NEW_GROUP_SLUG = 'boardmember';
    private const NEW_GROUP_NAME = 'BoardMember';

    public function up(): void
    {
        \App\Group::query()
            ->where('slug', self::OLD_GROUP_SLUG)
            ->update([
                'slug' => self::NEW_GROUP_SLUG,
                'name' => self::NEW_GROUP_NAME
            ]);
    }

    public function down(): void
    {
        \App\Group::query()
            ->where('slug', self::NEW_GROUP_SLUG)
            ->update([
                'slug' => self::OLD_GROUP_SLUG,
                'name' => self::OLD_GROUP_NAME
            ]);
    }
}
