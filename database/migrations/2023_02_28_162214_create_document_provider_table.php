<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDocumentProviderTable extends Migration
{
    public function up()
    {
        Schema::create('document_providers', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('document_id');
            $table->unsignedInteger('provider_id')->nullable();
            $table->string('country_code', 2);
            $table->string('name', 100);
            $table->string('tax_id', 100);
            $table->string('address',100);
            $table->string('city', 100);
            $table->string('postcode', 30);
            $table->timestamps();

            $table->foreign('document_id')->references('id')->on('documents')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('document_providers');
    }
}
