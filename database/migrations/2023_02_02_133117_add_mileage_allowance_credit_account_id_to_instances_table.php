<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddMileageAllowanceCreditAccountIdToInstancesTable extends Migration
{
    public function up(): void
    {
        Schema::table('instances', function (Blueprint $table) {
            $table
                ->integer('mileage_allowance_credit_account_id')
                ->unsigned()
                ->nullable()
                ->after('mileage_allowance_accounting_account_id');

            $table
                ->foreign('mileage_allowance_credit_account_id')
                ->references('id')
                ->on('accounting_accounts');
        });

        \Illuminate\Support\Facades\DB::statement(
            "UPDATE instances SET mileage_allowance_credit_account_id=mileage_allowance_accounting_account_id"
        );
    }

    public function down(): void
    {
        Schema::table('instances', function (Blueprint $table) {
            $table->dropForeign(['mileage_allowance_credit_account_id']);
            $table->dropColumn('mileage_allowance_credit_account_id');
        });
    }
}
