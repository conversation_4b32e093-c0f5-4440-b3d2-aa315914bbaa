<?php

declare(strict_types=1);

use App\Company;
use App\Instance;
use App\Request;
use App\User;
use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Report\Priv\Entity\Report;
use Modules\Report\Priv\Entity\ReportInstance;
use Modules\Report\Priv\Transformer\Input\RequestDetailed\RequestDetailedTransformer;
use Modules\Report\Priv\Transformer\Output\ColumnOutputDataTransformer;

class AddsDetailedRequestReport extends Migration
{
    private const MAPPING = [
        ["name" => "instance_id", "type" => "keyword"],
        ["name" => "Request number", "type" => "keyword"],
        ["name" => "Request date", "type" => "date", "format" => "yyyy-MM-dd HH:mm:ss"],
        ["name" => "Claim date", "type" => "date", "format" => "yyyy-MM-dd HH:mm:ss"],
        ["name" => "Approval date", "type" => "date", "format" => "yyyy-MM-dd HH:mm:ss"],
        ["name" => "Posting date", "type" => "date", "format" => "yyyy-MM-dd HH:mm:ss"],
        ["name" => "Request", "type" => "keyword"],
        ["name" => "Request status", "type" => "keyword"],
        ["name" => "Employee", "type" => "keyword"],
        ["name" => "Employee ERP ID", "type" => "keyword"],
        ["name" => "Employee HR ID", "type" => "keyword"],
        ["name" => "Approver", "type" => "keyword"],
        ["name" => "Approver ERP ID", "type" => "keyword"],
        ["name" => "Approver HR ID", "type" => "keyword"],
        ["name" => "Deputies HR ID", "type" => "keyword"],
        ["name" => "Deputies ERP ID'", "type" => "keyword"],
        ["name" => "Deputies HR ID", "type" => "keyword"],
        ["name" => "Company", "type" => "keyword"],
        ["name" => "Cost Centre", "type" => "keyword"],
        ["name" => "Trip type", "type" => "keyword"],
        ["name" => "Countries", "type" => "keyword"],
        ["name" => "Days", "type" => "keyword"],
        ["name" => "Start date", "type" => "date", "format" => "yyyy-MM-dd HH:mm:ss"],
        ["name" => "End date", "type" => "date", "format" => "yyyy-MM-dd HH:mm:ss"],
        ["name" => "Trip start", "type" => "keyword"],
        ["name" => "Destination", "type" => "keyword"],
        ["name" => "Trip end", "type" => "keyword"],
        ["name" => "Accommodation names", "type" => "keyword"],
        ["name" => "Means of transport", "type" => "keyword"],
        ["name" => "Others", "type" => "keyword"],
        ["name" => "expenses", "type" => "object"],
        ["name" => "account_dimensions", "type" => "object"],
    ];

    private const FILTERS = [
        [
            "name" => "Request date",
            "format" => "Y-m-d H:i:s",
            "frontend_name" => [
                "range" => [
                    "to" => "to",
                    "from" => "from",
                ],
            ],
        ],
        [
            "name" => "Employee",
            "resource" => [
                "model" => User::class,
                "property" => "full_name",
            ],
            "frontend_name" => "user_id",
        ],
        [
            "name" => "Cost Centre",
            "resource" => [
                "model" => Mpk::class,
                "property" => "name",
            ],
            "frontend_name" => "mpk_id",
        ],
        [
            "name" => "account_dimensions",
            "resource" => [
                "model" => AccountDimension::class,
                "valueModel" => AccountDimensionItem::class,
                "property" => "name",
                "valueProperty" => "name",
                "identifier" => "slug",
            ],
            "frontend_name" => ['name' => 'account-dimensions_', 'type' => 'contains'],
        ],
        [
            "name" => "Company",
            "resource" => [
                "model" => Company::class,
                "property" => "name",
            ],
            "frontend_name" => "company_id",
        ],
        [
            'name' => 'Request status',
            'resource' => [
                'model' => Request::class,
                'fromStaticMethod' => 'statusesList',
            ],
            'frontend_name' => [
                'name' => 'statuses',
                'type' => 'multiple'
            ],
        ],
    ];
    private const REPORT_NAME = 'request_detailed';

    public function up(): void
    {
        $report = new Report();
        $report->name = self::REPORT_NAME;
        $report->base_model = Request::class;
        $report->mapping = self::MAPPING;
        $report->filter = self::FILTERS;
        $report->input_transformer = RequestDetailedTransformer::class;
        $report->output_transformer = ColumnOutputDataTransformer::class;
        $report->save();

        $instance = Instance::query()->where('domain', 'LIKE', '%vtl%')->orWhere('domain', 'LIKE', 'mss048%')->first();

        if ($instance === null) {
            return;
        }

        $reportInstance = new ReportInstance();
        $reportInstance->report_id = $report->id;
        $reportInstance->instance_id = $instance->id;
        $reportInstance->output_transformer = ColumnOutputDataTransformer::class;
        $reportInstance->save();
    }

    public function down(): void
    {
        Report::query()->where('name', '=', self::REPORT_NAME)->delete();
    }
}
