<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCountableColumnToRequestAccessLumpSums extends Migration
{
    public function up(): void
    {
        Schema::table('request_access_lump_sums', function (Blueprint $table) {
            $table->boolean('countable')->after('confirmed')->default(false);
        });
    }

    public function down(): void
    {
        Schema::table('request_access_lump_sums', function (Blueprint $table) {
            $table->dropColumn('countable');
        });
    }
}
