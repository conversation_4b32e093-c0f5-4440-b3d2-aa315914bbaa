<?php

class Permissions extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('permissions', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->group();

            $table->string('ability');
            $table->string('name');
            $table->boolean('can')->default(false);
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('permissions');
    }
}
