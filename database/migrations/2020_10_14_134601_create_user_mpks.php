<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserMpks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::beginTransaction();
        try {
            Schema::create('user_mpks', function (Blueprint $table) {
                $table->increments('id');
                $table->integer('user_id')->unsigned()->index()->nullable(false);
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');
                $table->integer('mpk_id')->unsigned()->index()->nullable(false);
                $table->foreign('mpk_id')->references('id')->on('mpks')->onDelete('cascade')->onUpdate('cascade');
                $table->timestamps();
                $table->integer('created_by')->unsigned()->index()->nullable(true);
                $table->integer('updated_by')->unsigned()->index()->nullable(true);
            });

            Schema::table('permissions', function (Blueprint $table) {
                $table->string('scope', 255)->nullable(true)->after('ability');
                $table->unique(['ability', 'scope', 'group_id'], 'permissions_unique');

            });

        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }

        DB::commit();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('permissions', function (Blueprint $table) {
            $table->dropUnique('permissions_unique');
            $table->dropColumn('scope');

        });

        Schema::dropIfExists('user_mpks');
    }
}
