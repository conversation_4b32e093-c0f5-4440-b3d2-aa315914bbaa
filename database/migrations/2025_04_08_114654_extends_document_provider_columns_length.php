<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ExtendsDocumentProviderColumnsLength extends Migration
{
    public function up(): void
    {
        Schema::table('document_providers', static function (Blueprint $table) {
            $table->string('name', 255)->change();
            $table->string('address', 255)->change();
        });
    }

    public function down(): void
    {
        Schema::table('document_providers', static function (Blueprint $table) {
            $table->string('name', 100)->change();
        });
    }
}
