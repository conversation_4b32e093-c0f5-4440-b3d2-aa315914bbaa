<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Modules\DecisionMaker\Priv\Models\DecisionProcess;
use Modules\DecisionMaker\Priv\Models\DecisionProcessCondition;
use Modules\DecisionMaker\Priv\Models\DecisionProcessNode;
use Modules\DecisionMaker\Pub\Enums\DecisionProcessEnum;
use Modules\ExchangeRate\Priv\Conditions\DocumentHasCorporateCardPaymentCondition;
use Modules\ExchangeRate\Priv\Conditions\DocumentHasIssueDateAvailableCondition;
use Modules\ExchangeRate\Priv\Conditions\DocumentHasOwnPaymentCondition;
use Modules\ExchangeRate\Priv\Conditions\RequestHasAtLeastSettlementStatusCondition;
use Modules\ExchangeRate\Priv\Conditions\RequestHasPaidInstallmentsCondition;
use Modules\ExchangeRate\Priv\Conditions\RequestHasAcceptanceOfSettlementFirstDateCondition;
use Modules\ExchangeRate\Priv\Conditions\RequestHasStatusForEstimationCondition;
use Modules\ExchangeRate\Priv\Conditions\RequestHasTripEndDateCondition;
use Modules\ExchangeRate\Priv\Decisions\Document\DocumentIssueDateRateDecision;
use Modules\ExchangeRate\Priv\Decisions\Document\DocumentNbpCurrentRateDecision;
use Modules\ExchangeRate\Priv\Decisions\Document\DocumentRequestInstallmentRateDecision;
use Modules\ExchangeRate\Priv\Decisions\Document\DocumentRequestTripEndDateDecision;
use Modules\ExchangeRate\Priv\Decisions\ForeignTravelAllowance\ForeignTravelAllowanceInstallmentRateDecision;
use Modules\ExchangeRate\Priv\Decisions\ForeignTravelAllowance\ForeignTravelAllowanceNbpCurrentRateDecision;
use Modules\ExchangeRate\Priv\Decisions\ForeignTravelAllowance\ForeignTravelAllowanceRequestAcceptanceOfSettlementDateDecision;
use Modules\ExchangeRate\Priv\Decisions\ForeignTravelAllowance\ForeignTravelAllowanceRequestTripEndRateDecision;
use Modules\ExchangeRate\Priv\Decisions\ForeignTravelAllowanceEstimation\ForeignTravelAllowanceRequestEstimationNbpCurrentRateDecision;

class CreateDecisionStructureForExchangeRate extends Migration
{
    public function up(): void
    {
        Schema::table('decision_process_nodes', function (Blueprint $table) {
            $table->integer('order')->after('decision_parameters')->nullable();
        });

        Schema::table('decision_process_conditions', function (Blueprint $table) {
            $table->boolean('negate')->after('parameters')->default(false);
        });

        // Documents
        $decisionProcess = factory(DecisionProcess::class)->create([
            'slug' => DecisionProcessEnum::DOCUMENT_EXCHANGE_RATE_STRATEGY_SELECTION()
        ]);

        $this->addDocumentRequestInstallmentRateDecision($decisionProcess);
        $this->addDocumentIssueDateRateDecision($decisionProcess);
        // TODO: the don't want it for default be it must be set for amrest?
//        $this->addDocumentRequestTripEndDateDecision($decisionProcess);
        $this->addDocumentNbpCurrentRateDecision($decisionProcess);

        // ForeignTravelAllowances
        $decisionProcess = factory(DecisionProcess::class)->create([
            'slug' => DecisionProcessEnum::FOREIGN_TRAVEL_ALLOWANCE_STRATEGY_SELECTION()
        ]);

        $this->addForeignTravelAllowanceRequestInstallmentRateDecision($decisionProcess);
        $this->addForeignTravelAllowanceRequestAcceptanceOfSettlementDateRateDecision($decisionProcess);
        // TODO: they don't want it for default, be it must be set for amrest in future?
//        $this->addForeignTravelAllowanceRequestTripEndDateDecision($decisionProcess);
        $this->addForeignTravelAllowanceNbpCurrentRateDecision($decisionProcess);

        // ForeignTravelAllowanceEstimations
        $decisionProcess = factory(DecisionProcess::class)->create([
            'slug' => DecisionProcessEnum::FOREIGN_TRAVEL_ALLOWANCE_ESTIMATION_STRATEGY_SELECTION()
        ]);

        $this->addForeignTravelAllowanceRequestEstimationNbpCurrentRateDecision($decisionProcess);
    }


    public function down(): void
    {
        $decisionProcesses = DecisionProcess::whereIn(
            'slug',
            [
                DecisionProcessEnum::DOCUMENT_EXCHANGE_RATE_STRATEGY_SELECTION(),
                DecisionProcessEnum::FOREIGN_TRAVEL_ALLOWANCE_STRATEGY_SELECTION(),
                DecisionProcessEnum::FOREIGN_TRAVEL_ALLOWANCE_ESTIMATION_STRATEGY_SELECTION()
            ]
        );

        $decisionProcesses->each(function (DecisionProcess $decisionProcess) {
            $decisionProcessNodes = DecisionProcessNode::where('decision_process_id', $decisionProcess->id);
            $decisionProcessNodes->each(function (DecisionProcessNode $decisionProcessNode) {
                $decisionProcessNode->conditions()->delete();
            });
            $decisionProcessNodes->delete();
        });

        $decisionProcesses->delete();

        Schema::table('decision_process_nodes', function (Blueprint $table) {
            $table->dropColumn('order');
        });

        Schema::table('decision_process_conditions', function (Blueprint $table) {
            $table->dropColumn('negate');
        });
    }

    private function addDocumentRequestInstallmentRateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => DocumentRequestInstallmentRateDecision::CODE,
            'decision_parameters' => [],
            'order' => 10
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasPaidInstallmentsCondition::CODE
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => DocumentHasOwnPaymentCondition::CODE
        ]);
    }

    private function addDocumentIssueDateRateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => DocumentIssueDateRateDecision::CODE,
            'decision_parameters' => [
                'dayShift' => -1
            ],
            'order' => 20
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => DocumentHasCorporateCardPaymentCondition::CODE,
            'negate' => true
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => DocumentHasIssueDateAvailableCondition::CODE,
        ]);
    }

    private function addDocumentRequestTripEndDateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => DocumentRequestTripEndDateDecision::CODE,
            'decision_parameters' => [
                'dayShift' => -1
            ],
            'order' => 30
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => DocumentHasIssueDateAvailableCondition::CODE,
            'negate' => true
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasPaidInstallmentsCondition::CODE,
            'negate' => true
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasTripEndDateCondition::CODE
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasAtLeastSettlementStatusCondition::CODE
        ]);
    }


    private function addDocumentNbpCurrentRateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => DocumentNbpCurrentRateDecision::CODE,
            'decision_parameters' => [
                'dayShift' => -1,
            ],
            'order' => 40
        ]);
    }

    private function addForeignTravelAllowanceRequestInstallmentRateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => ForeignTravelAllowanceInstallmentRateDecision::CODE,
            'decision_parameters' => [],
            'order' => 10
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasPaidInstallmentsCondition::CODE
        ]);
    }

    private function addForeignTravelAllowanceRequestAcceptanceOfSettlementDateRateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => ForeignTravelAllowanceRequestAcceptanceOfSettlementDateDecision::CODE,
            'decision_parameters' => [
                'dayShift' => -1
            ],
            'order' => 20
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasPaidInstallmentsCondition::CODE,
            'negate' => true
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasAtLeastSettlementStatusCondition::CODE,
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasAcceptanceOfSettlementFirstDateCondition::CODE
        ]);

    }

    private function addForeignTravelAllowanceRequestTripEndDateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => ForeignTravelAllowanceRequestTripEndRateDecision::CODE,
            'decision_parameters' => [
                'dayShift' => -1
            ],
            'order' => 30
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasTripEndDateCondition::CODE
        ]);

        factory(DecisionProcessCondition::class)->create([
            'decision_process_node_id' => $node->id,
            'code' => RequestHasAtLeastSettlementStatusCondition::CODE
        ]);
    }

    private function addForeignTravelAllowanceNbpCurrentRateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => ForeignTravelAllowanceNbpCurrentRateDecision::CODE,
            'decision_parameters' => [
                'dayShift' => -1,
            ],
            'order' => 40
        ]);
    }

    private function addForeignTravelAllowanceRequestEstimationNbpCurrentRateDecision(DecisionProcess $decisionProcess): void
    {
        /** @var DecisionProcessNode $node */
        $node = factory(DecisionProcessNode::class)->create([
            'decision_process_id' => $decisionProcess->id,
            'operator_code' => 'and',
            'decision_code' => ForeignTravelAllowanceRequestEstimationNbpCurrentRateDecision::CODE,
            'decision_parameters' => [
                'dayShift' => -1,
            ],
            'order' => 20
        ]);
    }
}
