<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddNewDocumentElementTypes2 extends Migration
{
    private const TYPE_SUFIX = '-type';

    protected \Modules\Accounting\Priv\Repositories\DocumentElementGroupRepository $documentElementGroupRepository;

    protected \Modules\Accounting\Priv\Repositories\DocumentElementTypeRepository $documentElementTypeRepository;

    public function __construct()
    {
        $this->documentElementGroupRepository = resolve(\Modules\Accounting\Priv\Repositories\DocumentElementGroupRepository::class);
        $this->documentElementTypeRepository = resolve(\Modules\Accounting\Priv\Repositories\DocumentElementTypeRepository::class);
    }

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \App\Instance::all()->each(function (\App\Instance $instance) {
            $group = $this->documentElementGroupRepository->findBySlugAndInstanceId('expense-production-costs-group', $instance->id);
            if($group !== null)  {
                $this->prepareNewDocumentType($group, 'tools', $instance)->save();
                $this->prepareNewDocumentType($group, 'other-materials', $instance)->save();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \Modules\Accounting\Priv\Entities\DocumentElementType::query()->whereIn('slug',[
            'expense-production-costs-group-' . 'tools' . self::TYPE_SUFIX,
            'expense-production-costs-group-' . 'other-materials' . self::TYPE_SUFIX,
        ])->delete();
    }

    private function prepareNewDocumentType(\Modules\Accounting\Priv\Entities\DocumentElementGroup $documentElementGroup, string $slug, \App\Instance $instance): \Modules\Accounting\Priv\Entities\DocumentElementType
    {
        $vatNumber = $instance->vatNumbers->first();

        return new \Modules\Accounting\Priv\Entities\DocumentElementType([
            'instance_id' => $instance->id,
            'document_element_group_id' => $documentElementGroup->id,
            'vat_number_id' => $vatNumber->id,
            'accounting_account_id' => $instance->technical_account_id,
            'is_active' => \App\Enum\ActiveEnum::ACTIVE(),
            'order' => $documentElementGroup->types()->count()+1,
            'name' => $slug,
            'slug' => $documentElementGroup->slug.'-'.$slug.self::TYPE_SUFIX,
            'visible_in_trip' => 1,
            'visible_in_expense' => 1,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_DOCUMENT_DATE
        ]);
    }
}
