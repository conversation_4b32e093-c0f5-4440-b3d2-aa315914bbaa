<?php

declare(strict_types=1);

use App\Group;
use App\Permission;
use Illuminate\Database\Migrations\Migration;

class CopyRegularPermissionsToB2b extends Migration
{
    public function up()
    {
        \App\Instance::with('groups.permissions')->get()
            ->each(function (\App\Instance $instance) {
                $regular = $instance->groups->firstWhere('name', '=', \App\User::GROUP_NAME_REGULAR);
                $b2b = $instance->groups->firstWhere('name', '=', \App\User::GROUP_B2B);

                foreach ($regular->permissions as $permission) {
                    $this->createPermission($b2b, $permission);
                }
            });
    }

    private function createPermission(Group $group, Permission $permission): void
    {
        if (false === (bool) $permission->can) {
            return;
        }

        if ($group->permissions->contains('ability', $permission->ability)) {
            return;
        }

        Permission::forceCreate([
            'instance_id' => $group->instance_id,
            'group_id' => $group->id,
            'ability' => $permission->ability,
            'name' => $permission->name,
            'scope' => null,
            'can' => $permission->can,
            'created_at' => \Carbon\Carbon::now(),
            'updated_at' => \Carbon\Carbon::now(),
        ]);
    }

    public function down()
    {
    }
}
