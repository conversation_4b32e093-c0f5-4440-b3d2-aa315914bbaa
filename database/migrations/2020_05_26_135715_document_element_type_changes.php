<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DocumentElementTypeChanges extends Migration
{
    const IS_ACTIVE = 'is_active';
    const SLUG = 'slug';
    const UPDATED_AT = 'updated_at';
    const CREATED_AT = 'created_at';
    const CREATED_BY = 'created_by';
    const UPDATED_BY = 'updated_by';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_element_types', function (Blueprint $table) {
            $table->boolean(self::IS_ACTIVE)->nullable(false)->default(true)->after('instance_id');
            $table->integer(self::CREATED_BY)->after(self::CREATED_AT)->nullable(true)->unsigned()->default(null)->references('id')->on('users')->onDelete('SET NULL');
            $table->integer(self::UPDATED_BY)->after(self::UPDATED_AT)->nullable(true)->unsigned()->default(null)->references('id')->on('users')->onDelete('SET NULL');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_element_types', function (Blueprint $table) {
            $table->dropColumn(self::IS_ACTIVE);
            $table->dropColumn(self::UPDATED_BY);
            $table->dropColumn(self::CREATED_BY);
        });
    }
}
