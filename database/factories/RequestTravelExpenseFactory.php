<?php
/** @var Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;

$factory->define(App\RequestMealDeduction::class, function (Faker $faker) {
    static $instances;

    if ( !$instances ) {
        $instances = \App\Instance::with(['requests'])->whereNotIn('domain', [
            config('vaterval.demoDomain'),
            config('vaterval.emptyDomain'),
        ])->get();
    }

    $instance = $instances->random(1)->first();

    if($instance->requests->isNotEmpty()) {
        $request = $instance->requests->random(1)->first();
    } else {
        $request = factory(\App\Request::class)->make([
            'instance_id' => $instance
        ]);
    }

    return [
        'instance_id' => $instance,
        'request_id' => $request,
        'from' => \Carbon\Carbon::create(),
        'to' => \Carbon\Carbon::create()->endOfDay(),
        'breakfast' => rand(0, 1) === 1,
        'lunch' => rand(0, 1) === 1,
        'dinner' => rand(0, 1) === 1,
        'accomodation' => rand(0, 1) === 1,
        'drive' => rand(0, 1) === 1,
    ];
});