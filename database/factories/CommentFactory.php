<?php

use Faker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\Comment::class, function(Faker $faker) {
    static $instances;

    if(!$instances) {
        $instances = \App\Instance::with(['users', 'requests'])->whereNotIn('domain', [
                config('vaterval.demoDomain'),
                config('vaterval.emptyDomain'),
            ])->get();
    }

    $instance = $instances->random(1)->first();

    return [
        'instance_id' => $instance,
        'request_id'  => $instance->requests->random(1)->first(),
        'user_id'     => $instance->users->random(1)->first(),
        'content'     => $faker->paragraph(),
    ];
});
