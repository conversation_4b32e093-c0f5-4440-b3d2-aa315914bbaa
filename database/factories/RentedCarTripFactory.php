<?php

use <PERSON>aker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\RentedCarTrip::class, function(Faker $faker) {

    $currencies = \App\Currency::all();

    $pickup_at = Carbon\Carbon::now()->addDays(rand(2, 15))
        ->hour(rand(0,23))
        ->minute(rand(0,59))
        ->second(rand(0,59));

    $return_at = clone $pickup_at->addMinutes(rand(40, 12*60));

    return [
        'pickup_at' => $pickup_at,
        'return_at' => $return_at,
        'rent_cost' => $faker->randomFloat(null, 10, 200),
        'rent_cost_currency_id' => $currencies->random(1)->first()->id,
        'fuel_cost' => $faker->randomFloat(null, 10, 200),
        'fuel_cost_currency_id' => $currencies->random(1)->first()->id,
        'other_costs_amount' => $faker->randomFloat(null, 10, 200),
        'other_costs_currency_id' => $currencies->random(1)->first()->id,
        'amount' => $faker->randomFloat(null, 10, 20000),
        'amount_currency_id' => $currencies->random(1)->first()->id,
    ];
});
