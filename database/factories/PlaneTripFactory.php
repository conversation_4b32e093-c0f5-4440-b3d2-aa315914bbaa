<?php

use <PERSON>aker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\PlaneTrip::class, function(Faker $faker) {
    $currencies = \App\Currency::all();

    $arrival_at = Carbon\Carbon::now()->addDays(rand(2, 15))
        ->hour(rand(0,23))
        ->minute(rand(0,59))
        ->second(rand(0,59));

    $return_at = clone $arrival_at->addMinutes(rand(40, 12*60));

    $flight_classes = [
       'class_economy',
       'class_economy_premium',
       'class_business',
       'class_first',
    ];

    return [
        'arrival_at' => $arrival_at,
        'flight_class' => $flight_classes[rand(0, 3)],
        'round_trip' => (bool)rand(0,1),
        'return_at' => $return_at,
        'amount' => $faker->randomFloat(null, 10, 20000),
        'amount_currency_id' => $currencies->random(1)->first()->id,
        'number_of_paxes' => 1,
    ];
});
