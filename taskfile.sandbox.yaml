version: '3'

vars:
  SANDBOX_IMAGE_NAME: salesto:sandbox
  SANDBOX_CONTAINER_PREFIX: salesto-sandbox
  SANDBOX_DOCKERFILE: Dockerfile
  SANDBOX_CONTEXT: .
  SANDBOX_USER: allhands
  SANDBOX_WORKSPACE: /workspace

tasks:
  build:
    desc: 'Build the sandbox Docker image'
    cmds:
      - |
        echo "Building sandbox image: {{.SANDBOX_IMAGE_NAME}}" >&2
        DOCKER_BUILDKIT=1 docker build \
          --network=host \
          --tag {{.SANDBOX_IMAGE_NAME}} \
          --file {{.SANDBOX_DOCKERFILE}} \
          --target sandbox \
          {{.SANDBOX_CONTEXT}} >&2
        echo "{{.SANDBOX_IMAGE_NAME}}"
    silent: false

  build:force:
    desc: 'Force rebuild the sandbox Docker image (no cache)'
    cmds:
      - |
        echo "Force building sandbox image: {{.SANDBOX_IMAGE_NAME}}" >&2
        DOCKER_BUILDKIT=1 docker build \
          --network=host \
          --no-cache \
          --tag {{.SANDBOX_IMAGE_NAME}} \
          --file {{.SANDBOX_DOCKERFILE}} \
          --target sandbox \
          {{.SANDBOX_CONTEXT}} >&2
        echo "{{.SANDBOX_IMAGE_NAME}}"
    silent: false

  run:
    desc: 'Run a command in the sandbox container (using host Docker socket)'
    cmds:
      - |
        docker run -it --rm \
          --privileged \
          --network=host \
          --security-opt seccomp=unconfined \
          --security-opt apparmor=unconfined \
          --cgroupns=host \
          -v {{.SANDBOX_CONTEXT}}:{{.SANDBOX_WORKSPACE}} \
          -w {{.SANDBOX_WORKSPACE}} \
          -e DOCKER_HOST=unix:///var/run/docker.sock \
          -e SANDBOX_AUTO_SETUP=${SANDBOX_AUTO_SETUP:-true} \
          -v /var/run/docker.sock:/var/run/docker.sock:rw \
          -v /sys/fs/cgroup:/sys/fs/cgroup:rw \
          {{.SANDBOX_IMAGE_NAME}} \
          {{.CLI_ARGS}}

  run:dind:
    desc: 'Run a command in the sandbox container (Docker-in-Docker mode)'
    cmds:
      - |
        docker run -it --rm \
          --privileged \
          --network=host \
          --security-opt seccomp=unconfined \
          --security-opt apparmor=unconfined \
          --cgroupns=host \
          -v {{.SANDBOX_CONTEXT}}:{{.SANDBOX_WORKSPACE}} \
          -w {{.SANDBOX_WORKSPACE}} \
          -v /sys/fs/cgroup:/sys/fs/cgroup:rw \
          -v /var/lib/docker \
          {{.SANDBOX_IMAGE_NAME}} \
          {{.CLI_ARGS}}

  test:
    desc: 'Run sandbox functionality tests'
    cmds:
      - ./tests/setup/test_sandbox.sh

  test:debug:
    desc: 'Run sandbox functionality tests with debug output'
    cmds:
      - ./tests/setup/test_sandbox.sh --debug

  setup:
    desc: 'Setup sandbox environment (build image and run setup script)'
    cmds:
      - task: build
      - |
        echo "Running setup script in sandbox..."
        task sandbox:run -- ./setup.sh

  setup:dind:
    desc: 'Setup sandbox environment using Docker-in-Docker mode'
    cmds:
      - task: build
      - |
        echo "Running setup script in sandbox (Docker-in-Docker mode)..."
        task sandbox:run:dind -- ./setup.sh
