<?php

return [
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'all_request_documents_are_valid',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'document_is_valid_for_settlement',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'document_is_valid_for_accounting',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'trip_has_target_point',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_is_chronological',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'accounting_travel_expenses_valid',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_valid_for_finish',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_border_crossing_state_valid',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_national_plane_trip_rule',
        'parameters' => [
            'grade-0' => [
                "oneWayMaxAmount" => 430,
                "roundTripMaxAmount" => 860
            ],
            'grade-1' => [
                "oneWayMaxAmount" => 430,
                "roundTripMaxAmount" => 860
            ],
            'grade-2' => [
                "oneWayMaxAmount" => 860,
                "roundTripMaxAmount" => 1720
            ],
            'grade-3' => [
                "oneWayMaxAmount" => 860,
                "roundTripMaxAmount" => 1720
            ]

        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_national_plane_trip_rule',
        'parameters' => [
            'grade-0' => [
                "oneWayMaxAmount" => 430,
                "roundTripMaxAmount" => 860
            ],
            'grade-1' => [
                "oneWayMaxAmount" => 430,
                "roundTripMaxAmount" => 860
            ],
            'grade-2' => [
                "oneWayMaxAmount" => 860,
                "roundTripMaxAmount" => 1720
            ],
            'grade-3' => [
                "oneWayMaxAmount" => 860,
                "roundTripMaxAmount" => 1720
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_international_plane_trip_rule',
        'parameters' => [
            'grade-0' => [
                'oneWayMaxAmount' => 1320,
                'roundTripMaxAmount' => 3440,
            ],
            'grade-1' => [
                'oneWayMaxAmount' => 1320,
                'roundTripMaxAmount' => 3440,
            ],
            'grade-2' => [
                'oneWayMaxAmount' => 2150,
                'roundTripMaxAmount' => 4300,
            ],
            'grade-3' => [
                'oneWayMaxAmount' => 2150,
                'roundTripMaxAmount' => 4300,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_international_plane_trip_rule',
        'parameters' => [
            'grade-0' => [
                'oneWayMaxAmount' => 1320,
                'roundTripMaxAmount' => 3440,
            ],
            'grade-1' => [
                'oneWayMaxAmount' => 1320,
                'roundTripMaxAmount' => 3440,
            ],
            'grade-2' => [
                'oneWayMaxAmount' => 2150,
                'roundTripMaxAmount' => 4300,
            ],
            'grade-3' => [
                'oneWayMaxAmount' => 2150,
                'roundTripMaxAmount' => 4300,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_mileage_allowance_rule',
        'parameters' => [
            'grade-0' => [
                'maxDistance' => 1500,
            ],
            'grade-1' => [
                'maxDistance' => 1500,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_mileage_allowance_rule',
        'parameters' => [
            'grade-0' => [
                'maxDistance' => 1000,
            ],
            'grade-1' => [
                'maxDistance' => 1000,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_train_trip_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 250,
            ],
            'grade-1' => [
                'maxAmount' => 250,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_train_trip_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 200,
            ],
            'grade-1' => [
                'maxAmount' => 200,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_national_accommodation_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 344,
            ],
            'grade-1' => [
                'maxAmount' => 344,
            ],
            'grade-2' => [
                'maxAmount' => 430,
            ],
            'grade-3' => [
                'maxAmount' => 430,
            ]
        ]
    ],


    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_national_accommodation_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 344,
            ],
            'grade-1' => [
                'maxAmount' => 344,
            ],
            'grade-2' => [
                'maxAmount' => 430,
            ],
            'grade-3' => [
                'maxAmount' => 430,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_international_accommodation_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 516,
            ],
            'grade-1' => [
                'maxAmount' => 516,
            ],
            'grade-2' => [
                'maxAmount' => 1075,
            ],
            'grade-3' => [
                'maxAmount' => 1075,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_international_accommodation_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 516,
            ],
            'grade-1' => [
                'maxAmount' => 516,
            ],
            'grade-2' => [
                'maxAmount' => 1075,
            ],
            'grade-3' => [
                'maxAmount' => 1075,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_taxi_amount_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 215,
            ],
            'grade-1' => [
                'maxAmount' => 215,
            ],
            'grade-2' => [
                'maxAmount' => 215,
            ],
            'grade-3' => [
                'maxAmount' => 215,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_train_trip_max_service_class_rule',
        'parameters' => [
            'grade-0' => [
                'service_class' => 2,
            ],
            'grade-1' => [
                'service_class' => 2,
            ],
            'grade-2' => [
                'service_class' => 1,
            ],
            'grade-3' => [
                'service_class' => 1,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'unrequested_element_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'accounted_amount_greater_than_requested_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'all_access_lump_sump_confirmed_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'accounting_travel_expenses_has_accounting_dimensions',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'accounting_milleage_allowance_summary_amount',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'acounting_milleage_allowance_summary_has_accounting_dimensions',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_accounting_allowances_has_account_dimension',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'all_foreign_access_lump_sums_are_valid_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => \App\Services\RulesService\Rules\RequestTargetPointsAreBeforeTransportationElementsChronologyRule::NAME,
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'all_mileage_allowances_in_trip_duration_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'trip_course_confirmed_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_has_requested_amount',
        'parameters' => []
    ],

    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_has_settled_amount',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_trip_ended_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_isset_acceptor_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_isset_acceptor_of_settlement_rule',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_has_mpk',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_trip_between_hours',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_trip_took_at_least_six_hours',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'request_has_not_booked_accommodation',
        'parameters' => []
    ],
    [
        'level' => App\Rule::LEVEL_AUTOMATION,
        'name' => 'auto_acceptance_rule',
        'parameters' => [
            'grade-0' => [
                "auto_acceptance" => false,
                "maximum_percentage_exceedance" => 0
            ],
            'grade-1' => [
                "auto_acceptance" => false,
                "maximum_percentage_exceedance" => 0
            ],
            'grade-2' => [
                "auto_acceptance" => false,
                "maximum_percentage_exceedance" => 0
            ],
            'grade-3' => [
                "auto_acceptance" => false,
                "maximum_percentage_exceedance" => 0
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_AUTOMATION,
        'name' => 'auto_reservation_rule',
        'parameters' => [
            'grade-0' => [
                "auto_reservation" => false,
                "maximum_percentage_exceedance" => 10
            ],
            'grade-1' => [
                "auto_reservation" => false,
                "maximum_percentage_exceedance" => 10
            ],
            'grade-2' => [
                "auto_reservation" => false,
                "maximum_percentage_exceedance" => 10
            ],
            'grade-3' => [
                "auto_reservation" => false,
                "maximum_percentage_exceedance" => 10
            ],
        ]
    ],
    [
        'level' => App\Rule::LEVEL_AUTOMATION,
        'name' => 'settlement_auto_acceptance_rule',
        'parameters' => [
            'grade-0' => [
                "settlement_auto_acceptance" => false,
                "maximum_percentage_exceedance" => 10
            ],
            'grade-1' => [
                "settlement_auto_acceptance" => false,
                "maximum_percentage_exceedance" => 10
            ],
            'grade-2' => [
                "settlement_auto_acceptance" => false,
                "maximum_percentage_exceedance" => 10
            ],
            'grade-3' => [
                "settlement_auto_acceptance" => false,
                "maximum_percentage_exceedance" => 10
            ],
        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_document_exchange_rate_rule',
        'parameters' => [
            'enabled' => true
        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_border_crossing_exchange_rate_rule',
        'parameters' => [
            'enabled' => true
        ]
    ],
    [
        'level' => App\Rule::LEVEL_AUTOMATION,
        'name' => 'warning_low_level_for_automatically_acceptation_plane_trip_rule',
        'parameters' => [
            'enabled' => true,
            'grades' => [0, 1]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_AUTOMATION,
        'name' => 'error_trip_start_in_nb_of_days_rule',
        'parameters' => [
            'enabled' => true,
            'days' => 14
        ]
    ],
    [
        'level' => App\Rule::LEVEL_AUTOMATION,
        'name' => 'error_abroad_accomodation_rule',
        'parameters' => [
            'grade-0' => [
                'auto_acceptance' => false
            ],
            'grade-1' => [
                'auto_acceptance' => false
            ],
            'grade-2' => [
                'auto_acceptance' => true
            ],
            'grade-3' => [
                'auto_acceptance' => true
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'warning_intercontinental_plane_trip_rule',
        'parameters' => [
            'grade-0' => [
                'oneWayMaxAmount' => 2580,
                'roundTripMaxAmount' => 5160,
            ],
            'grade-1' => [
                'oneWayMaxAmount' => 2580,
                'roundTripMaxAmount' => 5160,
            ],
            'grade-2' => [
                'oneWayMaxAmount' => 4300,
                'roundTripMaxAmount' => 8600,
            ],
            'grade-3' => [
                'oneWayMaxAmount' => 4300,
                'roundTripMaxAmount' => 8600,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => 'error_intercontinental_plane_trip_rule',
        'parameters' => [
            'grade-0' => [
                'oneWayMaxAmount' => 2580,
                'roundTripMaxAmount' => 5160,
            ],
            'grade-1' => [
                'oneWayMaxAmount' => 2580,
                'roundTripMaxAmount' => 5160,
            ],
            'grade-2' => [
                'oneWayMaxAmount' => 4300,
                'roundTripMaxAmount' => 8600,
            ],
            'grade-3' => [
                'oneWayMaxAmount' => 4300,
                'roundTripMaxAmount' => 8600,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'total_requested_amount_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 99999999,
            ],
            'grade-1' => [
                'maxAmount' => 99999999,
            ],
            'grade-2' => [
                'maxAmount' => 99999999,
            ],
            'grade-3' => [
                'maxAmount' => 99999999,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_WARNING,
        'name' => 'total_settled_amount_rule',
        'parameters' => [
            'grade-0' => [
                'maxAmount' => 99999999,
            ],
            'grade-1' => [
                'maxAmount' => 99999999,
            ],
            'grade-2' => [
                'maxAmount' => 99999999,
            ],
            'grade-3' => [
                'maxAmount' => 99999999,
            ]
        ]
    ],
    [
        'level' => App\Rule::LEVEL_ERROR,
        'name' => \App\Services\RulesService\Rules\AllOfferTravelersAreRequestTravelersRule::NAME,
        'parameters' => []
    ],
    \App\Compliance\Instances\Amrest\Rules\ErrorAbroadTripAlwaysRequireAcceptationRule::defaultConfiguration(),
    \App\Compliance\Instances\Amrest\Rules\SettlementAlwaysRequireAcceptationRule::defaultConfiguration(),
];
