<?php

return array(
    0   =>
        array(
            'name'         => 'afghanistan',
            'polish_name'  => 'Afganistan',
            'country_code' => 'AF',
        ),
    1   =>
        array(
            'name'         => 'albania',
            'polish_name'  => 'Albania',
            'country_code' => 'AL',
        ),
    2   =>
        array(
            'name'         => 'algeria',
            'polish_name'  => 'Algieria',
            'country_code' => 'DZ',
        ),
    3   =>
        array(
            'name'         => 'andora',
            'polish_name'  => 'Andora',
            'country_code' => 'AD',
        ),
    4   =>
        array(
            'name'         => 'angola',
            'polish_name'  => 'Angola',
            'country_code' => 'AO',
        ),
    5   =>
        array(
            'name'         => 'saudi-arabia',
            'polish_name'  => 'Arabia Saudyjska',
            'country_code' => 'SA',
        ),
    6   =>
        array(
            'name'         => 'argentina',
            'polish_name'  => 'Argentyna',
            'country_code' => 'AR',
        ),
    7   =>
        array(
            'name'         => 'armenia',
            'polish_name'  => 'Armenia',
            'country_code' => 'AM',
        ),
    8   =>
        array(
            'name'         => 'australia',
            'polish_name'  => 'Australia',
            'country_code' => 'AU',
        ),
    9   =>
        array(
            'name'         => 'austria',
            'polish_name'  => 'Austria',
            'country_code' => 'AT',
        ),
    10  =>
        array(
            'name'         => 'azerbaijan',
            'polish_name'  => 'Azerbejdżan',
            'country_code' => 'AZ',
        ),
    11  =>
        array(
            'name'         => 'bangladesh',
            'polish_name'  => 'Bangladesz',
            'country_code' => 'BD',
        ),
    12  =>
        array(
            'name'         => 'belgium',
            'polish_name'  => 'Belgia',
            'country_code' => 'BE',
        ),
    13  =>
        array(
            'name'         => 'belarus',
            'polish_name'  => 'Białoruś',
            'country_code' => 'BY',
        ),
    14  =>
        array(
            'name'         => 'bosnia-and-herzegovina',
            'polish_name'  => 'Bośnia i Hercegowina',
            'country_code' => 'BA',
        ),
    15  =>
        array(
            'name'         => 'brazil',
            'polish_name'  => 'Brazylia',
            'country_code' => 'BR',
        ),
    16  =>
        array(
            'name'         => 'bulgaria',
            'polish_name'  => 'Bułgaria',
            'country_code' => 'BG',
        ),
    17  =>
        array(
            'name'         => 'chile',
            'polish_name'  => 'Chile',
            'country_code' => 'CL',
        ),
    18  =>
        array(
            'name'         => 'china',
            'polish_name'  => 'Chiny',
            'country_code' => 'CN',
        ),
    19  =>
        array(
            'name'         => 'croatia',
            'polish_name'  => 'Chorwacja',
            'country_code' => 'HR',
        ),
    20  =>
        array(
            'name'         => 'cyprus',
            'polish_name'  => 'Cypr',
            'country_code' => 'CY',
        ),
    21  =>
        array(
            'name'         => 'czechia',
            'polish_name'  => 'Czechy',
            'country_code' => 'CZ',
        ),
    22  =>
        array(
            'name'         => 'denmark',
            'polish_name'  => 'Dania',
            'country_code' => 'DK',
        ),
    23  =>
        array(
            'name'         => 'egypt',
            'polish_name'  => 'Egipt',
            'country_code' => 'EG',
        ),
    24  =>
        array(
            'name'         => 'ecuador',
            'polish_name'  => 'Ekwador',
            'country_code' => 'EC',
        ),
    25  =>
        array(
            'name'         => 'estonia',
            'polish_name'  => 'Estonia',
            'country_code' => 'EE',
        ),
    26  =>
        array(
            'name'         => 'ethiopia',
            'polish_name'  => 'Etiopia',
            'country_code' => 'ET',
        ),
    27  =>
        array(
            'name'         => 'finland',
            'polish_name'  => 'Finlandia',
            'country_code' => 'FI',
        ),
    28  =>
        array(
            'name'         => 'france',
            'polish_name'  => 'Francja',
            'country_code' => 'FR',
        ),
    29  =>
        array(
            'name'         => 'greece',
            'polish_name'  => 'Grecja',
            'country_code' => 'GR',
        ),
    30  =>
        array(
            'name'         => 'georgia',
            'polish_name'  => 'Gruzja',
            'country_code' => 'GE',
        ),
    31  =>
        array(
            'name'         => 'spain',
            'polish_name'  => 'Hiszpania',
            'country_code' => 'ES',
        ),
    32  =>
        array(
            'name'         => 'india',
            'polish_name'  => 'Indie',
            'country_code' => 'IN',
        ),
    33  =>
        array(
            'name'         => 'indonesia',
            'polish_name'  => 'Indonezja',
            'country_code' => 'ID',
        ),
    34  =>
        array(
            'name'         => 'iraq',
            'polish_name'  => 'Irak',
            'country_code' => 'IQ',
        ),
    35  =>
        array(
            'name'         => 'iran',
            'polish_name'  => 'Iran',
            'country_code' => 'IR',
        ),
    36  =>
        array(
            'name'         => 'ireland',
            'polish_name'  => 'Irlandia',
            'country_code' => 'IE',
        ),
    37  =>
        array(
            'name'         => 'islandia',
            'polish_name'  => 'Islandia',
            'country_code' => 'IS',
        ),
    38  =>
        array(
            'name'         => 'israel',
            'polish_name'  => 'Izrael',
            'country_code' => 'IL',
        ),
    39  =>
        array(
            'name'         => 'japan',
            'polish_name'  => 'Japonia',
            'country_code' => 'JP',
        ),
    40  =>
        array(
            'name'         => 'yemen',
            'polish_name'  => 'Jemen',
            'country_code' => 'YE',
        ),
    41  =>
        array(
            'name'         => 'jordan',
            'polish_name'  => 'Jordania',
            'country_code' => 'JO',
        ),
    42  =>
        array(
            'name'         => 'cambodia',
            'polish_name'  => 'Kambodża',
            'country_code' => 'KH',
        ),
    43  =>
        array(
            'name'         => 'canada',
            'polish_name'  => 'Kanada',
            'country_code' => 'CA',
        ),
    44  =>
        array(
            'name'         => 'qatar',
            'polish_name'  => 'Katar',
            'country_code' => 'QA',
        ),
    45  =>
        array(
            'name'         => 'kazakhstan',
            'polish_name'  => 'Kazachstan',
            'country_code' => 'KZ',
        ),
    46  =>
        array(
            'name'         => 'kenya',
            'polish_name'  => 'Kenia',
            'country_code' => 'KE',
        ),
    47  =>
        array(
            'name'         => 'kyrgyzstan',
            'polish_name'  => 'Kirgistan',
            'country_code' => 'KG',
        ),
    48  =>
        array(
            'name'         => 'colombia',
            'polish_name'  => 'Kolumbia',
            'country_code' => 'CO',
        ),
    49  =>
        array(
            'name'         => 'democratic-republic-of-the-congo',
            'polish_name'  => 'Kongo, Demokratyczna Republika Konga',
            'country_code' => 'CD',
        ),
    50  =>
        array(
            'name'         => 'south-korea',
            'polish_name'  => 'Korea Południowa',
            'country_code' => 'KR',
        ),
    51  =>
        array(
            'name'         => 'north-korea',
            'polish_name'  => 'Koreańska Republika Ludowo-Demokratyczna',
            'country_code' => 'KP',
        ),
    52  =>
        array(
            'name'         => 'costa-rica',
            'polish_name'  => 'Kostaryka',
            'country_code' => 'CR',
        ),
    53  =>
        array(
            'name'         => 'cuba',
            'polish_name'  => 'Kuba',
            'country_code' => 'CU',
        ),
    54  =>
        array(
            'name'         => 'kuwait',
            'polish_name'  => 'Kuwejt',
            'country_code' => 'KW',
        ),
    55  =>
        array(
            'name'         => 'laos',
            'polish_name'  => 'Laos',
            'country_code' => 'LA',
        ),
    56  =>
        array(
            'name'         => 'lebanon',
            'polish_name'  => 'Liban',
            'country_code' => 'LB',
        ),
    57  =>
        array(
            'name'         => 'libya',
            'polish_name'  => 'Libia',
            'country_code' => 'LY',
        ),
    58  =>
        array(
            'name'         => 'liechtenstein',
            'polish_name'  => 'Liechtenstein',
            'country_code' => 'LI',
        ),
    59  =>
        array(
            'name'         => 'lithuania',
            'polish_name'  => 'Litwa',
            'country_code' => 'LT',
        ),
    60  =>
        array(
            'name'         => 'luxembourg',
            'polish_name'  => 'Luksemburg',
            'country_code' => 'LU',
        ),
    61  =>
        array(
            'name'         => 'latvia',
            'polish_name'  => 'Łotwa',
            'country_code' => 'LV',
        ),
    62  =>
        array(
            'name'         => 'north-macedonia',
            'polish_name'  => 'Macedonia',
            'country_code' => 'MK',
        ),
    63  =>
        array(
            'name'         => 'malaysia',
            'polish_name'  => 'Malezja',
            'country_code' => 'MY',
        ),
    64  =>
        array(
            'name'         => 'malta',
            'polish_name'  => 'Malta',
            'country_code' => 'MT',
        ),
    65  =>
        array(
            'name'         => 'morocco',
            'polish_name'  => 'Maroko',
            'country_code' => 'MA',
        ),
    66  =>
        array(
            'name'         => 'mexico',
            'polish_name'  => 'Meksyk',
            'country_code' => 'MX',
        ),
    67  =>
        array(
            'name'         => 'moldova',
            'polish_name'  => 'Mołdowa',
            'country_code' => 'MD',
        ),
    68  =>
        array(
            'name'         => 'monaco',
            'polish_name'  => 'Monako',
            'country_code' => 'MC',
        ),
    69  =>
        array(
            'name'         => 'mongolia',
            'polish_name'  => 'Mongolia',
            'country_code' => 'MN',
        ),
    70  =>
        array(
            'name'         => 'netherlands',
            'polish_name'  => 'Niderlandy',
            'country_code' => 'NL',
        ),
    71  =>
        array(
            'name'         => 'germany',
            'polish_name'  => 'Niemcy',
            'country_code' => 'DE',
        ),
    72  =>
        array(
            'name'         => 'nigeria',
            'polish_name'  => 'Nigeria',
            'country_code' => 'NG',
        ),
    73  =>
        array(
            'name'         => 'norway',
            'polish_name'  => 'Norwegia',
            'country_code' => 'NO',
        ),
    74  =>
        array(
            'name'         => 'new-zealand',
            'polish_name'  => 'Nowa Zelandia',
            'country_code' => 'NZ',
        ),
    75  =>
        array(
            'name'         => 'oman',
            'polish_name'  => 'Oman',
            'country_code' => 'OM',
        ),
    76  =>
        array(
            'name'         => 'pakistan',
            'polish_name'  => 'Pakistan',
            'country_code' => 'PK',
        ),
    77  =>
        array(
            'name'         => 'palestinian-national-authority',
            'polish_name'  => 'Palestyńska Władza Narodowa',
            'country_code' => 'PS',
        ),
    78  =>
        array(
            'name'         => 'panama',
            'polish_name'  => 'Panama',
            'country_code' => 'PA',
        ),
    79  =>
        array(
            'name'         => 'peru',
            'polish_name'  => 'Peru',
            'country_code' => 'PE',
        ),
    80  =>
        array(
            'name'         => 'poland',
            'polish_name'  => 'Polska',
            'country_code' => 'PL',
        ),
    81  =>
        array(
            'name'         => 'portugal',
            'polish_name'  => 'Portugalia',
            'country_code' => 'PT',
        ),
    82  =>
        array(
            'name'         => 'south-africa',
            'polish_name'  => 'Republika Południowej Afryki',
            'country_code' => 'ZA',
        ),
    83  =>
        array(
            'name'         => 'russia',
            'polish_name'  => 'Rosja',
            'country_code' => 'RU',
        ),
    84  =>
        array(
            'name'         => 'romania',
            'polish_name'  => 'Rumunia',
            'country_code' => 'RO',
        ),
    85  =>
        array(
            'name'         => 'san-marino',
            'polish_name'  => 'San Marino',
            'country_code' => 'SM',
        ),
    86  =>
        array(
            'name'         => 'senegal',
            'polish_name'  => 'Senegal',
            'country_code' => 'SN',
        ),
    87  =>
        array(
            'name'         => 'serbia',
            'polish_name'  => 'Republika Serbii i Republika Czarnogóry',
            'country_code' => 'RS',
        ),
    88  =>
        array(
            'name'         => 'singapore',
            'polish_name'  => 'Singapur',
            'country_code' => 'SG',
        ),
    89  =>
        array(
            'name'         => 'slovakia',
            'polish_name'  => 'Słowacja',
            'country_code' => 'SK',
        ),
    90  =>
        array(
            'name'         => 'slovenia',
            'polish_name'  => 'Słowenia',
            'country_code' => 'SI',
        ),
    91  =>
        array(
            'name'         => 'usa-no-ny-washington',
            'polish_name'  => 'USA (bez N.Y. i Waszyngtonu)',
            'country_code' => 'US',
        ),
    92  =>
        array(
            'name'         => 'new-york',
            'polish_name'  => 'USA - Nowy Jork',
            'country_code' => 'US',
        ),
    93  =>
        array(
            'name'         => 'washington',
            'polish_name'  => 'USA - Waszyngton',
            'country_code' => 'US',
        ),
    94  =>
        array(
            'name'         => 'syria',
            'polish_name'  => 'Syria',
            'country_code' => 'SY',
        ),
    95  =>
        array(
            'name'         => 'switzerland',
            'polish_name'  => 'Szwajcaria',
            'country_code' => 'CH',
        ),
    96  =>
        array(
            'name'         => 'sweden',
            'polish_name'  => 'Szwecja',
            'country_code' => 'SE',
        ),
    97  =>
        array(
            'name'         => 'tajikistan',
            'polish_name'  => 'Tadżykistan',
            'country_code' => 'TJ',
        ),
    98  =>
        array(
            'name'         => 'thailand',
            'polish_name'  => 'Tajlandia',
            'country_code' => 'TH',
        ),
    99  =>
        array(
            'name'         => 'tanzania',
            'polish_name'  => 'Tanzania',
            'country_code' => 'TZ',
        ),
    100 =>
        array(
            'name'         => 'tunisia',
            'polish_name'  => 'Tunezja',
            'country_code' => 'TN',
        ),
    101 =>
        array(
            'name'         => 'turkey',
            'polish_name'  => 'Turcja',
            'country_code' => 'TR',
        ),
    102 =>
        array(
            'name'         => 'turkmenistan',
            'polish_name'  => 'Turkmenistan',
            'country_code' => 'TM',
        ),
    103 =>
        array(
            'name'         => 'ukraine',
            'polish_name'  => 'Ukraina',
            'country_code' => 'UA',
        ),
    104 =>
        array(
            'name'         => 'uruguay',
            'polish_name'  => 'Urugwaj',
            'country_code' => 'UY',
        ),
    105 =>
        array(
            'name'         => 'uzbekistan',
            'polish_name'  => 'Uzbekistan',
            'country_code' => 'UZ',
        ),
    106 =>
        array(
            'name'         => 'venezuela',
            'polish_name'  => 'Wenezuela',
            'country_code' => 'VE',
        ),
    107 =>
        array(
            'name'         => 'hungary',
            'polish_name'  => 'Węgry',
            'country_code' => 'HU',
        ),
    108 =>
        array(
            'name'         => 'united-kingdom',
            'polish_name'  => 'Wielka Brytania',
            'country_code' => 'GB',
        ),
    109 =>
        array(
            'name'         => 'vietnam',
            'polish_name'  => 'Wietnam',
            'country_code' => 'VN',
        ),
    110 =>
        array(
            'name'         => 'italy',
            'polish_name'  => 'Włochy',
            'country_code' => 'IT',
        ),
    111 =>
        array(
            'name'         => 'cote-divoire',
            'polish_name'  => 'Wybrzeże Kości Słoniowej',
            'country_code' => 'CI',
        ),
    112 =>
        array(
            'name'         => 'zimbabwe',
            'polish_name'  => 'Zimbabwe',
            'country_code' => 'ZW',
        ),
    113 =>
        array(
            'name'         => 'united-arab-emirates',
            'polish_name'  => 'Zjednoczone Emiraty Arabskie',
            'country_code' => 'AE',
        ),
    114 =>
        array(
            'name'         => 'other-countries',
            'polish_name'  => 'Państwa inne',
            'country_code' => null,
        ),
    115 =>
        array(
            'name'         => 'gibraltar',
            'polish_name'  => 'Gibraltar',
            'country_code' => 'GI',
        ),
    116 =>
        array(
            'name'         => 'hong-kong',
            'polish_name'  => 'Honkong',
            'country_code' => 'HK',
        ),
    117 =>
        array(
            'name'         => 'taiwan',
            'polish_name'  => 'Tajwan',
            'country_code' => 'TW',
        ),
    118 =>
        array(
            'name'         => 'montenegro',
            'polish_name'  => 'Czarnogóra',
            'country_code' => 'ME',
        ),
    120 =>
        array(
            'name'         => 'kosovo',
            'polish_name'  => 'Kosowo',
            'country_code' => 'XK',
        ),
    121 =>
        array(
            'name'         => 'vatican',
            'polish_name'  => 'Watykan',
            'country_code' => 'VA',
        ),
);