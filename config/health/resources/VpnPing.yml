name: VpnPing
abbreviation: mindento_vpn_ping
checker: PragmaRX\Health\Checkers\Ping
notify: true
binary: "{{ config('health.services.ping.bin') }}"
error_message:
  'The host "%s" exceeded the maximum accepted latency on ping: last ping was %s, accepted is %s'
column_size: 3
targets:
  - server:
      name: "{{ config('health.resources.vpn_ping_ip') }}"
      hostname: "{{ config('health.resources.vpn_ping_ip') }}"
      accepted_latency: 1000