<?php

use Modules\Accounting\Priv\Enum\AccountingAccountTypeEnum;
use Modules\Accounting\Priv\Enum\InstanceAccountEnum;

return [
    'super_admin_ids'              => [
        1,
    ],

    'document_element_group_names' => [
        [
            'name' => 'entertainment',
            'order' => 4,
            'slug' => 'expense-representation-group',
        ],
        [
            'name' => 'travel',
            'order' => 1,
            'slug' => 'expense-trip-group',
        ],
        [
            'name' => 'accommodation',
            'order' => 2,
            'slug' => 'expense-accommodatiom-group',
        ],
        [
            'name' => 'consumption',
            'order' => 3,
            'slug' => 'expense-consumption-group',
        ],
        [
            'name' => 'periodic',
            'order' => 5,
            'slug' => 'expense-periodic',
        ],
    ],
    'document_element_types'       => [
        [
            'name'   => 'flights',
            'group' => 'travel',
            'order' => 1,
            'vat' => 'P0',
            'slug' => 'expense-trip-group-flights-type',
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT,
            'request_element_type' => \App\PlaneTrip::RELATION_NAME,
        ],
        [
            'name'   => 'rail',
            'group' => 'travel',
            'order' => 2,
            'vat' => 'P1',
            'slug' => 'expense-trip-group-train-type',
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT,
            'request_element_type' => \App\TrainTrip::RELATION_NAME,
        ],
        [
            'name'   => 'taxi',
            'group' => 'travel',
            'order' => 3,
            'vat' => 'No VAT',
            'slug' => 'expense-trip-group-taxi-type',
            'account' => '********',
            'visible_in_trip' => true,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT
        ],
        [
            'name'   => 'rent-a-car',
            'group' => 'travel',
            'order' => 4,
            'vat' => 'P2',
            'slug' => 'expense-trip-group-car-rental-type',
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT,
            'request_element_type' => \App\RentedCarTrip::RELATION_NAME,
        ],
        [
            'name'   => 'company-car',
            'group' => 'travel',
            'order' => 5,
            'vat' => 'P2',
            'slug' => 'expense-trip-group-company-car-type',
            'request_element_type' => \App\CompanyCarTrip::OTHER_COSTS_DOCUMENT_TYPE,
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT
        ],
        [
            'name'   => 'fuel',
            'group' =>  'travel',
            'order' => 6,
            'vat' => 'P2',
            'slug' => 'expense-trip-group-fuel-type',
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT,
            'request_element_type' => \App\CompanyCarTrip::FUEL_DOCUMENT_TYPE,
        ],
        [
            'name'   => 'parking',
            'group' => 'travel',
            'order' => 7,
            'vat' => 'P2',
            'slug' => 'expense-trip-group-parking-type',
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT
        ],
        [
            'name'   => 'toll',
            'group' => 'travel',
            'order' => 8,
            'vat' => 'P2',
            'slug' => 'expense-trip-group-buisnes-trips-type',
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT
        ],
        [
            'name'   => 'hotel',
            'group' => 'accommodation',
            'order' => 1,
            'vat' => 'No VAT',
            'slug' => 'expense-accommodation-group-hotel-type',
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_DOCUMENT_DATE,
            'request_element_type' => \App\Accomodation::RELATION_NAME
        ],
        [
            'name'   => 'consumption',
            'group' => 'consumption',
            'order' => 1,
            'vat' => 'No VAT',
            'slug' => 'expense-consumption-group-consumption-type',
            'account' => '********',
            'visible_in_trip' => true,
            'visible_in_expense' => true,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_DOCUMENT_DATE
        ],
        [
            'name'   => 'external-meetings',
            'group' => 'entertainment',
            'order' => 1,
            'vat' => 'No VAT',
            'slug' => 'expense-representation-group-meeting-with-contractor-type',
            'account' => '********', //todo: change to correct - no info yet
            'visible_in_trip' => true,
            'visible_in_expense' => true,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_DOCUMENT_DATE
        ],
        [
            'name'   => 'internal-meetings',
            'group' => 'entertainment',
            'order' => 2,
            'vat' => 'No VAT',
            'slug' => 'expense-representation-group-internal-meeting-type',
            'account' => '********', //todo: change to correct - no info yet
            'visible_in_trip' => true,
            'visible_in_expense' => true,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_DOCUMENT_DATE
        ],



        [
            'name'   => 'Prom',
            'group' => 'Podróż',
            'order' => 6,
            'vat' => 'VZ08',
            'slug' => 'expense-trip-group-ferry-type',
            'account' => '403-2500',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_SETTLEMENT,
            'request_element_type' => \App\FerryBoatTrip::RELATION_NAME,
        ],
        [
            'name'   => 'periodic',
            'group' => 'periodic',
            'order' => 7,
            'vat' => null,
            'slug' => 'expense-periodic-group-periodic-type',
            'account' => '********',
            'visible_in_trip' => false,
            'visible_in_expense' => false,
            'is_active' => false,
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_DOCUMENT_DATE
        ],
    ],
    'vatNumbers'                   => [
        [
            'value' => 0,
            'name'  => 'F-ra VAT zakup 0 % - zak.zw.ze sprzed.op.',
            'code'  => 'P0',
            'accounting_account_code' => '********'
        ],
        [
            'value' => 8,
            'name'  => 'F-ra VAT zakup 8% - zak.zw.ze sprzed.op.',
            'code'  => 'P1',
            'accounting_account_code' => '********'
        ],
        [
            'value' => 23,
            'name'  => 'F-ra VAT zakup 23% - zak.zw.ze sprzed.op.',
            'code'  => 'P2',
            'accounting_account_code' => '********'
        ],
        [
            'value' => 5,
            'name'  => 'F-ra VAT zakup 5% - zak.zw.ze sprzed.op.',
            'code'  => 'P3',
            'accounting_account_code' => '********'
        ],
        [
            'value' => 0,
            'name'  => 'F-ra VAT zakup ZW - zak.zw.ze sprzed.op.',
            'code'  => 'PZ',
            'accounting_account_code' => '********'
        ],
        [
            'value' => 0,
            'name'  => 'Paragon, inny dokument',
            'code'  => 'No VAT',
            'accounting_account_code' => '********'
        ],
    ],
    'accountingAccounts'           => [
        [
            'name'           => 'input-tax',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-diets',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-diets-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-accommodation',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-accommodation-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-accommodation-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-accommodation-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-accommodation-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-accommodation-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-accommodation-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-accommodation-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-consumption',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-consumption-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-train',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-train-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-train-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-train-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-train-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-train-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-train-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-train-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-car-transport',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-car-transport-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],

        [
            'name'           => 'business-trips-car-transport-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
            'instance_account' => InstanceAccountEnum::YES(),
        ],
        [
            'name'           => 'business-trips-car-transport-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
            'instance_account' => InstanceAccountEnum::YES(),
        ],
        [
            'name'           => 'business-trips-car-transport-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
            'instance_account' => InstanceAccountEnum::YES(),
        ],
        [
            'name'           => 'business-trips-car-transport-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
            'instance_account' => InstanceAccountEnum::YES(),
        ],
        [
            'name'           => 'business-trips-car-transport-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
            'instance_account' => InstanceAccountEnum::YES(),
        ],
        [
            'name'           => 'business-trips-car-transport-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
            'instance_account' => InstanceAccountEnum::YES(),
        ],
        [
            'name'           => 'business-trips-planes',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-planes-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-planes-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-planes-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-planes-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-planes-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-planes-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-planes-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-other-transport',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-other-transport-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-other-transport-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-other-transport-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-other-transport-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-other-transport-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-other-transport-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-other-transport-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-fuel',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-fuel-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-fuel-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-fuel-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-fuel-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-fuel-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-fuel-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'business-trips-fuel-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'commission',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'commission-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'commission-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'commission-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'commission-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'commission-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'commission-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'commission-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'other-travel-expenses-parking-motorway-tolls',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'other-travel-expenses-parking-motorway-tolls-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'other-travel-expenses-parking-motorway-tolls-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'other-travel-expenses-parking-motorway-tolls-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'other-travel-expenses-parking-motorway-tolls-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'other-travel-expenses-parking-motorway-tolls-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'other-travel-expenses-parking-motorway-tolls-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'other-travel-expenses-parking-motorway-tolls-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'internal-training',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'internal-training-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'internal-training-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'internal-training-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'internal-training-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'internal-training-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'internal-training-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'internal-training-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'highway-vignette',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'highway-vignette-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'highway-vignette-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'highway-vignette-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'highway-vignette-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'highway-vignette-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'highway-vignette-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'highway-vignette-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'costs-of-representation',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'costs-of-representation-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'costs-of-representation-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'costs-of-representation-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'costs-of-representation-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'costs-of-representation-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'costs-of-representation-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'costs-of-representation-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'tax',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'tax-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'tax-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'tax-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'tax-powiazane',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'tax-powiazane-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'tax-powiazane-start-up',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'tax-powiazane-start-up-nkup',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::DEBIT(),
        ],
        [
            'name'           => 'settlements-with-other-domestic-suppliers',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::CREDIT(),
        ],
        [
            'name'           => 'other-settlements-with-employees',
            'account_number' => '********',
            'type'           => AccountingAccountTypeEnum::CREDIT(),
            'instance_account' => InstanceAccountEnum::YES(),
        ],




    ],
    'instanceAccounts' => [
        'providers_account'                   => '********',
        'employees_settlement_account'        => '********',
        'technical_account'                   => null,
        'national_db_travel_expenses_account' => '********',
        'foreign_db_travel_expenses_account'  => '********',
        'national_ct_travel_expenses_account' => '********',
        'foreign_ct_travel_expenses_account'  => '********',
    ],
    'travelExpensesRates' => [
        'national' => [
            'breakfast' => 0.25,
            'lunch' => 0.5,
            'dinner' => 0.25,
            'drive' => 0.2,
            'accomodation' => 1.5,
            'travelLessThan24h' => [
                'lessThan8hours' => 0,
                'from8To12hours' => 0.5,
                'over12hours' => 1,
            ],
            'travelOver24h' => [
                'fullDay' => 1,
                'lessThan8hours' => 0.5,
                'over8hours' => 1,
            ],
        ],
        'foreign' => [
            'breakfast' => 0.15,
            'lunch' => 0.3,
            'dinner' => 0.3,
            'drive' => 0.1,
            'accomodation' => 1.5,
            'travelLessThan24h' => [
                'lessThan8hours' => 0.33,
                'from8To12hours' => 0.5,
                'over12hours' => 1,
            ],
            'travelOver24h' => [
                'over12hours' => 1,
                'from8To12hours' => 0.5,
                'lessThan8hours' => 0.33,
            ],
        ],
    ],
    'privateCarAccounting' => [
        'lowCapacityEngine'   => 0.89,
        'upperCapacityEngine' => 1.15,
        'motorcycle'          => 0.69,
        'moped'               => 0.42,
    ],
    'currenciesOrderWeights' => [
        'PLN' => 1,
        'EUR' => 2,
        'USD' => 3,
    ],
    'emptyDomain' => env('EMPTY_DOMAIN', 'empty.vtl.localhost'),
    'demoDomain' => env('DEMO_DOMAIN', 'demo.vtl.localhost'),
    'allowedDocumentExtensions' => [
        'jpg',
        'jpeg',
        'png',
        'pdf',
    ]
];
