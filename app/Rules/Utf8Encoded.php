<?php

declare(strict_types=1);

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;

class Utf8Encoded implements Rule
{
    private const ENCODINGS = [
        'UTF-8'
    ];

    private const EXTENSIONS = [
        'csv',
    ];

    public function passes($attribute, $value): bool
    {
        /** @var UploadedFile $value */
        $splFileObject = $value->openFile();
        $extension = $splFileObject->getExtension();

        if (!in_array($extension, self::EXTENSIONS, true)) {
            return true;
        }

        $encoding = mb_detect_encoding($splFileObject->fgets(), 'UTF-8, ASCII');
        $passes = in_array($encoding, static::ENCODINGS);

        if ($passes === false) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, trans('validation.utf8-file', ['encoding' => $encoding]));
        }

        return $passes;
    }

    public function message(): string
    {
        return trans('validation.utf8-file');
    }
}
