<?php

declare(strict_types=1);

namespace App\Rules;

use App\Request;
use Carbon\Carbon;
use Illuminate\Contracts\Validation\Rule;

class TripStartDateIsLessOrEqualTripEnd extends AbstractTripDate implements Rule
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function passes($attribute, $value): bool
    {
        if ($this->shouldApplyValidation($this->request) === false) {
            return true;
        }

        $tripStartDateManual = Carbon::createFromFormat('Y-m-d H:i:s', $value)->startOfDay();
        $tripEndDateFromRequestElements = $this->request->getExtremeElementDate('min')->startOfDay();

        if ($tripStartDateManual->lessThanOrEqualTo($tripEndDateFromRequestElements)) {
            return true;
        }

        return false;
    }

    public function message(): string
    {
        return trans('validation.request-trip-start-date-must-be-less-or-equal-first-target-point');
    }
}
