<?php


namespace App\Services\BorderCrossing;

use App\Country;
use App\Repositories\LocationRepository;
use App\Request;
use App\Services\Country\CountryService;
use App\Services\RequestCourse\Location\LocationElement;
use App\Services\RequestCourse\Location\LocationElementsCollection;
use App\Services\RequestCourse\Location\PlaneTripLocationElement;
use App\Services\RequestCourse\Location\TrainTripLocationElement;
use App\Services\RequestCourse\RequestCourseService;
use Carbon\Carbon;
use App\Services\Cache;
use Illuminate\Support\Collection;

class BorderCrossingsEstimator
{
    private const DEFAULT_LAST_CROSSING_TIME_BEFORE_DESTINATION = 14;

    /** @var Request  */
    protected $request;

    /** @var BorderCrossingCollection|Collection */
    protected $crossings;

    /** @var Collection */
    protected $locations;

    /** @var CountryService */
    protected $countryService;

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->crossings = new BorderCrossingCollection();
        $this->countryService = resolve(CountryService::class);
    }

    public function estimate(bool $fresh = false, bool $forEstimation = true): BorderCrossingCollection
    {
        if ($fresh === true) {
            Cache::deleteTags('estimatedBorderCrossings'.$this->request->id);
        }

        return Cache::get('estimatedBorderCrossings'.$this->request->id, function() use ($fresh, $forEstimation) {
            return $this
                    ->setBorderCrossingsBasedOnRequestCourse(
                        RequestCourseService::instance()
                            ->setRequest($this->request)
                            ->getLocations($fresh, $forEstimation)
                            ->withoutAccommodationIfHaveAnotherElements()
                            ->sortAsc()
                );
        }, 100, [$this->request->cacheTag(), 'borderCrossings:'.$this->request->id]);
    }

    protected function setBorderCrossingsBasedOnRequestCourse(LocationElementsCollection $locations)
    {
        $this->locations = $locations;

        $locations->each(function(LocationElement $location, $key) use (&$locations) {

            $next = $locations->get($key + 1);
            $country = $location->getCountry($this->request->instance->location->country_code);

            if ($this->isFirstIndex($key)) {
                $this->addFirstCrossing($location);
                if($locations->count() === 1) {
                    ($this->isCrossingFromAnotherCountry($location->getCountry($this->request->instance->location->country_code)))
                        ? $this->addLastCrossing($location->getCountry($this->request->instance->location->country_code), $location)
                        : $this->updateLastCrossing($location);
                }
            } else if($this->isSecondIndex($key) && $this->collectionHasNextLocation($next)) {
                $this->addSecondCrossing($location, $next, $country);
            } else {
                if($this->collectionHasNextLocation($next)) {
                    $this->isCrossingFromAnotherCountry($country)
                        ? $this->addCrossing($country, $location, $next, $location instanceof PlaneTripLocationElement ? $location->getDateForBorderCrossing($this->request->instance) : null)
                        : $this->updateCrossing($location, $next, $location instanceof PlaneTripLocationElement ? $location->getDateForBorderCrossing($this->request->instance) : null);

                } else {
                    ($this->isCrossingFromAnotherCountry($country))
                        ? $this->addLastCrossing($country, $location)
                        : $this->updateLastCrossing($location);
                }
            }
        });

        if ($this->crossings->isNotEmpty()) {
            $endCountry = LocationRepository::getCountryFromLocation($this->request->endLocation);
            if ($this->crossings->last()->country_id !== $endCountry->id) {
                $locationElement = new LocationElement(
                    $this->request->endLocation,
                    $this->crossings->last() instanceof PlaneTripLocationElement
                        ?  $this->crossings->last()->getDateForBorderCrossing($this->request->instance)
                        : $this->request->trip_ends->endOfDay()
//                            ->subMinutes(self::DEFAULT_LAST_CROSSING_TIME_BEFORE_DESTINATION) // As requested in https://mindento.atlassian.net/browse/MIN-845
                    ,
                    false,
                    0
                );

                $this->addLastCrossing($endCountry, $locationElement);
            }

            //$this->setLastCrossingToEndOfDay();
            $this->markLastCrossingAsTargetCountry();
        }

        return $this->crossings;
    }

    protected function isFirstIndex($index)
    {
        return $index == 0;
    }

    protected function isSecondIndex($index)
    {
        return $index == 1;
    }

    protected function collectionHasNextLocation($next)
    {
        return $next !== null;
    }

    protected function isCrossingFromAnotherCountry(Country $country): bool
    {
        return $this->crossings->last()->country_id !== $country->id;
    }

    protected function addFirstCrossing(LocationElement $location)
    {
        if(!empty($location->getLocation()['country_code'])) {
            $country = $this->countryService->getCountryByCode($location->getLocation()['country_code']);
        } else {
            $country = LocationRepository::getCountryFromLocation($location->getLocation()['country_code'] ?? $this->request->startLocation);
        }

        $this->crossings->push(new BorderCrossingItem([
            'country_id' => $country->id,
            'country' => $country->toArray(),
            'start' => $location instanceof TrainTripLocationElement ? $location->getDepartureDate() : $this->request->trip_starts->copy(),
            'end' => $location->getDate()->copy(),
            'target' => $this->checkCountryIsDestination($country->id),
//            'target' => false,
            'instance_country_id' => $this->request->instance->country_id
        ]));
    }

    protected function addSecondCrossing(LocationElement $location, LocationElement $next, Country $country)
    {
        if($this->collectionHasNextLocation($next)) {
            $this->isCrossingFromAnotherCountry($country)
                ? $this->addCrossing($country, $location, $next, $location instanceof PlaneTripLocationElement ? $location->getDateForBorderCrossing($this->request->instance) : null)
                : $this->updateCrossing($location, $next, $location instanceof PlaneTripLocationElement ? $location->getDateForBorderCrossing($this->request->instance) : null);
        }
    }

    protected function addCrossing(Country $country, LocationElement $location, LocationElement $nextLocation, Carbon $date = null)
    {
        $this->crossings->push(new BorderCrossingItem([
            'country_id' => $country->id,
            'country' => $country->toArray(),
            'start' => $date instanceof Carbon
                ? $date->copy()
                : $location->getDate()->copy(),
            'end' => $nextLocation->getDate()->copy(),
            'target' => $this->checkCountryIsDestination($country->id),
//            'target' => $location->isTarget(),
            'instance_country_id' => $this->request->instance->country_id
        ]));
    }

    protected function updateCrossing(LocationElement $location, LocationElement $nextLocation, Carbon $date = null)
    {
        $element = $this->crossings->pop();
        $element->end = $date instanceof Carbon
                            && $element->end instanceof Carbon
                            && $element->end->greaterThan($date)
                                ? $date
                                : $nextLocation->getDate()->copy();
        $element->target = $element->target ? $element->target : $location->isTarget();
        $this->crossings->push($element);
    }

    protected function addLastCrossing(Country $country, LocationElement $location)
    {
        $this->crossings->push(new BorderCrossingItem([
            'country_id' => $country->id,
            'country'    => $country->toArray(),
            'start'      => $location instanceof PlaneTripLocationElement
                                && $location->getDateForBorderCrossing($this->request->instance)instanceof Carbon
                                && $location->getDateForBorderCrossing($this->request->instance)->greaterThan($location->getDate())
                                    ? $location->getDateForBorderCrossing($this->request->instance)
                                    : $location->getDate(),
            'end'        => $location instanceof PlaneTripLocationElement
                                && $location->getDateForBorderCrossing($this->request->instance)instanceof Carbon
                                && $location->getDateForBorderCrossing($this->request->instance)->greaterThan($location->getDate())
                                    ? $location->getDateForBorderCrossing($this->request->instance)
                                    : $this->request->trip_ends->copy(),
            'target'     => $location->isTarget(),
            'instance_country_id' => $this->request->instance->country_id
        ]));
    }

    protected function updateLastCrossing(LocationElement $location)
    {
        $element = $this->crossings->pop();

        $element->end = $location instanceof PlaneTripLocationElement
                            && $location->getDateForBorderCrossing($this->request->instance)instanceof Carbon
                            && $location->getDateForBorderCrossing($this->request->instance)->greaterThan($element->end)
                                ? $location->getDateForBorderCrossing($this->request->instance)
                                : $this->request->trip_ends->copy();
        $element->target = $element->target ? $element->target : $location->isTarget();

        $this->crossings->push($element);
    }

    protected function setLastCrossingToEndOfDay()
    {
        $last = $this->crossings->pop();
        $last->end = $last->end->endOfDay();
        $this->crossings->push($last);
    }

    protected function markLastCrossingAsTargetCountry(): void
    {
        $last = $this->crossings->pop();
        $last->target = true;
        $this->crossings->push($last);
    }

    protected function checkCountryIsDestination(int $countryId): bool
    {
        return $this->locations->filter(function (LocationElement $locationElement) use ($countryId) {
            $country = resolve(CountryService::class)->getCountryByCode($locationElement->getLocation()['country_code']);
            return $country->id === $countryId;
        })->count() > 0;
    }
}