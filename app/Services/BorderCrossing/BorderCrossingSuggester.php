<?php

namespace App\Services\BorderCrossing;

use App\Instance;
use App\Request;
use App\RequestBorderCrossing;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class BorderCrossingSuggester
{
    private static $instance;

    /**
     * @var Request
     */
    private $request;

    /**
     * @var Collection
     */
    private $borderCrossings;

    /** @var BorderCrossingCollection  */
    private $borderCrossingItems;

    protected function __construct(Request $request = null)
    {
        $this->request = $request;
        $this->borderCrossings = collect();
        $this->borderCrossingItems = collect();
    }

    /**
     * @return Request
     */
    public function getRequest(): Request
    {
        return $this->request;
    }

    /**
     * @param Request $request
     * @return BorderCrossingSuggester
     */
    public function setRequest(Request $request): BorderCrossingSuggester
    {
        $this->request = $request;

        return $this;
    }

    public static function instance(Request $request = null): BorderCrossingSuggester
    {
        if (static::$instance === null) {
            static::$instance = new static($request);
        }

        return static::$instance;
    }

    /**
     * @return Collection
     */
    public function suggest(bool $estimate = true): Collection
    {
        $this->borderCrossingItems = (new BorderCrossingService($this->request))->getBorderCrossingCollectionEstimated($estimate);
        $this->findFirstCrossingToAnotherCountry();
        $this->createBorderCrossingsBasedOnEstimation();

        $this->borderCrossings->transform(function(RequestBorderCrossing $current, $index) {
//            if($current->date->isStartOfDay()) {
//                $current->date = $this->predictDateForCrossing($current, $index);
//                $current->suggested = true;
//            }
            return $current;
        });

        $this->saveBorderCrossings();

        return $this->borderCrossings;
    }

    /**
     * @return void
     */
    protected function createBorderCrossingsBasedOnEstimation(): void
    {
        $this->borderCrossingItems->each(function (BorderCrossingItem $item) {
            $requestBorderCrossing = new RequestBorderCrossing();

            $requestBorderCrossing->instance_id = $this->getRequest()->instance->id;
            $requestBorderCrossing->request_id = $this->getRequest()->id;
            $requestBorderCrossing->country_id = $item->country_id;
            $requestBorderCrossing->date = $item->start->copy();
            $requestBorderCrossing->target = $item->target;;

            $this->borderCrossings->push($requestBorderCrossing);
        });
    }

    /**
     * @return void
     */
    protected function saveBorderCrossings(): void
    {
        $this->borderCrossings->each(function(RequestBorderCrossing $crossing) {
            $crossing->save();
        });
    }

    /**
     * Removes crossings from collection that are in the same country as instance's country
     * until first crossing from another country will appear
     */
    protected function findFirstCrossingToAnotherCountry()
    {
        while ($this->borderCrossingItems->first() !== null
            && $this->borderCrossingItems->first()->country_id === $this->getRequest()->instance->country_id) {
            $this->borderCrossingItems->shift();
        }
    }

    /**
     * @param $currentCrossing
     * @param int $currentCrossingIndex
     * @return Carbon
     */
    protected function predictDateForCrossing($currentCrossing, int $currentCrossingIndex): Carbon
    {
        $sameDayCrossings = $this->getCrossingsFromSameDay($currentCrossing);
        $prevDate = $this->getPrevTimeFromSameDay($sameDayCrossings, $currentCrossing, $currentCrossingIndex);
        $nextDate = $this->getNextTimeFromSameDay($sameDayCrossings, $currentCrossing, $currentCrossingIndex);

        return $prevDate->average($nextDate);
    }

    /**
     * @param $currentCrossing
     * @return Collection
     */
    protected function getCrossingsFromSameDay($currentCrossing): Collection
    {
        return $this->borderCrossings->filter(function($crossing) use($currentCrossing) {
            return $currentCrossing->date->isSameDay($crossing->date);
        });
    }

    /**
     * @param Collection $crossingsFromSameDay
     * @param $currentCrossing
     * @param int $currentCrossingIndex
     * @return Carbon
     */
    protected function getPrevTimeFromSameDay(Collection $crossingsFromSameDay, $currentCrossing, int $currentCrossingIndex): Carbon
    {
        $element = $crossingsFromSameDay->filter(function($crossing, $sameDayIndex) use(&$currentCrossingIndex) {
            return $sameDayIndex < $currentCrossingIndex && !$crossing->date->isStartOfDay();
        })->first();

        return $element ? $element->date : $currentCrossing->date->copy();
    }

    /**
     * @param Collection $crossingsFromSameDay
     * @param $currentCrossing
     * @param int $currentCrossingIndex
     * @return Carbon
     */
    protected function getNextTimeFromSameDay(Collection $crossingsFromSameDay, $currentCrossing, int $currentCrossingIndex): Carbon
    {
        $element = $crossingsFromSameDay->filter(function($crossing, $sameDayIndex) use(&$currentCrossingIndex) {
            return $sameDayIndex > $currentCrossingIndex && !$crossing->date->isStartOfDay();
        })->first();

        return $element ? $element->date : $currentCrossing->date->copy()->endOfDay();
    }
}