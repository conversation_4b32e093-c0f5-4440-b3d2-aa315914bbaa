<?php

declare(strict_types=1);

namespace App\Services\RequestPosting\Enums;

use App\Enum\AbstractEnum;

/**
 * Class ItemPostingDtoTypeEnum
 * @package App\Services\RequestPosting\Enums
 *
 * @method static ItemPostingDtoTypeEnum EXPENSE
 * @method static ItemPostingDtoTypeEnum DOCUMENT
 */
class ItemPostingDtoTypeEnum extends AbstractEnum
{
    private const EXPENSE = 'EXPENSE';
    private const DOCUMENT = 'DOCUMENT';
}