<?php

declare(strict_types=1);

namespace App\Services\RequestPosting;

use App\Services\RequestPosting\Enums\ItemPostingDetailDtoTypeEnum;
use Illuminate\Support\Collection;

class ItemPostingDetailDto
{
    private ItemPostingDetailDtoTypeEnum $type;
    private Collection $accountDimensions;
    private string $netAmount;
    private ?string $vatAccountCode;
    private ?string $vatAmount;
    private ?string $expenseType;

    public function __construct(
        ItemPostingDetailDtoTypeEnum $type,
        Collection $accountDimensions,
        string $netAmount,
        ?string $vatAccountCode,
        ?string $vatAmount,
        ?string $expenseType
    ) {
        $this->type = $type;
        $this->accountDimensions = $accountDimensions;
        $this->netAmount = $netAmount;
        $this->vatAccountCode = $vatAccountCode;
        $this->vatAmount = $vatAmount;
        $this->expenseType = $expenseType;
    }

    public function getType(): ItemPostingDetailDtoTypeEnum
    {
        return $this->type;
    }

    public function getAccountDimensions(): Collection
    {
        return $this->accountDimensions;
    }

    public function getNetAmount(): string
    {
        return $this->netAmount;
    }

    public function getVatAmount(): ?string
    {
        return $this->vatAmount;
    }

    public function getVatAccountCode(): ?string
    {
        return $this->vatAccountCode;
    }

    public function getExpenseType(): ?string
    {
        return $this->expenseType;
    }
}