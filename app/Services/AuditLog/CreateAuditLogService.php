<?php

namespace App\Services\AuditLog;

use App\AuditLog;
use Carbon\Carbon;

class CreateAuditLogService
{
    public function createLoginAuditLogFromArray($event)
    {
        $eventArray = (array) $event;
        if (isset($eventArray['credentials'])) {
            unset($eventArray['credentials']['password']);
        }

        $auditLog          = new AuditLog();
        $auditLog->event   = get_class($event);
        $auditLog->content = $eventArray;
        $auditLog->date    = Carbon::now();

        $auditLog->save();
    }
}