<?php

declare(strict_types=1);

namespace App\Services\Storage;

class StorageServiceDto
{
    protected $fileName;
    protected $fileTypeHeader;

    /**
     * @deprecated try to use $path instead
     */
    protected $fileContent = '';

    private ?string $path;

    public function __construct(
        string $fileName,
        string $fileTypeHeader,
        string $fileContent = null,
        string $path = null
    ) {
        $this->fileName = $fileName;
        $this->fileTypeHeader = $fileTypeHeader;
        $this->path = $path;

        if (!$path) {
            $this->fileContent = $fileContent;
        }
    }

    public function getFileName(): string
    {
        return $this->fileName;
    }

    public function getFileTypeHeader(): string
    {
        return $this->fileTypeHeader;
    }

    /**
     * @deprecated try to use getPath instead
     */
    public function getFileContent(): string
    {
        if ($this->fileContent) {
            return $this->fileContent;
        }

        if (\File::exists($this->getPath())) {
            return \File::get($this->getPath());
        }

        return '';
    }

    public function getPath(): ?string
    {
        return $this->path;
    }
}
