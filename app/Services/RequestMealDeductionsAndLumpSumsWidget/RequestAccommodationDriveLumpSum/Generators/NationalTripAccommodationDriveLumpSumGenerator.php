<?php


namespace App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestAccommodationDriveLumpSum\Generators;


use App\Request;
use \Illuminate\Support\Carbon;
use App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestAccommodationDriveLumpSum\AccommodationDriveLumpSum\AccommodationDriveLumpSumsCollection;

class NationalTripAccommodationDriveLumpSumGenerator extends AccommodationDriveLumpSumGenerator
{
    /**
     * RequestAccommodationDriveLumpSumGenerator constructor.
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request    = $request;
        $this->lumpSums = new AccommodationDriveLumpSumsCollection();
    }

    public function generate(): AccommodationDriveLumpSumsCollection
    {
        /** @var Carbon $start */
        $start         = $this->request->trip_starts->copy();
        $end           = $this->request->trip_ends->copy();
        $start->second = 0;

        while ($start->lte($end)) {
            $start->diffInHours($end) >= Carbon::HOURS_PER_DAY
                ? $this->addFullDayAccommodationDriveLumpSum($start)
                : $this->addAccommodationDriveLumpSum($start, $end);

            $start->addDay();
        }

        return parent::generate();
    }
}