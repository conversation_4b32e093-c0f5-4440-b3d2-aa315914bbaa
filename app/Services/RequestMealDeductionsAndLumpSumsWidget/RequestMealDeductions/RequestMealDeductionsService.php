<?php


namespace App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestMealDeductions;


use App\Events\RequestDeductionWidgetUpdated;
use App\Helpers\RequestEndLocation;
use App\Helpers\RequestStartLocation;
use App\Http\Responses\Response2;
use App\Repositories\RequestRepository;
use App\Request;
use App\Services\BorderCrossing\BorderCrossingService;
use App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestMealDeductions\Generators\RequestAbroadTripMealDeductionsGenerator;
use App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestMealDeductions\Generators\RequestNationalTripMealDeductionsGenerator;
use App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestMealDeductions\Responses\RequestMealDeductionsResponse;
use App\Services\TravelExpenses\TravelExpensesService;
use App\Traits\UpdateSummariesTrait;

class RequestMealDeductionsService
{
    use UpdateSummariesTrait;

    /** @var Request */
    protected $request;

    /** @var BorderCrossingService */
    protected $borderCrossingService;

    /** @var RequestRepository */
    protected $requestRepository;

    /** @var RequestMealDeductionsDAO */
    protected $mealDeductionsDAO;

    /** @var TravelExpensesService  */
    protected $travelExpensesService;

    private bool $shouldAddTravelAndLumpSums;

    public function __construct(
        BorderCrossingService $borderCrossingService,
        RequestRepository $requestRepository,
        RequestMealDeductionsDAO $DAO,
        TravelExpensesService $travelExpensesService,
        Request $request,
        ?bool $shouldAddTravelAndLumpSums = null
    ) {
        $this->borderCrossingService = $borderCrossingService;
        $this->requestRepository     = $requestRepository;
        $this->mealDeductionsDAO     = $DAO;
        $this->request               = $request;
        $this->travelExpensesService = $travelExpensesService->init($this->request);
        $this->shouldAddTravelAndLumpSums = $shouldAddTravelAndLumpSums ?? $request->canAccountDelegation();
    }

    protected function shouldAddTravelAndLumpSums(): bool
    {
        return $this->shouldAddTravelAndLumpSums;
    }

    public function recreateRequestDeductions($withEvent = true, bool $crossingsChanged)
    {
        if($crossingsChanged || $this->mealDeductionsDAO->count() === 0) {
            $this->mealDeductionsDAO->delete();

            $mealDeductionsGenerator = $this->request->national_trip
                ? new RequestNationalTripMealDeductionsGenerator($this->request)
                : new RequestAbroadTripMealDeductionsGenerator($this->request,
                                                               $this->borderCrossingService->getBorderCrossingCollection());

            $this->mealDeductionsDAO->save($mealDeductionsGenerator->generate());
        }

        $this->sendEvent($withEvent);
    }

    protected function sendEvent($withEvent)
    {
        if ($withEvent) {
            $payload = $this->mealDeductionsWidgetCollection()->toArray();

            event(new RequestDeductionWidgetUpdated($payload, $this->request->slug));

            $this->eventsEnabled = $withEvent;
            $this->sendUpdateSummariesEvent($this->request);
        }
    }

    public function mealDeductionsWidgetResponse(): Response2
    {
        if($this->request->border_crossing_state === false) {
            return Response2::collection([]);
        }

        $responseService = $this->createRequestMealDeductionsResponse();

        return $responseService->response();
    }

    public function mealDeductionsWidgetCollection()
    {
        if (false === $this->shouldAddTravelAndLumpSums()) {
            return collect();
        }

        if($this->request->border_crossing_state === false) {
            return collect();
        }

        $service = $this->createRequestMealDeductionsResponse();

        return $service->getMealDeductionsResponseCollection();
    }

    /**
     * @return RequestMealDeductionsResponse
     */
    private function createRequestMealDeductionsResponse(): RequestMealDeductionsResponse
    {
        $deductions = $this->mealDeductionsDAO->get();
        $expenses   = $this->travelExpensesService->setSettledExpensesCollection()->getExpensesCollection();

        $service = (new RequestMealDeductionsResponse(
            $deductions,
            $this->borderCrossingService->getBorderCrossingCollection(),
            new RequestStartLocation($this->request->trip_starts, $this->request->startLocation),
            new RequestEndLocation($this->request->trip_ends, $this->request->endLocation),
            $expenses
        ));

        return $service;
    }
}
