<?php

declare(strict_types=1);

namespace App\Services\TenantCloner\Job;

use App\Company;
use App\Group;
use App\HasManyMpkCollection;
use App\Instance;
use App\User;
use Carbon\Carbon;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Users\Priv\Services\PasswordGeneratorService;
use Modules\Users\Priv\Strategy\PasswordHashingStrategy;

class PrimaryUsersJob implements JobInterface
{
    protected const ADMIN = 'mu';
    protected const FINANCE = 'finance';
    protected const CONTROL = 'control';
    protected const AGENT = 'agent';
    protected const REGULAR = 'regular';
    protected const APPROVER = 'approver';
    protected const TAXI = 'taxi';
    protected const CFO = 'cfo';
    protected const CEO = 'ceo';
    protected const COORDINATOR = 'coordinator';
    protected const REGIONAL_POSITION = 'regpos';
    protected const BM = 'bm';
    protected const HR = 'hr';

    protected const USERS_TO_CREATE = [
        self::ADMIN => User::GROUP_NAME_ADMINISTRATOR,
        self::FINANCE => User::GROUP_NAME_FINANCE,
        self::CONTROL => User::GROUP_NAME_CONTROL,
        self::AGENT => User::GROUP_NAME_AGENT,
        self::REGULAR => User::GROUP_NAME_REGULAR,
        self::APPROVER => User::GROUP_NAME_APPROVER,
        self::TAXI => User::GROUP_NAME_TAXI,
        self::CFO => User::GROUP_NAME_CFO,
        self::CEO => User::GROUP_NAME_CEO,
        self::COORDINATOR => User::GROUP_NAME_COORDINATOR,
        self::REGIONAL_POSITION => User::GROUP_NAME_REGIONAL_POSITION,
        self::BM => User::GROUP_NAME_BM,
        self::HR => User::GROUP_NAME_HR,
    ];

    const MASTER_DOMAIN = "mindento.com";
    const EMAIL_TEMPLATE = '%s.%s@%s';

    /** @var PasswordHashingStrategy */
    protected $passwordHashingStrategy;

    /** @var PasswordGeneratorService */
    protected $passwordGeneratorService;

    public function __construct(
        PasswordHashingStrategy $passwordHashingStrategy,
        PasswordGeneratorService $passwordGeneratorService
    ) {
        $this->passwordHashingStrategy = $passwordHashingStrategy;
        $this->passwordGeneratorService = $passwordGeneratorService;
    }

    public function run(Instance $instance): void
    {
        $domainParts = explode('.', $instance->domain);
        $subDomain = $domainParts[0];

        foreach (self::USERS_TO_CREATE as $type => $groupName) {
            $regularGroup = Group::where(['name' => $groupName, 'instance_id' => $instance->id])->first();

            if (!$regularGroup) {
                continue;
            }

            $user = new User();
            $email = sprintf(self::EMAIL_TEMPLATE, $subDomain, $type, self::MASTER_DOMAIN);
            $user->email = $email;
            $user->instance_id = $instance->id;
            $user->mpk_id = Mpk::where('instance_id', $instance->id)->first()->id ?? null;
            $user->grade = 0;
            $user->level = 0;
            $user->company_id = Company::where('instance_id', $instance->id)->first()->id ?? null;
            $user->slug = str_random(16);
            $user->remember_token = str_random(16);
            $user->password = $this->passwordHashingStrategy->hash($this->passwordGeneratorService->random(16));
            $user->first_name = ucwords($type);
            $user->last_name = ucwords('user');
            $user->nationality_id = $instance->country_id;
            $user->citizenship_id = $instance->country_id;
            $user->internal = 1;
            $user->birth_date = Carbon::create(2000, 1, 1);
            $user->phone = '+48000000000';
            $user->employee_unique_identifier = $email;
            $user->save();

            $user->syncMpks(HasManyMpkCollection::createFromRequestData([
                ['id' => $user->mpk_id, 'percentage' => 100, 'main' => true],
            ]));

            $regularGroup->users()->attach([$user->id]);
        }
    }
}
