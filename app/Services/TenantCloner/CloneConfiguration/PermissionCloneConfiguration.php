<?php

declare(strict_types=1);

namespace App\Services\TenantCloner\CloneConfiguration;

use App\Group;
use App\Permission;

class PermissionCloneConfiguration extends AbstractCloneConfiguration
{
    /**
     * @inheritDoc
     */
    public function model(): string
    {
        return Permission::class;
    }

    /**
     * @inheritDoc
     */
    public function foreignKeys(): array
    {
        return [self::GROUP_ID => Group::class];
    }
}
