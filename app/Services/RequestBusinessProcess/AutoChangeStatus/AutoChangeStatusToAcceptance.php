<?php

namespace App\Services\RequestBusinessProcess\AutoChangeStatus;

use App\Request;
use App\Services\LoginAs\OriginallyLoggedAsService;
use App\Services\RequestBusinessProcess\OwnerActions\SendToAcceptance;
use App\Services\RequestBusinessProcess\RequestBusinessProcess;
use App\User;
use Illuminate\Contracts\Mail\Mailer;

class AutoChangeStatusToAcceptance extends RequestBusinessProcess
{
    private OriginallyLoggedAsService $originallyLoggedAsService;
    private Mailer $mailer;

    public function __construct(
        Request $request,
        User $user,
        OriginallyLoggedAsService $originallyLoggedAsService,
        Mailer $mailer
    ) {
        parent::__construct($request, $user);

        $this->originallyLoggedAsService = $originallyLoggedAsService;
        $this->mailer = $mailer;
    }

    public function run(array $data = []): bool
    {
        if (!$this->request->isPeriodicInvoice() && !$this->request->isPeriodicExpense()) {
            return false;
        }

        $process = new SendToAcceptance(
            $this->request,
            $this->user,
            $this->originallyLoggedAsService->getOriginallyLoggedAs(),
            $this->mailer
        );

        return $process->run([]);
    }
}