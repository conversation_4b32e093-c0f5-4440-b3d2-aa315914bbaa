<?php

declare(strict_types=1);

namespace App\Services\RequestBusinessProcess\AccountantActions;

use App\Services\LoginAs\OriginallyLoggedAsService;

class CancelAssignment extends AccountantBusinessProcess
{
    protected $isProxy;
    protected $proxyUser;

    public function run(array $data = []): bool
    {
        abort_unless($this->request->instance_id === $this->user->instance_id, 400, trans('error.invalid-user'));
        abort_unless(
            $this->user->can('cancelAssignmentToAccountant', $this->request),
            403,
            trans('error.user-cannot-cancel-assigned-accountant')
        );

        /** @var OriginallyLoggedAsService $proxyChecker */
        $proxyChecker = app()->make(OriginallyLoggedAsService::class);

        $this->isProxy = $proxyChecker->isLoggedAsAnyone();
        if (!empty($this->isProxy)) {
            $this->proxyUser = $proxyChecker->getOriginallyLoggedAs();
        }

        try {
            \DB::beginTransaction();
            $this->addComment();

            $this->request->accounting_user_id = null;

            $success = $this->request->save();


            \DB::commit();

            return $success;
        } catch (\Exception $exception) {
            \DB::rollBack();

            throw $exception;
        }

        return true;
    }

    public function getAutoCommentContent(): array
    {
        if (!$this->proxyUser) {
            $content = [
                'autocontent' => 'request-comment.user-removed-assigned-accountant-from-request',
                'params' => ['user' => $this->getAccountant()->full_name]
            ];
        } else {
            $content = [
                'autocontent' => 'request-comment.proxy-user-removed-assigned-accountant-from-request',
                'params' => [
                    'proxy' => $this->proxyUser->full_name,
                    'user' => $this->getAccountant()->full_name,
                ]
            ];
        }

        return $content;
    }
}
