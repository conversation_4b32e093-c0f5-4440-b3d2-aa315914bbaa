<?php


namespace App\Services\RequestBusinessProcess\OwnerActions;


use App\Notifications\RequestTripStarted;
use App\Services\RequestBusinessProcess\ChangeStatus\ChangeStatusToTrip;
use App\Services\RequestBusinessProcess\RequestBusinessProcess;

class ChangeRequestStatusToTrip extends RequestBusinessProcess
{

    public function run(array $data = []): bool
    {
        $success = ChangeStatusToTrip::instance($this->request, $this->user)->run($data);

        if ($success) {
            $this->sendNotifications();
        }

        return $success;
    }

    protected function sendNotifications()
    {
        $this->request->user->notify(new RequestTripStarted($this->request->slug));
    }
}