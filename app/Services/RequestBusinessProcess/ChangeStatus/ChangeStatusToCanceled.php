<?php

namespace App\Services\RequestBusinessProcess\ChangeStatus;

use App\Request;
use App\Services\RequestBusinessProcess\RequestBusinessProcess;
use App\User;
use Carbon\Carbon;
use Illuminate\Contracts\Events\Dispatcher;
use Modules\Common\Events\RequestCancelledEvent;

class ChangeStatusToCanceled extends RequestBusinessProcess
{
    private Dispatcher $eventDispatcher;

    public function __construct(Request $request, User $user)
    {
        parent::__construct($request, $user);
        $this->eventDispatcher = resolve(Dispatcher::class);
    }

    /**
     * @param array $data
     * @return bool
     * @throws \Exception
     */
    public function run(array $data = []): bool
    {
        try {
            \DB::beginTransaction();

            $this->request->status      = Request::STATUS_CANCELED;
            $this->request->canceled_at = Carbon::now();
            $success                    = $this->request->save();
            $this->eventDispatcher->dispatch(new RequestCancelledEvent($this->request->getSlug()));
            \DB::commit();

            return $success;
        } catch (\Throwable $exception) {
            \DB::rollBack();

            throw $exception;
        }
    }

    public function getAutoCommentContent(): array
    {
        return array(
            'autocontent' => 'request-comment.request-status-changed-to-canceled',
            'params' => []
        );
    }
}
