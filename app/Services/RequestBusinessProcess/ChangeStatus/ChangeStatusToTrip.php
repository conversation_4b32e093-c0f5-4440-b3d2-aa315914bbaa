<?php


namespace App\Services\RequestBusinessProcess\ChangeStatus;


use App\Request;
use App\Services\RequestBusinessProcess\RequestBusinessProcess;
use Carbon\Carbon;

class ChangeStatusToTrip extends RequestBusinessProcess
{
    public function run(array $data = []): bool
    {
        try {
            \DB::beginTransaction();

            $this->setSentAt();
            $this->setApplicatedAt();
            $this->resetAccountedAt();

            // $this->addAutoComment();

            $this->request->status = Request::STATUS_TRIP;

            $success = $this->request->save();
            \DB::commit();

            return $success;
        } catch (\Throwable $exception) {
            \DB::rollBack();

            throw $exception;
        }
    }

    protected function setApplicatedAt()
    {
        if ($this->request->applicated_at === null) {
            $this->request->applicated_at = Carbon::now();
        }
    }

    protected function setSentAt()
    {
        if ($this->request->sent_at === null) {
            $this->request->sent_at = Carbon::now();
        }
    }

    protected function resetAccountedAt()
    {
        $this->request->accounted_at = null;
    }

    public function getAutoCommentContent(): array
    {
        return array(
            'autocontent' => 'request-comment.request-status-changed-to-trip',
            'params' => []
        );
    }
}