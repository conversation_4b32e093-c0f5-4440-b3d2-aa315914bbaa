<?php


namespace App\Services\RequestBusinessProcess\ChangeStatus;


use App\Request;
use App\Services\RequestBusinessProcess\RequestBusinessProcess;
use App\Services\RequestNumber\GenerateRequestNumberService;
use Carbon\Carbon;

class ChangeStatusToWaitingForAcceptance extends RequestBusinessProcess
{
    /**
     * @param array $data
     * @return bool
     * @throws \Exception
     */
    public function run(array $data = []): bool
    {
        try {
            \DB::beginTransaction();

            $this->resetAcceptors();
            $this->setSentAt();
            $this->resetApplicatedAt();
            $this->resetAccountedAt();

            // $this->addAutoComment();

            $this->request->status = Request::STATUS_WAITING_FOR_ACCEPTANCE;
            resolve(GenerateRequestNumberService::class)->issueNumber($this->request);

            $success = $this->request->save();
            \DB::commit();

            return $success;
        } catch (\Throwable $exception) {
            \DB::rollBack();

            throw $exception;
        }
    }

    protected function setSentAt()
    {
        if ($this->request->sent_at === null) {
            $this->request->sent_at = Carbon::now();
        }
    }

    protected function resetApplicatedAt()
    {
        $this->request->applicated_at = null;
    }

    protected function resetAccountedAt()
    {
        $this->request->accounted_at = null;
    }

    protected function resetAcceptors()
    {
        $this->request->acceptors->map(function ($acceptor) {
            $acceptor->pivot->accepted = ($acceptor->pivot->step === 0 ? Request::ACCEPTOR_STATUS_PENDING : Request::ACCEPTOR_STATUS_WAITING_FOR_STEP);
            $acceptor->pivot->acceptance_date = null;
            $acceptor->pivot->notificated_at = null;
            $acceptor->pivot->token = static::generateAcceptorToken();

            $acceptor->pivot->save();
        });
    }

    public function getAutoCommentContent(): array
    {
        return array(
            'autocontent' => 'request-comment.request-status-changed-to-waiting-for-acceptance',
            'params' => []
        );
    }
}