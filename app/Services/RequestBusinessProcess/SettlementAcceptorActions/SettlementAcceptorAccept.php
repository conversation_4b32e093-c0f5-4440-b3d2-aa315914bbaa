<?php


namespace App\Services\RequestBusinessProcess\SettlementAcceptorActions;


use App\Notifications\RequestReturnedForImprovementOwnerNotification;
use App\Notifications\RequestSettlementHasBeenAcceptedNotification;
use App\Request;
use App\Services\LoginAs\OriginallyLoggedAsService;
use App\Services\RequestBusinessProcess\OwnerActions\SendNotificationsToCurrentStepSettlementAcceptors;
use App\Services\RequestBusinessProcess\SettlementAcceptance;
use App\User;
use Illuminate\Support\Carbon;

class SettlementAcceptorAccept extends SettlementAcceptorBusinessProcess
{
    protected $isProxy;
    protected $proxyUser;

    public function run(array $data = []): bool
    {
        abort_unless($this->requestCanBeSettled(), 422, trans('error.request-cannot-be-accepted-by-settlement'));
        abort_unless($this->user->can('acceptSettlement', $this->request), 403, trans('error.user-cannot-accept-settlement'));

        $this->request->load(['settlementAcceptors']);

        $acceptor                         = $this->getSettlementAcceptor();

        $acceptor->pivot->token           = null;
        $acceptor->pivot->save();

        $acceptor->pivot->accepted        = Request::ACCEPTOR_STATUS_ACCEPTED;
        $acceptor->pivot->acceptance_date = Carbon::now();


        /** @var OriginallyLoggedAsService $proxyChecker */
        $proxyChecker = app()->make(OriginallyLoggedAsService::class);

        $this->isProxy = $proxyChecker->isLoggedAsAnyone();
        if (!empty($this->isProxy)) {
            $this->proxyUser = $proxyChecker->getOriginallyLoggedAs();
        }

        try {
            \DB::beginTransaction();

            $statusSuccess = true;

            $acceptorSuccess = $acceptor->pivot->save();
            $success         = $acceptorSuccess && $statusSuccess;

//            $this->addAutoComment();

            if ($this->proxyUser !== null) {
                $comment = $comment = $this->addCommentAs($data['comment']['content'] ?? null, $this->proxyUser);
            } else {
                $comment = $comment = $this->addComment($data['comment']['content'] ?? null);
            }

            if($comment) {
                $acceptor->pivot->comment_id = $comment->id;
                $acceptor->pivot->save();
            }

            $step = $acceptor->pivot->step;

            $nextStepAcceptors = $this->request->settlementAcceptors()->where(['step' => $step + 1, 'accepted' => Request::ACCEPTOR_STATUS_WAITING_FOR_STEP])->get();
            $currentStepAccepted = ($this->request->settlementAcceptors()->where([['step', '=', $step],['accepted', '!=', Request::ACCEPTOR_STATUS_ACCEPTED]])->exists() === false);
            if ($this->requestNeedStatusChange()) {
                if (SettlementAcceptance::instance($this->request, $this->user)->run($data)) {
                    $this->sendNotifications();
                    \DB::commit();
                } else {
                    \DB::rollBack();
                }
            } elseif ($currentStepAccepted && $nextStepAcceptors->count() > 0) {
                $nextStepAcceptors->each(function(User $user) {
                    $user->pivot->accepted = Request::ACCEPTOR_STATUS_PENDING;
                    $user->pivot->save();
                });

                $success = true;

                \DB::commit();

                SendNotificationsToCurrentStepSettlementAcceptors::instance($this->request, $this->user)->run([]);

            } else {
                \DB::commit();
            }

        } catch (\Throwable $exception) {
            \Log::error($exception);
            \DB::rollBack();

            $success = false;
        }

        return $success;
    }

    protected function requestNeedStatusChange()
    {
        return $this->request->settlementAcceptors->count() === $this->request->settlementAcceptors->where('pivot.accepted',
                Request::ACCEPTOR_STATUS_ACCEPTED)->count();
    }

    protected function sendNotifications()
    {
        $this->request->user->notify(new RequestSettlementHasBeenAcceptedNotification($this->request->slug));
    }

    public function getAutoCommentContent(): array
    {
        if (\Auth::user()->isAgent()) {

            return [
                'autocontent' => 'request-comment.agent-request-settlement-acceptor-accepted',
            ];
        }

        if(!$this->proxyUser) {
            $content = [
                'autocontent' => 'request-comment.request-settlement-acceptor-accepted',
                'params' =>  ['user' => $this->getSettlementAcceptor()->full_name],
            ];
        }
        else {
            $content = [
                'autocontent' => 'request-comment.proxy-settlement-acceptor-accepted',
                'params' => [
                    'proxy' => $this->proxyUser->full_name,
                    'user' => $this->getSettlementAcceptor()->full_name,
                ]
            ];
        }

        return $content;
    }
}