<?php


namespace App\Services\AccountingBalance;


use App\Document;
use App\DocumentElement;
use App\Nova\Resources\Accounting\AccountingAccount;
use App\Request;
use App\RequestAccountingMileageAllowance;
use App\RequestAccountingTravelExpenses;
use Modules\Accounting\Priv\Entities\VatNumber;
use App\Vendors\Math;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Exceptions\AccountingAccountNotFound;
use Modules\Accounting\Priv\Services\AccountingAccountSelection\AccountingAccountSelectionService;
use Modules\Accounting\Priv\Services\AccountingAccountSelection\Strategy\AccountingAccountSelectionStrategy;

/**
 * @property Collection dbAccounts
 * @property Collection ctAccounts
 */
class AccountingBalance
{
    /** @var Request */
    protected $request;

    /** @var AccountInfoCollection */
    protected $accounts;

    /** @var AccountInfoCollection */
    protected $dbAccounts;

    /** @var AccountInfoCollection */
    protected $ctAccounts;
    private ?bool $shouldAddTravelAndLumpSums;

    private AccountingAccountSelectionService $accountingAccountSelectionService;

    /**
     * AccountingBalance constructor.
     * @param Request $request
     */
    public function __construct(
        Request $request,
        ?bool $shouldAddTravelAndLumpSums = null
    )
    {
        $this->request    = $request;
        $this->accounts   = new AccountInfoCollection();
        $this->dbAccounts = new AccountInfoCollection();
        $this->ctAccounts = new AccountInfoCollection();
        $this->shouldAddTravelAndLumpSums = $shouldAddTravelAndLumpSums ?? $request->isDelegation();
        $this->accountingAccountSelectionService = resolve(AccountingAccountSelectionService::class);
    }

    public function getBalance()
    {
        $this->setAccountsInfoFromDocumentElements();

        if ( $this->request->isInAccounting() === true && $this->request->lump_sum_accounted === true) {
            if($this->shouldAddTravelAndLumpSums) {
                $this->addTravelExpensesToAccountsInfo();
            }

            $this->addMileageAllowanceAccountInfo();
        }

        $this->accounts = $this->accounts->groupBy('type');

        $this->dbAccounts = (new AccountInfoCollection($this->accounts->get(AccountInfo::TYPE_DEBIT,
            collect())))->groupAccounts()->sort(AccountInfo::TYPE_DEBIT);
        $this->ctAccounts = (new AccountInfoCollection($this->accounts->get(AccountInfo::TYPE_CREDIT,
            collect())))->groupAccounts()->sort(AccountInfo::TYPE_CREDIT);

        return $this->dbAccounts->merge($this->ctAccounts->getAccounts());
    }

    public function getDebitAccountsSum(): string
    {
        return $this->dbAccounts->sum(AccountInfo::TYPE_DEBIT);
    }

    public function getCreditAccountsSum(): string
    {
        return $this->ctAccounts->sum(AccountInfo::TYPE_CREDIT);
    }

    /**
     * @param $gross
     * @param VatNumber $vatNumber
     * @return object
     */
    protected function getElementsNetAndVat(DocumentElement $element)
    {
        return (object)[
            'vat'   => $element->getVatInDefaultInstanceCurrency(),
            'net'   => $element->getNetInDefaultInstanceCurrency(),
            'gross' => $element->getGrossInDefaultInstanceCurrency()
        ];
    }

    protected function setAccountsInfoFromDocumentElements(): void
    {
        $this->request
            ->accountingDocuments
            ->filter(function (Document $document) {
                return $document->isAccounted();
            })
            ->each(function (Document $document) {
                $document->elements->each(function ($element) use (&$document) {
                    $this->getInvoiceAccounts($element);
            });
            $this->addPrivateExpenseAccountsInfo($document);
        });
    }

    protected function addPrivateExpenseAccountsInfo(Document $document): void
    {
        $exchangeRate = $document->getExchangeRate();
        $elementsSum = Math::round($document->elements->reduce(function($acc, DocumentElement $element) use(&$exchangeRate) {
            return $acc + $element->gross * $exchangeRate;
        }, 0), 2);

        $convertedGross = $document->getConvertedGross();
        if($convertedGross > $elementsSum) {
            $privateExpense = $convertedGross - $elementsSum;

            $this->accounts->push(new AccountInfo([
                'name'     => $document->debitAccount->getTranslatedName(),
                'number'   => $document->debitAccount->account_number,
                'ctAmount' => $privateExpense,
                'type'     => AccountInfo::TYPE_CREDIT,
                'order'    => '3'
            ]));

            $this->accounts->push(new AccountInfo([
                'name'     => $document->creditAccount->getTranslatedName(),
                'number'   => $document->creditAccount->account_number,
                'dbAmount' => $privateExpense,
                'type'     => AccountInfo::TYPE_DEBIT,
                'order'    => '3'
            ]));
        }
    }

    protected function addTravelExpensesToAccountsInfo(): void
    {
        if($this->request->accountingTravelExpenses->isNotEmpty()) {
            $this->request->accountingTravelExpenses->each(function(RequestAccountingTravelExpenses $expenses) {
                $dbAccount = $expenses->accountingAccount;

                if ($dbAccount instanceof AccountingAccount === false) {
                    $dbAccount = $this->accountingAccountSelectionService->debit()->getTravelExpensesAccountingAccount(
                        $this->request->instance,
                        $this->request
                    );
                }

                $creditAccountingAccount = $this->accountingAccountSelectionService->credit()->getTravelExpensesAccountingAccount(
                    $this->request->instance,
                    $this->request
                );

                $this->accounts->push(new AccountInfo([
                    'name'     => $dbAccount->getTranslatedName(),
                    'number'   => $dbAccount->account_number,
                    'dbAmount' => $expenses->instance_currency_amount,
                    'type'     => AccountInfo::TYPE_DEBIT,
                    'order'    => '3'
                ]));

                $this->accounts->push(new AccountInfo([
                    'name' => $creditAccountingAccount->getTranslatedName(),
                    'number' => $creditAccountingAccount->account_number,
                    'ctAmount' => $expenses->instance_currency_amount,
                    'type'     => AccountInfo::TYPE_CREDIT,
                    'order'    => '3'
                ]));
            });
        }
    }

    protected function addMileageAllowanceAccountInfo(): void
    {
        if($this->request->accountingMileageAllowances->isNotEmpty()) {
            $this->request->accountingMileageAllowances->each(function (RequestAccountingMileageAllowance $accountingMileageAllowance) {
                $debitAccountingAccount = $accountingMileageAllowance->accountingAccount;

                if ($debitAccountingAccount instanceof AccountingAccount === false) {
                    try {
                        $debitAccountingAccount = $this->accountingAccountSelectionService->debit()->getMileageAllowanceAccountingAccount(
                            $this->request->instance,
                            $this->request
                        );
                    } catch (AccountingAccountNotFound $e) {
                        $debitAccountingAccount = $this->accountingAccountSelectionService->debit()->getTravelExpensesAccountingAccount(
                            $this->request->instance,
                            $this->request
                        );
                    }
                }

                $dbAccount = $debitAccountingAccount;
                $ctAccount = $this->request->mileageAllowancesCreditAccount;

                $this->accounts->push(new AccountInfo([
                    'name'     => $dbAccount->getTranslatedName(),
                    'number'   => $dbAccount->account_number,
                    'dbAmount' => $accountingMileageAllowance->amount,
                    'type'     => AccountInfo::TYPE_DEBIT,
                    'order'    => '3'
                ]));

                if($ctAccount !== null) {
                    $this->accounts->push(new AccountInfo([
                        'name'     => $ctAccount->getTranslatedName(),
                        'number'   => $ctAccount->account_number,
                        'ctAmount' => $accountingMileageAllowance->amount,
                        'type'     => AccountInfo::TYPE_CREDIT,
                        'order'    => '3'
                    ]));
                }
            });
        }
    }

    /**
     * @param DocumentElement $element
     * @param $exchangeRate
     * @param $ctInstanceAccount
     */
    protected function getInvoiceAccounts(DocumentElement $element): void
    {
        $vat          = $element->vatNumber;

        if (($vat instanceof VatNumber) === false) {
            return;
        }
        $document = $element->document;
        $exchangeRate = $document->getExchangeRate();

        $vAccount     = $vat->accountingAccount;
        $account      = $element->accountingAccount;
        $elementGross = Math::multiply($element->gross, $exchangeRate, 2);

        $amounts = $this->getElementsNetAndVat($element);

        if ($vAccount) {
            $this->accounts->push(new AccountInfo([
                'name'     => $vAccount->getTranslatedName(),
                'number'   => $vAccount->account_number,
                'dbAmount' => $amounts->vat,
                'type'     => AccountInfo::TYPE_DEBIT,
                'order'    => '2'
            ]));
        }

        if ($account) {
            $this->accounts->push(new AccountInfo([
                'name'     => $account->getTranslatedName(),
                'number'   => $account->account_number,
                'dbAmount' => $amounts->net,
                'type'     => AccountInfo::TYPE_DEBIT,
                'order'    => '1'
            ]));
        }

        $this->accounts->push(new AccountInfo([
            'name'     => $document->creditAccountingAccount->getTranslatedName(),
            'number'   => $document->creditAccountingAccount->account_number,
            'ctAmount' => $elementGross,
            'type'     => AccountInfo::TYPE_CREDIT,
            'order'    => '3'
        ]));
    }
}
