<?php

declare(strict_types=1);

namespace App\Services\Monitoring;

use App\Instance;
use App\Interfaces\Monitoring\InstanceMonitoringCheckServiceInterface;
use App\Interfaces\Monitoring\MonitoringServiceInterface;
use App\Services\Monitoring\Resources\Checks\ServiceStatusDto;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use PragmaRX\Health\Service;
use PragmaRX\Health\Support\Resource;
use Ramsey\Uuid\Uuid;

class MonitoringService implements MonitoringServiceInterface
{
    public const MINDENTO_GLOBAL_CHECK = 'MindentoGlobalCheck';
    public const MINDENTO_GLOBAL_CHECK_ABBREVIATION = 'mindento_global_check';
    public const ERROR = 'error';
    public const OK = 'ok';
    public const MAINTENANCE = 'maintenance';
    protected const SERVICE_STATUSES_COLLECTION_NAME = 'serviceStatuses';

    protected $healthService;
    protected $instanceMonitoringCheckService;

    public function __construct(
        Service $healthService,
        InstanceMonitoringCheckServiceInterface $instanceMonitoringCheckService
    ) {
        $this->healthService = $healthService;
        $this->instanceMonitoringCheckService = $instanceMonitoringCheckService;
    }

    public function getServiceStatuses(Instance $instance): array
    {
        try {
            if ($instance->getMaintenanceModeSettings()->isEnabled() === true) {
                $serviceStatusDto = new ServiceStatusDto(
                    Uuid::uuid4()->toString(),
                    $_SERVER['HTTP_HOST'],
                    self::MINDENTO_GLOBAL_CHECK,
                    self::MAINTENANCE,
                    json_encode([]),
                    Carbon::now()->toDateTimeString(),
                    self::MINDENTO_GLOBAL_CHECK_ABBREVIATION
                );

                return [self::SERVICE_STATUSES_COLLECTION_NAME => [$serviceStatusDto->toArray()]];
            }

            $basicChecks = $this->healthService->checkResources();
            $instanceChecks = $this->instanceMonitoringCheckService->checkResources($instance);

            return [self::SERVICE_STATUSES_COLLECTION_NAME => $this->transform(
                    $basicChecks->merge($instanceChecks)
                )
                ->values()
                ->toArray()
            ];
        } catch (\Throwable $exception) {
            report($exception);

            $serviceStatusDto = new ServiceStatusDto(
                Uuid::uuid4()->toString(),
                $_SERVER['HTTP_HOST'],
                self::MINDENTO_GLOBAL_CHECK,
                self::ERROR,
                json_encode($exception->getMessage(), JSON_PRETTY_PRINT),
                Carbon::now()->toDateTimeString(),
                self::MINDENTO_GLOBAL_CHECK_ABBREVIATION
            );

            return [self::SERVICE_STATUSES_COLLECTION_NAME => [$serviceStatusDto->toArray()]];
        }
    }

    protected function transform(Collection $checks): Collection
    {
        return $checks->transform(function (Resource $resource) {
            $isHealthy = $resource->isHealthy();
            $targets = $resource->targets;
            $errors = null;

            if ($isHealthy === false) {
                $targetsResults = $targets->map(function ($target) use ($errors) {
                    return [
                        'healthy' => $target->getResult()->healthy,
                        'name' => $target->getName(),
                        'errorMessage' => $target->getResult()->errorMessage,
                    ];
                });

                $errors[] = $targetsResults;
            }

            $serviceStatusDto = new ServiceStatusDto(
                $resource->id,
                $_SERVER['HTTP_HOST'],
                $resource->name,
                $resource->isHealthy() ? self::OK : self::ERROR,
                json_encode($errors, JSON_PRETTY_PRINT),
                Carbon::now()->toDateTimeString(),
                $resource->abbreviation
            );

            return $serviceStatusDto->toArray();
        });
    }
}