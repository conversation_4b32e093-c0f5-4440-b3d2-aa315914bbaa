<?php

declare(strict_types=1);

namespace App\Services\Monitoring\Resources\Checks;

class ServiceStatusDto
{
    protected $id;
    protected $host;
    protected $check;
    protected $status;
    protected $details;
    protected $lastChecked;
    protected $abbreviation;

    public function __construct(
        string $id,
        string $host,
        string $check,
        string $status,
        string $details,
        string $lastChecked,
        string $abbreviation
    ) {
        $this->id = $id;
        $this->host = $host;
        $this->check = $check;
        $this->status = $status;
        $this->details = $details;
        $this->lastChecked = $lastChecked;
        $this->abbreviation = $abbreviation;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getHost(): string
    {
        return $this->host;
    }

    public function getCheck(): string
    {
        return $this->check;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getDetails(): string
    {
        return $this->details;
    }

    public function getLastChecked(): string
    {
        return $this->lastChecked;
    }

    public function getAbbreviation(): string
    {
        return $this->abbreviation;
    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }
}
