<?php


namespace App\Services\RulesService\Rules;


use App\Helpers\NameTranslation;
use App\Instance;
use App\Request;
use App\Services\RulesService\Message\RuleMessage;

class RequestTripEndedRule extends Rule
{
    const NAME = 'request_trip_ended_rule';

    /** @var Request */
    protected $model;

    /**
     * RequestNeedTargetPointRule constructor.
     * @param Request $request
     * @throws \Throwable
     */
    public function __construct(Request $model, Instance $instance)
    {
        parent::__construct($model, $instance);
    }

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->requestTripEnded();
    }

    protected function requestTripEnded(): bool
    {
        if (!$this->model->trip_ends->isPast()) {
            $this->messages->push(new RuleMessage($this, new NameTranslation('rules.request-trip-not-ended'), false));
            return false;
        }

        return true;
    }
}