<?php

namespace App\Services\RulesService;

use App\Compliance\Accommodation;
use App\Request;
use App\User;
use Modules\Analytics\Priv\Entities\RequestAccountDimensionItem;

class ConfigResolver
{
    protected array $config;

    protected ?User $user;

    private $model;

    public function __construct(array $config, ?User $user, $model = null)
    {
        $this->config = $config;
        $this->user = $user;
        $this->model = $model;
    }

    public function resolve(): array
    {
        if ($this->user === null) {
            return $this->resolveForNullableUser();
        }

        $skipMessage = $this->resolveSkipMessage();

        $resolvedForMountedUser = $this->resolveForMountedUser();

        $resolvedForMountedRequest = $this->resolveForMountedRequestAccountDimension($resolvedForMountedUser);

        if ($resolvedForMountedRequest !== null) {
            return array_merge($resolvedForMountedRequest, $skipMessage);
        }

        return array_merge($resolvedForMountedUser, $skipMessage);
    }

    protected function resolveForNullableUser(): array
    {
        return $this->config;
    }

    protected function resolveSkipMessage(): array
    {
        return ['skip_message' => $this->config['skip_message'] ?? false];
    }

    protected function resolveForMountedUser(): array
    {
        return $this->config['grade-' . $this->user->grade] ?? $this->config;
    }

    private function resolveForMountedRequestAccountDimension(array $resolvedConfigForMountedUser): ?array
    {
        if ($this->model === null) {
            return null;
        }

        if ($this->model instanceof Accommodation) {
            $accountDimensionItems = $this->model->getRequestAccountDimensionItems();
        } else if ($this->model instanceof Request) {
            $accountDimensionItems = $this->model->accountDimensionItems;
        } elseif ($this->model->request instanceof Request) {
            $accountDimensionItems = $this->model->request->accountDimensionItems;
        } else {
            return null;
        }

        $accountDimensionCodes = $this->fetchAccountDimensionCodesForResolvedConfig($resolvedConfigForMountedUser);

        if (count($accountDimensionCodes) === 0) {
            return null;
        }

        foreach ($accountDimensionCodes as $accountDimensionCode) {
            /** @var RequestAccountDimensionItem $accountDimensionItem */
            $accountDimensionItem = $accountDimensionItems->filter(
                function (RequestAccountDimensionItem $accountDimensionItem) use ($accountDimensionCode) {
                    return $accountDimensionItem->accountDimension->code === $accountDimensionCode;
                }
            )->first();

            if ($accountDimensionItem === null) {
                continue;
            }

            return $resolvedConfigForMountedUser[sprintf(
                'account_dimension-%s-item-%s',
                $accountDimensionItem->accountDimension->code,
                $accountDimensionItem->accountDimensionItem->code
            )] ?? [];
        }

        return [];
    }

    private function fetchAccountDimensionCodesForResolvedConfig(array $resolvedConfigForMountedUser): array
    {
        $accountDimensionCodes = [];

        foreach ($resolvedConfigForMountedUser as $key => $value) {
            if (preg_match('/^account_dimension-([^-\s]+)-item-[^-\s]+$/', $key, $matches)) {
                $accountDimensionCodes[] = $matches[1];
            }
        }

        return array_unique($accountDimensionCodes);
    }
}