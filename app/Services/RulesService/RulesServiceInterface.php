<?php


namespace App\Services\RulesService;


use App\Compliance\Exceptions\ComplianceRulesFailedException;
use App\Services\RulesService\Message\RuleMessageCollection;
use App\Services\RulesService\Rules\Rule;
use Illuminate\Support\Collection;

interface RulesServiceInterface
{
    /**
     * @param Rule $rule
     * @return RulesServiceInterface
     */
    public function addRule(Rule $rule): RulesServiceInterface;

    /**
     * @param Collection $rules
     * @return RulesServiceInterface
     */
    public function addRules(Collection $rules): RulesServiceInterface;

    /**
     * @return RuleMessageCollection
     */
    public function getMessages(): RuleMessageCollection;

    /**
     * @param $ruleName
     * @return bool
     */
    public function isValid($ruleName): bool;

    /**
     * @return bool
     */
    public function allRulesAreValid(): bool;

    /**
     * @return RulesServiceInterface
     */
    public function validate(): RulesServiceInterface;

    /**
     * @param Collection $rules
     * @return RuleMessageCollection
     */
    public function validateWithMessages(Collection $rules):RuleMessageCollection;

    /**
     * @param Collection $rules
     * @return RuleMessageCollection
     * @throws ComplianceRulesFailedException
     */
    public function validateWithException(Collection $rules):RuleMessageCollection;

}