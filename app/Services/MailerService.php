<?php

declare(strict_types=1);

namespace App\Services;

use App\BillingAPI\DTOs\Notification\EmailNotificationSettingsDto;
use App\Enum\EmailNotificationModeEnum;
use App\Instance;
use Illuminate\Contracts\Mail\Mailable;
use Illuminate\Mail\Mailer as LaravelMailer;
use Illuminate\Support\Collection;

class MailerService
{
    protected LaravelMailer $mailer;

    public function __construct(LaravelMailer $mailer)
    {
        $this->mailer = $mailer;
    }

    public function send(Mailable $message, Instance $instance, Collection $emails): void
    {
        $emailNotificationSettingsDto = $instance->getEmailNotificationSettings();

        $emails->each(function (string $email) use ($message, $emailNotificationSettingsDto) {
            $message = clone $message;

            if ($emailNotificationSettingsDto instanceof EmailNotificationSettingsDto) {
                switch ((string)$emailNotificationSettingsDto->getMode()) {
                    case (string)EmailNotificationModeEnum::ENABLED():
                        $recipient = $email;
                        break;
                    case (string)EmailNotificationModeEnum::FORWARDING():
                        $recipient = (string)$emailNotificationSettingsDto->getEmail();
                        break;
                    case (string)EmailNotificationModeEnum::DISABLED();
                        $recipient = null;
                        break;
                }
            }

            if ($recipient !== null) {
                $message->to($email);

                $this->mailer->queue($message);
            }
        });
    }
}
