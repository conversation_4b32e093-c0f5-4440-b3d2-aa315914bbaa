<?php

declare(strict_types=1);

namespace App\Services\Notification\NotificationStrategies;

use App\DTO\Notification\NotificationDto;
use App\Repositories\RequestRepository;
use App\Services\Notification\NotificationList;
use Illuminate\Support\Collection;

class RequestOwnerStrategy implements StrategyInterface
{
    public const STRATEGY_CHANNELS = [
        NotificationList::DB_CHANNEL,
        NotificationList::MAIL_CHANNEL,
    ];

    /** @var  RequestRepository */
    protected $requestRepository;

    public function __construct(RequestRepository $requestRepository)
    {
        $this->requestRepository = $requestRepository;
    }

    public function getUsers(NotificationDto $notificationDto): Collection
    {
        return  collect([$this->requestRepository->findById($notificationDto->getRequestDto()->id)->user]);
    }

    public function getChannels(): Collection
    {
        return collect(self::STRATEGY_CHANNELS);
    }
}