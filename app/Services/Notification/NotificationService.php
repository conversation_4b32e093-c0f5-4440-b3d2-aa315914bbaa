<?php

declare(strict_types=1);

namespace App\Services\Notification;

use App\DTO\Notification\NotificationDto;
use App\Interfaces\Services\Notification\NotificationService as NotificationServiceInterface;
use App\Repositories\RequestRepository;
use App\Repositories\UserAssistantRepository;
use App\Repositories\UserDeputyRepository;
use App\Services\Notification\NotificationStrategies\StrategyInterface;
use App\User;
use Illuminate\Contracts\Notifications\Dispatcher;
use App\Notifications\Notification;
use Illuminate\Log\Logger;
use Illuminate\Support\Collection;

class NotificationService implements NotificationServiceInterface
{
    /** @var RequestRepository */
    protected $requestRepository;

    /** @var UserDeputyRepository */
    protected $userDeputyRepository;

    /** @var UserAssistantRepository */
    protected $userAssistantRepository;

    /** @var NotificationStrategyService */
    protected $notificationStrategyService;

    /** @var Dispatcher */
    protected $dispatcher;

    protected Logger $logger;

    public function __construct(
        RequestRepository $requestRepository,
        UserDeputyRepository $userDeputyRepository,
        UserAssistantRepository $userAssistantRepository,
        NotificationStrategyService $notificationStrategyService,
        Dispatcher $dispatcher,
        Logger $logger
    ) {
        $this->requestRepository = $requestRepository;
        $this->userDeputyRepository = $userDeputyRepository;
        $this->userAssistantRepository = $userAssistantRepository;
        $this->notificationStrategyService = $notificationStrategyService;
        $this->dispatcher = $dispatcher;
        $this->logger = $logger;
    }

    public function notify(NotificationDto $notificationDto): void
    {
        $this->notifyInternal($notificationDto);
    }

    public function notifyNow(NotificationDto $notificationDto): void
    {
        $this->notifyInternal($notificationDto, true);
    }

    public function notifyInternal(NotificationDto $notificationDto, $now = false): void
    {
        try {
            $strategies = $this->notificationStrategyService->getStrategies((string)$notificationDto->getNotificationType());
            $notificationClass = (string)$notificationDto->getNotificationType();

            $strategies->each(function (string $strategyClass, string $name)
                use($notificationClass, $notificationDto, $now) {
                    /** @var StrategyInterface $strategy */
                    $strategy = resolve($strategyClass);
                    $notification = new $notificationClass($notificationDto);

                    if ($strategy instanceof StrategyInterface) {
                        $this->releaseMessageMultiple(
                            $strategy->getUsers($notificationDto),
                            $notification,
                            $strategy->getChannels(),
                            $now
                        );
                    }
            });
        } catch (\Throwable $exception) {
            $this->logger->warning((string)$exception);

            throw $exception;
        }
    }

    protected function releaseMessageMultiple(Collection $users, Notification $notification, Collection $channels, bool $now = false): void
    {
        $users->each(function (User $user) use ($notification, $channels, $now) {
            $this->releaseMessage($user, $notification, $channels->toArray(), $now);
        });
    }

    protected function releaseMessage(User $user, Notification $notification, array $channels, bool $now = false): void
    {
        $notification = clone $notification;
        $notification->locale($user->locale);

        $this->dispatcher->{$now ? 'sendNow': 'send'}(
            $user,
            $notification,
            $channels
        );
    }
}
