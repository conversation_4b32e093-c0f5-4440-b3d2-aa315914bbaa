<?php

declare(strict_types=1);

namespace App\Services\Notification;

use App\Interfaces\ClearNotificationServiceInterface;
use App\Interfaces\Repositories\NotificationRepositoryInterface;
use Illuminate\Support\Facades\DB;

class ClearNotificationService implements ClearNotificationServiceInterface
{
    /**
     * @var NotificationRepositoryInterface
     */
    protected $notificationRepository;

    /**
     * ClearNotificationService constructor.
     *
     * @param NotificationRepositoryInterface $notificationRepository
     */
    public function __construct(NotificationRepositoryInterface $notificationRepository)
    {
        $this->notificationRepository = $notificationRepository;
    }

    /** @inheritDoc */
    public function clearAllOlderThan(int $days): bool
    {
        $databaseNotifications = $this->notificationRepository->getOlderThanDaysBuilder($days);

        if ($databaseNotifications->get()->count() > 0) {
            DB::beginTransaction();

            try {
                $databaseNotifications->delete();
                DB::commit();
                $this->clearAllOlderThan($days);
            } catch (\Throwable $exception) {
                DB::rollBack();

                throw $exception;
            }
        }

        return true;
    }
}