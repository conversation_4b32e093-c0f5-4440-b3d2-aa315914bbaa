<?php

namespace App\Services\RequestCourse\Elements;

use App\Request;
use App\Services\RequestCourse\Elements\Contracts\RequestCourseElementInterface;
use App\Services\RequestCourse\Location\LocationElementsCollection;
use App\Services\RequestCourse\Location\TrainTripLocationElement;
use App\TrainTrip;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class TrainTripCollection implements RequestCourseElementInterface
{
    use SwapCountryCodeForEstimationTrait;

    protected $request;
    protected $forEstimation;

    public function __construct(Request $request, bool $forEstimation = false)
    {
        $this->request = $request;
        $this->forEstimation = $forEstimation;
    }

    public function getElementsCollection(): Collection
    {
        return TrainTrip::where(['request_id' => $this->request->id])->with(['destinationLocation', 'departureLocation'])->get();
    }

    public function unifyLocationElements(Collection $locations = null): LocationElementsCollection
    {
        if(!$locations) {
            $locations = $this->getElementsCollection();
        }

        $unifiedLocations = new LocationElementsCollection();

        $locations->each(function(TrainTrip $trip) use (&$unifiedLocations) {
            $unifiedLocations->push(new TrainTripLocationElement($this->swapCountryCodeForEstimation($trip->departureLocation, $trip->destinationLocation, $this->forEstimation), $trip->departure_at, false, $trip->weight, $trip->departure_at, $trip->arrival_at));
            $unifiedLocations->push(new TrainTripLocationElement($trip->destinationLocation, $trip->arrival_at instanceof Carbon ? $trip->arrival_at : $trip->departure_at, false, $trip->weight+0.01, $trip->departure_at, $trip->arrival_at));

            if($trip->round_trip) {
                $unifiedLocations->push(new TrainTripLocationElement($trip->destinationLocation, $trip->getReturnStartDate(), false, $trip->return_weight, $trip->departure_at, $trip->arrival_at));
                $unifiedLocations->push(new TrainTripLocationElement($trip->departureLocation, $trip->getReturnStartDate(), false, $trip->return_weight+0.01, $trip->departure_at, $trip->arrival_at));
            }
        });

        return $unifiedLocations;
    }
}