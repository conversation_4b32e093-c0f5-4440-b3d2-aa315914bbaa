<?php

declare(strict_types=1);

namespace App\Services\DefaultAcceptor\Strategies;

use App\Repositories\UserRepository;
use App\Request;
use App\Services\DefaultAcceptor\Collections\AcceptorsCollection;
use App\Services\DefaultAcceptor\Consts\AttachDefaultAcceptorStrategies;
use App\Services\DefaultAcceptor\Dtos\AcceptorDto;
use App\Services\DefaultAcceptor\Enums\AcceptingHierarchyStrategyEnum;
use App\User;
use Illuminate\Support\Collection;

class DirectSupervisorAndDefinedUsers extends AbstractStrategy
{
    protected UserRepository $userRepository;

    protected array $givenAcceptorSlug;

    public function __construct(
        Request $request,
        UserRepository $repository,
        ?StrategyInterface $fallbackStrategy = null,
        AcceptingHierarchyStrategyEnum $acceptingHierarchyStrategyEnum,
        array $givenAcceptorSlugs
    ) {
        parent::__construct($request, $fallbackStrategy, $acceptingHierarchyStrategyEnum);
        $this->userRepository = $repository;
        $this->givenAcceptorSlug = $givenAcceptorSlugs;
    }

    /**
     * @inheritDoc
     */
    public function defaultAcceptorsOnCreate(): AcceptorsCollection
    {
        $acceptors = $this->createdSelectedStrategy()->defaultAcceptorsOnCreate();

        $users = $this->userRepository->findBySlugsAndInstanceId(
            $this->givenAcceptorSlug,
            $this->request->instance->id
        );

        foreach ($this->givenAcceptorSlug as $slug) {
            $user = $users->where('slug', $slug)->first();

            if ($user) {
                $acceptors->push(new AcceptorDto($user, $this->getNextStep($acceptors)));
            }
        }

        return $acceptors;
    }

    /**
     * @return Collection
     */
    public function defaultSettlementAcceptorsOnCreate(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnCreate();
    }

    /**
     * @return Collection
     */
    public function defaultAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnCreate();
    }

    /**
     * @return Collection
     */
    public function defaultSettlementAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnCreate();
    }

    public function defaultSettlementAcceptorsOnSendToSettlementAcceptance(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnCreate();
    }

    /**
     * @return AttachDefaultAcceptorStrategies
     */
    public function defaultAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
        return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::OVERWRITE_ALL);
    }

    /**
     * @return AttachDefaultAcceptorStrategies
     */
    public function defaultSettlementAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
        return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::OVERWRITE_ALL);
    }

    private function createdSelectedStrategy(): AbstractStrategy
    {
        return new DirectSupervisor(
            $this->request,
            $this->userRepository,
            $this->fallbackStrategy,
            $this->acceptingHierarchyStrategyEnum
        );
    }
}
