<?php

namespace App\Services\DefaultAcceptor\Strategies;

use App\Repositories\Criteria\IdCriterion;
use App\Repositories\UserRepository;
use App\Request;
use App\Services\DefaultAcceptor\Collections\AcceptorsCollection;
use App\Services\DefaultAcceptor\Consts\AttachDefaultAcceptorStrategies;
use App\Services\DefaultAcceptor\Dtos\AcceptorDto;
use App\Services\DefaultAcceptor\Enums\AcceptingHierarchyStrategyEnum;
use App\Services\DefaultAcceptor\Exceptions\DefaultAcceptorNotFoundException;
use App\User;
use Illuminate\Support\Collection;

class DirectSupervisor extends AbstractStrategy
{
    /** @var UserRepository  */
    protected $userRepository;

    /**
     * DirectSupervisor constructor.
     *
     * @param  Request                 $request
     * @param  UserRepository          $repository
     * @param  StrategyInterface|null  $fallbackStrategy
     */
    public function __construct(Request $request, UserRepository $repository, ?StrategyInterface $fallbackStrategy = null, AcceptingHierarchyStrategyEnum $acceptingHierarchyStrategyEnum)
    {
        parent::__construct($request, $fallbackStrategy, $acceptingHierarchyStrategyEnum);
        $this->userRepository = $repository;
    }

    /**
     * @return Collection
     */
    public function defaultAcceptorsOnCreate(): AcceptorsCollection
    {
        $user = $this->request->user;
        $acceptors = AcceptorsCollection::empty();

        $acceptorsToAttach = collect();
        if($user->user_id !== null) {
            $acceptorsToAttach = $this->userRepository->getByCriteria([
                new IdCriterion([$user->user_id])
            ]);
        }

        if($acceptorsToAttach->isEmpty() && $this->fallbackStrategy !== null) {
            $acceptors = $this->fallbackStrategy->defaultAcceptorsOnCreate();
        }

        $currentStep = $this->getNextStep($acceptors);
        $acceptorsToAttach->each(function(User $user) use ($acceptors, $currentStep) {
            $acceptors->push(new AcceptorDto($user, $currentStep));
        });

        return $acceptors;
    }

    /**
     * @return Collection
     */
    public function defaultSettlementAcceptorsOnCreate(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnCreate();
    }

    /**
     * @return Collection
     */
    public function defaultAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return AcceptorsCollection::empty();
    }

    /**
     * @return Collection
     */
    public function defaultSettlementAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return AcceptorsCollection::empty();
    }

    public function defaultSettlementAcceptorsOnSendToSettlementAcceptance(): AcceptorsCollection
    {
        return AcceptorsCollection::empty();
    }

    /**
     * @return AttachDefaultAcceptorStrategies
     */
    public function defaultAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
         return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::ADD);
    }

    /**
     * @return AttachDefaultAcceptorStrategies
     */
    public function defaultSettlementAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
         return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::ADD);
    }
}
