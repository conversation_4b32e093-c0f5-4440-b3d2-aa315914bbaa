<?php

declare(strict_types=1);

namespace App\Services\DefaultAcceptor\Strategies;

use App\Repositories\UserRepository;
use App\Request;
use App\Services\DefaultAcceptor\Collections\AcceptorsCollection;
use App\Services\DefaultAcceptor\Dtos\AcceptorDto;
use App\Services\DefaultAcceptor\Enums\AcceptingHierarchyStrategyEnum;
use App\Services\DefaultAcceptor\Enums\StrategyAmountSourceEnum;

class SupervisorsWithGradeAcceptanceLimitAndUserWithPermission extends SupervisorsWithGradeAcceptanceLimit
{
    private UserRepository $userRepository;
    private string $permissionName;

    public function __construct(
        Request $request,
        UserRepository $userRepository,
        ?StrategyInterface $fallbackStrategy = null,
        AcceptingHierarchyStrategyEnum $acceptingHierarchyStrategyEnum,
        array $gradeLimits,
        string $permissionName,
        StrategyAmountSourceEnum $amountSource
    ) {
        parent::__construct($request, $fallbackStrategy, $acceptingHierarchyStrategyEnum, $gradeLimits, $amountSource);
        $this->userRepository = $userRepository;
        $this->permissionName = $permissionName;
    }

    /**
     * @param string $amount
     * @return AcceptorsCollection
     * @throws \App\Exceptions\WrongCollectionItemTypeException
     */
    protected function findAcceptors(string $amount): AcceptorsCollection
    {
        $acceptors = parent::findAcceptors($amount);

        $user = $this->userRepository->findUserWithPermission($this->permissionName, $this->request->user->company);

        if ($user !== null) {
            $acceptors->push(new AcceptorDto($user, $this->getNextStep($acceptors)));
        }


        return $acceptors;
    }
}