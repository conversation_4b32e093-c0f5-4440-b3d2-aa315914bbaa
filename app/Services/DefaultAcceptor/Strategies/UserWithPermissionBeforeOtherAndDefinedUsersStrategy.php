<?php

declare(strict_types=1);

namespace App\Services\DefaultAcceptor\Strategies;

use App\Repositories\UserRepository;
use App\Request;
use App\Services\DefaultAcceptor\Collections\AcceptorsCollection;
use App\Services\DefaultAcceptor\Consts\AttachDefaultAcceptorStrategies;
use App\Services\DefaultAcceptor\Dtos\AcceptorDto;
use App\Services\DefaultAcceptor\Enums\AcceptingHierarchyStrategyEnum;
use Illuminate\Support\Collection;

class UserWithPermissionBeforeOtherAndDefinedUsersStrategy extends AbstractStrategy
{
    protected StrategyInterface $dependentStrategy;

    protected UserRepository $userRepository;

    protected string $permissionName;

    protected array $userSlugs;

    public function __construct(
        Request                        $request,
        StrategyInterface              $dependentStrategy,
        UserRepository                 $userRepository,
        AcceptingHierarchyStrategyEnum $acceptingHierarchyStrategyEnum,
        string                         $permissionName,
        array                          $userSlugs = []
    )
    {
        parent::__construct($request, null, $acceptingHierarchyStrategyEnum);
        $this->dependentStrategy = $dependentStrategy;
        $this->userRepository = $userRepository;
        $this->permissionName = $permissionName;
        $this->userSlugs = $userSlugs;
    }

    /**
     * @inheritDoc
     */
    public function defaultAcceptorsOnCreate(): AcceptorsCollection
    {
        $acceptors = new AcceptorsCollection();

        $user = $this->userRepository->findUserWithPermission($this->permissionName, $this->request->user->company);

        if ($user !== null) {
            $acceptors->push(new AcceptorDto($user, $this->getNextStep($acceptors)));
        }

        $dependentStrategyAcceptors = $this->dependentStrategy->defaultAcceptorsOnCreate();

        if ($acceptors->isEmpty()) {
            return $dependentStrategyAcceptors;
        }

        foreach ($dependentStrategyAcceptors->getGruppedByStep() as $step => $stepAcceptors) {
            $nextStep = $this->getNextStep($acceptors);
            /** @var AcceptorDto $acceptorDto */
            foreach ($stepAcceptors as $acceptorDto) {
                $newAcceptorDto = new AcceptorDto($acceptorDto->getUser(), $nextStep);
                $acceptors->push($newAcceptorDto);
            }
        }

        if (count($this->userSlugs) > 0) {
            $users = $this->userRepository->findBySlugsAndInstanceId(
                $this->userSlugs,
                $this->request->instance->id
            );

            foreach ($this->userSlugs as $slug) {
                $user = $users->where('slug', $slug)->first();

                if ($user) {
                    $acceptors->push(new AcceptorDto($user, $this->getNextStep($acceptors)));
                }
            }
        }

        return $acceptors;
    }

    /**
     * @inheritDoc
     */
    public function defaultSettlementAcceptorsOnCreate(): AcceptorsCollection
    {
        $acceptors = $this->dependentStrategy->defaultAcceptorsOnCreate();

        return $acceptors;
    }

    /**
     * @return Collection
     */
    public function defaultAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        $acceptors = new AcceptorsCollection();

        $user = $this->userRepository->findUserWithPermission($this->permissionName, $this->request->user->company);

        if ($user !== null) {
            $acceptors->push(new AcceptorDto($user, $this->getNextStep($acceptors)));
        }

        $dependentStrategyAcceptors = $this->dependentStrategy->defaultAcceptorsOnSendToAcceptance();

        if ($acceptors->isEmpty()) {
            return $dependentStrategyAcceptors;
        }

        foreach ($dependentStrategyAcceptors->getGruppedByStep() as $step => $stepAcceptors) {
            $nextStep = $this->getNextStep($acceptors);
            /** @var AcceptorDto $acceptorDto */
            foreach ($stepAcceptors as $acceptorDto) {
                $newAcceptorDto = new AcceptorDto($acceptorDto->getUser(), $nextStep);
                $acceptors->push($newAcceptorDto);
            }
        }

        return $acceptors;
    }

    /**
     * @return Collection
     */
    public function defaultSettlementAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return $this->dependentStrategy->defaultSettlementAcceptorsOnSendToAcceptance();
    }

    public function defaultSettlementAcceptorsOnSendToSettlementAcceptance(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnSendToAcceptance();
    }

    /**
     * @return AttachDefaultAcceptorStrategies
     */
    public function defaultAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
        return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::OVERWRITE_ALL);
    }

    /**
     * @return AttachDefaultAcceptorStrategies
     */
    public function defaultSettlementAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
        return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::OVERWRITE_ALL);
    }
}