<?php


namespace App\Services\DefaultAcceptor\Strategies\Amrest;


use App\Accomodation;
use App\Cost;
use Modules\Accounting\Priv\Entities\DocumentElementType;
use App\PlaneTrip;
use App\PrivateCarTrip;
use App\Request;
use App\Services\DefaultAcceptor\Strategies\Amrest\consts\RequestElementTypes;
use App\TrainTrip;
use Illuminate\Support\Collection;

class RequestElementTypesMapper
{
    public function map(Request $request): Collection
    {
        $requestElementTypes = collect();

        $request->accomodations->each(function (Accomodation $accomodation) use ($requestElementTypes) {
            if ($accomodation->isNational()) {
                $requestElementTypes->push(RequestElementTypes::ACCOMMODATION_DOMESTIC);
            } else {
                $requestElementTypes->push(RequestElementTypes::ACCOMMODATION_FOREIGN);
            }
        });

        $request->trainTrips->each(function (TrainTrip $trainTrip) use ($requestElementTypes) {
            $requestElementTypes->push(RequestElementTypes::TRAIN_TRIP);
        });

        $request->privateCarTrips->each(function (PrivateCarTrip $privateCarTrip) use ($requestElementTypes) {
            $requestElementTypes->push(RequestElementTypes::PRIVATE_CAR);
        });

        $request->planeTrips->each(function (PlaneTrip $planeTrip) use ($requestElementTypes) {

            if ($planeTrip->isNational() && !$planeTrip->round_trip) {
                $requestElementTypes->push(RequestElementTypes::DOMESTIC_FLIGHT_ONE_WAY);
            }

            if ($planeTrip->isNational() && $planeTrip->round_trip) {
                $requestElementTypes->push(RequestElementTypes::DOMESTIC_FLIGHT_TWO_WAYS);
            }

            if ($planeTrip->isEuropean() && !$planeTrip->round_trip) {
                $requestElementTypes->push(RequestElementTypes::EUROPEAN_FLIGHT_ONE_WAY);
            }

            if ($planeTrip->isEuropean() && $planeTrip->round_trip) {
                $requestElementTypes->push(RequestElementTypes::EUROPEAN_FLIGHT_TWO_WAYS);
            }

            if ($planeTrip->isIntercontinental() && !$planeTrip->round_trip) {
                $requestElementTypes->push(RequestElementTypes::INTERCONTINENTAL_FLIGHT_ONE_WAY);
            }

            if ($planeTrip->isIntercontinental() && $planeTrip->round_trip) {
                $requestElementTypes->push(RequestElementTypes::INTERCONTINENTAL_FLIGHT_TWO_WAYS);
            }
        });

        $documentElementTypes = collect();
        $request->instance->documentElementTypes()->each(function (DocumentElementType $documentElementType) use ($documentElementTypes) {
            $documentElementTypes->put($documentElementType->slug, $documentElementType->type);
        });

        $request->costs->each(function (Cost $cost) use($documentElementTypes, $requestElementTypes) {
            if($documentElementTypes->get($cost->elementType->slug) == 'taxi'){
                $requestElementTypes->push(RequestElementTypes::TAXI);
            }
        });

        return $requestElementTypes;
    }
}
