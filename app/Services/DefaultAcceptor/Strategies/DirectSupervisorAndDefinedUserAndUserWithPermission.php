<?php

declare(strict_types=1);

namespace App\Services\DefaultAcceptor\Strategies;

use App\Repositories\UserRepository;
use App\Request;
use App\Services\DefaultAcceptor\Collections\AcceptorsCollection;
use App\Services\DefaultAcceptor\Consts\AttachDefaultAcceptorStrategies;
use App\Services\DefaultAcceptor\Dtos\AcceptorDto;
use App\Services\DefaultAcceptor\Enums\AcceptingHierarchyStrategyEnum;

class DirectSupervisorAndDefinedUserAndUserWithPermission extends AbstractStrategy
{
    private UserRepository $userRepository;

    private string $givenAcceptorSlug;

    private string $permissionName;

    public function __construct(
        Request $request,
        UserRepository $repository,
        StrategyInterface $fallbackStrategy,
        AcceptingHierarchyStrategyEnum $acceptingHierarchyStrategyEnum,
        string $givenAcceptorSlug,
        string $permissionName
    ) {
        parent::__construct($request, $fallbackStrategy, $acceptingHierarchyStrategyEnum);
        $this->userRepository = $repository;
        $this->givenAcceptorSlug = $givenAcceptorSlug;
        $this->permissionName = $permissionName;
    }

    public function defaultAcceptorsOnCreate(): AcceptorsCollection
    {
        return $this->fallbackStrategy->defaultAcceptorsOnCreate();
    }

    public function defaultSettlementAcceptorsOnCreate(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnCreate();
    }

    public function defaultAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnCreate();
    }

    public function defaultSettlementAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnCreate();
    }

    public function defaultSettlementAcceptorsOnSendToSettlementAcceptance(): AcceptorsCollection
    {
        $acceptors = $this->fallbackStrategy->defaultAcceptorsOnCreate();
        $instance = $this->request->instance;

        $definedUser = $this->userRepository->findBySlugAndInstanceId($this->givenAcceptorSlug, $instance->id);
        if ($definedUser !== null && $acceptors->contains(function (AcceptorDto $acceptorDto) use ($definedUser) {
                return $acceptorDto->getUser()->id === $definedUser->id;
            }) === false) {
            $acceptors->push(new AcceptorDto($definedUser, $this->getNextStep($acceptors)));
        }


        $user = $this->userRepository->findUserWithPermission($this->permissionName, $this->request->user->company);

        if ($user !== null) {
            $acceptors->push(new AcceptorDto($user, $this->getNextStep($acceptors)));
        }

        return $acceptors;
    }

    public function defaultAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
        return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::OVERWRITE_ALL);
    }

    public function defaultSettlementAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
        return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::OVERWRITE_ALL);
    }
}
