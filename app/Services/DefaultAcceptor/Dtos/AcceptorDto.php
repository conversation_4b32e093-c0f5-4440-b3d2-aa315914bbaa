<?php

declare(strict_types=1);

namespace App\Services\DefaultAcceptor\Dtos;

use App\User;

class AcceptorDto
{
    /**
     * @var User
     */
    protected $user;

    /**
     * @var int
     */
    protected $step;

    /**
     * AcceptorDto constructor.
     * @param User $user
     * @param int $step
     */
    public function __construct(User $user, int $step = 0)
    {
        $this->user = $user;
        $this->step = $step;
    }

    /**
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }

    /**
     * @return int
     */
    public function getStep(): int
    {
        return $this->step;
    }
}