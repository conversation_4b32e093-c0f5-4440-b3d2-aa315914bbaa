<?php

declare(strict_types=1);

namespace App\Services\DefaultAcceptor\Decisions;

use App\Repositories\UserRepository;
use App\Services\DefaultAcceptor\Strategies\RandomFromDefinedUsersStrategy;
use App\Services\DefaultAcceptor\Strategies\WithoutAcceptorStrategy;
use Modules\DecisionMaker\Pub\Interfaces\DecisionInterface;
use Modules\FeatureSwitcher\Pub\Facades\FeatureSwitcherFacade;

class RandomFromDefinedUsersDecision extends AbstractAcceptorStrategyDecision implements DecisionInterface
{
    public const CODE = 'RandomFromDefinedUsersDecision';

    protected UserRepository $userRepository;

    protected array $userSlugs;

    public function __construct(
        UserRepository $userRepository,
        FeatureSwitcherFacade $featureSwitcherFacade,
        array $users
    ) {
        parent::__construct($featureSwitcherFacade);
        $this->userRepository = $userRepository;
        $this->userSlugs = $users;
    }

    public function execute(array $params = [])
    {
        $request = $params[self::REQUEST];

        return new RandomFromDefinedUsersStrategy(
            $request,
            new WithoutAcceptorStrategy(),
            $this->userRepository,
            $this->getAacceptingHierarchyStrategyEnum($request),
            $this->userSlugs
        );
    }
}
