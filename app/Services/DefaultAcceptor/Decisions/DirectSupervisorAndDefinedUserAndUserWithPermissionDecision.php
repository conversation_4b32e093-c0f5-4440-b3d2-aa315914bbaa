<?php

declare(strict_types=1);

namespace App\Services\DefaultAcceptor\Decisions;

use App\Services\DefaultAcceptor\Strategies\DirectSupervisorAndDefinedUserAndUserWithPermission;
use Modules\DecisionMaker\Pub\Interfaces\DecisionInterface;
use App\Repositories\UserRepository;
use Modules\FeatureSwitcher\Pub\Facades\FeatureSwitcherFacade;

class DirectSupervisorAndDefinedUserAndUserWithPermissionDecision extends RequestOwnerStrategyDecision implements
    DecisionInterface
{
    public const CODE = 'DirectSupervisorAndDefinedUserAndUserWithPermissionDecision';

    private UserRepository $userRepository;
    private DirectSupervisorStrategyDecision $directSupervisorStrategyDecision;
    private string $slug;
    private string $permissionName;

    public function __construct(
        UserRepository $userRepository,
        DirectSupervisorStrategyDecision $directSupervisorStrategyDecision,
        FeatureSwitcherFacade $featureSwitcherFacade,
        string $slug,
        string $permissionName
    ) {
        parent::__construct($featureSwitcherFacade);
        $this->userRepository = $userRepository;
        $this->directSupervisorStrategyDecision = $directSupervisorStrategyDecision;
        $this->slug = $slug;
        $this->permissionName = $permissionName;
    }

    public function execute(array $params = [])
    {
        $request = $params[self::REQUEST];

        return new DirectSupervisorAndDefinedUserAndUserWithPermission(
            $request,
            $this->userRepository,
            $this->directSupervisorStrategyDecision->execute($params),
            $this->getAacceptingHierarchyStrategyEnum($request),
            $this->slug,
            $this->permissionName
        );
    }
}
