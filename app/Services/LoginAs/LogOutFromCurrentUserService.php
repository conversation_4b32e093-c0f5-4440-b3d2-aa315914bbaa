<?php


namespace App\Services\LoginAs;


use App\Repositories\UserLoggedAsRepository;
use App\User;
use App\UserLoggedAs;
use Carbon\Carbon;

class LogOutFromCurrentUserService
{
    /** @var User */
    protected $currentUser;

    /** @var UserLoggedAsRepository */
    protected $repository;

    /**
     * LogOutFromCurrentUserService constructor.
     *
     * @param  User                    $currentUser
     * @param  UserLoggedAsRepository  $repository
     */
    public function __construct(User $currentUser, UserLoggedAsRepository $repository)
    {
        $this->currentUser = $currentUser;
        $this->repository  = $repository;
    }

    public static function createFromUserAndRepository(
        User $currentUser,
        UserLoggedAsRepository $repository
    ): LogOutFromCurrentUserService {
        return new self($currentUser, $repository);
    }

    public function logOutFromAll(): void
    {
        $this->repository->getCurrentSessions($this->currentUser)
            ->each(function (UserLoggedAs $loggedAs) {
                $loggedAs->to = new Carbon();
                $loggedAs->save();
            });
    }

    public function logOutFromUser(User $user, string $type): void
    {
        $this->repository->getCurrentSessions($this->currentUser)
            ->filter(function (UserLoggedAs $userLoggedAs) use($type, $user) {
                return $userLoggedAs->type == $type && $userLoggedAs->loggedAs->id == $user->id;
            })
            ->each(function (UserLoggedAs $loggedAs) {
                $loggedAs->to = new Carbon();
                $loggedAs->save();
            });
    }
}
