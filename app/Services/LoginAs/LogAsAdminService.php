<?php

namespace App\Services\LoginAs;

use App\Repositories\UserLoggedAsRepository;
use App\User;
use App\UserLoggedAs;
use Carbon\Carbon;

class LogAsAdminService
{
    /** @var User */
    protected $currentUser;

    /** @var User */
    protected $loggingTo;

    /** @var UserLoggedAsRepository */
    protected $loggedAsRepository;

    /** @var string $logType */
    protected $logType;

    /**
     * LogAsAdminService constructor.
     *
     * @param  UserLoggedAsRepository  $loggedAsRepository
     */
    public function __construct(UserLoggedAsRepository $loggedAsRepository)
    {
        $this->loggedAsRepository = $loggedAsRepository;

        $this->logType = UserLoggedAs::LOG_TYPE_ADMIN;
    }

    public function currentUser(User $currentUser): LogAsAdminService
    {
        $this->currentUser = $currentUser;

        return $this;
    }

    public function loginAs(User $loggingTo): User
    {
        $this->loggingTo = $loggingTo;

        abort_unless($this->canUserLogIn(), 400,
            trans('error.cannot-login-as-user', ['user' => $this->loggingTo->full_name]));

        return \DB::transaction(function () {
            $originallyLoggedAsService = app()->make(OriginallyLoggedAsService::class);
            $isAlreadyLoggedAs = $originallyLoggedAsService->isLoggedAsAnyone();
            
            $originalUser = $isAlreadyLoggedAs ? $originallyLoggedAsService->getOriginallyLoggedAs() : $this->currentUser;
            
            if (!$this->isLoggingToHimself()) {
                $log = new UserLoggedAs([
                    'user_id'   => $originalUser->id,
                    'logged_as' => $this->loggingTo->id,
                    'from'      => new Carbon(),
                    'type'      => $this->logType,
                ]);

                $log->save();
            }

            $originallyLoggedAsService
                ->setLoggedAs($this->loggingTo)
                ->setOriginallyLoggedAs($originalUser);

            return $this->loggingTo;
        });
    }

    protected function isLoggingToHimself(): bool
    {
        return $this->loggingTo->slug === $this->currentUser->slug;
    }

    protected function canUserLogIn(): bool
    {
        if ($this->isLoggingToHimself()) {

            return true;
        }

        if ( ! ($this->currentUser->isAdmin() || $this->currentUser->isSuperAdmin())) {

            return false;
        }

        if ($this->currentUser->isAdmin() && $this->loggingTo->isSuperAdmin()) {

            return false;
        }

        return true;
    }
}
