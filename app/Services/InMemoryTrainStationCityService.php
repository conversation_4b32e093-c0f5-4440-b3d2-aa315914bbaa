<?php

declare(strict_types=1);

namespace App\Services;

use App\Helpers\CsvHelper;
use App\Interfaces\InMemoryTrainStationCityServiceInterface;
use Illuminate\Support\Collection;

class InMemoryTrainStationCityService implements InMemoryTrainStationCityServiceInterface
{


    /** @var Collection|null */
    protected $cityNames;

    /** @var string  */
    protected $filePath;

    /** @var string */
    protected $delimiter;

    public function init(string $filePath, string $delimiter)
    {
        $this->filePath = $filePath;
        $this->delimiter = $delimiter;
    }

    public function getCityNamePlByCode(string $code): ?string
    {
        return empty($this->getCityCodeInternal($code)['pl']) === false ? $this->getCityCodeInternal($code)['pl'] : null;
    }

    public function getCityNameEnByCode(string $code): ?string
    {
        return empty($this->getCityCodeInternal($code)['en']) === false ? $this->getCityCodeInternal($code)['en'] : null;
    }

    public function getCityCodeInternal(string $code): array
    {
        if ($this->filePath === null || $this->delimiter === null) {
            throw new \Exception(sprintf('Please initialized %s first', self::class));
        }

        if ($this->cityNames === null) {
            $this->readFile();
        }

        return $this->cityNames->get($code);
    }

    protected function readFile(): void
    {
        $this->cityNames = collect();
        if (($handle = fopen($this->filePath, "r")) !== false) {
            while (($data = fgetcsv($handle, 1000, $this->delimiter, '"')) !== false) {
                $cityNamePl = $data[1];
                $cityNameEn = empty($data[3]) === false ? $data[3] : CsvHelper::removePolishChars($data[1]);
                $cityCode = $data[0];

                $this->cityNames->put($cityCode, ['pl' => $cityNamePl, 'en' => $cityNameEn]);
            }
            fclose($handle);
        }
    }
}
