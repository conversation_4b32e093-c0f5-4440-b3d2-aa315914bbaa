<?php

declare(strict_types=1);

namespace App\Services\Subsummary;

use App\Currency;
use App\Exceptions\Summary\CanNotRegenerateRequestTravelExpensesException;
use App\Instance;
use App\Interfaces\Services\Subsummary\AllowanceSeparatorServiceInterface;
use App\Repositories\RequestAccountingTravelExpensesRepository;
use App\Repositories\RequestRepository;
use App\Request;
use App\RequestAccountingTravelExpenses;
use App\Services\Currency\CurrencyService;
use App\Services\RequestService;
use App\Services\RulesService\Rules\AccountingMilleageAllowanceSummaryAmountRule;
use App\Services\RulesService\Rules\AccountingMilleageAllowanceSummaryHasAccountingDimensionsRule;
use App\Services\RulesService\Rules\AccountingTravelExpensesHasAccountingDimensionsRule;
use App\Services\RulesService\Rules\AccountingTravelExpensesValidRule;
use App\Services\RulesService\Rules\RequestAccountingAllowancesHasAccountDimensionsRule;
use App\Services\RulesService\RulesService;
use App\Vendors\Math;
use App\Vendors\MoneyCalculator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Exceptions\AccountingAccountNotFound;
use Modules\Accounting\Priv\Repositories\AccountingAccountRepository;
use Modules\Accounting\Priv\Services\AccountingAccountSelection\AccountingAccountSelectionService;
use Modules\ExchangeRate\Pub\Enums\DocumentExchangeRateSourceEnum;
use Modules\ExchangeRate\Pub\Facades\ExchangeRateFacade;
use Modules\ExchangeRate\Pub\Interfaces\Factories\ForeignTravelAllowanceDtoFactory;

class AllowanceSeparatorService implements AllowanceSeparatorServiceInterface
{
    const DONT_ADD_ALLOWANCES_WITH_ZERO_VALUE = 'dontAddAllowancesWithZeroValue';
    const SUM_FOREIGN_AND_DOMESTIC_BUSINESS_DIETS = 'sumForeignAndDomesticBusinessDiets';

    protected AccountingAccountRepository $accountingAccountRepository;
    protected RulesService $ruleService;
    protected RequestAccountingTravelExpensesRepository $accountingTravelExpensesRepository;
    protected RequestService $requestService;
    protected RequestRepository $requestRepository;
    protected CurrencyService $currencyService;
    protected ForeignTravelAllowanceDtoFactory $foreignTravelAllowanceDtoFactory;
    protected ExchangeRateFacade $exchangeRateFacade;
    private subSummaryService $subSummaryService;

    protected $dontAddAllowancesWithZeroValue;

    protected $sumForeignAndDomesticBusinessDiets;

    private AccountingAccountSelectionService $accountingAccountSelectionService;

    public function __construct(
        AccountingAccountRepository $accountingAccountRepository,
        RulesService $ruleService,
        RequestAccountingTravelExpensesRepository $accountingTravelExpensesRepository,
        RequestService $requestService,
        RequestRepository $requestRepository,
        CurrencyService $currencyService,
        ForeignTravelAllowanceDtoFactory $foreignTravelAllowanceDtoFactory,
        ExchangeRateFacade $exchangeRateFacade,
        SubsummaryService $subSummaryService,
        AccountingAccountSelectionService $accountingAccountSelectionService
    ) {
        $this->accountingAccountRepository = $accountingAccountRepository;
        $this->ruleService = $ruleService;
        $this->requestService = $requestService;
        $this->accountingTravelExpensesRepository = $accountingTravelExpensesRepository;
        $this->requestRepository = $requestRepository;
        $this->currencyService = $currencyService;
        $this->foreignTravelAllowanceDtoFactory = $foreignTravelAllowanceDtoFactory;
        $this->exchangeRateFacade = $exchangeRateFacade;
        $this->subSummaryService = $subSummaryService;
        $this->accountingAccountSelectionService = $accountingAccountSelectionService;
    }

    public function regenerateRequestTravelExpensesFromSummary(string $slug): bool
    {
        $currentUser = Auth::user();

        try {
            DB::beginTransaction();
            /** @var Request $request */
            $request = $this->requestRepository->findBySlug($slug);

            $this->prepareSettings($request->instance->getModuleSettings('allowances'));

            if ($request !== null) {
                if ($currentUser->can('update', $request) !== true) {
                    throw CanNotRegenerateRequestTravelExpensesException::create($slug);
                }

                $allowances = $request->accountingTravelExpenses;
                $request->mileage_allowance_amount = $request->sumMileageAllowances();
                $request->save();

                if ($allowances instanceof Collection && $allowances->isEmpty() === false) {
                    $allowances->each(function ($allowance) {
                        $this->accountingTravelExpensesRepository->delete($allowance->id);
                    });
                }

                $this->requestService->removeAccountingMileageAllowances($request);

                $request->refresh();

                $success = $this->createMultipleRequestTravelExpensesFromSummary(
                        $request,
                        new SubsummaryService($request)
                    ) instanceof Collection;

                $this->createRequestAccountingMileageAllowances($request);

                if ($success === true) {
                    DB::commit();

                    return true;
                }
            }

            throw CanNotRegenerateRequestTravelExpensesException::create($slug);
        } catch (\Throwable $exception) {
            DB::rollBack();

            \report($exception);

            return false;
        }
    }

    public function createMultipleRequestTravelExpensesFromSummary(
        Request $request,
        SubsummaryService $summaryService
    ): Collection {
        if ($request->type == Request::TYPE_EXPENSE) {
            return false;
        }

        $this->prepareSettings($request->instance->getModuleSettings('allowances'));

        $addedAllowancesElements = new Collection();
        $requestAllowancesSummary = $summaryService->getSummary();

        if ($requestAllowancesSummary instanceof Collection && $requestAllowancesSummary->isEmpty() === false) {
            $request = $this->setCachedSumOnRequest($request, $summaryService);
            $countElements = $this->countTravelExpenseAllowanceElement($requestAllowancesSummary);
            $counter = 0;

            foreach ($requestAllowancesSummary->toArray() as $index => $singleAllowanceCategory) {
                if (!empty($singleAllowanceCategory['elements'])) {
                    list($counter, $addedAllowancesElements) = $this->separateAllowancesAndSumInCategory(
                        $request,
                        $singleAllowanceCategory,
                        $counter,
                        $index,
                        $countElements,
                        $addedAllowancesElements
                    );
                }
            }
        }

        return $addedAllowancesElements;
    }

    protected function getAmountFromTravelExpenseAmountInForeignCurrency(
        Request $request,
        array $travelExpenseElement,
        string $percentage = '100',
        int $roundMode = PHP_ROUND_HALF_UP
    ): string {
        if($this->isAmountFromTravelExpenseValid($request, $travelExpenseElement)) {
            $result = Math::subtract(Math::asString($travelExpenseElement['amount']), Math::asString($travelExpenseElement['deductions']));

            return MoneyCalculator::percentageOfAndRound($result, $percentage, 2, $roundMode);
        }

        return "0";
    }

    protected function isAmountFromTravelExpenseValid(Request $request, array $travelExpenseElement): bool
    {
        $accountingTravelExpensesGross = $request->sumAccountingTravelExpensesInInstanceCurrency();

        $amountValid = (($request->travel_expense_cache - $accountingTravelExpensesGross)
                - $travelExpenseElement['convertedAmount']) >= 0;

        return $amountValid;
    }

    protected function countTravelExpenseAllowanceElement(Collection $requestAllowancesSummary): int
    {
        $counted = 0;

        foreach ($requestAllowancesSummary->toArray() as $requestAllowancesSummaryItem) {
            if (isset($requestAllowancesSummaryItem['elements']) === true
                && empty($requestAllowancesSummaryItem['elements']) === false
            ) {
                $counted = $counted + count($requestAllowancesSummaryItem['elements']);
            }
        }

        return $counted;
    }

    protected function prepareSettings(array $allowancesSettings): void
    {
        $this->dontAddAllowancesWithZeroValue = true;
        $this->sumForeignAndDomesticBusinessDiets = false;

        if (empty($allowancesSettings) === false
            && isset($allowancesSettings[self::DONT_ADD_ALLOWANCES_WITH_ZERO_VALUE]) === true
            && is_bool($allowancesSettings[self::DONT_ADD_ALLOWANCES_WITH_ZERO_VALUE]) === true
            && isset($allowancesSettings[self::SUM_FOREIGN_AND_DOMESTIC_BUSINESS_DIETS]) === true
            && is_bool($allowancesSettings[self::SUM_FOREIGN_AND_DOMESTIC_BUSINESS_DIETS]) === true
        ) {
            $this->dontAddAllowancesWithZeroValue = true;
            $this->sumForeignAndDomesticBusinessDiets = false;
        }
    }

    protected function setCachedSumOnRequest(Request $request, SubsummaryService $summaryService): Request
    {
        $request->travel_expense_cache = Math::round($summaryService->getAmount() ?? 0.00, 2);
        $request->save();

        return $request;
    }

    protected function getMatchingAccountingAccount(
        string $accountingAccountKind,
        Request $request,
        bool $isNationalTrip = true
    ): AccountingAccount {
        try {
            return $this->accountingAccountSelectionService->debit()->getAccountingAccountByKind(
                $request->instance,
                $request,
                $this->subSummaryService->matchKind($accountingAccountKind),
                !$isNationalTrip
            );
        } catch (AccountingAccountNotFound $e) {
        }

        return $this->accountingAccountSelectionService->debit()->getTravelExpensesAccountingAccount(
            $request->instance,
            $request,
            !$isNationalTrip
        );
    }

    protected function beginValidation(Request $request, int $countElements, int $counter): void
    {
        if ($countElements === $counter) {
            $this->ruleService->addRules(collect([
                new AccountingTravelExpensesValidRule($request, $request->instance),
                 new AccountingTravelExpensesHasAccountingDimensionsRule($request, $request->instance),
                new AccountingMilleageAllowanceSummaryHasAccountingDimensionsRule($request, $request->instance),
                new AccountingMilleageAllowanceSummaryAmountRule($request, $request->instance),
                new RequestAccountingAllowancesHasAccountDimensionsRule($request, $request->instance),
            ]))->validate();
        }
    }

    protected function prepareSeparatedElement(
        Request $request,
        ?AccountingAccount $accountingAccount,
        array $travelExpenseAllowanceElement,
        int $countElements,
        int $counter,
        int $mpkId,
        string $percentage = '100',
        int $roundMode = PHP_ROUND_HALF_UP
    ): array {
        $currency = $this->currencyService->getCurrencyByCode($travelExpenseAllowanceElement['currency']);

        $exchangeRateStrategyResultDto = null;
        if($currency !== null) {
            $foreignRequestTravelAllowanceDto = $this->foreignTravelAllowanceDtoFactory->createFromForeignTravelAllowanceAndUser(
                $request,
                RequestAccountingTravelExpenses::class,
                $travelExpenseAllowanceElement['currency']
            );

            $exchangeRateStrategyResultDto = $this->exchangeRateFacade->getForForeignTravelAllowanceAndUser($foreignRequestTravelAllowanceDto);
        }

        return [
            'request_id' => $request->id,
            'instance_id' => $request->instance_id,
            'cost_of_earning' => true,
            'accounting_account_id' => $accountingAccount->id,
            'mpk_id' => $mpkId,
            'amount' => $this->getAmountFromTravelExpenseAmountInForeignCurrency($request, $travelExpenseAllowanceElement, $percentage, $roundMode),
            'currency_id' => $currency->id ?? $request->instance->currency_id,
            'exchange_rate' => (string) $exchangeRateStrategyResultDto->getRate() ?? Currency::DEFAULT_CURRENCY_EXCHANGE_RATE,
            'exchange_rate_date' => $exchangeRateStrategyResultDto->getEffectiveDate()->format('Y-m-d') ?? null,
            'exchange_rate_strategy' => empty($exchangeRateStrategyResultDto->getStrategy()) ? null : ((string) $exchangeRateStrategyResultDto->getStrategy())::CODE,
            'exchange_rate_source' => empty($exchangeRateStrategyResultDto->getStrategy()) ? DocumentExchangeRateSourceEnum::EMPTY() : DocumentExchangeRateSourceEnum::STRATEGY(),
            'is_last_element' => $countElements === $counter
        ];
    }

    protected function separateAllowancesAndSumInCategory(
        Request $request,
        $singleAllowanceCategory,
        int $counter,
        string $index,
        int $countElements,
        Collection $addedAllowancesElements
    ): array {
        foreach ($singleAllowanceCategory['elements'] as $travelExpenseAllowanceElement) {
            $counter++;

            $accountingAccount = $this->getMatchingAccountingAccount(
                $index,
                $request,
                $travelExpenseAllowanceElement['countryCode'] === $request->instance->country->country_code
            );

            $amount = $this->getAmountFromTravelExpenseAmountInForeignCurrency($request, $travelExpenseAllowanceElement);

            if ($amount == 0 && $this->dontAddAllowancesWithZeroValue === true) {
                $this->beginValidation($request, $countElements, $counter);
            } else {
                $this->splitExpensesOnMpk(
                    $request,
                    $accountingAccount,
                    $travelExpenseAllowanceElement,
                    $countElements,
                    $counter,
                    $addedAllowancesElements
                );
            }
        }

        return [$counter, $addedAllowancesElements];
    }


    /**
     * @param Request $request
     * @return void
     * @throws \Throwable
     */
    public function createRequestAccountingMileageAllowances(Request $request): void
    {
        $sumMileageAllowances = $request->sumMileageAllowances();
        $request->mileage_allowance_amount = $sumMileageAllowances;
        $request->save();

        if (!$sumMileageAllowances) {
            return;
        }

        foreach ($request->user->hasMpks as $number => $mpk) {
            $roundMode = $number % 2 ? PHP_ROUND_HALF_DOWN : PHP_ROUND_HALF_UP;
            $amount = MoneyCalculator::percentageOfAndRound(
                $sumMileageAllowances,
                (string) $mpk->pivot->percentage,
                2,
                $roundMode
            );

            $this->requestService->createAccountingMileageAllowance(
                $request,
                $amount,
                $mpk->id
            );
        }
    }

    private function splitExpensesOnMpk(
        Request $request,
        AccountingAccount $accountingAccount,
        $travelExpenseAllowanceElement,
        int $countElements,
        int $counter,
        Collection $addedAllowancesElements
    ): void {
        $mpkCollection = $request->user->hasMpks;

        // a backward for a case when a user does not have assigned multiple mpks
        if ($mpkCollection->isEmpty()) {
            $newAllowanceElement = $this->prepareSeparatedElement(
                $request,
                $accountingAccount,
                $travelExpenseAllowanceElement,
                $countElements,
                $counter,
                $request->mpk_id
            );

            $this->accountingTravelExpensesRepository->create($newAllowanceElement);
            $addedAllowancesElements->push($newAllowanceElement);

            return;
        }

        // when user has multiple mpks
        foreach ($mpkCollection as $number => $mpk) {
            $roundMode = $number % 2 ? PHP_ROUND_HALF_DOWN : PHP_ROUND_HALF_UP;

            $newAllowanceElement = $this->prepareSeparatedElement(
                $request,
                $accountingAccount,
                $travelExpenseAllowanceElement,
                $countElements,
                $counter,
                $mpk->id,
                (string) $mpk->pivot->percentage,
                $roundMode
            );

            $this->accountingTravelExpensesRepository->create($newAllowanceElement);
            $addedAllowancesElements->push($newAllowanceElement);
        }
    }
}
