<?php


namespace App\Services;


class Cache
{
    public static function get($key, callable $callback, $time = 10,  array $tags = [])
    {
        if(!static::cacheEnabled()) {
            return call_user_func($callback);
        }

        $cachedValue = \Cache::tags($tags)->get($key);
        if (!is_null($cachedValue)) {
            return $cachedValue;
        }

        $value = call_user_func($callback);
        \Cache::tags($tags)->put($key, $value, $time);

        return $value;
    }

    public static function forever($key, callable $callback, array $tags = [])
    {
        if(!static::cacheEnabled()) {
            return call_user_func($callback);
        }

        $cachedValue = \Cache::tags($tags)->get($key);
        if (!is_null($cachedValue)) {
            return $cachedValue;
        }

        $value = call_user_func($callback);
        \Cache::tags($tags)->forever($key, $value);

        return $value;
    }

    public static function cacheEnabled()
    {
        return (config('vaterval.use_cache'));
    }

    public static function delete($key)
    {
        \Cache::delete($key);
    }

    public static function deleteTags($tags)
    {
        if(!is_array($tags)) {
            $tags = [$tags];
        }

        if(static::cacheEnabled() === true) {
            \Cache::tags($tags)->flush();
        }
    }
}