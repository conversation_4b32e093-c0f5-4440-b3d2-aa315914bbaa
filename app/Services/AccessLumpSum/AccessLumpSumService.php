<?php


namespace App\Services\AccessLumpSum;


use App\Collections\AmountInCurrencyCollection;
use App\Country;
use App\Exceptions\IncalculableLumpSumException;
use App\Instance;
use App\LumpSum;
use App\Request;
use App\Services\AccessLumpSum\AccessLumpSumItem\AccessLumpSumItem;
use App\Services\AccessLumpSum\AccessLumpSumItem\ForeignAccessLumpSumItem;
use App\Services\AccessLumpSum\AccessLumpSumItem\NationalAccessLumpSumItem;
use App\Services\BorderCrossing\BorderCrossingCollection;
use App\Services\BorderCrossing\BorderCrossingService;
use App\Services\Cache;
use App\Vendors\Math;
use Illuminate\Support\Collection;

class AccessLumpSumService
{
    /** @var Request */
    protected $request;

    /** @var BorderCrossingService */
    protected $borderCrossingService;

    /** @var AccessLumpSumCollection */
    protected $lumpSums;

    /**
     * TravelExpensesService constructor.
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request               = $request;
        $this->lumpSums              = new AccessLumpSumCollection();
    }

    /**
     * @return Collection
     */
    public function calculate()
    {
        $key = 'accessLumpSum_'.$this->request->id;

        return Cache::get($key , function() {
            if( ! $this->request->isDelegation() || $this->request->isUnrealized()) {
                return new AccessLumpSumCollection();
            }

            $this->request->load(['travelExpenses']);
            $this->borderCrossingService = new BorderCrossingService($this->request);

            $this->setLumpSumsBasedOnBorderCrossings($this->borderCrossingService->getBorderCrossingTargetsCollection(true));
            return $this->sumByCountry();
        }, 100, [$this->request->cacheTag()]);

    }

    protected function sumByCountry()
    {
        $collection = collect();
        $this->lumpSums->each(function (AccessLumpSumItem $lumpSum) use (&$collection) {
            if ($lumpSum->checked
                && $lumpSum->getCountry() instanceof Country
                && $lumpSum->getCountry()->country_code !== Country::COUNTRY_CODE_PL
                && empty($lumpSum->getAmount()) === false && is_numeric($lumpSum->getAmount()) === true
            ) {
                if (!$collection->has($lumpSum->country_id)) {
                    $collection->put($lumpSum->country_id, LumpSum::fromLumpSum($lumpSum));
                } else {
                    $element = $collection[$lumpSum->country_id];

                    $element->addAmount($lumpSum->getAmount())->addConvertedAmount($lumpSum->getConvertedAmount())->incrementDuration();

                    $collection[$lumpSum->country_id] = $element;
                }
            }
        });

        return $collection;
    }

    protected function setLumpSumsBasedOnBorderCrossings(BorderCrossingCollection $crossings): void
    {
        $this->request->accessLumpSums()->with('location')->each(function ($lumpSum) use (&$crossings) {
            $crossing = $crossings->filter(function ($crossing) use (&$lumpSum) {
                return $crossing->start->lte($lumpSum->date) && $crossing->end->gt($lumpSum->date);
            })->first();

            if ($crossing) {
                $data = [
                    'date'            => $lumpSum->date,
                    'country_id'      => $crossing->country_id,
                    'checked'         => $lumpSum->checked,
                    'destination'     => $lumpSum->destination,
                    'city'            => $lumpSum->location->city,
                    'type'            => $lumpSum->type,
                    'settlement_date' => $this->getSettlementDate(),
                    'trip_ends'       => $this->request->trip_ends,
                    'instance_id'     => $this->request->instance_id,
                    'request_slug'    => $this->request->slug
                ];

                ($crossing->country_id === $this->request->instance->country_id
                    ? $this->lumpSums->push(new NationalAccessLumpSumItem($data))
                    : $this->lumpSums->push(new ForeignAccessLumpSumItem($data))
                );
            } else {
              //  \Log::error((new IncalculableLumpSumException)->getMessage());
            }
        });
    }

    protected function getSettlementDate()
    {
        return $this->request->getSettlementDate();
    }

    public function getAmount(AmountInCurrencyCollection $total, bool $inInstanceCurrency = true): AmountInCurrencyCollection
    {
        $this->calculate()->each(function (LumpSum $lumpSum) use ($total, $inInstanceCurrency) {
            if ($inInstanceCurrency) {
                $total->addAmount($this->request->instance->currency->code, $lumpSum->getRoundedConvertedAmount());
            } else {
                $total->addAmount($lumpSum->currency, $lumpSum->getRoundedAmount());
            }
        });

        return $total;
    }
}
