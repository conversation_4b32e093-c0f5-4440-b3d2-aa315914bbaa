<?php


namespace App\Services\Balance;

use App\Collections\AmountInCurrencyCollection;
use App\Document;
use App\Installment;
use App\Repositories\InstallmentRepository;
use App\Repositories\RequestRepository;
use App\Request;
use App\Services\AccessLumpSum\AccessLumpSumService;
use App\Services\AccommodationLumpSum\AccommodationLumpSumService;
use App\Services\Cache;
use App\Services\DriveLumpSum\DriveLumpSumService;
use App\Services\TravelExpenses\TravelExpensesService;
use App\Vendors\Math;
use Illuminate\Support\Collection;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Pub\Facades\FeatureSwitcherFacade;

class BalanceService
{
    protected $request;

    protected $documents;

    private bool $calculateInInstanceCurrency;
    private bool $shouldAddTravelAndLumpSums;

    public function __construct(
        Request $request,
        ?bool $shouldAddTravelAndLumpSums = null
    ) {
        $this->request  = $request;
        $this->documents = $request
            ->accountingDocuments()
            ->with(['elements', 'currency'])
            ->get()
            ->filter(function (Document $document) {
                return $document->isSettled();
        });

        $this->calculateInInstanceCurrency = $this->calculateBalanceInInstanceCurrency();
        $this->shouldAddTravelAndLumpSums = $shouldAddTravelAndLumpSums ?? $request->canAccountDelegation();
    }

    public function getPaymentFromOwnResources()
    {
        $key = 'balancePaymentFromOwnResources'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            $documents = $this->documents->filter(function(Document $document) {
                return $document->payment === Document::PAYMENT_TYPE_OWN;
            });

            return $this->sumDocumentsAmount($documents);
        }, 100, [$this->request->cacheTag()]);


    }

    public function getPaymentFromServiceCard()
    {
        $key = 'balancePaymentFromServiceCard'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            $documents = $this->documents->filter(function(Document $document) {
                return $document->payment === Document::PAYMENT_TYPE_SERVICE_CARD;
            });

            return $this->sumDocumentsAmount($documents);
        }, 100, [$this->request->cacheTag()]);
    }

    public function getPaymentFromTransfer()
    {
        $key = 'balancePaymentFromTransfer'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            $documents = $this->documents->filter(function(Document $document) {
                return $document->payment === Document::PAYMENT_TYPE_TRANSFER;
            });

            return $this->sumDocumentsAmount($documents);
        }, 100, [$this->request->cacheTag()]);
    }

    public function getPaymentFromCorporateCard()
    {
        $key = 'balancePaymentFromCorporateCard'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            $documents = $this->documents->filter(function(Document $document) {
                return $document->payment === Document::PAYMENT_TYPE_CORPORATE_CARD;
            });

            return $this->sumDocumentsAmount($documents);
        }, 100, [$this->request->cacheTag()]);
    }

    public function getTotalExpensesAmount()
    {
        $key = 'balanceTotalExpensesAmount'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            return $this->sumDocumentsAmount($this->documents);
        }, 100, [$this->request->cacheTag()]);
    }

    public function getTravelExpensesAndOwnResourcesAmount()
    {
        $key = 'balanceTravelExpensesAndOwnResourcesAmount'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            $expenses = $this->getTravelExpensesWithLumpSums();
            $employeeExpenses = $this->getPaymentFromOwnResources();
            $mileageAllowances = $this->getMileageAllowanceAmount();

            $employeeExpenses->each(function($amount, $currency) use (&$expenses) {
                $this->addAmount($expenses, $amount);
            });

            $mileageAllowances->each(function($amount, $currency) use (&$expenses) {
                $this->addAmount($expenses, $amount);
            });

            return $this->request->lump_sum_settled ? $expenses : collect();
        }, 100, [$this->request->cacheTag()]);
    }

    protected function sumDocumentsAmount(Collection $documents): Collection
    {
        $amounts = collect();
        $documents->each(function (Document $document) use (&$amounts) {
            $currencyCode = $this->calculateInInstanceCurrency
                ? $document->instance->currency->code
                : $document->currency->code;

            $documentAmount = $this->calculateInInstanceCurrency
                ? $document->grossConvertedSettledAmount()
                : $document->grossSettledAmount();

            if (!$amounts->has($currencyCode)) {
                $amount = collect([
                    'amount' => Math::add(0, $documentAmount),
                    'currency' => $currencyCode
                ]);
            } else {
                $amount = $amounts->get($currencyCode);
                $amount['amount'] = Math::add($amount['amount'], $documentAmount);
            }

            $amounts->put($currencyCode, $amount);
        });

        return $amounts;
    }

    public function calculateEmployee()
    {
        $key = 'balanceEmployee'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            $employeeBalance = $this->getTravelExpensesWithLumpSums();
            $totalExpenses = $this->getPaymentFromOwnResources();
            $mileageAllowances = $this->getMileageAllowanceAmount();
            $installments = $this->getInstallments()->map(function($amount) {
                $amount['amount'] = Math::negative($amount['amount']);
                return $amount;
            });

            $totalExpenses->each(function($amount, $currency) use (&$employeeBalance) {
                $this->addAmount($employeeBalance, $amount);
            });

            $installments->each(function($amount, $currency) use (&$employeeBalance) {
                $this->addAmount($employeeBalance, $amount);
            });

            $mileageAllowances->each(function($amount, $currency) use (&$employeeBalance) {
                $this->addAmount($employeeBalance, $amount);
            });

            return $employeeBalance;
        }, 100, [$this->request->cacheTag()]);
    }

    protected function addAmount(Collection &$employeeBalance, $amount)
    {
        if($employeeBalance->has($amount['currency'])) {
            $acc = $employeeBalance->get($amount['currency']);
            $acc['amount'] = Math::add($acc['amount'], $amount['amount']);
        } else {
            $acc = $amount;
        }

        $employeeBalance->put($amount['currency'], $acc);
    }

    private function cacheKey(string $prefix): string
    {
        return sprintf('%s-%s', $prefix, $this->shouldAddTravelAndLumpSums() ? '1' : '0');
    }

    public function getTravelExpensesWithLumpSums()
    {
        $travelExpensesInInstanceCurrency = $this->calculateTravelExpensesInInstanceCurrency();
        $key = 'balanceTravelExpensesWithLumpSums' . $this->request->id . '-' . ($travelExpensesInInstanceCurrency ? '1' : '0');

        return Cache::get($this->cacheKey($key) , function() use ($travelExpensesInInstanceCurrency) {

            if(!$this->shouldAddTravelAndLumpSums()) {
                return collect();
            }

            $total = new AmountInCurrencyCollection();

            resolve(TravelExpensesService::class)->init($this->request)->getSettledAmount($total, $travelExpensesInInstanceCurrency);
            (new AccessLumpSumService($this->request))->getAmount($total, $travelExpensesInInstanceCurrency);
            (new AccommodationLumpSumService($this->request, resolve(RequestRepository::class)))->getAmount($total, $travelExpensesInInstanceCurrency);
            (new DriveLumpSumService($this->request))->getAmount($total, $travelExpensesInInstanceCurrency);

            return $total->greaterThanZero() > 0 && $this->request->lump_sum_settled ? $total : collect();
        }, 100, [$this->request->cacheTag()]);

    }

    public function getInstallments(bool $onlyPaid = true): Collection
    {
        $key = 'balanceInstallments' . $this->request->id . $onlyPaid;

        $installments = Cache::get($this->cacheKey($key), function () use ($onlyPaid) {
            $installments = collect();
            ($onlyPaid === true
                ? resolve(InstallmentRepository::class)->getInstallmentsPaidWithCurrencyForRequest($this->request)
                : resolve(InstallmentRepository::class)->getInstallmentsAllWithCurrencyForRequest($this->request)
            )
                ->each(function (Installment $installment) use (&$installments) {
                    $currencyCode = $this->calculateInInstanceCurrency
                        ? $installment->getConvertedAmountCurrency()->code
                        : $installment->currency->code;

                    $amount = $this->calculateInInstanceCurrency
                        ? $installment->getConvertedAmount()
                        : $installment->amount;

                    if ($installments->has($currencyCode)) {
                        $acc = $installments->get($currencyCode);
                        $acc['amount'] = Math::add($acc['amount'], $amount);
                    } else {
                        $acc = [
                            'amount' => $amount,
                            'currency' => $currencyCode,
                            'dates' => collect()
                        ];
                    }

                    if ($installment->date) {
                        $acc['dates']->push($installment->date->toDateString());
                    }

                    $installments->put($currencyCode, $acc);
                });

            return $installments;
        }, 100, [$this->request->cacheTag()]);

        return $installments instanceof Collection ? $installments : collect([]);
    }

    public function getAmountToReturnForEmployee()
    {
        $key = 'balanceAmountToReturnForEmployee'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            return $this->calculateEmployee()->filter(function($amount) {
                return $amount['amount'] > 0;
            });
        }, 100, [$this->request->cacheTag()]);
    }

    public function getAmountEmployeeCredit()
    {
        $key = 'balanceAmountEmployeeCredit'. $this->request->id;

        return Cache::get($this->cacheKey($key) , function() {
            return $this->calculateEmployee()->filter(function($amount) {
                return $amount['amount'] < 0;
            })->map(function($amount) {
                $amount['amount'] *= -1;

                return $amount;
            });
        }, 100, [$this->request->cacheTag()]);
    }

    public function getMileageAllowanceAmount()
    {
        $key = 'balanceMileageAllowance'.$this->request->id;

        return Cache::get($this->cacheKey($key), function() {
            if(!$this->shouldAddTravelAndLumpSums()) {
                return collect();
            }

            $amount = $this->request->sumMileageAllowances();
            return !$amount || $this->request->lump_sum_settled === false
                ? collect()
                : collect([
                    $this->request->instance->currency->code => [
                    'amount' => $amount,
                    'currency' => $this->request->instance->currency->code
                ]
            ]);
        }, 100, [$this->request->cacheTag()]);
    }

    protected function round(Collection $collection)
    {
        return $collection->map(function($amount) {
            $amount['amount'] = Math::round($amount['amount'], 2);
            return $amount;
        });
    }

    public function getBalance(): Collection
    {
        $key = 'balance'. $this->request->id;
        $this->setBalance();
        $balance = Cache::get($this->cacheKey($key) , function() {
            return $this->setBalance();
        }, 100, [$this->request->cacheTag()]);

        /** @var  Collection $balance */
        $payForEmployeeSum = $balance->firstWhere('name', '=', 'request-summary.to-pay-for-employee');

        if ($balance === null
            || $balance->isEmpty() === true
            || is_array($payForEmployeeSum) === false
        ) {
            return $this->getDefaultForEmptyBalance($balance);
        }

        $balance = $this->prependTotalElementCalculatedFromSelectedElements(
            $balance,
            [
                'request-summary.qualified-expenses-amount',
                'request-summary.due-travel-expenses',
                'request-summary.mileage-allowance'
            ]
        );

        return $balance;
    }

    protected function setBalance(): Collection
    {
        $balance = collect();

        if ( ! $this->request->lump_sum_settled && $this->request->documents->isEmpty()) {
            return $balance;
        }

        $balance->push([
            'name' => 'request-summary.qualified-expenses-amount',
            'values' => $this->round($this->getTotalExpensesAmount()->values())
        ]);
        $balance->push([
            'name' => 'request-summary.payments-from-own-resources',
            'values' => $this->round($this->getPaymentFromOwnResources()->values())
        ]);
        $balance->push([
            'name' => 'request-summary.service-card-payments',
            'values' => $this->round($this->getPaymentFromServiceCard()->values())
        ]);
        $balance->push([
            'name' => 'request-summary.corporate-card-payments',
            'values' => $this->round($this->getPaymentFromCorporateCard()->values())
        ]);
        $balance->push([
            'name' => 'request-summary.transfer-payments',
            'values' => $this->round($this->getPaymentFromTransfer()->values())
        ]);
        $travelExpensesWithLumpSums = $this->getTravelExpensesWithLumpSums();
        $balance->push([
            'name' => 'request-summary.due-travel-expenses',
            'values' => $this->round($travelExpensesWithLumpSums->values())
        ]);
        $balance->push([
            'name' => 'request-summary.mileage-allowance',
            'values' => $this->round($this->getMileageAllowanceAmount()->values())
        ]);
        $balance->push([
            'name' => 'request-summary.in-total-own-resources-and-travel-expenses',
            'values' => $this->round($this->getTravelExpensesAndOwnResourcesAmount()->values())
        ]);
        $balance->push([
            'name' => 'request-summary.installments',
            'values' => $this->round($this->getInstallments()->values())
        ]);
        $balance->push([
            'name' => 'request-summary.to-pay-for-employee',
            'values' => $this->round($this->getAmountToReturnForEmployee()->values())
        ]);
        $balance->push([
            'name' => 'request-summary.return',
            'values' => $this->round($this->getAmountEmployeeCredit()->values())
        ]);

        return $balance->filter(function($item) {
            return $item['values']->isNotEmpty();
        })->values();
    }

    protected function shouldAddTravelAndLumpSums()
    {
        return $this->shouldAddTravelAndLumpSums;
    }

    protected function getDefaultForEmptyBalance(Collection $balance): Collection
    {
        return $balance->push([
            'name' => 'request-summary.to-pay-for-employee',
            'values' => collect([
                $this->request->instance->currency->code => [
                    'amount' => 0,
                    'currency' => $this->request->instance->currency->code
                ]
            ])->values()
        ]);
    }

    public function prependTotalElementCalculatedFromSelectedElements(Collection $balance, array $filter): Collection
    {
        $sum = collect();
        $elementsToSum = $balance->filter(function ($item) use ($filter) {
            return in_array($item['name'], $filter);
        });

        $elementsToSum->each(function ($item) use (&$sum) {
            if (isset($item['values']) && $item['values'] instanceof Collection) {
                $item['values']->each(function ($singleEntryInCurrency) use (&$sum) {
                    $summedCurrencyGroup = $sum->where('currency', $singleEntryInCurrency['currency'])->pop();

                    if ($summedCurrencyGroup instanceof Collection && $summedCurrencyGroup->isNotEmpty()) {
                        $summedCurrencyGroup['amount'] = Math::add($summedCurrencyGroup['amount'], $singleEntryInCurrency['amount']);
                    } else {
                        $sum->push(collect([
                            'currency'      => $singleEntryInCurrency['currency'],
                            'amount'        => $singleEntryInCurrency['amount'],
                        ]));
                    }
                });
            }

        });

        $balance->prepend([
            'name'      => 'request-summary.get-balance-sum',
            'values'    => $this->round($sum)
        ]);

        return $balance;
    }

    protected function calculateTravelExpensesInInstanceCurrency(): bool
    {
        return resolve(FeatureSwitcherFacade::class)->isEnabledForCompany(
            FeatureEnum::FEATURE_TRAVEL_EXPENSES_IN_BALANCE_IN_INSTANCE_CURRENCY(),
            $this->request->company
        );
    }

    protected function calculateBalanceInInstanceCurrency(): bool
    {
        return resolve(FeatureSwitcherFacade::class)->isEnabledForCompany(
            FeatureEnum::FEATURE_BALANCE_IN_INSTANCE_CURRENCY(),
            $this->request->company
        );
    }
}
