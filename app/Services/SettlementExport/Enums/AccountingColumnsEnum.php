<?php

declare(strict_types=1);

namespace App\Services\SettlementExport\Enums;

use App\Enum\AbstractEnum;
use Modules\FeatureSwitcher\Pub\Interfaces\FeatureEnumClassInterface;

/**
 * Class AccountingColumnsEnum
 * @package App\Services\SettlementExport\Enums
 *
 * @method static AccountingColumnsEnum ACCOUNT
 * @method static AccountingColumnsEnum MPK
 * @method static AccountingColumnsEnum PROJECT
 */
class AccountingColumnsEnum extends AbstractEnum implements FeatureEnumClassInterface
{
    private const ACCOUNT = 'ACCOUNT';
    private const MPK = 'MPK';
    private const PROJECT = 'PROJECT';
}