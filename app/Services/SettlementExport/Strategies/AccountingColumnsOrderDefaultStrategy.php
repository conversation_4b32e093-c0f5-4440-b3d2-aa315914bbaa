<?php

declare(strict_types=1);

namespace App\Services\SettlementExport\Strategies;

use App\Services\SettlementExport\Enums\AccountingColumnsEnum;

class AccountingColumnsOrderDefaultStrategy implements AccountingColumnsOrderStrategyInterface
{
    private array $orderedColumns;

    public function __construct(array $orderedColumns)
    {
        $this->orderedColumns = $orderedColumns;
    }

    public function getColumns(): array
    {
        if(count($this->orderedColumns) === 0){
            return AccountingColumnsEnum::toArray();
        }

        return $this->orderedColumns;
    }
}