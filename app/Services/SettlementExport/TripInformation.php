<?php

namespace App\Services\SettlementExport;

use App\Request;
use App\Services\BorderCrossing\BorderCrossingItem;
use App\Services\BorderCrossing\BorderCrossingService;
use Illuminate\Support\Collection;
use Modules\Allowance\Priv\RequestTravelTypesCollection;

class TripInformation
{
    protected BorderCrossingService $borderCrossingService;
    protected Request $request;

    public function process(Request $request): array
    {
        $this->borderCrossingService = new BorderCrossingService($request);
        $this->request = $request;

        return [
            'targetCountries' => $this->getTargetCountries(),
            'tripTime' => $this->getTripTime(),
            'isUnrealized' => $this->request->isUnrealized(),
            'meansOfTransport' => $this->getMeansOfTransport(),
        ];
    }

    private function getTargetCountries(): string
    {
        return $this->borderCrossingService->getBorderCrossingTargetsCollection()
            ->pluck('country.name')->map(function ($countryName) {
                return trans($countryName);
            })
            ->implode(', ');
    }

    private function getMeansOfTransport(): string
    {
        return RequestTravelTypesCollection::makeTripsFromRequest($this->request)->getTranslatedAndCommaSeparated();
    }

    private function getTripTime(): Collection
    {
        $nationalTime = collect();
        $abroadTime = collect();
        $crossingItems = $this->borderCrossingService->getBorderCrossingTargetsCollection(true);
        $crossingItems->each(function (BorderCrossingItem $crossingItem) use (&$nationalTime, &$abroadTime) {
            if ($crossingItem->isInstanceCountry()) {
                $nationalTime->push([
                    'start' => $crossingItem->start->format('Y.m.d H:i'),
                    'end' => $crossingItem->end->format('Y.m.d H:i'),
                    'city_start' => $crossingItem->city_start,
                    'city_end' => $crossingItem->city_end,
                ]);
            } else {
                $abroadTime->push([
                    'start' => $crossingItem->start->format('Y.m.d H:i'),
                    'end' => $crossingItem->end->format('Y.m.d H:i'),
                    'countryName' => trans($crossingItem->country->getNameAttribute())
                ]);
            }
        });

        return collect([
            'national' => $nationalTime,
            'abroad' => $abroadTime
        ]);
    }
}
