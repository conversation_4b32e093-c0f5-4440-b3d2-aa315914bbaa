<?php

declare(strict_types=1);

namespace App\Services\Outbox;

use App\OutboxItem;
use Domain\Mindento\Requests\ExportTicketMindentoRequest;
use Domain\Mindento\Services\ProcessMindentoRequestService;
use Domain\Obt\DTOs\ExportTicketMindentoDTO;

class SendTicketOutboxAction implements OutboxActionInterface
{
    protected ProcessMindentoRequestService $processMindentoRequestService;

    public function __construct(ProcessMindentoRequestService $processMindentoRequestService)
    {
        $this->processMindentoRequestService = $processMindentoRequestService;
    }

    public function handle(OutboxItem $outbox): void
    {
        if ($outbox->reservation->ticket) {
            $request = new ExportTicketMindentoRequest(
                new ExportTicketMindentoDTO(
                    $outbox->reservation->mindento_request_slug,
                    $outbox->reservation->mindento_element_id,
                    $outbox->reservation->mindento_element_type,
                    $outbox->reservation->ticket,
                    $outbox->reservation->ticket_number ?? '',
                    false,
                    $outbox->reservation->reference_numbers
                )
            );

            $this->processMindentoRequestService->process($request, $outbox->reservation->offer->customer->domain);
        }
    }
}
