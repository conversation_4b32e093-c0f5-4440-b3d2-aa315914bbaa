<?php declare(strict_types=1);


namespace App\Services\TenantCreator\Artisan\DataProviders\CSV;


class Group extends CSVDataProvider
{
    const NAME_INDEX = 0;

    const INDEXES_AMOUNT = 1;

    protected function indexesAmount(): int
    {
        return self::INDEXES_AMOUNT;
    }

    protected function parseFileData(): array
    {
        $parsed = [];

        foreach ($this->data as $line)
        {
            $parsed[] = [
                'name' => $line[self::NAME_INDEX],
            ];
        }

        return $parsed;
    }
}
