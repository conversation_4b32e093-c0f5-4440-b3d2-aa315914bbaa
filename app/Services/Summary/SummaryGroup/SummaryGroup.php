<?php


namespace App\Services\Summary\SummaryGroup;


use Modules\Accounting\Priv\Entities\DocumentElementGroup;
use App\Helpers\NameTranslation;
use App\Instance;
use App\Interfaces\RequestElementDocumentableInterface;
use App\Services\RulesService\Message\RuleMessageCollection;
use App\Services\Summary\SummaryElement\SummaryElement;
use App\Services\Summary\SummaryElement\SummaryElementCollection;
use Illuminate\Contracts\Support\Arrayable;

class SummaryGroup implements Arrayable
{
    const ACCOMMODATION_LUMP_SUM_GROUP_SLUG = 'expense-accommodatiom-group';
    const ACCESS_LUMP_SUM_GROUP_SLUG = 'expense-trip-group';
    const TRAVEL_EXPENSES_GROUP_SLUG = 'travel-expense-drive-trip-group';
    const DRIVE_LUMP_SUM_GROUP_SLUG = 'travel-expense-drive-trip-group';
    const TRAVEL_EXPENSE_AND_DRIVE_LUMP_SUM_GROUP_SLUG = 'travel-expense-drive-trip-group';
    const MILEAGE_ALLOWANCE_GROUP_SLUG = 'expense-trip-group';

    public $slug;

    /** @var NameTranslation */
    public $name;
    /** @var boolean */
    public $countSettled;

    /** @var SummaryElementCollection  */
    protected $elements;

    /** @var RuleMessageCollection */
    protected $rules;

    protected string $instanceCurrencyCode;

    /**
     * SummaryGroup constructor.
     * @param $slug
     * @param $name
     */
    public function __construct($slug, NameTranslation $name, $countSettled, string $instanceCurrencyCode)
    {
        $this->slug         = $slug;
        $this->name         = $name;
        $this->countSettled = $countSettled;

        $this->instanceCurrencyCode = $instanceCurrencyCode;
        $this->elements = new SummaryElementCollection();
        $this->rules    = new RuleMessageCollection();
    }

    public static function fromEloquentModel(DocumentElementGroup $group, $countSettled, string $instanceCurrencyCode)
    {
        return new static($group->slug, new NameTranslation($group->name), $countSettled, $instanceCurrencyCode);
    }

    public function addElement(SummaryElement $element)
    {
        $this->elements->push($element);

        return $this->elements->keys()->last();
    }

    public function updateElement($key, SummaryElement $element)
    {
        $this->elements->put($key, $element);

        return $key;
    }

    public function getElements(): SummaryElementCollection
    {
        return $this->elements;
    }

    public function searchForRequestElement(RequestElementDocumentableInterface $element)
    {
        return $this->elements->search(function (SummaryElement $el) use (&$element) {
            return SummaryElement::createSIDForRequestElement($element) === $el->getSID();
        });
    }

    public function getElement($key): ?SummaryElement
    {
        return $this->elements->get($key);
    }

    public function getSettledConvertedAmount()
    {
        return $this->countSettled ? $this->elements->sumSettledConvertedAmounts($this->instanceCurrencyCode) : collect([
            'amount' => 0,
            'currency' => $this->instanceCurrencyCode,
        ]);
    }

    public function getRequestedConvertedAmount()
    {
        return $this->elements->sumRequestedConvertedAmounts($this->instanceCurrencyCode);
    }

    public function getSettledForeignAmounts()
    {
        return $this->countSettled ? $this->elements->sumSettledForeignAmounts() : collect();
    }

    public function getRequestedForeignAmounts()
    {
        return $this->elements->sumRequestedForeignAmounts();
    }

    public function anyElementHasBrokeComplianceRules()
    {
        return $this->elements->filter(function(SummaryElement $element) {
            return $element->hasBrokenRules();
        })->isNotEmpty();
    }

    public function getRules(): RuleMessageCollection
    {
        $this->rules = new RuleMessageCollection();
        $this->rules = $this->rules->merge($this->elements->getRules());
        return $this->rules;
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return [
            'name'                     => $this->name->toArray(),
            'slug'                     => $this->slug,
            'requestedConvertedAmount' => $this->getRequestedConvertedAmount()->toArray(),
            'requestedForeignAmounts'  => $this->getRequestedForeignAmounts()->toArray(),
            'settledConvertedAmount'   => $this->getSettledConvertedAmount()->toArray(),
            'settledForeignAmounts'    => $this->getSettledForeignAmounts()->toArray(),
            'elements'                 => $this->getElements()->toArray(),
            'hasBrokenRules'           => $this->anyElementHasBrokeComplianceRules(),
        ];
    }

    public function hasElements(): bool
    {
        return $this->getElements()->isNotEmpty();
    }
}