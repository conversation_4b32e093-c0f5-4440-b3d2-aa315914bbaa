<?php


namespace App\Services\Summary\SummaryExpenseType;


use App\Helpers\NameTranslation;
use App\Services\Summary\Interfaces\SummaryExpenseTypeInterface;
use App\Services\Summary\SummaryDocument\SummaryDocument;
use App\Services\Summary\SummaryDocument\SummaryDocumentCollection;

class SummaryExpenseType implements SummaryExpenseTypeInterface
{
    const ICON_SOURCE = 'type';

    /** @var NameTranslation */
    public $name;

    protected $type;

    protected $exchangeRateFrom;

    protected $settlementDate;

    protected $id;

    protected $documents;

    protected string $instanceCurrencyCode;


    /**
     * SummaryExpenseType constructor.
     */
    public function __construct(NameTranslation $name, $type, $exchangeRate, $settlementDate, $id = null, string $instanceCurrencyCode)
    {
        $this->name             = $name;
        $this->type             = $type;
        $this->exchangeRate     = $exchangeRate;
        $this->id               = $id;
        $this->settlementDate   = $settlementDate;
        $this->instanceCurrencyCode = $instanceCurrencyCode;

        $this->documents = new SummaryDocumentCollection();
    }

    public function addDocument(SummaryDocument $document)
    {
//        $document = $this->setDocumentExchangeRateDate($document);
        $this->documents->push($document);
    }

    public function getConvertedAmount()
    {
        return $this->documents->sumConvertedAmounts($this->instanceCurrencyCode);
    }

    public function getForeignAmounts()
    {
        return $this->documents->sumForeignAmounts();
    }

    public function getDocuments(): SummaryDocumentCollection
    {
        return $this->documents;
    }

    public function toArray()
    {
        return [
            'name'            => $this->name->toArray(),
            'type'            => $this->getType(),
            'iconSource'      => static::ICON_SOURCE,
            'id'              => $this->id,
            'convertedAmount' => $this->getConvertedAmount()->toArray(),
            'foreignAmounts'  => $this->getForeignAmounts()->values()->toArray(),
            'documents'       => $this->documents->toArray()
        ];
    }

    public function getType(): ?string
    {
        return $this->type;
    }
}