<?php


namespace App\Services\StatusChange;


use App\Notifications\RequestCanceledByOwner;
use App\Notifications\RequestRejectedAcceptorNotification;
use App\Notifications\RequestRejectedOwnerNotification;
use App\Notifications\RequestReturnedForImprovementAcceptorNotification;
use App\Notifications\RequestReturnedForImprovementOwnerNotification;
use App\Notifications\RequestSettlementHasBeenAcceptedNotification;
use App\Notifications\RequestSettlementToImprovementNotification;
use App\Notifications\RequestWaitingForAcceptanceNotification;
use App\Notifications\RequestWaitingForSettlementNotification;
use App\Notifications\YourRequestHasBeenAcceptedNotification;
use App\Repositories\UserRepository;
use App\Request;
use App\User;
use Illuminate\Support\Facades\Log;

class StatusChangeNotificationService
{
    private $prevStatus = null;

    private $currentStatus = null;

    private static $instance = null;

    /** @var UserRepository */
    protected $userRepository;

    private function __construct()
    {
        $this->userRepository = resolve(UserRepository::class);
    }

    public static function instance(): StatusChangeNotificationService
    {

        if (static::$instance === null) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    public function sendNotifications(Request $request, $prevStatus, $currentStatus)
    {
        $this->prevStatus    = $prevStatus;
        $this->currentStatus = $currentStatus;

        if ($this->checkStatuses(Request::STATUS_DRAFT, Request::STATUS_WAITING_FOR_ACCEPTANCE)) {
            $request->acceptors()->each(function (User $user) use ($request) {
                $user->notify(new RequestWaitingForAcceptanceNotification(
                    $request->slug,
                    $user->slug,
                    base64_encode(
                        $this->userRepository->getLowResolutionAvatarForUser($user, true)->getFileContent()
                    )
                ));
            });
        }

        if ($this->checkStatuses(Request::STATUS_WAITING_FOR_ACCEPTANCE, Request::STATUS_REJECTED)) {
            $request->acceptors()->where('id', "!=", \Auth::user()->id)->get()->each(function (User $user) use ($request
            ) {
                $user->notify(new RequestRejectedAcceptorNotification($request->slug));
            });

            $request->user->notify(new RequestRejectedOwnerNotification($request->slug));
        }

        if ($this->checkStatuses(Request::STATUS_WAITING_FOR_ACCEPTANCE, Request::STATUS_CANCELED)) {
            $request->acceptors->each(function (User $user) use ($request) {
                $user->notify(new RequestCanceledByOwner($request->slug));
            });
        }

        if ($this->checkStatuses(Request::STATUS_WAITING_FOR_ACCEPTANCE, Request::STATUS_DRAFT)) {
            $request->acceptors()->where('id', "!=", \Auth::user()->id)->get()->each(function (User $user) use ($request
            ) {
                $user->notify(new RequestReturnedForImprovementAcceptorNotification($request->slug));
            });

            $request->user->notify(new RequestReturnedForImprovementOwnerNotification($request->slug));
        }

        if ($this->checkStatuses(
            Request::STATUS_WAITING_FOR_ACCEPTANCE,
            [Request::STATUS_UPCOMING_TRIP, Request::STATUS_SETTLEMENT])
        ) {
            $request->user->notify(new YourRequestHasBeenAcceptedNotification($request->slug));
        }

//        if ($this->checkStatuses(Request::STATUS_UPCOMING_TRIP, Request::STATUS_TRIP)) {
//            $request->user->notify(new RequestTripStarted($request));
//        }

        if ($this->checkStatuses(Request::STATUS_SETTLEMENT, Request::STATUS_ACCEPTANCE_OF_SETTLEMENT)) {
            $request->settlementAcceptors()->each(function (User $user) use ($request) {
                $user->notify(new RequestWaitingForSettlementNotification(
                    $request->slug,
                    $user->slug,
                    base64_encode(
                        $this->userRepository->getLowResolutionAvatarForUser($user, true)->getFileContent()
                    )
                ));
            });
        }

        if ($this->checkStatuses(Request::STATUS_ACCEPTANCE_OF_SETTLEMENT, Request::STATUS_SETTLEMENT)) {
            $request->user->notify(new RequestSettlementToImprovementNotification($request->slug));
        }

        if ($this->checkStatuses(Request::STATUS_ACCOUNTING, Request::STATUS_SETTLEMENT)) {
            $request->user->notify(new RequestSettlementToImprovementNotification($request->slug));
        }

        if ($this->checkStatuses(Request::STATUS_ACCEPTANCE_OF_SETTLEMENT, Request::STATUS_ACCOUNTING)) {
            $request->user->notify(new RequestSettlementHasBeenAcceptedNotification($request->slug));
        }
    }

    private function currentStatusIn(array $statuses): bool
    {
        return in_array($this->currentStatus, $statuses);
    }

    private function prevStatusIn(array $statuses): bool
    {
        return in_array($this->prevStatus, $statuses);
    }

    private function checkStatuses($prev, $curr)
    {

        if (!is_array($prev)) {
            $prev = [$prev];
        }

        if (!is_array($curr)) {
            $curr = [$curr];
        }

        return $this->prevStatusIn($prev) && $this->currentStatusIn($curr);
    }
}