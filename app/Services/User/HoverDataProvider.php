<?php

namespace App\Services\User;

use App\Repositories\UserRepository;
use App\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\FeatureSwitcher\Priv\Exceptions\FeatureNotFoundException;
use Modules\FeatureSwitcher\Priv\Services\FeatureService;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;

class HoverDataProvider
{
    private FeatureService $featureService;

    public function __construct(FeatureService $featureService)
    {
        $this->featureService = $featureService;
    }

    public function getHoverDataForUser(User $user): array
    {
        $hoverData = [];

        try {
            $hoverDataAttributes = $this->getHoverDataAttributes($user);

            if (empty($hoverDataAttributes)) {
                return $hoverData;
            }

            foreach ($hoverDataAttributes as $attribute) {
                $hoverData[] = $this->createHoverDataItem($user, $attribute);
            }
        } catch (FeatureNotFoundException $exception) {
            return $hoverData;
        }

        return $hoverData;
    }

    private function getHoverDataAttributes(User $user): ?array
    {
        $company = $user->getCompany();
        $instance = $user->instance;

        return $this->featureService->getTextParameters(
            FeatureEnum::FEATURE_USER_HOVER(),
            $company,
            $instance
        );
    }

    private function createHoverDataItem(User $user, string $attribute): array
    {
        $name = trans('user.' . $attribute);

        $value = Str::contains($attribute, '.')
            ? $this->getRelatedAttributeValue($user, $attribute)
            : $user->$attribute;

        return [
            'name' => $name,
            'field' => $attribute,
            'value' => $value
        ];
    }

    private function getRelatedAttributeValue($model, $attribute)
    {
        $segments = explode('.', $attribute);

        foreach ($segments as $segment) {
            if (!isset($model->$segment)) {
                return null;
            }
            $model = $model->$segment;
        }

        return $model;
    }
}