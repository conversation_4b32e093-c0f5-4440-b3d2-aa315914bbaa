<?php


namespace App\Services\Search;


use App\Http\Responses\DocumentResponse;
use App\Http\Responses\RequestResponse;
use App\Instance;
use App\Repositories\Criteria\InstanceV2Criterion;
use App\Repositories\Criteria\SearchPhraseCriterion;
use App\Repositories\Criteria\UserCanReadCriterion;
use App\Repositories\DocumentRepository;
use App\Repositories\RequestRepository;

class SearchService
{
    protected $documentRepository;
    protected $requestRepository;

    public function __construct(DocumentRepository $documentRepository, RequestRepository $requestRepository)
    {
        $this->requestRepository = $requestRepository;
        $this->documentRepository = $documentRepository;
    }

    /**
     * @return array
     */
    public function search(Instance $instance)
    {
        return [
            'requests' => $this->searchRequests($instance),
            'documents' => $this->searchDocuments($instance)
        ];
    }

    /**
     * @return array
     */
    protected function searchRequests(Instance $instance)
    {
        $requests = $this->requestRepository->sortRequestsList($this->requestRepository->getByCriteria([
            new InstanceV2Criterion($instance),
            new UserCanReadCriterion(),
            new SearchPhraseCriterion(),
        ]));

        return RequestResponse::collection($requests)->getData();
    }

    /**
     * @return array
     */
    protected function searchDocuments(Instance $instance)
    {
        $documents = $this->documentRepository->getByCriteria([
            new InstanceV2Criterion($instance),
            new UserCanReadCriterion(),
            new SearchPhraseCriterion(),
        ]);

        return DocumentResponse::collection($documents)->getData();
    }
}