<?php

declare(strict_types=1);

namespace App\Services;

use App\Document;
use App\DocumentElement;
use App\Repositories\DocumentElementRepository;
use App\Repositories\DocumentRepository;
use App\Request;
use Modules\Analytics\Pub\Dtos\DocumentElementDto;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;

class DocumentService
{
    protected AccountDimensionItemFacade $accountDimensionItemFacade;

    protected DocumentRepository $documentRepository;

    protected DocumentElementRepository $documentElementRepository;

    public function __construct(
        AccountDimensionItemFacade $accountDimensionItemFacade,
        DocumentRepository $documentRepository,
        DocumentElementRepository $documentElementRepository
    ) {
        $this->accountDimensionItemFacade = $accountDimensionItemFacade;
        $this->documentRepository = $documentRepository;
        $this->documentElementRepository = $documentElementRepository;
    }

    public function createNewElement(Document $document, array $data): DocumentElement
    {
        if($document->request->status !== Request::STATUS_ACCOUNTING) {

            $element = $this->documentElementRepository->findByDocumentIdRequestElementIdTypeAndExpenseTypeId(
                $document->id,
                $data['request_element']['id'],
                $data['request_element']['type'],
                $data['type_id']
            );

            if($element !== null) {
                $element = $this->documentRepository->updateElement($document->id, $element->id, $data);
                $element->refresh();

                return $element;
            }
        }

        $element = $this->documentRepository->createElement($document->id, $data);

        $this->accountDimensionItemFacade->setDefaultsForDocumentElement(DocumentElementDto::createFromDocumentElement($element));

        return $element;
    }
}
