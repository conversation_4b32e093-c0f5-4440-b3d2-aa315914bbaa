<?php


namespace App\Services\Copy\Element;


use App\TargetPoint;

class TargetPointCopier extends ElementCopier
{
    /** @var TargetPoint */
    protected $element;

    /** @var TargetPoint */
    protected $copiedElement;

    protected static $locationRelationNames = ['location'];

    protected function overwriteDates()
    {
        $this->copiedElement->date = $this->copiedElement->date->addDays($this->daysToAdd);
    }
}