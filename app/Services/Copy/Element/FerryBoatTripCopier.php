<?php


namespace App\Services\Copy\Element;


use App\FerryBoatTrip;
use App\Interfaces\RequestElementInterface;

class FerryBoatTripCopier extends ElementCopier
{
    /** @var RequestElementInterface|FerryBoatTrip */
    protected $element;

    /** @var RequestElementInterface|FerryBoatTrip */
    protected $copiedElement;

    protected static $locationRelationNames = ['departureLocation', 'destinationLocation'];

    protected function overwriteDates()
    {
        $this->copiedElement->departure_at = $this->copiedElement->departure_at->addDays($this->daysToAdd);

        if($this->copiedElement->round_trip) {
            $this->copiedElement->return_at = $this->copiedElement->return_at->addDays($this->daysToAdd);
        }
    }
}