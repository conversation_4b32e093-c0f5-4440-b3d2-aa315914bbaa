<?php


namespace App\Services\Copy\Element;


use App\PrivateAccomodation;

class PrivateAccommodationCopier extends ElementCopier
{
    /** @var PrivateAccomodation */
    protected $element;

    /** @var PrivateAccomodation */
    protected $copiedElement;

    protected static $locationRelationNames = ['location'];

    protected function overwriteDates()
    {
        $this->copiedElement->arrival_at   = $this->copiedElement->arrival_at->addDays($this->daysToAdd);
        $this->copiedElement->departure_at = $this->copiedElement->departure_at->addDays($this->daysToAdd);
    }
}