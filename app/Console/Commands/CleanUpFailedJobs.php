<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CleanUpFailedJobs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:failed-jobs {--days=7 : The number of days to retain failed jobs data before deletion}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up failed jobs from the database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $days = $this->option('days');

        $count = \DB::table('failed_jobs')
            ->where('failed_at', '<', now()->subDays($days))
            ->delete();

        $this->info('Cleaned up ' . $count . ' failed jobs older than ' . $days . ' days successfully!');    }
}
