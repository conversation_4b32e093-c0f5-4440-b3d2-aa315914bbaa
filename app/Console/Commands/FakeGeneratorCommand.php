<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Company;
use App\Group;
use App\Instance;
use App\Provider;
use App\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\App;
use \Symfony\Component\Console\Helper\ProgressBar;

class FakeGeneratorCommand extends AbstractCommand
{
    protected const MAX_QTY = 100000;
    protected const AVAILABLE_IN_ENVIRONMENTS = ['local', 'testing', 'staging'];
    protected const DEFAULT_QTY = 25000;
    protected const GENERATE_USERS = 'generate_users';
    protected const REMOVE_USERS = 'remove_users';
    protected const GENERATE_PROVIDERS = 'generate_providers';
    protected const REMOVE_PROVIDERS = 'remove_providers';
    protected const CHOICES = [self::GENERATE_USERS, self::REMOVE_USERS, self::GENERATE_PROVIDERS, self::REMOVE_PROVIDERS];
    protected const CHOICE_QUESTION = 'what would you like to do?';
    protected const COMMAND_SUCCESS = 'Command %s finished with count %s!';
    protected const COMMAND_ERROR = 'Command has unexpected error, please contact developer.';
    protected const FAKE = 'Fake';

    protected $signature = 'fake:generator';
    protected $description = 'Load number of fake providers for given instance';

    public function handleInternal(): void
    {
        try {
            $instance = $this->askForInstance();
            $choice = $this->choice(self::CHOICE_QUESTION, self::CHOICES, 2);

            switch ($choice) {
                case self::GENERATE_USERS:
                    $progressBar = $this->generateFakeUsers($instance);
                    break;
                case self::REMOVE_USERS:
                    $progressBar = $this->removeUnusedUsers($instance);
                    break;
                case self::GENERATE_PROVIDERS:
                    $progressBar = $this->generateFakeProviders($instance);
                    break;
                case self::REMOVE_PROVIDERS:
                    $progressBar = $this->removeUnusedProviders($instance);
                    break;
            }

            $this->output->newLine(2);
            $this->output->success(sprintf(self::COMMAND_SUCCESS, $choice, $progressBar->getMaxSteps()));
        } catch (\Throwable $exception) {
            throw $exception;
            $this->output->error(self::COMMAND_ERROR);
        }
    }

    protected function removeUnusedProviders(Instance $instance): ProgressBar
    {
        $providers = Provider::withTrashed()->where('instance_id', $instance->id);
        $progressBar = $this->output->createProgressBar($providers->count());
        $progressBar->start();
        $providers->chunk(100, function (Collection $providers) use (&$progressBar) {
            $providers->each(function (Provider $provider) use (&$progressBar) {
                if ($provider->documents()->count() === 0
                    && str_contains($provider->name, self::FAKE)
                ) {
                    $provider->delete();
                    $provider->forceDelete();
                }

                $progressBar->advance();
            });
        });

        $progressBar->finish();
        return $progressBar;
    }

    protected function generateFakeProviders(Instance $instance): ProgressBar
    {
        $qty = $this->askForQty();
        $progressBar = $this->output->createProgressBar($qty);
        $progressBar->start();

        for ($qty; $qty > 0; $qty--) {
            factory(\App\Provider::class)->create([
                'instance_id' => $instance,
                'name' => self::FAKE . random_int(0,10000)
            ]);

            $progressBar->advance();
        }

        $progressBar->finish();
        return $progressBar;
    }

    protected function askForQty(): int
    {
        $qty = $this->ask('Qty:', self::DEFAULT_QTY);

        if (empty($qty) === true || is_numeric($qty) === false || $qty > self::MAX_QTY) {
            $this->warn(
                sprintf(
                    'QTY is incorrect or value exceeds: %s!',
                    number_format(self::MAX_QTY, 0, '.', ' '
                    )
                )
            );
            exit;
        }

        return (int)$qty;
    }

    protected function askForInstance(): Instance
    {
        if (in_array(App::environment(), self::AVAILABLE_IN_ENVIRONMENTS) !== true) {
            $this->output->warning(
                sprintf(
                    'This command is available only in following environments: %s',
                    implode(', ', self::AVAILABLE_IN_ENVIRONMENTS)
                )
            );
            exit;
        }

        $instances = Instance::all()->transform(function (Instance $instance) {
            return [
                'id' => $instance->id,
                'name' => $instance->domain
            ];
        })->toArray();

        $this->table(['id', 'name'], $instances);
        $instanceId = $this->ask('Select instance ID:');
        $instance = Instance::find($instanceId);

        if (empty($instanceId) === true || ($instance instanceof Instance) === false) {
            $this->warn('Instance ID is incorrect!');
            exit;
        }

        return $instance;
    }

    protected function generateFakeUsers(Instance $instance): ProgressBar
    {
        $qty = $this->askForQty();
        $company = $this->askForCompany($instance);

        $progressBar = $this->output->createProgressBar($qty);
        $progressBar->start();
        $groups = Group::where('instance_id', $instance->id)->get();

        for ($qty; $qty > 0; $qty--) {
            $user = factory(\App\User::class)->create([
                'instance_id'   => $instance->id,
                'company_id'    => $company->id,
                'first_name'    => self::FAKE . random_int(0, 10000)
            ]);

            /** @var User $user*/

            $user->groups()->save($groups->random(1)->first());

            $progressBar->advance();
        }

        return $progressBar;
    }

    protected function removeUnusedUsers(Instance $instance): ProgressBar
    {
        $company = $this->askForCompany($instance);
        $users = \App\User::withTrashed()->where([
            ['instance_id','=', $instance->id],
            ['company_id','=', $company->id]
        ]);
        $progressBar = $this->output->createProgressBar($users->count());
        $progressBar->start();
        $users->chunk(100, function (Collection $users) use (&$progressBar) {
            $users->each(function (\App\User $user) use (&$progressBar) {
                if ($user->requests()->count() === 0
                    && ($user->supervisor()->first() instanceof \App\User) === false
                    && str_contains($user->first_name, self::FAKE)
                ) {
                    $user->delete();
                    $user->forceDelete();
                }

                $progressBar->advance();
            });
        });

        $progressBar->finish();
        return $progressBar;
    }

    protected function askForCompany(Instance $instance): Company
    {
        $companies = Company::all()->where('instance_id', $instance->id)->transform(function (Company $company) {
            return [
                'id' => $company->id,
                'name' => $company->name
            ];
        })->toArray();

        $this->table(['id', 'name'], $companies);
        $companyId = $this->ask('Select company ID:');
        $company = Company::find($companyId);

        if (empty($companyId) === true || ($company instanceof Company) === false) {
            $this->warn('Company ID is incorrect!');
            exit;
        }

        return $company;
    }
}
