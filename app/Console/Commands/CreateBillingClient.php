<?php


namespace App\Console\Commands;


use App\Exceptions\ValidationException;
use App\Instance;
use App\User;
use Illuminate\Console\Command;

class CreateBillingClient extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:billing-client';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dodaje klienta oauth do bazy dla Vaterval Billing';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {

            $instance = Instance::with(['companies','mpks'])->first();

            $email = '<EMAIL>';
            $user = factory(User::class)->create([
                'email' => $email,
                'employee_unique_identifier' => $email,
                'mpk_id' => $instance->mpks->first()->id,
                'company_id' => $instance->companies->first()->id,
                'instance_id' => $instance->id,
                'first_name' => 'Użytkownik',
                'last_name' => 'Techniczny',
                'phone' => '*********',
                'grade' => '0',
                'level' => '0',
                'erp_id' => '0'
            ]);
        } catch (ValidationException $exception) {
            dd($exception->errors());
        }

        $group = \App\Group::where(['name' => 'Billing', 'instance_id' => $user->instance_id])->first();
        $group->users()->attach([$user->id]);

        \DB::insert("insert into oauth_clients (id, user_id, name, secret, redirect) values (?, ?, ?, ?, ?)", [
            3,
            $user->id,
            'Vaterval Billing',
            bcrypt('WS3gzPs3EqRISyXQTndbI9g3aISDqqrKsKrCyul5'),
            'localhost'
        ]);
    }
}
