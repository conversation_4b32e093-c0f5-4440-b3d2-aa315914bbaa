<?php

namespace App\Console\Commands;

use App\Instance;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\FeatureSwitcher\Priv\Console\AbstractConfigurationCommand;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Question\ChoiceQuestion;

class RemoveUnusedAccountDimensionItems extends AbstractConfigurationCommand
{
    private const ALL_COMPANIES = 'ALL';
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'account_dimensions:remove-unused-items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Remove unused instance's account dimension items";

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    function handleInternal(): void
    {
        $instance = $this->askForInstance();
        $accountDimensionId = $this->askForAccountDimensionId($instance);
        $this->removeUnused($instance->id, $accountDimensionId);
    }

    private function removeUnused(int $instanceId, int $accountDimensionId): void
    {
        AccountDimensionItem::query()
            ->select('account_dimension_items.*')
            ->distinct()
            ->leftJoin(
                'request_account_dimension_items',
                'request_account_dimension_items.account_dimension_item_id',
                '=',
                'account_dimension_items.id'
            )
            ->leftJoin(
                'document_account_dimension_items',
                'document_account_dimension_items.account_dimension_item_id',
                '=',
                'account_dimension_items.id'
            )
            ->where('account_dimension_items.instance_id', $instanceId)
            ->where('account_dimension_items.account_dimension_id', $accountDimensionId)
            ->whereNull('document_account_dimension_items.id')
            ->whereNull('request_account_dimension_items.id')
            ->delete();
    }

    protected function askForAccountDimensionId(Instance $instance): int
    {
        $accountDimensions = AccountDimension::where('instance_id', $instance->id)->with('company')->get();

        $accountDimensionOptions = [];
        /** @var Instance $instance */
        foreach ($accountDimensions as $accountDimension) {
            $company = $accountDimension->company ? $accountDimension->company->code : self::ALL_COMPANIES;
            $accountDimensionOptions[$accountDimension->id] = sprintf(
                '[%s] [%s] %s',
                $company,
                $accountDimension->code,
                $accountDimension->name
            );
        }
        /**
         * @var QuestionHelper $helper
         */
        $helper = $this->getHelper('question');

        $question = new ChoiceQuestion(
            'Please select account dimension ([company_code] [dim_code] dim_name):',
            $accountDimensionOptions
        );

        $phrase = $helper->ask($this->input, $this->output, $question);

        return array_search($phrase, $accountDimensionOptions);
    }
}
