<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\IdentityProviders\IdentityProviderFacade;
use App\Notifications\WelcomeUserNotification;
use App\User;
use Illuminate\Console\Command;
use Modules\Users\Priv\Services\UserService;

class WelcomeSingleUserCommand extends Command
{
    protected $signature = 'users:welcome-single {user}';

    protected $description = 'Reset users password to random and send email';

    public function handle(
        IdentityProviderFacade $identityProviderFacade,
        UserService $userService
    ) {
        /** @var User $user */
        $user = User::where(['id' => $this->argument('user')])
            ->whereNull('blocked_at')
            ->first();

        $pinCode = $userService->createNewPinCode($user);

        $user->notify(new WelcomeUserNotification($user->instance->domain, $user->email, '[USE PASSWORD RESET]', $user->full_name, $pinCode, $identityProviderFacade->passwordResetUrl($user)), ['mail']);
    }
}
