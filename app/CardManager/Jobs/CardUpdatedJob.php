<?php

declare(strict_types=1);

namespace App\CardManager\Jobs;

use App\CardManager\Events\CardUpdatedEvent;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Mindento\Tracer\Traits\Traceable;

class CardUpdatedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Traceable;

    /**
     * @var int
     */
    protected $userId;

    /**
     * CorporateCardUpdatedJob constructor.
     * @param User $user
     */
    public function __construct(int $userId)
    {
        $this->userId = $userId;
    }

    public function handle()
    {
        event(new CardUpdatedEvent($this->userId));
    }
}