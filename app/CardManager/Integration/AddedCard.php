<?php

declare(strict_types=1);

namespace App\CardManager\Integration;

class AddedCard
{
    private string $url;
    private array $formData;
    private string $status;
    private string $id;
    private ?string $number;

    public function __construct(
        string $url,
        array $formData,
        string $status,
        string $id,
        ?string $number
    ) {
        $this->url = $url;
        $this->formData = $formData;
        $this->status = $status;
        $this->id = $id;
        $this->number = $number;
    }

    public function formData(): array
    {
        return [
            'url' => $this->url,
            'form_data' => $this->formData
        ];
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getNumber(): ?string
    {
        return $this->number;
    }
}
