<?php

namespace App\CardManager\Integration\Requests;

class GetSingleCardRequest extends Request
{
    const URI = 'cards';

    /** @var string */
    protected $slug;

    /**
     * AddCardRequest constructor.
     * @param string $successUrl
     * @param string $errorUrl
     */
    public function __construct(string $slug)
    {
        $this->slug = $slug;
    }

    protected function getURI(): string
    {
        return static::URI . '/' . $this->slug;
    }

    public function sendRequest()
    {
        return $this->client->get($this->getURI());
    }
}
