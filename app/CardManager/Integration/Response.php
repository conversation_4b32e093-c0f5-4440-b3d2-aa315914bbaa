<?php


namespace App\CardManager\Integration;


use App\CardManager\Exceptions\BadResponseStructure;

class Response
{
    protected $success = false;
    protected $errors = null;
    protected $data = null;

    protected const EXPECTED_RESPONSE_KEYS = ['data', 'errors', 'success'];

    public function __construct(\stdClass $cmResponse)
    {
        $this->parse($cmResponse);
    }

    /**
     * @param \stdClass $cmResponse
     * @throws BadResponseStructure
     */
    protected function parse(\stdClass $cmResponse): void
    {
        $this->checkStructure($cmResponse);

        $this->setSuccess($cmResponse->success);
        $this->setData($cmResponse->data);
        $this->setErrors($cmResponse->errors);
    }
    /**
     * @param \stdClass $cmResponse
     * @throws BadResponseStructure
     */
    protected function checkStructure(\stdClass $cmResponse): void
    {
        $missingKeys = array_diff(array_keys((array) $cmResponse), static::EXPECTED_RESPONSE_KEYS);

        if(count($missingKeys)) {
            throw new BadResponseStructure('Missing response keys: '. implode(', ', $missingKeys));
        }
    }

    /**
     * @param bool $success
     */
    public function setSuccess(bool $success): void
    {
        $this->success = $success;
    }

    /**
     * @param null $errors
     */
    public function setErrors($errors): void
    {
        $this->errors = $errors;
    }

    /**
     * @param null $data
     */
    public function setData($data): void
    {
        $this->data = (object) $data;
    }

    /**
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * @return mixed
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * @return \stdClass
     */
    public function getData()
    {
        return $this->data;
    }
}
