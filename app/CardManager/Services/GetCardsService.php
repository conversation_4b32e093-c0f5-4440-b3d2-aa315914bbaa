<?php


namespace App\CardManager\Services;


use App\CardManager\Card\CardCollection;
use App\CardManager\Factories\CardFactory;
use App\CardManager\Integration\Requests\GetCardsByIdsRequest;
use App\Helpers\Client\ClientInterface;
use App\Services\Cache;
use Illuminate\Support\Collection;

class GetCardsService
{
    /** @var ClientInterface */
    protected $client;

    /** @var Collection */
    protected $ids;

    /** @var CardCollection */
    protected $cards;

    /**
     * DeleteCardService constructor.
     * @param ClientInterface $client
     * @param int $ids
     */
    public function __construct(ClientInterface $client, Collection $ids)
    {
        $this->client = $client;
        $this->ids    = $ids;

        $this->cards = new CardCollection();
    }

    public function get(): CardCollection
    {
        if ($this->ids->isEmpty()) {
            return $this->cards;
        }

        $response = Cache::get('cm-list-response-'.$this->ids->implode('-'), function() {
            return (new GetCardsByIdsRequest($this->ids))->setClient($this->client)->send();
        }, 0.5);


        collect($response->getData())->each(function($card) {
           $this->cards->push(CardFactory::fromForm($card));
        });
        return $this->cards;
    }
}
