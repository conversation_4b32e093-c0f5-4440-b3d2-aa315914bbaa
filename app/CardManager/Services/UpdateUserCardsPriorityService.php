<?php


namespace App\CardManager\Services;


use App\CardManager\Card\CardCollection;
use App\CardManager\Card\CardRepository;
use App\User;
use Illuminate\Support\Collection;

class UpdateUserCardsPriorityService
{
    /** @var CardRepository */
    protected $repository;

    /** @var User */
    protected $user;

    /** @var Collection */
    protected $cardsCollection;

    /**
     * UpdateUserCardsPriorityService constructor.
     * @param  CardRepository  $repository
     * @param  Collection  $cardsCollection
     */
    public function __construct(Collection $cardsCollection, CardRepository $repository, User $user)
    {
        $this->cardsCollection = $cardsCollection;
        $this->repository      = $repository;
        $this->user            = $user;
    }

    public function updatePriority(): CardCollection
    {
        $userCards = $this->repository->getAvailableCardsByUser($this->user);
        $slugs = $this->cardsCollection->intersect($userCards->pluck('slug'));

        $this->repository->updatePriority($slugs, $this->user);

        return $this->repository->getAvailableCardsByUser($this->user)->sortByPriority();
    }
}
