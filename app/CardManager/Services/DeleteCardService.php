<?php


namespace App\CardManager\Services;


use App\CardManager\Card\Card;
use App\CardManager\Factories\CardFactory;
use App\CardManager\Integration\Requests\DeleteCardRequest;
use App\Helpers\Client\ClientInterface;
use App\Repositories\CardRepository;

class DeleteCardService
{
    /** @var CardRepository */
    protected $cardRepository;

    /** @var ClientInterface */
    protected $client;

    /** @var \App\Card */
    protected $card;

    /**
     * DeleteCardService constructor.
     * @param CardRepository $cardRepository
     * @param ClientInterface $client
     * @param string $card
     */
    public function __construct(CardRepository $cardRepository, ClientInterface $client, \App\Card $card)
    {
        $this->cardRepository = $cardRepository;
        $this->client         = $client;
        $this->card           = $card;
    }

    public function delete(): Card
    {
        abort_unless(\Auth::user()->can('delete', $this->card), 403, access_denied_err_msg('delete', $this->card));

        $response = (new DeleteCardRequest($this->card->slug))->setClient($this->client)->send();
        $cmCard = CardFactory::fromForm($response->getData());

        if($cmCard->isDeleted()) {
            $this->cardRepository->deleteBySlug($this->card->slug);
        }

        return $cmCard;
    }
}
