<?php declare(strict_types=1);

namespace App\ERP\Instances\Amrest\Documents\Partials;


use App\Document;
use App\ERP\Instances\Amrest\Documents\ValueObjects\CompanyCode;
use App\ERP\Instances\Amrest\Documents\ValueObjects\DocDate;
use App\Request;

class InvoiceHeader
{
    /** @var DocDate */
    public $DocDate;

    /** @var string */
    public $Reference;

    /** @var string */
    public $Currency;

    /** @var CompanyCode */
    public $CompanyCode;

    /**
     * InvoiceHeader constructor.
     *
     * @param DocDate $DocDate
     * @param string $Reference
     * @param string $Currency
     * @param CompanyCode $CompanyCode
     */
    public function __construct(DocDate $DocDate, string $Reference, string $Currency, CompanyCode $CompanyCode)
    {
        $this->DocDate     = $DocDate;
        $this->Reference   = $Reference;
        $this->Currency    = $Currency;
        $this->CompanyCode = $CompanyCode;
    }

    public static function createFromDocument(Document $document, Request $request): InvoiceHeader
    {
        return new InvoiceHeader(
            DocDate::createFromCarbon($document->issue_date),
            (string) $document->document_number,
            $document->currency->code,
            CompanyCode::createFromCompany($request->user->company)
        );
    }

    /**
     * @return DocDate
     */
    public function getDocDate(): DocDate
    {
        return $this->DocDate;
    }

    /**
     * @return string
     */
    public function getReference(): string
    {
        return $this->Reference;
    }

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->Currency;
    }

    /**
     * @return CompanyCode
     */
    public function getCompanyCode(): CompanyCode
    {
        return $this->CompanyCode;
    }

}
