<?php

namespace App\ERP\Instances\Amrest\Services;

use App\ERP\Instances\Amrest\SFTPClient;
use App\ERP\Services\ERPNotificationService;
use App\Instance;
use App\Repositories\DocumentRepository;
use App\Repositories\RequestRepository;
use App\Services\Cache;
use Carbon\Carbon;

class SyncMDSService
{
    public function run(Instance $instance): void
    {
        Cache::get("amrest_sap_dms_sync", function () use ($instance) {
            $configSyncTime        = config('erp.amrest_sap_dms_sync_time');
            $configSyncTimeExplode = explode(':', $configSyncTime);
            $now                   = Carbon::now();
            $syncTime              = Carbon::createFromTime($configSyncTimeExplode[0], $configSyncTimeExplode[1], 0);

            if ($now->greaterThan($syncTime) && $now->lessThan($syncTime->addHour())) {
                $sftpClient = new SFTPClient(
                    (string) config('erp.amrest_sap_dms_host'),
                    (string) config('erp.amrest_sap_dms_user_name'),
                    (string) config('erp.amrest_sap_dms_user_password'),
                    (string) config('erp.amrest_sap_dms_path'),
                    (string) config('erp.amrest_sap_dms_key_path'),
                    (string) config('erp.amrest_sap_dms_phrase_in_name')
                );

                (new UpdateDMSService(
                    $instance,
                    $sftpClient,
                    new RequestRepository(),
                    new DocumentRepository(),
                    resolve(ERPNotificationService::class)
                ))->update();
            }

            return true;
        }, now()->addHour());
    }
}
