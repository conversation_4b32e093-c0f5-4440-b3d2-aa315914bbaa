<?php declare(strict_types=1);

namespace App\ERP\Contracts;


use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Entities\VatNumber;

interface ERPElement
{
    public function isAccountTravelExpense(): bool;

    public function isOwnPaidDocumentElement(): bool;

    public function isServiceCardPaidDocumentElement(): bool;

    public function isPrivateCarLumpSumElement(): bool;

    public function getAccount(): ?AccountingAccount;

    public function getVatNumber(): ?VatNumber;

    public function getMPK(): string;

    public function getAmount(): string;
}
