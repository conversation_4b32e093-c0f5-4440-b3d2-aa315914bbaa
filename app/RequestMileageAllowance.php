<?php

namespace App;

use App\Helpers\NameTranslation;
use App\Interfaces\RequestElementObservableInterface;
use App\Traits\DestinationNameTrait;
use App\Traits\InstanceTrait;
use App\Traits\RequestTrait;
use App\Vendors\Math;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Entities\Traits\AccountingAccountTrait;
use Modules\Accounting\Priv\Entities\Traits\MpkTrait;
use Modules\Analytics\Priv\Entities\RequestMileageAllowanceAccountDimensionItem;
use Modules\Analytics\Priv\Entities\Traits\ProjectTrait;

/**
 * App\RequestMileageAllowance
 *
 * @property int id
 * @property Carbon departure_date
 * @property Carbon arrival_date
 * @property Location departureLocation
 * @property Location destinationLocation
 * @property bool round_trip
 * @property string vehicle_type
 * @property bool cost_of_earning
 * @property float distance
 * @property AccountingAccount accountingAccount
 * @property Collection accountDimensionItems
 * @property int $id
 * @property int $instance_id
 * @property int $request_id
 * @property int|null $accounting_account_id
 * @property int $private_car_trip_id
 * @property int $mpk_id
 * @property \Illuminate\Support\Carbon|null $departure_date
 * @property \Illuminate\Support\Carbon|null $arrival_date
 * @property bool $round_trip
 * @property string $vehicle_type
 * @property bool $cost_of_earning
 * @property string|null $distance
 * @property string $license_plate
 * @property int|null $project_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read AccountingAccount|null $accountingAccount
 * @property-read \App\Location|null $departureLocation
 * @property-read \App\Location|null $destinationLocation
 * @property-read \App\Instance $instance
 * @property-read \Modules\Accounting\Priv\Entities\Mpk $mpk
 * @property-read \Modules\Analytics\Priv\Entities\Project|null $project
 * @property-read \App\Request $request
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance query()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereAccountingAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereArrivalDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereCostOfEarning($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereDepartureDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereLicensePlate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereMpkId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance wherePrivateCarTripId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereProjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereRoundTrip($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestMileageAllowance whereVehicleType($value)
 * @mixin \Eloquent
 */
class RequestMileageAllowance extends Model implements RequestElementObservableInterface
{
    use InstanceTrait;
    use RequestTrait;
    use MpkTrait;
    use AccountingAccountTrait;
    use DestinationNameTrait;
    use ProjectTrait;

    const RELATION_NAME = 'mileage_allowance';

    protected $fillable = [
        'instance_id',
        'request_id',
        'mpk_id',
        'accounting_account_id',
        'departure_date',
        'arrival_date',
        'round_trip',
        'vehicle_type',
        'cost_of_earning',
        'distance',
        'private_car_trip_id',
        'license_plate',
        'project_id',
    ];

    protected $casts = [
        'round_trip' => 'boolean',
        'cost_of_earning' => 'boolean',
    ];

    protected $dates = [
        'departure_date',
        'arrival_date'
    ];

    protected function accountDimensionItems()
    {
        return $this->hasMany(RequestMileageAllowanceAccountDimensionItem::class);
    }

    public function departureLocation()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'departure_from']);
    }


    public function destinationLocation()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'arrival_to']);
    }

    public function getName($separator = ' → '): ?string
    {
        return $this->getNameTranslation($separator)->translate();
    }

    public function getNameTranslation($separator = ' → '): NameTranslation
    {
        $from = $this->departureLocation->city ?? null;
        $to = $this->destinationLocation->city ?? null;

        return new NameTranslation('request-summary.private-car-trip', ['details' => $this->getFromToString($from, $to, $separator)]);
    }

    public function amount(): string
    {
        return Math::multiply($this->distance, $this->multiplyRate());
    }

    public function multiplyRate()
    {
        return config('vaterval.privateCarAccounting.' . $this->vehicle_type);
    }

    public function getVehicleTypeTrans(): string
    {
        switch ($this->vehicle_type) {
            case PrivateCarTrip::VEHICLE_TYPE_LOW_CAPACITY_ENGINE:
                return trans('request-element.nether-engine-capacity', ['capacity' => '900cm<sup>3</sup>']);
            case PrivateCarTrip::VEHICLE_TYPE_UPPER_CAPACITY_ENGINE;
                return trans('request-element.upper-engine-capacity', ['capacity' => '900cm<sup>3</sup>']);
            case PrivateCarTrip::VEHICLE_TYPE_MOPED;
                return trans('request-element.moped');
            case PrivateCarTrip::VEHICLE_TYPE_MOTORCYCLE;
                return trans('request-element.motorcycle');
            default:
                return '';
        }
    }
}
