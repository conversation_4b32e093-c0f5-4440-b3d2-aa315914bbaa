<?php

namespace App;

use App\ElasticSearchConfigurators\TrainTripIndexConfigurator;
use App\Helpers\NameTranslation;
use App\Interfaces\Models\LocalizableTripElementInterface;
use App\Interfaces\RequestElementAcceptanceSourceInterface;
use App\Interfaces\RequestElementDateableInterface;
use App\Interfaces\RequestElementDocumentableInterface;
use App\Interfaces\RequestElementNameTranslatedInterface;
use App\Interfaces\RequestElementObservableInterface;
use App\Interfaces\RequestElementSearchableInterface;
use App\Repositories\LocationRepository;
use App\Traits\InstanceTrait;
use App\Traits\Models\AccountedAmount;
use App\Traits\Models\ConvertedAmount;
use App\Traits\Models\OriginalAmountsHelper;
use App\Traits\Models\RequestElementAcceptanceSourceTrait;
use App\Traits\PolymorphicDocumentElementsTrait;
use App\Traits\DestinationNameTrait;
use App\Traits\PolymorphicOfferTrait;
use App\Traits\RequestTrait;
use App\Traits\SuggestedElementTypeTrait;
use App\Vendors\Math;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\TripPlanner\Pub\Enums\RequestElementAcceptanceSourceEnum;
use ScoutElastic\Searchable;


/**
 * App\TrainTrip
 *
 * @property Currency amountCurrency
 * @property mixed amount
 * @property boolean searcher_disabled
 * @property integer service_class
 * @property string search_uuid
 * @property Location departureLocation
 * @property Location destinationLocation
 * @property string uuid
 * @property RequestElementAcceptanceSourceEnum request_element_acceptance_source
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int $instance_id
 * @property int $request_id
 * @property string $uuid
 * @property RequestElementAcceptanceSourceEnum $request_element_acceptance_source
 * @property int $type_id
 * @property \Illuminate\Support\Carbon $departure_at
 * @property \Illuminate\Support\Carbon|null $arrival_at
 * @property \Illuminate\Support\Carbon|null $return_at
 * @property int $round_trip
 * @property bool $searcher_disabled
 * @property int|null $service_class
 * @property string|null $amount
 * @property int|null $amount_currency_id
 * @property int $weight
 * @property int $return_weight
 * @property string $search_uuid
 * @property-read \App\Currency|null $amountCurrency
 * @property-read \App\Location|null $departureLocation
 * @property-read \App\Location|null $destinationLocation
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\DocumentElement[] $documentElements
 * @property-read int|null $document_elements_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Document[] $documents
 * @property-read int|null $documents_count
 * @property-read \App\Instance $instance
 * @property-read \App\Offer|null $offer
 * @property-read \App\Request $request
 * @property-read \Modules\Accounting\Priv\Entities\DocumentElementType|null $suggestedElementType
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip newQuery()
 * @method static \Illuminate\Database\Query\Builder|TrainTrip onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereAmountCurrencyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereArrivalAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereDepartureAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereRequestElementAcceptanceSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereReturnAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereReturnWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereRoundTrip($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereSearchUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereSearcherDisabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereServiceClass($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereWeight($value)
 * @method static \Illuminate\Database\Query\Builder|TrainTrip withTrashed()
 * @method static \Illuminate\Database\Query\Builder|TrainTrip withoutTrashed()
 * @mixin \Eloquent
 * @property int $is_accepted
 * @method static \Illuminate\Database\Eloquent\Builder|TrainTrip whereIsAccepted($value)
 */
class TrainTrip extends AbstractRequestElement implements
    RequestElementObservableInterface,
    RequestElementDocumentableInterface,
    RequestElementDateableInterface,
    RequestElementSearchableInterface,
    RequestElementNameTranslatedInterface,
    LocalizableTripElementInterface,
    RequestElementAcceptanceSourceInterface
{
    use SoftDeletes;
    use RequestTrait;
    use InstanceTrait;
    use ConvertedAmount;
    use OriginalAmountsHelper;
	use AccountedAmount;
    use SuggestedElementTypeTrait;
    use PolymorphicDocumentElementsTrait;
    use DestinationNameTrait;
    use PolymorphicOfferTrait;
    use RequestElementAcceptanceSourceTrait;

    const RELATION_NAME = 'train_trip';

    protected $table = 'request_train_trips';

    protected $touches = ['request'];

    protected $fillable = [
        'uuid',
        'request_id',
        'instance_id',
        'departure_at',
        'return_at',
        'round_trip',
        'amount',
        'amount_currency_id',
	    'weight',
        'searcher_disabled',
        'service_class',
        'search_uuid'
    ];

    protected $casts = [
        'searcher_disabled' => 'boolean',
    ];

    protected $dates = [
        'departure_at',
        'return_at',
        'arrival_at'
    ];

    public function toSearchableArray()
    {
        $arr = array_only($this->toArray(), [
            'id',
            'arrival_at',
            'return_at',
            'amount',
        ]);
        if($this->departureLocation) {
            $arr['locations'][] = $this->departureLocation->toSearchableArray();
        }
        if($this->destinationLocation) {
            $arr['locations'][] = $this->destinationLocation->toSearchableArray();
        }

        return $arr;
    }

    public function documents()
    {
        return $this->morphToMany(Document::class, 'element', 'request_element_document');
    }

    public function amountCurrency()
    {
        return $this->hasOne(Currency::class, 'id', 'amount_currency_id');
    }

    public function departureLocation()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'departure_location']);
    }

    public function destinationLocation()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'destination_location']);
    }


    public function getStartDate()
    {
        return $this->departure_at;
    }

    public function getReturnStartDate()
    {
        return $this->return_at;
    }

    public function setStartDate($value)
    {
        $this->departure_at = $value;

        return $this;
    }

    public function setEndDate($value)
    {
        $this->arrival_at = $value;

        return $this;
    }

    public function getEndDate()
    {
        return $this->arrival_at;
    }

    public function getOriginalAmounts(): array
    {
        return $this->addOriginalItem([], $this->amountCurrency, $this->amount);
    }

    public function getAmountAttribute()
    {
        return Math::round($this->attributes['amount'], 2);
    }

    public function getName($separator = ' → '): ?string
    {
        return $this->getNameTranslation($separator)->translate();
    }

    public function getNameTranslation($separator = ' → '): NameTranslation
    {
        return new NameTranslation('request-summary.train-trip', ['details' => $this->getShortName($separator)]);
    }

    public function getShortName($separator = ' → ')
    {
        $from = $this->departureLocation->city ?? null;
        $to = $this->destinationLocation->city ?? null;

        return  $this->getFromToString($from, $to, $separator);
    }

    public function getType(): ?string
    {
        return static::RELATION_NAME;
    }

    public function getSearcherDisabledAttribute()
    {
        return $this->attributes['searcher_disabled'];
    }

    public function setSearcherDisabledAttribute($value)
    {
        $this->attributes['searcher_disabled'] = $value;
    }

    public function getNameTranslated(?string $lang = null): string
    {
        return trans('request-summary.train-trip', ['details' => $this->getShortName()], $lang ? $lang : $this->request->user->locale);
    }

    public function isNational(): bool
    {
        $instanceCountryId = $this->instance->country_id;
        $startLocationIsNational = LocationRepository::getCountryFromLocation($this->departureLocation)->id === $instanceCountryId;
        $endLocationIsNational   = LocationRepository::getCountryFromLocation($this->destinationLocation)->id === $instanceCountryId;

        return $startLocationIsNational && $endLocationIsNational;
    }

    public function getStartDateAttributeName(): string
    {
        return 'departure_at';
    }

    public function getEndDateAttributeName(): string
    {
        return 'return_at';
    }
}
