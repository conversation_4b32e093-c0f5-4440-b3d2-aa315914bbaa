<?php

declare(strict_types=1);

namespace App\BillingAPI\DTOs;

use App\User;
use Illuminate\Contracts\Support\Arrayable;
use Modules\TripPlanner\Priv\Entities\RequestTraveler;

class TrainPassengerDTO implements Arrayable
{
    protected RequestTraveler $requestTraveler;

    public function __construct(RequestTraveler $requestTraveler)
    {
        $this->requestTraveler = $requestTraveler;
    }

    public function getUserId(): int
    {
        return $this->requestTraveler->requestTraveler->id;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'traveler_slug' => $this->requestTraveler->slug,
            'user_slug' => $this->requestTraveler->requestTraveler->slug,
            'phone' => $this->requestTraveler->requestTraveler->phone
        ];
    }

    public static function fromRequestTraveler(RequestTraveler $traveler): TrainPassengerDTO
    {
        return new TrainPassengerDTO($traveler);
    }
}