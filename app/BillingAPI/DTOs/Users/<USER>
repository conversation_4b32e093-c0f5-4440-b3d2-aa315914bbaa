<?php

declare(strict_types=1);

namespace App\BillingAPI\DTOs\Users;

use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;

abstract class AbstractUserDto implements Arrayable
{
    /** @var string */
    protected $slug;

    /** @var string|null */
    protected $erpId;

    /** @var int */
    protected $instanceId;

    /** @var string */
    protected $domain;

    /** @var string */
    protected $subdomain;

    /** @var int */
    protected $companyId;

    /** @var string|null */
    protected $avatar;

    /** @var string */
    protected $email;

    /** @var string */
    protected $firstName;

    /** @var string */
    protected $lastName;

    /** @var string|null */
    protected $passportNumber;

    /** @var Carbon|null */
    protected $passportIssueDate;

    /** @var Carbon|null */
    protected $passportValidDate;

    /** @var Carbon|null */
    protected $birthDate;

    /** @var string|null */
    protected $phone;

    /** @var string|null */
    protected $sex;

    /** @var array */
    protected $groups;

    /** @var string|null */
    protected $citizenship;

    /** @var string|null */
    protected $level;

    /** @var string|null */
    protected $policy;

    /** @var Carbon|null */
    protected $blockedAt;

    /** @var string|null */
    protected $mpkCode;

    /** @var string|null */
    protected $nationality;

    protected ?bool $internal;

    public function __construct(
        string $slug,
        ?string $erpId,
        int $instanceId,
        string $domain,
        string $subdomain,
        int $companyId,
        ?string $avatar,
        string $email,
        string $firstName,
        string $lastName,
        ?string $passportNumber,
        ?Carbon $passportIssueDate,
        ?Carbon $passportValidDate,
        ?Carbon $birthDate,
        ?string $phone,
        ?string $sex,
        array $groups,
        ?string $citizenship,
        ?string $level,
        ?string $policy,
        ?Carbon $blockedAt,
        ?string $mpkCode,
        ?string $nationality,
        ?bool $internal
    ) {
        $this->slug = $slug;
        $this->erpId = $erpId;
        $this->instanceId = $instanceId;
        $this->domain = $domain;
        $this->subdomain = $subdomain;
        $this->companyId = $companyId;
        $this->avatar = $avatar;
        $this->email = $email;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->passportNumber = $passportNumber;
        $this->passportIssueDate = $passportIssueDate;
        $this->passportValidDate = $passportValidDate;
        $this->birthDate = $birthDate;
        $this->phone = $phone;
        $this->sex = $sex;
        $this->groups = $groups;
        $this->citizenship = $citizenship;
        $this->level = $level;
        $this->policy = $policy;
        $this->blockedAt = $blockedAt;
        $this->mpkCode = $mpkCode;
        $this->nationality = $nationality;
        $this->internal = $internal;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getErpId(): ?string
    {
        return $this->erpId;
    }

    public function getInstanceId(): int
    {
        return $this->instanceId;
    }

    public function getDomain(): string
    {
        return $this->domain;
    }

    public function getSubdomain(): string
    {
        return $this->subdomain;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function getPassportNumber(): ?string
    {
        return $this->passportNumber;
    }

    public function getPassportIssueDate(): ?Carbon
    {
        return $this->passportIssueDate;
    }

    public function getPassportValidDate(): ?Carbon
    {
        return $this->passportValidDate;
    }

    public function getBirthDate(): ?Carbon
    {
        return $this->birthDate;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getSex(): ?string
    {
        return $this->sex;
    }

    public function getGroups(): array
    {
        return $this->groups;
    }

    public function getCitizenship(): ?string
    {
        return $this->citizenship;
    }

    public function getLevel(): ?string
    {
        return $this->level;
    }

    public function getPolicy(): ?string
    {
        return $this->policy;
    }

    public function getBlockedAt(): ?Carbon
    {
        return $this->blockedAt;
    }

    public function getMpkCode(): ?string
    {
        return $this->mpkCode;
    }

    public function getNationality(): ?string
    {
        return $this->nationality;
    }

    public function toArray()
    {
        return [
            'slug'                  => $this->getSlug(),
            'erpId'                 => $this->getErpId(),
            'instanceId'            => $this->getInstanceId(),
            'domain'                => $this->getDomain(),
            'subdomain'             => $this->getSubdomain(),
            'companyId'             => $this->getCompanyId(),
            'avatar'                => $this->getAvatar(),
            'email'                 => $this->getEmail(),
            'firstName'             => $this->getFirstName(),
            'lastName'              => $this->getLastName(),
            'passportNumber'        => $this->getPassportNumber(),
            'passportIssueDate'     => $this->getPassportIssueDate() instanceof Carbon ? $this->getPassportIssueDate()->toDateString() : null,
            'passportValidDate'     => $this->getPassportValidDate() instanceof Carbon ? $this->getPassportValidDate()->toDateString() : null,
            'birthDate'             => $this->getBirthDate() instanceof Carbon ? $this->getBirthDate()->toDateString() : null,
            'phone'                 => $this->getPhone(),
            'sex'                   => $this->getSex(),
            'groups'                => $this->getGroups(),
            'citizenship'           => $this->getCitizenship(),
            'level'                 => $this->getLevel(),
            'policy'                => $this->getPolicy(),
            'blockedAt'             => $this->getBlockedAt() instanceof Carbon ? $this->getBlockedAt()->toDateString() : null,
            'mpkCode'               => $this->getMpkCode(),
            'nationality'           => $this->getNationality(),
            'internal'              => $this->internal,
        ];
    }
}