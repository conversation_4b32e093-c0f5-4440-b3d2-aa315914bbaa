<?php

declare(strict_types=1);

namespace App\BillingAPI\DTOs\Notification;

use App\Enum\EmailNotificationModeEnum;
use App\BillingAPI\ValueObjects\EmailNotificationMode;
use App\BillingAPI\ValueObjects\Email;
use App\Instance;

class EmailNotificationSettingsDto
{
    /** @var int */
    protected $instanceId;

    /** @var string */
    protected $domain;

    /** @var EmailNotificationMode */
    protected $mode;

    /** @var Email|null */
    protected $email;

    public function __construct(int $instanceId, string $domain, string $mode, ?string $email = null)
    {
        $this->instanceId = $instanceId;
        $this->domain = $domain;

        if ((empty($email) === false && is_string($email)) || $mode === (string)EmailNotificationModeEnum::FORWARDING()) {
            $this->email = new Email($email);
        }

        $this->mode = new EmailNotificationMode($mode);
    }

    public static function createFromSettings(Instance $instance)
    {
        return new self(
            $instance->id,
            $instance->domain,
            isset($instance->email_notifications_settings['mode']) === true
                ? $instance->email_notifications_settings['mode']
                : (string)EmailNotificationModeEnum::ENABLED(),
            isset($instance->email_notifications_settings['email']) === true
                ? $instance->email_notifications_settings['email']
                : null
        );
    }

    public function getMode(): EmailNotificationMode
    {
        return $this->mode;
    }

    public function getEmail(): ?Email
    {
        return $this->email;
    }

    public function getInstanceId(): int
    {
        return $this->instanceId;
    }

    public function getDomain(): string
    {
        return $this->domain;
    }

    public function toArray(): array
    {
        return  [
            'mode'      => (string)$this->getMode(),
            'email'     => (string)$this->getEmail()
        ];
    }
}
