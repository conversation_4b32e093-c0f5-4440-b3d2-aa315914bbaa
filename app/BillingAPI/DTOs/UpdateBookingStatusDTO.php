<?php


namespace App\BillingAPI\DTOs;


use Carbon\Carbon;

class UpdateBookingStatusDTO
{
    /** @var string */
    protected $searchUuid;

    /** @var string */
    protected $status;

    /** @var array */
    protected $reservationAttributes;

    /** @var array */
    protected $remarks;

    /** @var string */
    protected $reservationMessage;

    /** @var string|null */
    protected $elementType;

    /** @var string|null */
    protected $errorType;

    /** @var bool */
    protected $cancellable;

    /** @var Carbon|null */
    protected $cancelDate;

    protected string $reservationUuid;

    protected string $ticketNumber;

    protected ?string $bicIsoCode;
    protected ?string $paymentStatus;

    /**
     * UpdateBookingStatusDTO constructor.
     * @param string $searchUuid
     * @param string $status
     * @param array $reservationAttributes
     * @param array $remarks
     * @param string $reservationMessage
     * @param string|null $elementType
     * @param string|null $errorType
     * @param string $reservationUuid
     * @param string $ticketNumber
     */
    public function __construct(
        string $searchUuid,
        string $status,
        array $reservationAttributes,
        array $remarks,
        string $reservationMessage,
        ?string $elementType,
        ?string $errorType,
        string $reservationUuid,
        string $ticketNumber,
        ?string $paymentStatus = null,
        ?string $bicIsoCode = null,
        ?bool $cancellable = false,
        ?Carbon $cancelDate = null
    ) {
        $this->searchUuid = $searchUuid;
        $this->status = $status;
        $this->reservationAttributes = $reservationAttributes;
        $this->remarks = $remarks;
        $this->reservationMessage = $reservationMessage;
        $this->elementType = $elementType;
        $this->errorType = $errorType;
        $this->reservationUuid = $reservationUuid;
        $this->ticketNumber = $ticketNumber;
        $this->cancellable = $cancellable;
        $this->cancelDate = $cancelDate;
        $this->paymentStatus = $paymentStatus;
        $this->bicIsoCode = $bicIsoCode;
    }


    public static function fromRequest(array $data): UpdateBookingStatusDTO
    {
        return new static(
            $data['searchUuid'],
            $data['status'],
            data_get($data, 'reservation.attributes.bookedAttributes', []),
            data_get($data, 'reservation.attributes.remarks', []),
            $data['result_message'] ?? '',
            $data['mindento_element_type'] ?? '',
            $data['error_type'] ?? '',
            $data['uuid'] ?? '',
            $data['ticket_number'] ?? '',
            empty($data['payment']) === false && empty($data['payment']['status']) === false ? $data['payment']['status'] : null,
            empty($data['payment']) === false && empty($data['payment']['bic_iso_code']) === false ? $data['payment']['bic_iso_code'] : null,
            $data['cancellable'] ?? '',
            isset($data['cancel_date']) && is_string($data['cancel_date'])
            && preg_match('/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/', $data['cancel_date'])
                ? Carbon::createFromFormat('Y-m-d H:i:s', $data['cancel_date'])
                : null
        );
    }

    /**
     * @return string
     */
    public function getSearchUuid(): string
    {
        return $this->searchUuid;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @return array
     */
    public function getReservationAttributes(): array
    {
        return $this->reservationAttributes;
    }

    /**
     * @return array
     */
    public function getRemarks(): array
    {
        return $this->remarks;
    }

    /**
     * @return string
     */
    public function getReservationMessage(): string
    {
        return $this->reservationMessage;
    }

    /**
     * @return string|null
     */
    public function getElementType(): ?string
    {
        return $this->elementType;
    }
    /**
     * @return string|null
     */
    public function getErrorType(): ?string
    {
        return $this->errorType;
    }

    public function getReservationUuid(): string
    {
        return $this->reservationUuid;
    }

    public function getTicketNumber(): string
    {
        return $this->ticketNumber;
    }

    /**
     * @return bool
     */
    public function isCancellable(): ?bool
    {
        return $this->cancellable;
    }

    /**
     * @return Carbon|null
     */
    public function getCancelDate(): ?Carbon
    {
        return $this->cancelDate;
    }

    public function getBicIsoCode(): ?string
    {
        return $this->bicIsoCode;
    }

    public function getPaymentStatus(): ?string
    {
        return $this->paymentStatus;
    }
}
