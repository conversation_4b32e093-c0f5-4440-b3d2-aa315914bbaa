<?php

declare(strict_types=1);

namespace App\BillingAPI\DTOs;

use App\BillingAPI\Client\Services\Flights\FlightClassMapper;
use App\Helpers\TimeRange;
use Illuminate\Foundation\Http\FormRequest;

class ResearchFlightSearchDTO extends FlightFiltersAndSortingDTO
{
    /** @var bool */
    protected $restrictToFba;

    /** @var bool */
    protected $directOnly;

    /** @var string */
    protected $flightClass;

    /**
     * @inheritDoc
     */
    public function __construct(
        ?string $sort,
        ?TimeRange $targetDepartureTime,
        ?TimeRange $returnDepartureTime,
        bool $restrictToFba,
        bool $directOnly,
        string $flightClass
    ) {
        $this->restrictToFba = $restrictToFba;
        $this->directOnly = $directOnly;
        $this->flightClass = $flightClass;
        parent::__construct($sort, $targetDepartureTime, $returnDepartureTime);
    }


    public static function fromRequest(FormRequest $request)
    {
        return new static(
            $request->sort,
            static::timeRangeFromArray($request->target_departure_time),
            static::timeRangeFromArray($request->return_departure_time),
            $request->restrict_to_fba == 1 ? true : false,
            $request->direct_only == 1 ? true : false,
            $request->flight_class
        );
    }

    /**
     * @return string
     */
    public function getFlightClass(): string
    {
        return FlightClassMapper::fromMindentoToBilling($this->flightClass);
    }

    public function toArray()
    {
        return array_merge(
            parent::toArray(),
            [
                'restrict_to_fba' => $this->restrictToFba,
                'direct_availability' => $this->directOnly,
                'class_of_service' => $this->getFlightClass()
            ]
        );
    }
}