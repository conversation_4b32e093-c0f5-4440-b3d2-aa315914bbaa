<?php

declare(strict_types=1);

namespace App\BillingAPI\Responses;

use App\Http\Responses\Response2;
use Modules\Analytics\Priv\Entities\RequestAccountDimensionItem;

class RequestAccountDimensionItemResponse extends Response2
{
    protected function transform(RequestAccountDimensionItem $requestAccountDimensionItem): array
    {
        return [
            'account_dimension' => AccountDimensionResponse::item($requestAccountDimensionItem->accountDimension)->getData(),
            'account_dimension_item' => AccountDimensionItemResponse::item($requestAccountDimensionItem->accountDimensionItem)->getData()
        ];
    }
}