<?php

declare(strict_types=1);

namespace App\BillingAPI\Responses;

use App\Accomodation;
use App\Http\Responses\Response2;
use Carbon\Carbon;

class AccommodationSummaryResponse extends Response2
{
    protected function transform(Accomodation $accomodation)
    {
        $offer = $accomodation->offer;
        $fullOffer = $offer->full_offer;

        return [
            'reservation_id' => $offer->reservation_uuid,
            'type' => $accomodation->getType(),
            'name' => $fullOffer[0]['attributes']['name'],
            'address' => $fullOffer[0]['attributes']['address'],
            'status' => $offer->status,
            'arrival_at' => Carbon::createFromFormat('Y-m-d H:i:s', $offer->search_params['date_from'])->format('Y-m-d'),
            'departure_at' => Carbon::createFromFormat('Y-m-d H:i:s', $offer->search_params['date_to'])->format('Y-m-d'),
            'cancelation_deadline_at' => $offer->isCancelable() ? $offer->getCancelDate()->toIso8601String() : null,
            'ticket_number' => $offer->ticket_number,
            'locations' => [
                LocationSummaryResponse::item($accomodation->location)->getData()
            ]
        ];
    }
}