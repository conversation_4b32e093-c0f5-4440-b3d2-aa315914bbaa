<?php

declare(strict_types=1);

namespace App\BillingAPI\Responses;

use App\Http\Responses\Response2;
use App\Location;

class LocationSummaryResponse extends Response2
{
    protected function transform(Location $location)
    {
        return [
            'type' => $location->column,
            'country' => $location->country,
            'country_code' => $location->country_code,
            'city' => $location->city,
            'province' => $location->province,
            'address' => $location->address,
            'long' => $location->long,
            'lat' => $location->lat,
            'name' => $location->name,
            'formatted_address' => $location->formatted_address,
        ];
    }
}