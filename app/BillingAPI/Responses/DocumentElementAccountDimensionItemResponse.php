<?php

declare(strict_types=1);

namespace App\BillingAPI\Responses;

use App\Http\Responses\Response2;
use Modules\Analytics\Priv\Entities\DocumentElementAccountDimensionItem;

class DocumentElementAccountDimensionItemResponse extends Response2
{
    protected function transform(DocumentElementAccountDimensionItem $documentElementAccountDimensionItem): array
    {
        return [
            'account_dimension' => AccountDimensionResponse::item($documentElementAccountDimensionItem->accountDimension)->getData(),
            'account_dimension_item' => AccountDimensionItemResponse::item($documentElementAccountDimensionItem->accountDimensionItem)->getData()
        ];
    }
}