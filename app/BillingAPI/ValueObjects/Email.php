<?php

declare(strict_types=1);

namespace App\BillingAPI\ValueObjects;

use App\Exceptions\InvalidEmailException;

class Email
{
    /** @var string */
    protected $value;

    public function __construct(string $value)
    {
        if ($this->isValid($value) === false) {
            throw InvalidEmailException::create();
        }

        $this->value = $value;
    }

    public function __toString()
    {
        return $this->value;
    }

    protected function isValid(string $value): bool
    {
        return (bool)filter_var($value, FILTER_VALIDATE_EMAIL);
    }
}
