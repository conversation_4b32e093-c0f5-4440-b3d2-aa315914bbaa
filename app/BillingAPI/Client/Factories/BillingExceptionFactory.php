<?php

namespace App\BillingAPI\Client\Factories;

use App\BillingAPI\Client\ExceptionHandler\ErrorCodesInfo;
use App\BillingAPI\Client\Exceptions\InvoiceNotFoundException;
use App\BillingAPI\Client\Exceptions\NoObtFoundException;
use App\BillingAPI\Client\Exceptions\NotAvailableReservationServicePKPException;
use App\BillingAPI\Client\Exceptions\OfferExpiredException;
use App\BillingAPI\Client\Exceptions\ReservationAlreadyExistsException;
use App\BillingAPI\Client\Exceptions\UndefinedBillingException;
use App\Exceptions\PaymentLimitExceededException;

class BillingExceptionFactory
{
    /** @var int */
    private $errorCode;

    /** @var array  */
    private $parameters;

    /**
     * BillingExceptionFactory constructor.
     *
     * @param  int    $errorCode
     * @param  array  $parameters
     */
    public function __construct(int $errorCode, array $parameters = [])
    {
        $this->errorCode = $errorCode;
        $this->parameters = $parameters;
    }

    public function getException(): \Exception
    {
        $exception = new UndefinedBillingException();

        switch ($this->errorCode) {
            case ErrorCodesInfo::NO_OBT_FOUND:
                $exception = new NoObtFoundException();
                break;
            case ErrorCodesInfo::OFFER_EXPIRED:
                $exception = new OfferExpiredException();
                break;
            case ErrorCodesInfo::INVOICE_NOT_FOUND:
                $exception = new InvoiceNotFoundException();
                break;
            case ErrorCodesInfo::NOT_AVAILABLE_RESERVATION_SERVICE_PKP:
                $exception = new NotAvailableReservationServicePKPException($this->parameters);
                break;
            case ErrorCodesInfo::RESERVATION_ALREADY_EXISTS:
                $exception = new ReservationAlreadyExistsException();
                break;
            case ErrorCodesInfo::PAYMENT_LIMIT_EXCEEDED:
                $exception = new PaymentLimitExceededException();
                break;
        }

        return $exception;
    }
}
