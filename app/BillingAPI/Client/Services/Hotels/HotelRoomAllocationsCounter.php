<?php

declare(strict_types=1);

namespace App\BillingAPI\Client\Services\Hotels;

use InvalidArgumentException;

class HotelRoomAllocationsCounter
{
    private int $numberOfRooms = 0;
    private int $numberOfGuests = 0;

    public function __construct(array $roomAllocations)
    {
        foreach ($roomAllocations as $roomAllocation) {
            if (!property_exists($roomAllocation, 'guests') || !is_array($roomAllocation->guests)) {
                throw new InvalidArgumentException("The required property 'guests' is missing from the object or is not an array.");
            }

            $this->numberOfGuests += count($roomAllocation->guests);
        }
        $this->numberOfRooms = count($roomAllocations);
    }

    public function getCountedRooms(): int
    {
        return $this->numberOfRooms;
    }

    public function getCountedGuests(): int
    {
        return $this->numberOfGuests;
    }
}
