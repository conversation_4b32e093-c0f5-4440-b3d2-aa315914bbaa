<?php


namespace App\BillingAPI\Client\Services\VocabularySearcher;


use App\Permission;
use App\Services\Cache;
use Illuminate\Support\Collection;

class VocabularySearcher
{
    const MIN_SEARCH_PHRASE_LENGTH = 3;

    const RESULTS_LIMIT = 10;

    /** @var Collection  */
    protected $locations;

    /** @var string  */
    protected $cacheKeyBase;

    public function __construct(Collection $locations, string $cacheKeyBase)
    {
        $this->locations = $locations;
        $this->cacheKeyBase = $cacheKeyBase;
    }

    /**
     * @param null $searchPhrase
     * @param bool $smart Defines if algorithm should look for phrase shortened til it finds some results
     * i.e for "phrase=Warszawa, Polska" it will look for "Warszawa, Polska", then "Warszawa, Polsk", then "Warszawa Pols" and so on,
     * if no results found alogrithm will stop on phrase length equal to MIN_SEARCH_PHRASE_LENGTH
     *
     * @return Collection
     */
    public function search($searchPhrase = null, bool $smart = false): Collection
    {
        return $this->parse($searchPhrase, $smart);
    }

    protected function unique(Collection $locations)
    {
        return $locations->unique(function(Location $location) {
           return $location->getFormattedAddress();
        });
    }

    /**
     * Filter, sort then transform collection to response
     * if search phrase is shorter than MIN_SEARCH_PHRASE_LENGTH return first RESULTS_LIMIT of input collection
     * @param $locations
     * @param bool $smart {@see static::import()}
     * @param null $searchPhrase
     *
     * @return Collection
     */
    protected function parse($searchPhrase=null, bool $smart = false): Collection
    {
        if(!$searchPhrase || strlen($searchPhrase) < static::MIN_SEARCH_PHRASE_LENGTH) {
            return $this->toResponse($this->locations);
        }

        $phrase = strtolower($searchPhrase);

        return Cache::forever(
            $this->generateCacheKey($phrase, $smart), function() use (&$phrase, $smart) {
                return $this->toResponse(
                    $this->sort($this->filter($this->locations, $phrase, $smart), $phrase)
            );
        });
    }

    /**
     * @param $phrase
     * @param $smart
     * @return string
     */
    protected function generateCacheKey($phrase, $smart): string
    {
        return $this->cacheKeyBase. '_'.str_slug($phrase) . ($smart ? '_smart' : '');
    }

    /**
     * Filter locations by search phrase - look if formatted address (name, code) starts with given string
     * @param $locations
     * @param $phrase string Search phrase
     * @param bool $smart
     * @return mixed
     */
    protected function filter($locations, string $phrase, bool $smart)
    {
        if(!$smart) {
            $results = $this->filterByPhrase($locations, $phrase);

            if ($results->isNotEmpty()) {
                return $this->filterByPhrase($locations, $phrase);
            }
        }

        $found = collect();
        while (strlen($phrase) > static::MIN_SEARCH_PHRASE_LENGTH && $found->isEmpty()) {
            $found = $this->filterByPhrase($locations, $phrase);

            $phrase = substr($phrase, 0, -1);
        }

        return $found;
    }

    /**
     * @param $locations
     * @param string $phrase
     * @return Collection
     */
    protected function filterByPhrase($locations, string $phrase)
    {
        return $locations->filter(function(Location $location) use (&$phrase) {
            $phrase = remove_accents($phrase);
            $searchText = remove_accents($location->getFormattedAddress() . ' '. $location->city);

            return str_contains(strtolower($searchText), strtolower($phrase)) && $location->type !== Location::TYPE_CITY;
        });
    }

    /**
     * Sort locations by text similarity with search phrase
     * @param $locations
     * @param $phrase
     * @return mixed
     */
    protected function sort($locations, $phrase)
    {
        return $locations
            ->sort(function(Location $a,Location $b) use (&$phrase) {
                $firstSimilarityPercentage = 0;
                $secondSimilarityPercentage = 0;

                $aName = explode(' ', $a->name)[0];
                $bName = explode(' ', $b->name)[0];

		        similar_text(strtolower($aName), $phrase, $firstSimilarityPercentage);
		        similar_text(strtolower($bName), $phrase, $secondSimilarityPercentage);

		        $firstStartsWith = starts_with(strtolower($aName), $phrase);
		        $secondStartsWith = starts_with(strtolower($bName), $phrase);

                if($firstStartsWith && $secondStartsWith) {
                    return $secondSimilarityPercentage <=> $firstSimilarityPercentage;
                }

                if($firstStartsWith) {
                    return -1;
                } else if($secondStartsWith) {
                    return 1;
                } else {
                    return $secondSimilarityPercentage <=> $firstSimilarityPercentage;
                }

            })
            ->sort(function (Location $a, Location $b) {
                if (is_numeric($a->priority) === false) {
                    return -1;
                } else if ($a->priority < $b->priority) {
                    return 1;
                } else {
                    return -1;
                }
            })
            ->values();
    }

    /**
     * Take RESULTS_LIMIT from locations then map collection to response array
     * @param $locations
     * @return mixed
     */
    protected function toResponse($locations)
    {
        $locations = $locations->values();

        return $this->groupByCity($locations->map(function(Location $location) {
            return $location->toResponseData();
        }));
    }

    protected function groupByCity($locations): Collection
    {
        $grouped = collect();

        $locations->groupBy('city')->each(function (Collection $locations, string $city) use (&$grouped) {
            if ($this->issetPriority($locations)) {
                $grouped->prepend([
                    'city' => $city,
                    'stations' => $this->issetPriority($locations)
                        ? $locations
                        : $locations->sortBy('name')->values()
                ]);
            } else {
                $grouped->push([
                    'city' => $city,
                    'stations' => $this->issetPriority($locations)
                        ? $locations
                        : $locations->sortBy('name')->values()
                ]);
            }
        });

        return $grouped;
    }

    protected function issetPriority($locations): bool
    {
        if (isset($locations[0]['additional_data']['priority'])
            && $locations[0]['additional_data']['priority'] != null
        ) {
            return true;
        }

        return false;
    }
}
