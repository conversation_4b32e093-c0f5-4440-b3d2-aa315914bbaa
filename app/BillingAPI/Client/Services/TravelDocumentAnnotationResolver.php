<?php


namespace App\BillingAPI\Client\Services;


use App\Accomodation;
use App\PlaneTrip;
use App\TrainTrip;

class TravelDocumentAnnotationResolver
{
    public function byRequestElementType(string $type): string
    {
        switch ($type) {
        case Accomodation::RELATION_NAME:
            return trans('document.voucher');
        case TrainTrip::RELATION_NAME:
            return trans('document.ticket');
        case PlaneTrip::RELATION_NAME:
            return trans('document.confirmation');
        }
    }
}
