<?php

namespace App\BillingAPI\Client\Services\Flights;

use App\BillingAPI\Client\Services\VocabularySearcher\Location;
use App\Compliance\Compliance;
use App\PlaneTrip;
use App\Repositories\LocationRepository;
use App\Services\RulesService\Message\RuleMessageCollection;
use App\User;

class FlightComplianceChecker
{
    /** @var Location */
    protected $departureLocation;

    /** @var Location */
    protected $destinationLocation;

    /** @var VocabularyAutocompleteService */
    protected $vocabularyService;

    /** @var LocationRepository */
    protected $locationRepository;

    /**
     * FlightComplianceChecker constructor.
     * @param VocabularyAutocompleteService $vocabularyService
     */
    public function __construct(VocabularyAutocompleteService $vocabularyService)
    {
        $this->setVocabularyService($vocabularyService);
        
        $this->locationRepository = resolve(LocationRepository::class);
    }

    /**
     * @param VocabularyAutocompleteService $vocabularyService
     * @return FlightComplianceChecker
     */
    public function setVocabularyService(VocabularyAutocompleteService $vocabularyService): FlightComplianceChecker
    {
        $this->vocabularyService = $vocabularyService;
        $this->vocabularyService->setLocations();

        return $this;
    }

    /**
     * @param string $departureLocation
     * @return FlightComplianceChecker
     */
    public function setDepartureLocation(string $departureLocation): FlightComplianceChecker
    {

        $this->departureLocation = $this->vocabularyService->getStationByCode($departureLocation) ?? $this->vocabularyService->getCityByCode($departureLocation);

        return $this;
    }

    /**
     * @param string $destinationLocation
     * @return FlightComplianceChecker
     */
    public function setDestinationLocation(string $destinationLocation): FlightComplianceChecker
    {
        $this->destinationLocation = $this->vocabularyService->getStationByCode($destinationLocation)  ?? $this->vocabularyService->getCityByCode($destinationLocation);

        return $this;
    }

    public function check(PlaneTrip $planeTrip, User $user): RuleMessageCollection
    {
        $planeTrip = $this->setLocationsForPlaneTrip($planeTrip);

        return Compliance::create($planeTrip->instance)->planeTripRetrieved($planeTrip, $user);
    }

    protected function setLocationsForPlaneTrip(PlaneTrip $planeTrip): PlaneTrip
    {
        $flightFrom = $this->locationRepository->makeLocationForRequestElement([
            'city'         => $this->departureLocation->city,
            'country_code' => $this->departureLocation->countryCode,
            'additional_data' => [
                'continent_code' => $this->departureLocation->continentCode,
            ]
        ], $planeTrip->id, PlaneTrip::RELATION_NAME, 'flight_from', $planeTrip->instance->id);

        $flightTo = $this->locationRepository->makeLocationForRequestElement([
            'city'         => $this->destinationLocation->city,
            'country_code' => $this->destinationLocation->countryCode,
            'additional_data' => [
                'continent_code' => $this->destinationLocation->continentCode,
            ]
        ], $planeTrip->id, PlaneTrip::RELATION_NAME, 'flight_to', $planeTrip->instance->id);

        $planeTrip->setRelation('flightFromLocation', $flightFrom);
        $planeTrip->setRelation('flightToLocation', $flightTo);

        return $planeTrip;
    }
}
