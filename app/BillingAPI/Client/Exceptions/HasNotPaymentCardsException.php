<?php

declare(strict_types=1);

namespace App\BillingAPI\Client\Exceptions;

use App\Http\Responses\Response2;
use Throwable;

class HasNotPaymentCardsException extends \Exception implements HasErrorMessageInterface, ErrorMessageSlugInterface
{
    public function __construct($message = "", $code = 0, Throwable $previous = null)
    {
        if (empty($message) === true) {
            $message = trans($this->getMessageSlug());
        }

        parent::__construct($message, $code, $previous);
    }

    public function render()
    {
        $response = Response2::item([])->success(false);

        $response->addError(trans($this->getMessageSlug()));
        return $response;
    }

    public static function getMessageSlug(): string
    {
        return 'error.payment-authorization-failed';
    }
}
