<?php

namespace App\BillingAPI\Client\Exceptions;

use App\BillingAPI\Client\ExceptionHandler\ErrorCodesInfo;

class OfferExpiredException extends \Exception implements HasErrorMessageInterface, ErrorMessageSlugInterface
{
    public function __construct()
    {
        parent::__construct(trans($this->getMessageSlug()));
    }

    public static function getMessageSlug(): string
    {
        return 'error.' . ErrorCodesInfo::OFFER_EXPIRED;
    }
}
