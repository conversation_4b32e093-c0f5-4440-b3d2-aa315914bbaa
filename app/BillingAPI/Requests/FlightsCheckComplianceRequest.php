<?php


namespace App\BillingAPI\Requests;


use App\Exceptions\ValidationException;
use Illuminate\Foundation\Http\FormRequest;

class FlightsCheckComplianceRequest extends FormRequest
{
    public function authorize() {
        return true;
    }

    public function rules() {
        return [
            'flight_from' => 'required|string',
            'flight_to' => 'required|string',
            'round_trip' => 'required|boolean',
            'amount' => 'required',
            'currency' => 'required|string|exists:currencies,code',
            'slug' => 'required|string'
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        if($validator->fails()) {
            throw new ValidationException($validator);
        }
    }
}
