<?php

declare(strict_types=1);

namespace App\BillingAPI\Facades;

use App\BillingAPI\DTOs\Users\UserCreatedDto;
use App\BillingAPI\DTOs\Users\UserUpdatedDto;
use App\BillingAPI\Jobs\NewUserSyncJob;
use App\BillingAPI\Jobs\UpdateUserSyncJob;

final class BillingUserFacade
{
    public function addUser(UserCreatedDto $userCreatedDto): void
    {
        NewUserSyncJob::dispatch($userCreatedDto);
    }

    public function updateUser(UserUpdatedDto $userUpdatedDto): void
    {
        UpdateUserSyncJob::dispatch($userUpdatedDto);
    }
}