<?php


namespace App\BillingAPI\Controllers;


use App\BillingAPI\Client\Client;
use App\BillingAPI\Client\Exceptions\NoOfferFoundException;
use App\BillingAPI\Client\Exceptions\OfferServiceNotFoundException;
use App\BillingAPI\Client\Services\Hotels\HotelsOfferService;
use App\BillingAPI\Client\Services\OfferServiceFactory;
use App\BillingAPI\Client\Services\Payment\UpdatePaymentStatusService;
use App\BillingAPI\Client\Services\UpdateBookingStatusService;
use App\BillingAPI\DTOs\UpdateBookingStatusDTO;
use App\Http\Responses\Response2;
use App\Offer;
use Illuminate\Http\Request;

class ClientController extends \App\Http\Controllers\Controller
{
    public function updateOffers()
    {
        try {
            $service = OfferServiceFactory::create(\request()->get('type'));
        } catch (OfferServiceNotFoundException $e) {
            abort(422, trans('error.unrecognized-offer-service'));
        }

        $request =json_decode(json_encode(\request()->all()));
        $uuid = \request()->get('uuid');
        /** @var Offer $offer */
        $offer = Offer::where(['search_uuid' => $uuid])->first();

        throw_if(!$offer, new NoOfferFoundException());

        $offers = $service->setClient(new Client($offer->request->user))->offersToResponse($request, $offer);

        $service->broadcastOffersChanged($offers->toArray(), $uuid);

        return Response2::item([]);
    }

    /**
     * Action is called only for chunked search queries (hotels)
     *
     * @param Request $httpRequest
     * @return Response2
     * @throws \Throwable
     */
    public function searchQueryCompleted(Request $httpRequest)
    {
        try {
            /** @var HotelsOfferService $service */
            $service = OfferServiceFactory::create($httpRequest->get('type'));
        } catch (OfferServiceNotFoundException $e) {
            abort(422, trans('error.unrecognized-offer-service'));
        }

        $uuid = $httpRequest->get('uuid');

        $service->broadcastSearchCompleted(
            $uuid,
            $httpRequest->get('chunks', []),
            $httpRequest->get('state')
        );

        return Response2::item([]);
    }

    public function updateOfferBookingStatus()
    {
        return (new UpdateBookingStatusService(UpdateBookingStatusDTO::fromRequest(\request()->toArray())))->update();
    }

    public function updateOfferPaymentStatus()
    {
        return (new UpdatePaymentStatusService(request()->get('uuid'), request()->get('status')))->update();
    }
}
