<?php

declare(strict_types=1);

namespace App\BillingAPI\Services;

use App\BillingAPI\DTOs\Users\UserCreatedDto;
use App\BillingAPI\DTOs\Users\UserUpdatedDto;
use App\BillingAPI\Exceptions\MindentoUserSyncException;
use App\BillingAPI\Interfaces\UserSyncServiceInterface;
use App\Helpers\Client\ClientInterface;
use App\Repositories\UserRepository;
use Illuminate\Contracts\Events\Dispatcher;
use Modules\Users\Priv\Events\NewUserSyncedWithBillingEvent;
use Psr\Log\LoggerInterface;

class UserSyncServiceStub implements UserSyncServiceInterface
{
    public function addUser(UserCreatedDto $userCreatedDto): bool
    {
        return true;
    }

    public function updateUser(UserUpdatedDto $userUpdatedDto): bool
    {
        return true;
    }
}