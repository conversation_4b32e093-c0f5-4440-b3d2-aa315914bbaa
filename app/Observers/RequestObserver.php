<?php

namespace App\Observers;

use App\Document;
use App\Enum\AvailableLanguagesEnum;
use App\Events\Request\StatusChangeEvent;
use App\Installment;
use App\Jobs\RegenerateRequestCachesJob;
use App\Repositories\RequestRepository;
use App\Request;
use App\RequestCache;
use App\RequestChange;
use App\Services\Request\RequestCachesService;
use App\Services\RequestCourse\RequestCourseService;
use App\Transaction\Transaction;
use Modules\Report\Priv\Entity\Report;
use Modules\Report\Priv\Job\UpdateReportResourceJob;
use Modules\Report\Priv\Repository\CachedReportRepository;

class RequestObserver
{
    protected const ELASTIC_RELATION_UPDATE = [
        Installment::class => self::REQUEST_ID,
        Transaction::class => self::REQUEST_ID,
        Document::class => self::REQUEST_ID,
    ];

    const REQUEST_ID = 'request_id';

    protected RequestRepository $requestRepository;
    protected RequestCachesService $requestCachesService;
    private CachedReportRepository $cachedReportRepository;

    public function __construct(
        RequestRepository $requestRepository,
        RequestCachesService $requestCachesService,
        CachedReportRepository $cachedReportRepository
    ) {
        $this->requestRepository = $requestRepository;
        $this->requestCachesService = $requestCachesService;
        $this->cachedReportRepository = $cachedReportRepository;
    }

    public function saved(Request $request)
    {
        $changes = $request->getDirty();

        if (empty($changes) === false) {
            $this->saveChanges($request, $changes);
            $this->updateRelatedModelsInElastic($request);

            // Request is not viewable on this status. So it will trigger cascade of problems when any service trying to get this model.
            if (in_array($request->status, RequestCache::NOT_UPDATABLE_REQUEST_STATUSES) === false) {
                $this->updateRequestCache($request);
            }

            $this->cachedReportRepository->findByModel($request->instance, $request)->each(
                function (Report $report) use ($request) {
                    UpdateReportResourceJob::dispatch($report->id, $request->id);
                }
            );
        }

        if ($request->isDirty('status')) {
            event(new StatusChangeEvent($request->slug));
        }
    }

    public function saving(Request $request): Request
    {
        if ($request->type === Request::TYPE_EXPENSE || $request->type === Request::TYPE_INVOICE) {
            $data = [
                (string)AvailableLanguagesEnum::EN() => $request->purpose,
                (string)AvailableLanguagesEnum::PL() => $request->purpose
            ];
        } else {
            $data = [
                (string)AvailableLanguagesEnum::EN() => $this->formatRequestNameForGivenLang(
                    $request,
                    (string)AvailableLanguagesEnum::EN()
                ),
                (string)AvailableLanguagesEnum::PL() => $this->formatRequestNameForGivenLang(
                    $request,
                    (string)AvailableLanguagesEnum::PL()
                ),
            ];
        }

        $request->name = json_encode($data, JSON_UNESCAPED_UNICODE);

        return $request;
    }

    public function saveChanges(Request $request, $changes)
    {
        RequestChange::create([
            'instance_id' => $request->instance_id,
            self::REQUEST_ID => $request->id,
            'status' => $request->status,
            'user_id' => \Auth::user()->id ?? null,
            'changes' => $changes,
        ]);
    }

    public function updateRelatedModelsInElastic(Request $request): void
    {
        if (empty(self::ELASTIC_RELATION_UPDATE) === false) {
            foreach (self::ELASTIC_RELATION_UPDATE as $modelClass => $relationField) {
                call_user_func([$modelClass, 'where'], $relationField, $request->id)->searchable();
            }
        }
    }

    public function updateRequestCache(Request $request): void
    {
        if ($request->wasRecentlyCreated === false) {
            // It will take too much time when doing sync. For example 40-50s when accepting settlement with many documents and "lump sums summaries".
            RegenerateRequestCachesJob::dispatch($request->slug);
        }
    }

    protected function formatRequestNameForGivenLang(Request $request, string $lang)
    {
        $service = RequestCourseService::instance()->setRequest($request);

        return sprintf(
            '%s %s',
            $service->getRequestName(false, $lang),
            $request->isUnrealized() === true ? sprintf(
                ' - %s',
                trans('request.trip-did-not-have-place', [], $lang)
            ) : ''
        );
    }
}
