<?php

namespace App\Observers;

use App\Interfaces\RequestElementObservableInterface;
use App\RequestChange;
use App\Services\Cache;

class RequestElementsObserver
{

    public function saved(RequestElementObservableInterface $element)
    {
        $changes = $element->getDirty();

        if($changes) {
            $this->saveChanges($element, $changes, 'element saved');
        }

        Cache::deleteTags([self::cacheTag(get_class($element), $element->request->slug)]);
    }


    public function deleted(RequestElementObservableInterface $element)
    {
        Cache::deleteTags([self::cacheTag(get_class($element), $element->request->slug)]);
    }

    public function deleting(RequestElementObservableInterface $element)
    {
        $changes = $element->getDirty();

        $this->saveChanges($element, $changes, 'element saved');
    }

    public function saveChanges(RequestElementObservableInterface $element, $changes, $note = "")
    {
        $changes = array_merge($changes, [
            'note'         => $note,
            'element_type' => get_class($element),
            'element_id'   => $element->id
        ]);

        //@todo zmienić id instancji
        RequestChange::create([
            'instance_id' => $element->request->instance_id,
            'request_id'  => $element->request_id,
            'status'      => $element->request->status,
            'user_id'     => \Auth::user()->id ?? null,
            'changes'     => $changes,
        ]);

//        $request = $element->request;
//        $request->touch();

//        event(new RequestSummaryChanged($request));
    }

    public static function cacheTag(string $elementClassName, string $slug): string
    {
        return $elementClassName . '-' . $slug;
    }
}
