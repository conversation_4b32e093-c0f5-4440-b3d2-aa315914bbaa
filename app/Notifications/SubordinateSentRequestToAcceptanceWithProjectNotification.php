<?php

namespace App\Notifications;

use App\Interfaces\NotificationInterface;
use App\Mail\NotificationMail;
use App\Repositories\RequestRepository;
use App\Request;
use App\Traits\Notifications\NotifiablePreferences;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\SlackAttachment;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Queue\SerializesModels;
use Modules\Analytics\Priv\Entities\Project;

class SubordinateSentRequestToAcceptanceWithProjectNotification extends Notification
    implements NotificationInterface, ShouldQueue
{
    use Queueable;
    use NotifiablePreferences;
    use SerializesModels;

    const NAME = 'notification.subordinate-sent-request-to-acceptance-with-project';

    const TYPE = 'SubordinateSentRequestToAcceptanceWithProject';

    const NOTIFICATION_LEVEL = 'info';

    /**
     * @var string $slug;
     */
    private $slug;

    /**
     * Create a new notification instance.
     *
     * @param $request
     */
    public function __construct(string $slug) {
        $this->slug = $slug;
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }

    public function toBroadcast($notifiable) {
        return new BroadcastMessage($this->prepareArray($notifiable));
    }

    public function toSMS($notifiable) {
        return [];
    }

    public function toMail($notifiable) {
        $request = resolve(RequestRepository::class)->findById($this->slug);
        return (new NotificationMail($notifiable))->greeting(trans('notification-email.hello'))
            ->line(trans('notification-email.subordinate-sent-request-to-acceptance-with-project-message', [
                'user' => $request->user->full_name,
                'name' => $request->getNameAttribute($notifiable->locale),
                'mpk' => $request->mpk->code,
                'project' => $request->project !== null ? $request->project->code : ''
            ]))
//            ->action(trans('notification-email.action'), $this->request->getFrontendUrl())
            ->subject(trans('notification-email.subordinate-sent-request-to-acceptance-with-project-subject', [
                'user' => $request->user->full_name,
                'mpk' => $request->mpk->code,
                'project' => $request->project !== null ? $request->project->code : ''
            ]));
    }

    public function toSlack(User $user): SlackMessage
    {
        $request = resolve(RequestRepository::class)->findBySlug($this->slug);
        return $this->createSlackMessage($user)->attachment(function (SlackAttachment $attachment) use ($request, $user) {
            $attachment->title(trans('notification-email.subordinate-sent-request-to-acceptance-with-project-subject', [
                'user' => $request->user->full_name,
                'mpk' => $request->mpk->code,
                'project' => $request->project !== null ? $request->project->code : ''
            ]))
                ->content(trans('notification-email.subordinate-sent-request-to-acceptance-with-project-message', [
                    'user' => $request->user->full_name,
                    'name' => $request->getNameAttribute($user->locale),
                    'mpk' => $request->mpk->code,
                    'project' => $request->project !== null ? $request->project->code : ''
                ]))
                ->fields(MindentoSlackMessage::fieldsForRequest($request))
                ->action(trans('notification-email.action'), $request->getFrontendUrl());
        });
    }

    public function toDatabase($notifiable) {
        return $this->prepareArray($notifiable);
    }

    private function prepareArray($notifiable) {
        $request = resolve(RequestRepository::class)->findById($this->slug);
        return [
            'data' => [
                'name' => static::getName(),
                'notificationType' => static::TYPE,
                'request_id' => $request->slug,
                'request_type' => $request->type,
                'purpose' => $request->purpose,
                'level' => static::NOTIFICATION_LEVEL,
                'translationParams' => [
                    'user' => $request->user->full_name,
                    'route' => $request->name_short,
                    'date' => $request->trip_date,
                    'mpk' => $request->mpk->code,
                    'project' => $request->project instanceof Project ? $request->project->code : ''
                ]
            ],
            'read_at' => null,
            'request_id_slug' => $request->slug
        ];
    }
}