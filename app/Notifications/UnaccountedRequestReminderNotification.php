<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Interfaces\AssistantNotificationInterface;
use App\Mail\NotificationMail;
use App\Repositories\RequestRepository;
use App\Request;
use App\Traits\Notifications\NotifiablePreferences;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\SlackAttachment;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Queue\SerializesModels;

class UnaccountedRequestReminderNotification extends Notification
    implements AssistantNotificationInterface, ShouldQueue
{
    use Queueable;
    use NotifiablePreferences;
    use SerializesModels;

    const NAME = 'notification.unaccounted-request-reminder';

    const TYPE = 'UnaccountedRequestReminder';

    const NOTIFICATION_LEVEL = 'info';

    /** @var string */
    private $requestSlug;

    public function __construct(string $requestSlug) {
        $this->requestSlug = $requestSlug;
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }

    public function toBroadcast($notifiable) {
        return new BroadcastMessage($this->prepareArray($notifiable));
    }

    public function toSMS($notifiable) {
        return [];
    }

    public function toMail($notifiable)
    {
        /** @var Request $request */
        $request = resolve(RequestRepository::class)->findById($this->requestSlug);

        return (new NotificationMail($notifiable))->greeting(trans('notification-email.hello'))
            ->line(trans(
                'notification-email.unaccounted-request-reminder-message-' . $request->type,
                    ['name' => $request->getNameAttribute($request->user->locale)],
                    $request->user->getLocale()
                )
            )
            ->action(trans(
                'notification-email.action-settlement',
                    [],
                    $request->user->getLocale()
                ),
                $request->getFrontendUrl()
            )
            ->subject(trans(
                'notification-email.unaccounted-request-reminder-subject',
                [
                    'requestNumber' => $request->number,
                    'userName' => $request->user->full_name
                ],
                $request->user->getLocale()
                )
            );
    }

    public function toSlack(User $user): SlackMessage
    {
        $request = resolve(RequestRepository::class)->findBySlug($this->requestSlug);
        return $this->createSlackMessage($user)->attachment(function (SlackAttachment $attachment) use ($request) {
            $attachment->title(trans(
                    'notification-email.unaccounted-request-reminder-subject',
                    [
                        'requestNumber' => $request->number,
                        'userName' => $request->user->full_name
                    ],
                    $request->user->getLocale()
                )
            )
                ->content(trans(
                        'notification-email.unaccounted-request-reminder-message-' . $request->type,
                        ['name' => $request->getNameAttribute($request->user->locale)],
                        $request->user->getLocale()
                    )
                )
                ->action(trans(
                    'notification-email.action-settlement',
                    [],
                    $request->user->getLocale()
                ),
                    $request->getFrontendUrl()
                );
        });
    }

    public function toDatabase($notifiable) {
        return $this->prepareArray($notifiable);
    }

    private function prepareArray($notifiable) {
        /** @var Request $request */
        $request = resolve(RequestRepository::class)->findById($this->requestSlug);

        return [
            'data' => [
                'name' => static::getName(),
                'notificationType' => static::TYPE,
                'request_id' => $request->slug,
                'request_type' => $request->type,
                'purpose' => $request->purpose,
                'level' => static::NOTIFICATION_LEVEL,
                'translationParams' => [
                    'route' => $request->name_short,
                    'date' => $request->trip_date,
                ],
            ],
            'read_at' => null,
            'request_id_slug' => $request->slug
        ];
    }
}
