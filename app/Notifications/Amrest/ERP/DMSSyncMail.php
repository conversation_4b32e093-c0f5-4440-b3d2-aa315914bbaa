<?php

declare(strict_types=1);

namespace App\Notifications\Amrest\ERP;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class DMSSyncMail extends Mailable
{
    use Queueable, SerializesModels;

    const ERROR = 'Error';
    const INFO = 'Info';

    protected $message;
    protected $isError;
    protected $instanceDomain;

    public function __construct(string $instanceDomain, string $message, bool $isError)
    {
        $this->instanceDomain = $instanceDomain;
        $this->message = $message;
        $this->isError = $isError;
    }

    public function build(): DMSSyncMail
    {
        return $this->from('<EMAIL>')
            ->subject(sprintf('SAP DMS Sync - %s', $this->isError ? self::ERROR : self::INFO))
            ->view('mail.notification.erp.dms-sync')
            ->with([
                'messageContent'           => $this->message,
                'instanceDomain'    => $this->instanceDomain
            ]);
    }
}