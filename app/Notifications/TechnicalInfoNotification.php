<?php

namespace App\Notifications;

use App\Mail\NotificationMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class TechnicalInfoNotification extends Notification implements ShouldQueue {
    use Queueable;

    /**
     * TechnicalInfoNotification constructor.
     */
    public function __construct()
    {
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }


    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable) {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable) {
        return (new NotificationMail($notifiable))->greeting('')
            ->markdown('mail.notification.technical-info')
            ->subject('*MINDENTO TECHNICAL SUPPORT NOTIFICATION* - web browsers information');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable) {
        return [//
        ];
    }
}
