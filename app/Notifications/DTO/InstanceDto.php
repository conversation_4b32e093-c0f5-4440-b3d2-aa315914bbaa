<?php

declare(strict_types=1);

namespace App\Notifications\DTO;

use App\Instance;

class InstanceDto
{
    protected int $id;

    protected string $locale;

    public function __construct(int $id, string $locale)
    {
        $this->id = $id;
        $this->locale = $locale;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getLocale(): string
    {
        return $this->locale;
    }

    public static function fromInstance(Instance $instance): InstanceDto
    {
        return new InstanceDto($instance->id, $instance->locale);
    }
}
