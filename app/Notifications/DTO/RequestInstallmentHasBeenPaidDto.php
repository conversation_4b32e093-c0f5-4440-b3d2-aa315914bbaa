<?php

declare(strict_types=1);

namespace App\Notifications\DTO;

use App\Installment;
use App\Request;

class RequestInstallmentHasBeenPaidDto extends RequestForCashAdvanceDTO
{
    /** @var string|null */
    public $amount;

    /** @var string|null */
    public $exchangeRate;

    /** @var string|null */
    public $currency;

    public function __construct(Request $request, Installment $installment)
    {
        parent::__construct($request);

        $this->amount = $installment->amount;
        $this->exchangeRate = $installment->exchange_rate;
        $this->currency = $installment->currency->code;
    }
}