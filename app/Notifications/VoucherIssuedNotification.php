<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Accomodation;
use App\DTO\Notification\VoucherIssuedDto;
use App\Ics\Generators\OutlookOfficeIcsGenerator;
use App\Ics\Link;
use App\Interfaces\AssistantNotificationInterface;
use App\Mail\NotificationMail;
use App\Notifications\DTO\DocumentDto;
use App\Notifications\DTO\RequestDto;
use App\PlaneTrip;
use App\TrainTrip;
use App\Traits\Notifications\NotifiablePreferences;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Queue\SerializesModels;

class VoucherIssuedNotification extends Notification
    implements AssistantNotificationInterface, ShouldQueue
{
    use Queueable;
    use NotifiablePreferences;
    use SerializesModels;

    const NAME = 'notification-email.voucher-issued-message';

    const TYPE = 'VoucherIssued';

    const NOTIFICATION_LEVEL = 'info';

    /**
     * @var RequestDto
     */
    protected $requestDto;

    /** @var DocumentDto */
    protected $documentDto;

    protected ?string $offerErrorMessageSlug;

    public function __construct(VoucherIssuedDto $voucherIssuedDto)
    {
        $this->requestDto = $voucherIssuedDto->getRequestDto();
        $this->documentDto = $voucherIssuedDto->getDocumentDto();
        $this->offerErrorMessageSlug = $voucherIssuedDto->getOfferErrorMessageSlug();
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage($this->prepareArray($notifiable));
    }

    public function toSMS($notifiable)
    {
        return [];
    }

    public function toMail($notifiable)
    {
        $notificationMail = (new NotificationMail($notifiable))->greeting(trans('notification-email.hello'))
            ->line(trans('notification-email.voucher-issued-message',
                [
                    'trip_name' => $this->requestDto->name,
                    'trip_starts' => $this->requestDto->tripStarts,
                    'trip_ends' => $this->requestDto->tripEnds,
                    'reservation_name_message' => $this->documentDto->getName($this->locale)
                ]
            ))
        ;

        if($this->offerErrorMessageSlug !== null) {
            $notificationMail->line(trans('notification-email.voucher-issued-message_alert', [
                    'reservation_error_message' => ($this->offerErrorMessageSlug !== null ? trans($this->offerErrorMessageSlug, [], $this->locale) : null)
                ]
            ));
        }

        $notificationMail
            ->subject(trans('notification-email.voucher-issued-subject',
                [
                    'reservation_name_subject' => $this->documentDto->getName($this->locale)
                ]
            ))
            ->attach(base_path($this->documentDto->getFilePath()));

        $notificationMail = $this->attachCalendarEvents($notificationMail);

        return $notificationMail;
    }

    public function toDatabase($notifiable)
    {
        return $this->prepareArray($notifiable);
    }

    private function prepareArray($notifiable)
    {
        return [
            'data' => [
                'name' => static::getName(),
                'notificationType' => static::TYPE,
                'request_id' => $this->requestDto->requestSlug,
                'request_type' => $this->requestDto->requestType,
                'purpose' => $this->requestDto->purpose,
                'level' => static::NOTIFICATION_LEVEL,
                'translationParams' => [
                    'trip_name' => $this->requestDto->name,
                    'trip_starts' => $this->requestDto->tripStarts,
                    'trip_ends' => $this->requestDto->tripEnds,
                    'date' => $this->requestDto->date,
                    'reservation_name_message' => $this->documentDto->getName($this->locale)
                ],
            ],
            'read_at' => null,
            'request_id_slug' => $this->requestDto->requestSlug
        ];
    }

    private function attachCalendarEvents(NotificationMail $notificationMail): NotificationMail
    {
        if ($this->documentDto->isRoundTrip() === true) {
            $from = $this->documentDto->getStartsAt()->toDateTime();
            $to = $this->documentDto->getEndsAt()->toDateTime();

            $targetDescription = $this->formatDescription(sprintf(
                '%s%s → %s%s',
                $this->documentDto->getCity(),
                empty($this->documentDto->getFromCodeTarget()) === false ? sprintf(' (%s)', $this->documentDto->getFromCodeTarget()) : '',
                $this->documentDto->getReturnCity(),
                empty($this->documentDto->getToCodeTarget()) === false ? sprintf(' (%s)', $this->documentDto->getToCodeTarget()) : '',
            ));
            $html = $this->getHtmlForType($this->documentDto->getElementType(), $targetDescription, $from, $to);

            $calendarEventForTarget = Link::create($targetDescription, $from, $to)
                ->description($targetDescription)
                ->address($this->documentDto->getCity())
                ->html($html);

            $returnDescription = $this->formatDescription(sprintf(
                '%s%s → %s%s',
                $this->documentDto->getReturnCity(),
                empty($this->documentDto->getFromCodeReturn()) === false ? sprintf(' (%s)', $this->documentDto->getFromCodeReturn()) : '',
                $this->documentDto->getCity(),
                empty($this->documentDto->getToCodeReturn()) === false ? sprintf(' (%s)', $this->documentDto->getToCodeReturn()) : '',
            ));
            $returnFrom = $this->documentDto->getReturnStartsAt()->toDateTime();
            $returnTo = $this->documentDto->getReturnEndsAt()->toDateTime();
            $returnHtml = $this->getHtmlForType($this->documentDto->getElementType(), $returnDescription, $returnFrom, $returnTo, true);

            $calendarEventForReturn = Link::create($returnDescription, $returnFrom, $returnTo)
                ->description($returnDescription)
                ->address($this->documentDto->getReturnCity())
                ->html($returnHtml);

            $notificationMail->attachData($calendarEventForTarget->formatWith(
                new OutlookOfficeIcsGenerator()),
                sprintf('%s.ics', $this->purifyIcsFileName($this->generateIcsFileName())),
                [
                    'mime' => 'text/calendar;charset=UTF-8;method=PUBLISH', // Could be REQUEST, check later if does not work.
                ]
            );
            $notificationMail->attachData($calendarEventForReturn->formatWith(
                new OutlookOfficeIcsGenerator()),
                sprintf('%s.ics', $this->purifyIcsFileName($this->generateIcsFileName(true))),
                [
                    'mime' => 'text/calendar;charset=UTF-8;method=PUBLISH', // Could be REQUEST, check later if does not work.
                ]
            );
        } else {
            $from = $this->documentDto->getElementType() === Accomodation::class
                ? $this->documentDto->getStartsAt()->hour(17)->minute(0)->toDateTime()
                : $this->documentDto->getStartsAt()->toDateTime();
            $to = $this->documentDto->getElementType() === Accomodation::class
                ? $this->documentDto->getEndsAt()->hour(9)->minute(0)->toDateTime()
                : $this->documentDto->getEndsAt()->toDateTime();

            $description = $this->documentDto->getElementType() === PlaneTrip::class
                ? $this->formatDescription(sprintf(
                    '%s%s → %s%s',
                    $this->documentDto->getCity(),
                    empty($this->documentDto->getFromCodeTarget()) === false ? sprintf(' (%s)', $this->documentDto->getFromCodeTarget()) : '',
                    $this->documentDto->getReturnCity(),
                    empty($this->documentDto->getToCodeTarget()) === false ? sprintf(' (%s)', $this->documentDto->getToCodeTarget()) : '',
                ))
                : $this->formatDescription($this->documentDto->getName($this->locale));

            $html = $this->getHtmlForType($this->documentDto->getElementType(), $description, $from, $to);

            $calendarEvent = Link::create($description, $from, $to)
                ->description($description)
                ->address($this->documentDto->getElementType() === Accomodation::class ? $this->documentDto->getAddress() : $this->documentDto->getCity())
                ->html($html);
            $notificationMail->attachData($calendarEvent->formatWith(
                new OutlookOfficeIcsGenerator()),
                sprintf('%s.ics', $this->purifyIcsFileName($this->generateIcsFileName())),
                [
                    'mime' => 'text/calendar;charset=UTF-8;method=PUBLISH', // Could be REQUEST, check later if does not work.
                ]
            );
        }

        return $notificationMail;
    }

    private function escapeString(?string $field = '' ): string
    {
        return addcslashes($field, "\r\n,;");
    }

    public function formatDescription(string $description): string
    {
        switch ($this->documentDto->getElementType()) {
            case TrainTrip::class:
                return sprintf('%s %s', trans('notification-email.calendar-event-train-prefix'), $description);
            case PlaneTrip::class:
                return sprintf('%s %s', trans('notification-email.calendar-event-flight-prefix'), $description);
            case Accomodation::class:
                return sprintf('%s %s %s', trans('notification-email.calendar-event-hotel-prefix'), $this->documentDto->getTransportationNameTarget(), $this->documentDto->getCity());
        }
    }

    private function getHtmlForType(string $type, string $description, \DateTime $from, \DateTime $to, $return = false): string
    {
        switch ($type) {
            case TrainTrip::class:
                return $this->getHtmlForTrain($description, $from, $to);
            case PlaneTrip::class:
                return $this->getHtmlForPlane($description, $from, $to, $return);
            case Accomodation::class:
                return $this->getHtmlForHotel($description, $from, $to);
        }
    }

    private function getHtmlForHotel(string $description, \DateTime $from, \DateTime $to): string
    {
        return '<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">\n
                <HTML>\n
                <HEAD>\n
                <META NAME="Generator" CONTENT="MS Exchange Server version 16.0.14026.20240">\n
                <TITLE></TITLE>\n
                </HEAD>\n
                <BODY>\n\n\n
                    <P><FONT SIZE=2>'. $this->escapeString($this->documentDto->getTransportationNameTarget()) . '</FONT>\n\n
                    <P><FONT SIZE=2>'. $this->escapeString($this->documentDto->getAddress()) . '</FONT>\n\n
                    </P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-booking-number') . ': '. $this->documentDto->getTicketNumber() .'</FONT>\n\n
                    <BR><FONT SIZE=2>'. trans('notification-email.calendar-event-guest-name') . ': ' . $this->escapeString($this->requestDto->userFullName) . '</FONT>\n
                    </P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-hotel-check-in-date') . ': '. $from->format('d.m.Y') . '</FONT>\n\n
                    <BR><FONT SIZE=2>'. trans('notification-email.calendar-event-hotel-check-out-date') . ': '. $to->format('d.m.Y') .'</FONT>\n
                    </P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-request-number') . ': ' . $this->requestDto->number . ' <a href="' . $this->requestDto->requestFrontendUrl . '">' . trans('notification-email.action') . '</a></FONT>\n
                    </P>\n\n
                </BODY>\n
                </HTML>';
    }

    private function getHtmlForTrain(string $description, \DateTime $from, \DateTime $to): string
    {
        return '<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">\n
                <HTML>\n
                <HEAD>\n
                <META NAME="Generator" CONTENT="MS Exchange Server version 16.0.14026.20240">\n
                <TITLE></TITLE>\n
                </HEAD>\n
                <BODY>\n\n\n
                    <P>'. $this->escapeString($description) . ' ' . $this->escapeString(sprintf('%s %s', $this->documentDto->getTransportationNameTarget(), $this->documentDto->getTransportationNumberTarget())) . '</P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-ticket-number') . ': '. $this->escapeString($this->documentDto->getTicketNumber()) . '</FONT>\n\n
                    <BR><FONT SIZE=2>'. trans('notification-email.calendar-event-passenger-name') . ': ' . $this->escapeString($this->requestDto->userFullName) . '</FONT>\n
                    </P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-departure-date') . ': '. $from->format('H:i d.m.Y') .'</FONT>\n\n
                    <BR><FONT SIZE=2>'. trans('notification-email.calendar-event-arrival-date') . ': '. $to->format('H:i d.m.Y') .'</FONT>\n
                    </P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-request-number') . ': ' . $this->requestDto->number . ' <a href="' . $this->requestDto->requestFrontendUrl . '">' . trans('notification-email.action') . '</a></FONT>\n
                    </P>\n\n
                </BODY>\n
                </HTML>';
    }

    private function getHtmlForPlane(string $description, \DateTime $from, \DateTime $to, bool $return = false): string
    {
        $flightNumber = $return === false ? $this->documentDto->getTransportationNumberTarget() : $this->documentDto->getTransportationNumberReturn();
        $airlineName = $return === false ? $this->documentDto->getTransportationNameTarget() : $this->documentDto->getTransportationNameReturn();

        return '<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">\n
                <HTML>\n
                <HEAD>\n
                <META NAME="Generator" CONTENT="MS Exchange Server version 16.0.14026.20240">\n
                <TITLE></TITLE>\n
                </HEAD>\n
                <BODY>\n\n\n
                    <P>'. $this->escapeString($description) . '</P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-reservation-number') . ': '. $this->escapeString($this->documentDto->getTicketNumber()) . '</FONT>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-ticket-number') . ': '. ($return === false ? $this->escapeString($this->documentDto->getTargetReferenceNumber()) : $this->escapeString($this->documentDto->getReturnReferenceNumber())) . '</FONT>\n\n
                    <BR><FONT SIZE=2>'. trans('notification-email.calendar-event-passenger-name') . ': ' . $this->escapeString($this->requestDto->userFullName) . '</FONT>\n
                    </P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-flight-number') . ': '. $flightNumber .'</FONT>\n\n
                    <BR><FONT SIZE=2>'. trans('notification-email.calendar-event-airline-name') . ': '. $airlineName .'</FONT>\n
                    </P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-flight-departure-date') . ': '. $from->format('H:i d.m.Y') . '</FONT>\n\n
                    <BR><FONT SIZE=2>'. trans('notification-email.calendar-event-flight-arrival-date') . ': '. $to->format('H:i d.m.Y') .'</FONT>\n
                    </P>\n\n
                    <P><FONT SIZE=2>'. trans('notification-email.calendar-event-request-number') . ': ' . $this->requestDto->number . ' <a href="' . $this->requestDto->requestFrontendUrl . '">' . trans('notification-email.action') . '</a></FONT>\n
                    </P>\n\n
                </BODY>\n
                </HTML>';
    }

    private function generateIcsFileName(bool $return = false): string
    {
        switch ($this->documentDto->getElementType()) {
            case TrainTrip::class:
                return sprintf('%s_%s', trans('notification-email.calendar-event-train-prefix'), $this->documentDto->getCity());
            case PlaneTrip::class:
                return sprintf(
                    '%s_%s',
                    $return === false ? $this->documentDto->getFromCodeTarget() : $this->documentDto->getFromCodeReturn(),
                    $return === false ? $this->documentDto->getToCodeTarget() : $this->documentDto->getToCodeReturn()
                );
            case Accomodation::class:
                return sprintf('%s_%s', trans('notification-email.calendar-event-hotel-prefix'), $this->documentDto->getCity());
        }
    }

    private function purifyIcsFileName(string $fileName): string
    {
        return preg_replace('/\s+/', '_', $fileName);
    }
}
