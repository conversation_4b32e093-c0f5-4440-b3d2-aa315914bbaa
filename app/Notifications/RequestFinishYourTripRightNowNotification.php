<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Interfaces\NotificationInterface;
use App\Mail\NotificationMail;
use App\Repositories\RequestRepository;
use App\Request;
use App\Traits\Notifications\NotifiablePreferences;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\SlackAttachment;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Queue\SerializesModels;

class RequestFinishYourTripRightNowNotification extends Notification implements NotificationInterface, ShouldQueue
{
    use Queueable;
    use NotifiablePreferences;
    use SerializesModels;

    const NAME = 'notification.request-did-not-have-place';

    const TYPE = 'RequestFinishYourTripRightNowNotification';

    const NOTIFICATION_LEVEL = 'info';

    private $requestSlug;

    public function __construct(string $requestSlug)
    {
        $this->requestSlug = $requestSlug;
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage($this->prepareArray($notifiable));
    }

    public function toSMS($notifiable)
    {
        return [];
    }

    public function toMail($notifiable)
    {
        /** @var Request $request */
        $request = resolve(RequestRepository::class)->findBySlug($this->requestSlug);

        return (new NotificationMail($notifiable))->greeting(trans('notification-email.hello'))
            ->subject(trans('notification-email.request-set-as-unrealized-subject',
                [
                    'name' => $request->getRequestNameShort(),
                    'trip_starts' => $request->getTripStartsFormatted(),
                    'trip_ends' => $request->getTripEndsFormatted()
                ]
            ))
            ->line(trans('notification-email.request-set-as-unrealized-message', [
                'name' => $request->getRequestNameShort(),
                'trip_starts' => $request->getTripStartsFormatted(),
                'trip_ends' => $request->getTripEndsFormatted()
            ]));
    }

    public function toSlack(User $user): SlackMessage
    {
        $request = resolve(RequestRepository::class)->findBySlug($this->requestSlug);

        return $this->createSlackMessage($user)
            ->attachment(function (SlackAttachment $attachment) use ($request) {
                $attachment->title(trans('notification-email.request-set-as-unrealized-subject',
                    [
                        'name' => $request->getRequestNameShort(),
                        'trip_starts' => $request->getTripStartsFormatted(),
                        'trip_ends' => $request->getTripEndsFormatted()
                    ]
                ))
                    ->fields(MindentoSlackMessage::fieldsForRequest($request))
                    ->action(trans('notification-email.action'), $request->getFrontendUrl());

                $attachment->content(trans('notification-email.request-set-as-unrealized-message', [
                    'name' => $request->getRequestNameShort(),
                    'trip_starts' => $request->getTripStartsFormatted(),
                    'trip_ends' => $request->getTripEndsFormatted()
                ]));
            });
    }

    public function toDatabase($notifiable)
    {
        return $this->prepareArray($notifiable);
    }

    private function prepareArray($notifiable)
    {
        /** @var Request $request */
        $request = resolve(RequestRepository::class)->findBySlug($this->requestSlug);

        return [
            'data' => [
                'name' => static::getName(),
                'notificationType' => static::TYPE,
                'request_id' => $request->slug,
                'request_type' => $request->type,
                'purpose' => $request->purpose,
                'level' => static::NOTIFICATION_LEVEL,
                'translationParams' => [
                    'user' => $request->user->full_name,
                    'route' => $request->name_short,
                    'date' => $request->trip_date,
                ]
            ],
            'read_at' => null,
            'request_id_slug' => $request->slug,
        ];
    }
}