<?php

namespace App\Notifications;

use App\Interfaces\NotificationInterface;
use App\Mail\NotificationMail;
use App\Request;
use App\Traits\Notifications\NotifiablePreferences;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\SlackAttachment;
use Illuminate\Notifications\Messages\SlackMessage;

class OfferRemarksNotification extends Notification implements NotificationInterface, ShouldQueue {
    use Queueable;
    use NotifiablePreferences;

    protected $remarks;
    const NAME = 'notification.offer-remarks';

    const TYPE = 'OfferRemarksNotification';

    const NOTIFICATION_LEVEL = 'info';
    /**
     * @var Request $request
     */
    private $request;

    /**
     * Create a new notification instance.
     *
     * @param $request
     */
    public function __construct(array $remarks) {
        $this->remarks = $remarks;
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }

    public function toBroadcast($notifiable) {
        return new BroadcastMessage($this->prepareArray($notifiable));
    }

    public function toSMS($notifiable) {
        return [];
    }

    public function toMail($notifiable) {
        return (new NotificationMail($notifiable))->greeting(trans('notification-email.hello'))
            ->markdown('mail.notification.offer-remarks', [
                'remarks' => $this->remarks,
            ])
            ->subject(trans('notification-email.offer-remarks-subject'));
    }

    public function toSlack(User $user): SlackMessage
    {
        return $this->createSlackMessage($user)
            ->attachment(function (SlackAttachment $attachment) use ($user) {
                $attachment->title(trans('notification-email.offer-remarks-subject'))
                    ->content(view('mail.notification.offer-remarks', [
                        'remarks' => $this->remarks,
                    ])->render())
                ;
            });
    }

    public function toDatabase($notifiable) {
        return $this->prepareArray($notifiable);
    }

    private function prepareArray($notifiable) {
        return [
            'data' => [
                'name' => static::getName(),
                'notificationType' => static::TYPE,
                'level' => static::NOTIFICATION_LEVEL,
                'translationParams' => [
                ],
            ],
            'read_at' => null,
        ];
    }


}
