<?php

namespace App\Notifications;

use App\Mail\NotificationMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class NewPasswordNotification extends Notification implements ShouldQueue {
    use Queueable;
    private $newPassword;

    /**
     * Create a new notification instance.
     *
     * @param $newPassword
     */
    public function __construct(string $newPassword) {

        $this->newPassword = $newPassword;
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable) {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable) {
        return (new NotificationMail($notifiable))->greeting(trans('notification-email.hello'))
            ->line(trans('notification-email.your-password-is: ') . $this->newPassword)
            ->subject(trans('notification-email.new-password-notification'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable) {
        return [//
        ];
    }
}
