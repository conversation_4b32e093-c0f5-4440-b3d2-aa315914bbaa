<?php

declare(strict_types=1);

namespace App\IdentityProviders\Exceptions;

use Exception;

final class KeycloakUsernameExistsException extends Exception
{
    protected array $context;

    public function __construct(string $message, array $context)
    {
        parent::__construct($message);
        $this->context = $context;
    }
    
    public function context()
    {
        return $this->context;
    }

    public static function create(int $userId, array $context): KeycloakUsernameExistsException
    {
        return new static("Error occured while exporting user {$userId} to Keycloak", $context);
    }
}
