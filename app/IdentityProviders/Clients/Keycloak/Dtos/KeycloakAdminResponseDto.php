<?php

declare(strict_types=1);

namespace App\IdentityProviders\Clients\Keycloak\Dtos;

final class KeycloakAdminResponseDto
{
    public const ERROR_MESSAGE_USER_EXISTS_WITH_SAME_USERNAME = 'User exists with same username';

    private array $response;

    public function __construct(array $response)
    {
        $this->response = $response;
    }

    public function isSuccess(): bool
    {
        if (!empty($this->response['errorMessage']) || !empty($this->response['error'])) {
            return false;
        }

        return true;
    }

    public static function createFromResponse(array $response): KeycloakAdminResponseDto
    {
        return new self($response);
    }

    public function hasErrorMessage(string $errorMessage): bool
    {
        return !empty($this->response['errorMessage']) && $this->response['errorMessage'] === $errorMessage;
    }

    public function getErrors(): array
    {
        $errors = [];

        if(!empty($this->response['errorMessage'])) {
            $errors['keycloakErrorMessage'] = $this->response['errorMessage'];
        }

        if(!empty($this->response['error'])) {
            $errors['keycloakErrors'] = $this->response['error'];
        }

        return $errors;
    }

    /**
     * @param string $key
     * @param mixed|null $default
     * @return mixed|null
     */
    public function get(string $key, $default = null)
    {
        return isset($this->response[$key]) ? $this->response[$key] : $default;
    }

    public function response(): array
    {
        return $this->response ?? [];
    }
}
