<?php

namespace App\Compliance\Instances\Amrest\Rules;

use App\Currency;
use App\Helpers\NameTranslation;
use App\Instance;
use App\Repositories\ExchangeRateRepository;
use App\Request;
use App\Services\BorderCrossing\BorderCrossingItem;
use App\Services\BorderCrossing\BorderCrossingService;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;
use App\Services\TravelExpenseSingleton\TravelExpenseSingletonService;
use App\TravelExpense;
use Modules\Common\ValueObjects\CurrencyCode;
use Modules\Common\ValueObjects\ExchangeRateProviderSlug;
use Modules\ExchangeRate\Pub\Facades\FxRateFacade;

class ErrorBorderCrossingExchangeRateRule extends Rule
{
    const NAME = 'error_border_crossing_exchange_rate_rule';

    /** @var Request */
    protected $model;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->hasExchangeRate();
    }

    /**
     * @return bool
     */
    protected function hasExchangeRate(): bool
    {
        if (!isset($this->config['enabled']) || $this->config['enabled'] === false) {
            return true;
        }

        $borderCrossingTargets = (new BorderCrossingService($this->model))->getBorderCrossingTargetsCollection();

        $currencies = collect();

        $borderCrossingTargets->each(function (BorderCrossingItem $borderCrossingItem) use (&$currencies) {
            /** @var TravelExpense $travelExpense */
            $travelExpense = resolve(TravelExpenseSingletonService::class)->getTravelExpenseByCountryIdForDate($borderCrossingItem->country->id, $this->model->instance_id, $borderCrossingItem->start);
            $currencies->push($travelExpense->currency);
        });

        $date = $this->model->trip_ends->copy();

        $currencies->each(function (Currency $currency) use ($date) {

            if ($currency->id != $this->model->instance->currency->id) {
                $exchangeRate = resolve(FxRateFacade::class)->findLastRate(
                    $this->model->instance->exchange_rate_provider,
                    new CurrencyCode($currency->code)
                );

                if ($date->greaterThan($exchangeRate->getEffectiveDate())) {
                    $this->messages->push(new RuleMessage($this,
                        new NameTranslation('rules.document-can-not-settlement'), false));

                    return false;
                }
            }
        });

        return true;
    }
}
