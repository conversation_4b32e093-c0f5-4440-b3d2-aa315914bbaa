<?php


namespace App\Compliance\Instances\Amrest\Rules;


use App\Helpers\NameTranslation;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;
use App\Services\Summary\SummaryElement\SummaryElement;

class UnrequestedElementAppearedRule extends Rule
{
    const NAME = 'unrequested_element_rule';

    /** @var SummaryElement */
    protected $model;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = false;
        $this->messages->push(new RuleMessage($this, new NameTranslation('rules.unrequested-element-appeared'), false));
    }

    public function getValidationValues(): array
    {
        return [
            'config_value' => true,
            'request_value' => $this->valid,
            'compare_values' => Rule::COMPARE_VALUES_SIMPLE_REVERT
        ];
    }
}
