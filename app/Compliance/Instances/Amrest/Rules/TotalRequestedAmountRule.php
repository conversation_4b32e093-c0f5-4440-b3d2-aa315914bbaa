<?php

declare(strict_types=1);

namespace App\Compliance\Instances\Amrest\Rules;

use App\Helpers\NameTranslation;
use App\Request;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;
use App\Vendors\Math;

class TotalRequestedAmountRule extends Rule
{
    const NAME = 'total_requested_amount_rule';

    /** @var Request */
    protected $model;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = true;
        if (Math::leftIsGreeterOrEqualRight(
            (string) $this->model->getBasicSummary(' - ')['requestedConvertedAmount']['amount'],
            (string) $this->config['maxAmount']
        )) {
            $this->messages->push(
                new RuleMessage(
                    $this,
                    new NameTranslation(
                        'rules.total-requested-amount-limit-exceeded',
                        [
                        ]
                    ),
                    false
                )
            );

            $this->valid = false;
        }
    }

    public function getValidationValues(): array
    {
        return [
            'config_value' => $this->config['maxAmount'],
            'request_value' => (string) $this->model->getBasicSummary(' - ')['requestedConvertedAmount']['amount'],
            'compare_values' => Rule::COMPARE_VALUES_SIMPLE
        ];
    }
}
