<?php


namespace App\Compliance\Instances\Amrest\Rules;


use App\Compliance\Accommodation;
use App\Compliance\Instances\Amrest\Contracts\AccommodationErrorInterface;
use App\Helpers\NameTranslation;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;

class ErrorNationalAccommodationRule extends Rule implements AccommodationErrorInterface
{
    const NAME = 'error_national_accommodation_rule';

    /** @var Accommodation */
    protected $model;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->isAmountValid();
    }

    protected function isAmountValid(): bool
    {
        if ($this->model->getConvertedAmountPerNight() > $this->config['maxAmount']) {
            $this->messages->push(
                new RuleMessage(
                    $this,
                    new NameTranslation(
                        'rules.accommodation-error',
                        [
                            'amount' => $this->config['maxAmount'],
                            'currency' => $this->model->getInstance()->currency->code
                        ]
                    ),
                    false
                )
            );

            return false;
        }

        return true;
    }
}