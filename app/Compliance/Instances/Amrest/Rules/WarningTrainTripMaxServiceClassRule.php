<?php


namespace App\Compliance\Instances\Amrest\Rules;


use App\Helpers\NameTranslation;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;
use App\TrainTrip;

class WarningTrainTripMaxServiceClassRule extends Rule
{
    const NAME = 'warning_train_trip_max_service_class_rule';

    /** @var TrainTrip */
    protected $model;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->isServiceClassValid();
    }

    protected function isServiceClassValid(): bool
    {
        $maxServiceClass = $this->config['service_class'] ?? null;

        if($this->model->service_class === null || $maxServiceClass === null) {
            return true;
        }

        if ($maxServiceClass > $this->model->service_class) {
            $this->messages->push(new RuleMessage($this,
                new NameTranslation('rules.train-trip-max-service-class-warning',
                    ['service_class' => $maxServiceClass]),
                    false)
            );

            return false;
        }

        return true;
    }

    public function getValidationValues(): array
    {
        return [
            'config_value' => $this->config['service_class'],
            'request_value' => $this->model->service_class,
            'compare_values' => Rule::COMPARE_VALUES_SIMPLE_REVERT
        ];
    }

}
