<?php

namespace App\Compliance\Instances\Amrest\Rules;

use App\Document;
use App\ExchangeRate\Exceptions\ExchangeRateTripNotFinishedException;
use App\Helpers\NameTranslation;
use App\Instance;
use App\Repositories\ExchangeRateRepository;
use App\Request;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;
use Carbon\Carbon;
use Modules\Common\ValueObjects\CurrencyCode;
use Modules\Common\ValueObjects\ExchangeRateProviderSlug;
use Modules\ExchangeRate\Pub\Facades\FxRateFacade;

class ErrorDocumentExchangeRateRule extends Rule
{
    const NAME = 'error_document_exchange_rate_rule';

    /** @var Document */
    protected $model;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->hasExchangeRate();
    }

    /**
     * @return bool
     * @throws ExchangeRateTripNotFinishedException
     */
    protected function hasExchangeRate(): bool
    {
        if (!isset($this->config['enabled']) || $this->config['enabled'] === false) {
            return true;
        }

        if ($this->model->currency->id === $this->model->instance->currency->id) {
            return true;
        }


        if ($this->model->request->type == Request::TYPE_TRIP) {
            if (!isset($this->model->request->trip_ends)) {
                throw new ExchangeRateTripNotFinishedException();
            }
            /** @var Carbon $date */
            $date = $this->model->request->trip_ends->copy()->addDay()->startOfDay();
        } else {
            if ($this->model->issue_date === null) {
                return false;
            }

            /** @var Carbon $date */
            $date = $this->model->issue_date->copy()->addDay()->startOfDay();
        }

        if (!$date) {
            return false;
        }

        $exchangeRate = resolve(FxRateFacade::class)->findLastRate(
            $this->model->instance->exchange_rate_provider,
            new CurrencyCode($this->model->currency->code)
        );

        if ($date->greaterThan($exchangeRate->getEffectiveDate())) {
            $this->messages->push(new RuleMessage($this,
                new NameTranslation('rules.document-can-not-settlement'), false));

            return false;
        }

        return true;
    }
}
