<?php


namespace App\Compliance\Instances\Amrest\Rules;


use App\Helpers\NameTranslation;
use App\PrivateCarTrip;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;

class WarningMileageAllowanceRule extends Rule
{
    const NAME = 'warning_mileage_allowance_rule';

    /** @var PrivateCarTrip */
    protected $model;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->isDistanceValid();
    }

    protected function isDistanceValid(): bool
    {
        if($this->model->distance > $this->config['maxDistance']) {
            $this->messages->push(new RuleMessage($this, new NameTranslation('rules.warning-mileage-allowance', [
                'distance' => $this->config['maxDistance'],
                'route'    => $this->model->getShortName(),
            ]), false));

            return false;
        }

        return true;
    }

    public function getValidationValues(): array
    {
        return [
            'config_value' => $this->config['maxDistance'],
            'request_value' => $this->model->distance,
            'compare_values' => Rule::COMPARE_VALUES_PERCENT
        ];
    }
}
