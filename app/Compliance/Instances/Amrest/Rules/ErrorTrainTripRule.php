<?php


namespace App\Compliance\Instances\Amrest\Rules;


use App\Helpers\NameTranslation;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;
use App\TrainTrip;

class ErrorTrainTripRule extends Rule
{
    const NAME = 'error_train_trip_rule';

    /** @var TrainTrip */
    protected $model;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->isAmountValid();
    }

    protected function isAmountValid(): bool
    {
        $amount = $this->model->round_trip ? $this->config['maxAmount'] * 2 : $this->config['maxAmount'];

        if ($this->model->getConvertedAmount() > $amount) {
            $this->messages->push(
                new RuleMessage(
                    $this,
                    new NameTranslation(
                        'rules.train-trip-error',
                        [
                            'amount' => $amount,
                            'currency' => $this->model->instance->currency->code
                        ]
                    ),
                    false
                )
            );

            return false;
        }

        return true;
    }
}