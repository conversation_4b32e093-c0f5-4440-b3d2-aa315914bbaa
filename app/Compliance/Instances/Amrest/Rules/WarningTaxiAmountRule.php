<?php


namespace App\Compliance\Instances\Amrest\Rules;


use App\Cost;
use App\Helpers\NameTranslation;
use App\Services\RulesService\Message\RuleMessage;
use App\Services\RulesService\Rules\Rule;

class WarningTaxiAmountRule extends Rule
{
    const NAME = 'warning_taxi_amount_rule';
    const TAXI_TYPE_SLUG = 'expense-trip-group-taxi-type';

    /** @var Cost */
    protected $model;

    protected $convertedAmount;

    protected $maxAmount;

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        if (!$this->costIsTaxi()) {
            $this->valid = true;
        } else {
            $this->valid = $this->isAmountValid();
        }
    }

    protected function costIsTaxi()
    {
        return $this->model->getType() === static::TAXI_TYPE_SLUG;
    }

    protected function isAmountValid(): bool
    {
        $this->maxAmount = $this->config['maxAmount'] ?? null;

        if ($this->maxAmount === null) {
            return true;
        }

        $this->convertedAmount = $this->model->getConvertedAmount();

        if ($this->convertedAmount > $this->maxAmount) {
            $this->messages->push(
                new RuleMessage(
                    $this,
                    new NameTranslation(
                        'rules.taxi-amount-error',
                        [
                            'amount' => $this->maxAmount,
                            'currency' => $this->model->instance->currency->code
                        ]
                    ),
                    false
                )
            );

            return false;
        }

        return true;
    }

    public function getValidationValues(): array
    {
        return [
            'config_value' => $this->maxAmount,
            'request_value' => $this->convertedAmount,
            'compare_values' => Rule::COMPARE_VALUES_SIMPLE,
        ];
    }
}
