<?php

namespace App\Compliance\Instances\Amrest\Factories;

use App\Compliance\Accommodation;
use App\Compliance\Instances\Amrest\Contracts\AccommodationWarningInterface;
use App\Compliance\Instances\Amrest\Rules\WarningAccommodationByLimitsRule;
use App\Compliance\Instances\Amrest\Rules\WarningInternationalAccommodationRule;
use App\Compliance\Instances\Amrest\Rules\WarningLocationAccommodationRule;
use App\Compliance\Instances\Amrest\Rules\WarningNationalAccommodationRule;
use App\Repositories\RuleRepository;
use App\User;

class AccommodationWarningRuleFactory
{
    private RuleRepository $ruleRepository;

    public function __construct(RuleRepository $ruleRepository)
    {
        $this->ruleRepository = $ruleRepository;
    }

    public function create(Accommodation $accommodation, User $user): AccommodationWarningInterface
    {
        $instance = $accommodation->getInstance();

        if ($this->ruleRepository->checkIfRuleExists(WarningLocationAccommodationRule::NAME, $instance->id)) {
            $warningLocationAccommodationRule = new WarningLocationAccommodationRule($accommodation, $instance, $user);

            $validationValues = $warningLocationAccommodationRule->getValidationValues();

            if ($validationValues['config_value'] !== null) {
                return $warningLocationAccommodationRule;
            }
        }

        if ($this->ruleRepository->checkIfRuleExists(WarningAccommodationByLimitsRule::NAME, $instance->id)) {
            return new WarningAccommodationByLimitsRule($accommodation, $instance, $user);
        }

        if ($accommodation->isNational()) {
            return new WarningNationalAccommodationRule($accommodation, $instance, $user);
        }

        return new WarningInternationalAccommodationRule($accommodation, $instance, $user);
    }
}
