<?php


namespace App\Compliance\Instances\Amrest\Factories;


use App\Compliance\Instances\Amrest\Contracts\PlaneTripErrorInterface;
use App\Compliance\Instances\Amrest\Rules\ErrorNationalPlaneTripRule;
use App\PlaneTrip;
use App\User;


class PlaneTripErrorRuleFactory
{
    //TODO: change else to International

    /**
     * @param PlaneTrip $trip
     * @param User $user
     * @return PlaneTripErrorInterface
     * @throws \Throwable
     */
    public function create(PlaneTrip $trip, User $user): PlaneTripErrorInterface
    {
        if ($trip->isNational()) {
            return new ErrorNationalPlaneTripRule($trip, $trip->instance, $user);
        } else {
            return new ErrorNationalPlaneTripRule($trip, $trip->instance, $user);
        }
    }
}