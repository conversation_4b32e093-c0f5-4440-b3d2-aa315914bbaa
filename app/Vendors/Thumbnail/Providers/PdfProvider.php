<?php

namespace App\Vendors\Thumbnail\Providers;

use App\Vendors\Thumbnail\Contracts\ThumbnailProviderInterface;
use Imagick;

class PdfProvider extends ThumbnailProvider implements ThumbnailProviderInterface
{
    public function make()
    {
        if($this->CLIConvertExists()) {
            $this->makeByCLI();
        } else {
            $this->makeByLibrary();
        }
    }

    protected function makeByCLI(): void
    {
        exec("convert -density 288 ".$this->getOriginalFilePath() . "[0]"." -background white -alpha remove -filter lagrange -distort resize ".implode('x', $this->size)." -quality 95 -sampling-factor 1:1 ".$this->cachePath);
    }

    protected function makeByLibrary()
    {
        $im = new \Imagick($this->getOriginalFilePath() . "[0]"); // 0-first page, 1-second page
        $im->setImageColorspace(255); // prevent image colors from inverting
        $im->setimageformat("jpg");

        if ($im->getImageAlphaChannel()) {
            // Remove alpha channel
            $im->setImageAlphaChannel(11);

            // Set image background color
            $im->setImageBackgroundColor('white');

            // Merge layers
            $im->mergeImageLayers(imagick::LAYERMETHOD_FLATTEN);
        }

        $im->thumbnailimage($this->size[ 0 ], $this->size[ 1 ], true); // width and height
        $im->writeimage($this->cachePath);

        $im->clear();
        $im->destroy();
    }

    public function getOriginalFilePath(): string
    {
        $orgName = str_replace('-' . join('x', $this->size), '', $this->absolutePath);
        $orgName = pathinfo($orgName);

        return $orgName[ 'dirname' ] . DIRECTORY_SEPARATOR . $orgName[ 'filename' ];
    }

    protected function CLIConvertExists()
    {
        $returnVal = null;
        $out = [];

        exec('convert', $out, $returnVal);

        return $returnVal === 0;
    }

}