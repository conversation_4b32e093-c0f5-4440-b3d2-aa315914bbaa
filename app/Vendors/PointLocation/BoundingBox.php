<?php

declare(strict_types=1);

namespace App\Vendors\PointLocation;

use InvalidArgumentException;
use JsonException;

class BoundingBox
{
    private Point $southwest;
    private Point $northeast;

    public function __construct(Point $southwest, Point $northeast)
    {
        $this->southwest = $southwest;
        $this->northeast = $northeast;
    }

    public function getSouthwest(): Point
    {
        return $this->southwest;
    }

    public function getNortheast(): Point
    {
        return $this->northeast;
    }

    public static function fromArray(array $data): self
    {
        if (!isset($data['southwest'], $data['northeast'])) {
            throw new InvalidArgumentException("Invalid structure: 'southwest' and 'northeast' keys are required.");
        }

        $southwest = Point::create(
            (float) $data['southwest']['lat'],
            (float) $data['southwest']['lng']
        );

        $northeast = Point::create(
            (float) $data['northeast']['lat'],
            (float) $data['northeast']['lng']
        );

        return new self($southwest, $northeast);
    }

    /**
     * @throws JsonException
     */
    public function toJson(): string
    {
        $data = [
            'southwest' => [
                'lat' => $this->southwest->getLat(),
                'lng' => $this->southwest->getLng()
            ],
            'northeast' => [
                'lat' => $this->northeast->getLat(),
                'lng' => $this->northeast->getLng()
            ]
        ];

        return json_encode($data, JSON_THROW_ON_ERROR);
    }
}
