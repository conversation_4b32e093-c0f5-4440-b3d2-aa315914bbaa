<?php

declare(strict_types=1);

namespace App\Vendors\OCR\Services\Async\Drivers\Responses;

use App\Exceptions\Ocr\OcrCanNotGetTokenFromProviderException;
use Psr\Http\Message\StreamInterface;

class TokenResponse
{
    /** @var string */
    protected $token;

    public function __construct(string $token)
    {
        $this->token = $token;
    }

    public static function createFromSkanujToResponse(StreamInterface $response): TokenResponse
    {
        $result = \GuzzleHttp\json_decode($response);

        if ($result === null || isset($result->code) === false || $result->code != 10 || isset($result->token) === false) {
            throw OcrCanNotGetTokenFromProviderException::create(
                OcrCanNotGetTokenFromProviderException::DEFAULT_MESSAGE
            );
        }

        return new self($result->token);
    }

    public function getToken(): string
    {
        return $this->token;
    }
}