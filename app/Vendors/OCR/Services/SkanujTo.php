<?php


namespace App\Vendors\OCR\Services;

use App\Document;
use App\Exceptions\OcrDidNotRespondException;
use App\Vendors\OCR\AuthConfig;
use App\Vendors\OCR\Contracts\OcrServiceInterface;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class SkanujTo implements OcrServiceInterface
{
    const SLEEP_TIME = 15;
    /**
     * @var Document
     */
    protected $file;

    /**
     * @var \GuzzleHttp\Client
     */
    protected $client;
    /**
     * @var string
     */
    protected $documentId;
    /**
     * @var int
     */
    protected $safeLoopLimit = 20;
    /**
     * @var bool
     */
    protected $gotResponse = false;

    protected $baseUrl;

    /** @var AuthConfig */
    protected $authConfig;

    protected $token;
    protected $company;


    public function processImage(): string
    {
        if(!$this->file) {
            throw new \Exception('No file added');
        }

        $this->token = $this->getToken();
        $this->company = $this->getCompany();

        $docId = ($this->uploadDoc($this->file));

        if(!$docId)
            return ("Błąd podczas wysyłania pliku");

        $safeLoop = 0;
        $docs = [];
        $ocrDidNotRespond = false;

        while (true) {
            sleep(static::SLEEP_TIME);
            $docs = $this->getProcessedDocsList();
            if($safeLoop == $this->safeLoopLimit) {
                $ocrDidNotRespond = true;
                break;
            }
            if (count($docs)) {
                if (in_array($docId, $docs)) {
                    break;
                }
            }
            ++$safeLoop;
        }

        if($ocrDidNotRespond) {
            throw new OcrDidNotRespondException();
        }

        $content = "";

        if (in_array($docId, $docs)) {
            $content = ($this->getProcessedDoc($docId));
            $this->setDocumentStatus($docId, 1);
        }

        return json_encode($content);
    }

    public function setDocumentStatus($id, $status)
    {
        try {
            $response = $this->client->post($this->authConfig->getBaseUrl() . "document", [
                'headers' => [
                    "company_id" => $this->company,
                    "token" => $this->token
                ],
                'form_params' =>  [
                    'mode' => 'change-status',
                    'id' => $id,
                    'status' => $status
                ],
            ]);

            $result = \GuzzleHttp\json_decode($response->getBody());

        } catch (\Throwable $e) {
            return null;
        }

        return !(!$result || !isset($result->code) || $result->code != 10);
    }

    public function getProcessedDoc($id)
    {
        try {
            $response = $this->client->get($this->authConfig->getBaseUrl() . "document/mode/one-xt-pos?id=$id", [
                'headers' => [
                    "company_id" => $this->company,
                    "token" => $this->token
                ],
            ]);

            return \GuzzleHttp\json_decode($response->getBody());

        } catch (\Throwable $e) {
            return null;
        }
    }

    public function uploadDoc(Document $file)
    {
        $postFields = array(
            ['name' =>'mode', 'contents' => 'upload-file'],
            ['name' =>'multipages', 'contents' => 0]
        );

        if($file->accounting_type == Document::ACCOUNTING_TYPE_RECEIPT) {
            array_push($postFields, ['name' => 'extra', 'contents' => json_encode(['type' => 5])]);
        }

        $content = file_get_contents($this->file->getFilePath());
        try {
            $response = $this->client->post($this->authConfig->getBaseUrl() . "document", [
                'headers' => [
                    "company_id" => $this->company,
                    "token" => $this->token
                ],
                'multipart' => array_merge([
                    [
                        'contents' => $content,
                        'name'     => 'FILE',
                        'filename' => $this->file->file_name
                    ],
                ], $postFields),
            ]);

        } catch (RequestException $e) {
            return null;
        }

        if ($response->getStatusCode() != 200) {
            return null;
        }

        return \GuzzleHttp\json_decode($response->getBody(), true)['good-uploads'][0]['doc_id'] ?? null;
    }

    //Wszystkie niewyeksportowane
    public function getProcessedDocsList()
    {
        try {
            $response = $this->client->get($this->authConfig->getBaseUrl() . "document/mode/list-id-to-retrieve?status=0", [
                'headers' => [
                    "company_id" => $this->company,
                    "token" => $this->token
                ]
            ]);

        } catch (RequestException $e) {
            return null;
        }

        try {
            $result =  \GuzzleHttp\json_decode($response->getBody(), true);
            return is_array($result) ? $result : [];
        } catch (\Throwable $exception) {
            return [];
        }

    }

    protected function getToken()
    {
        try {
            $response = $this->client->post($this->authConfig->getBaseUrl() . "auth", [
                'form_params' => array(
                    'email' => $this->authConfig->getUserId(),
                    'password' => $this->authConfig->getPassword()
                ),
            ]);
            $result  = \GuzzleHttp\json_decode($response->getBody());

        } catch (\Throwable $e) {
            return null;
        }

        if (!$result || !isset($result->code) || $result->code != 10) {
            throw new \Exception('Invalid auth');
        }

        return $result->token;
    }

    public function getCompany()
    {
        try {
            $response = $this->client->post($this->authConfig->getBaseUrl() . "user", [
                'headers' => [
                    'token' => $this->token,
                ],
                'form_params' => array(
                    'mode' => 'get-user-company'
                ),
            ]);
            $result  = \GuzzleHttp\json_decode($response->getBody());

        } catch (\Throwable $e) {
            return null;
        }

        return $result[0]->id ?? null;
    }

    public function setHttpClient(Client $client) {
        $this->client = $client;
    }

    public function setFile(Document $file)
    {
        $this->file = $file;
    }

    public function setAuthConfig(AuthConfig $authConfig)
    {
        $this->authConfig = $authConfig;
    }

    public function setSafeLoopLimit($safeLoopLimit)
    {
        $this->safeLoopLimit = (int) $safeLoopLimit;
    }

    public function getFilePath()
    {
        return $this->file->getFilePath();
    }
}