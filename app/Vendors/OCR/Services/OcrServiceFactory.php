<?php


namespace App\Vendors\OCR\Services;


use App\Exceptions\IncorrectOcrServiceException;
use App\Vendors\OCR\Contracts\OcrServiceInterface;

/**
 * Class OcrServiceFactory
 * @package App\Vendors\OCR\Services
 */
class OcrServiceFactory
{
    /**
     * @param $service
     * @return OcrServiceInterface
     * @throws \Exception
     */
    public static function create($service): OcrServiceInterface
    {
        switch (strtolower($service)) {
            case 'nudelta': {
                $ocr = new NuDelta(false);
                break;
            }
            case 'skanujto': {
                $ocr = new SkanujTo();
                break;
            }
            case 'fake': {
                $ocr = new FakeOcr();
                break;
            }
            default: {
                throw new IncorrectOcrServiceException('OCR service '. $service . ' not found');
            }
        }

        return $ocr;
    }
}