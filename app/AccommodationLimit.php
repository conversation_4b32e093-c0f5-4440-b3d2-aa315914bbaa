<?php

declare(strict_types=1);

namespace App;

use App\Traits\CountryTrait;
use App\Traits\CurrencyTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * App\AccommodationLimit
 *
 * @property int id
 * @property string accommodation_limit
 * @property Currency currency
 * @property int|mixed country_id
 * @property int|null instance_id
 * @property Carbon start_date
 * @property Carbon end_date
 * @property int $id
 * @property int|null $instance_id
 * @property int $currency_id
 * @property int|null $country_id
 * @property string $accommodation_limit
 * @property \Illuminate\Support\Carbon|null $start_date
 * @property \Illuminate\Support\Carbon|null $end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Country|null $country
 * @property-read \App\Currency $currency
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereAccommodationLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereCurrencyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccommodationLimit whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class AccommodationLimit extends Model
{
    use CurrencyTrait;
    use CountryTrait;

    protected $dates =[
        'start_date',
        'end_date',
    ];
}
