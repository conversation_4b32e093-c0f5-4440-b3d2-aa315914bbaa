<?php

namespace App;

use App\Traits\DocumentTrait;
use App\Traits\InstanceTrait;
use App\Traits\UserTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * App\DocumentChange
 *
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $instance_id
 * @property int $document_id
 * @property int|null $user_id
 * @property array $changes
 * @property-read \App\Document $document
 * @property-read \App\Instance $instance
 * @property-read \App\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange whereChanges($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange whereDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentChange whereUserId($value)
 * @mixin \Eloquent
 */
class DocumentChange extends Model {
    use InstanceTrait;
    use UserTrait;
    use DocumentTrait;

    protected $fillable = [
        'changes',
        'instance_id',
        'document_id',
        'user_id',
        'changes',
    ];

    protected $casts = [
        'changes' => 'array',
    ];
}









