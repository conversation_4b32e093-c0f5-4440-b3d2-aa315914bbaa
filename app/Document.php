<?php

namespace App;

use App\DTO\Document\DocumentReceivedDateSettingDto;
use App\ElasticSearchConfigurators\DocumentIndexConfigurator;
use App\Enum\ERPDocumentType;
use App\Interfaces\DocumentElementChangeObeservableInterface;
use App\Interfaces\Models\SearchWithRulesInterface;
use App\Ocr\Enum\ColumnNameEnum;
use App\Ocr\InstanceOcr;
use App\Ocr\OcrHint;
use App\Traits\CurrencyTrait;
use App\Traits\FileUriTrait;
use App\Traits\InstanceTrait;
use App\Traits\MpkTrait;
use App\Traits\RequestTrait;
use App\Traits\SaveQuietlyTrait;
use App\Traits\TransactionTrait;
use App\Traits\UserTrait;
use App\Vendors\MoneyCalculator;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Modules\Accounting\Priv\Dtos\ErpAccountedDocumentDto;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Analytics\Priv\Entities\DocumentAccountDimensionItem;
use Modules\Analytics\Priv\Entities\Project;
use Modules\Analytics\Priv\Entities\Traits\ProjectTrait;
use Modules\Common\ValueObjects\CurrencyCode;
use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateStrategyResultDto;
use Modules\ExchangeRate\Pub\Enums\DocumentExchangeRateSourceEnum;
use Modules\ExchangeRate\Pub\Enums\DocumentExchangeRateStrategyEnum;
use Modules\ExchangeRate\Pub\Facades\ExchangeRateFacade;
use Modules\ExchangeRate\Pub\Facades\FxRateFacade;
use ScoutElastic\Searchable;

/**
 * App\Document
 *
 * @property int id
 * @property string slug
 * @property string name
 * @property int instance_id
 * @property mixed mpk_id
 * @property Mpk mpk
 * @property mixed project_id
 * @property Project project
 * @property int user_id
 * @property int request_id
 * @property string file_extension
 * @property string file_name
 * @property string file_path
 * @property string status
 * @property mixed ocr_data
 * @property mixed document_accounting_number
 * @property mixed document_number
 * @property mixed corrected_document_number
 * @property Carbon issue_date
 * @property mixed annotation
 * @property mixed received_date
 * @property Carbon|null vat_date the VAT due date (set from "received_date" by default)
 * @property mixed gross
 * @property mixed exchange_rate
 * @property Carbon|null exchange_rate_date
 * @property mixed payment
 * @property mixed ocr_time
 * @property Carbon|null ocr_started_at
 * @property Carbon|null ocr_finished_at
 * @property string|null ocr_file_md5
 * @property string|null ocr_provider_id
 * @property string|null ocr_error
 * @property mixed accounting_date
 * @property mixed file_optimized
 * @property mixed type
 * @property Currency currency
 * @property Collection elements
 * @property mixed documentable
 * @property mixed accounting_type
 * @property mixed currency_id
 * @property Carbon|null settled_at
 * @property mixed accounted_at
 * @property mixed updated_at
 * @property string title
 * @property bool requested
 * @property bool self_generated
 * @property int provider_id
 * @property Provider provider
 * @property bool|null is_margin
 * @property Carbon|null erp_accounted_at
 * @property Instance instance
 * @property string|null erp_id
 * @property int debit_account_id
 * @property int credit_account_id
 * @property int credit_accounting_account_id
 * @property AccountingAccount creditAccount
 * @property AccountingAccount debitAccount
 * @property AccountingAccount creditAccountingAccount
 * @property DocumentExchangeRateSourceEnum|null exchange_rate_source
 * @property DocumentExchangeRateStrategyEnum|null exchange_rate_strategy
 * @property Collection<DocumentAccountDimensionItem> accountDimensionItems
 * @property int $id
 * @property string $slug
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $instance_id
 * @property int|null $mpk_id
 * @property int|null $project_id
 * @property int $user_id
 * @property int $request_id
 * @property string|null $type
 * @property string $name
 * @property string $file_path
 * @property string $file_name
 * @property string $file_optimized
 * @property string $file_extension
 * @property string|null $ocr_data
 * @property int|null $ocr_time
 * @property string|null $ocr_started_at
 * @property string|null $ocr_finished_at
 * @property string|null $ocr_error
 * @property string|null $ocr_provider_id
 * @property string|null $ocr_file_md5
 * @property string|null $status
 * @property int $ocr_retry
 * @property int|null $debit_account_id
 * @property int|null $credit_account_id
 * @property int|null $credit_accounting_account_id
 * @property int|null $provider_id
 * @property string|null $document_number
 * @property string|null $corrected_document_number
 * @property string|null $document_accounting_number
 * @property \Illuminate\Support\Carbon|null $issue_date
 * @property \Illuminate\Support\Carbon|null $received_date
 * @property \Illuminate\Support\Carbon|null $vat_date
 * @property \Illuminate\Support\Carbon|null $accounting_date
 * @property string|null $annotation
 * @property float|null $gross
 * @property int|null $currency_id
 * @property string|null $exchange_rate
 * @property \Illuminate\Support\Carbon|null $exchange_rate_date
 * @property DocumentExchangeRateStrategyEnum $exchange_rate_strategy
 * @property DocumentExchangeRateSourceEnum $exchange_rate_source
 * @property string|null $payment
 * @property string|null $accounting_type
 * @property bool $requested
 * @property int $readonly
 * @property bool $self_generated
 * @property string|null $settled_at
 * @property string|null $accounted_at
 * @property \Illuminate\Support\Carbon|null $erp_accounted_at
 * @property string|null $erp_id
 * @property int|null $is_margin
 * @property bool $is_divided_for_mpk
 * @property-read Collection|\App\Accomodation[] $accomodations
 * @property-read int|null $accomodations_count
 * @property-read Collection|DocumentAccountDimensionItem[] $accountDimensionItems
 * @property-read int|null $account_dimension_items_count
 * @property-read Collection|\App\DocumentAttachment[] $attachments
 * @property-read int|null $attachments_count
 * @property-read Collection|\App\BusTrip[] $busTrips
 * @property-read int|null $bus_trips_count
 * @property-read Collection|\App\CompanyCarTrip[] $companyCarTrips
 * @property-read int|null $company_car_trips_count
 * @property-read Collection|\App\Cost[] $costs
 * @property-read int|null $costs_count
 * @property-read AccountingAccount|null $creditAccount
 * @property-read AccountingAccount|null $creditAccountingAccount
 * @property-read \App\Currency|null $currency
 * @property-read AccountingAccount|null $debitAccount
 * @property-read Collection|\App\DocumentElement[] $elements
 * @property-read int|null $elements_count
 * @property-read Collection|\App\FerryBoatTrip[] $ferryBoatTrips
 * @property-read int|null $ferry_boat_trips_count
 * @property \ScoutElastic\Highlight|null $highlight
 * @property-read string|null $title
 * @property-read \App\Instance $instance
 * @property-read Mpk|null $mpk
 * @property-read Collection|\App\Ocr\OcrHint[] $ocrHints
 * @property-read int|null $ocr_hints_count
 * @property-read Collection|\App\PlaneTrip[] $planeTrips
 * @property-read int|null $plane_trips_count
 * @property-read Project|null $project
 * @property-read \App\Provider|null $provider
 * @property-read Collection|\App\RentedCarTrip[] $rentedCarTrips
 * @property-read int|null $rented_car_trips_count
 * @property-read \App\Request $request
 * @property-read Collection|\App\TrainTrip[] $trainTrips
 * @property-read int|null $train_trips_count
 * @property-read \App\Transaction\Transaction|null $transaction
 * @property-read Collection|\App\UnrequestedElement[] $unrequestedElements
 * @property-read int|null $unrequested_elements_count
 * @property-read \App\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Document newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Document newQuery()
 * @method static \Illuminate\Database\Query\Builder|Document onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Document query()
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereAccountedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereAccountingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereAccountingType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereAnnotation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCorrectedDocumentNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCreditAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCreditAccountingAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCurrencyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDebitAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDocumentAccountingNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDocumentNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereErpAccountedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereErpId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereExchangeRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereExchangeRateDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereExchangeRateSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereExchangeRateStrategy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereFileExtension($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereFileOptimized($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereGross($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereIsMargin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereIssueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMpkId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereOcrData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereOcrError($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereOcrFileMd5($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereOcrFinishedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereOcrProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereOcrRetry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereOcrStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereOcrTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document wherePayment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereProjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereReadonly($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereReceivedDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereRequested($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereSelfGenerated($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereSettledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVatDate($value)
 * @method static \Illuminate\Database\Query\Builder|Document withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Document withoutTrashed()
 * @mixin \Eloquent
 * @property-read \App\DocumentProvider|null $documentProvider
 * @property float|null $net
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereNet($value)
 * @property float|null $tax
 * @property float|null $reverse_tax
 * @method static Builder|Document hasProvider($providerId)
 * @method static Builder|Document whereDocumentNumberStrict($documentNumber)
 * @method static Builder|Document whereTax($value)
 * @property-read Collection|Mpk[] $hasMpks
 * @property-read int|null $has_mpks_count
 * @property-read Collection|\App\PassengerCarTrip[] $passengerCarTrips
 * @property-read int|null $passenger_car_trips_count
 * @property-read Collection|\App\ReplacementCarTrip[] $replacementCarTrips
 * @property-read int|null $replacement_car_trips_count
 * @method static Builder|Document invoices()
 * @method static Builder|Document issuedBetween(\Carbon\Carbon $from, \Carbon\Carbon $to)
 * @method static Builder|Document notFromBilling()
 * @method static Builder|Document whereIsDividedForMpk($value)
 * @property-read Collection|InstanceOcr[] $instanceOcr
 * @property-read int|null $instance_ocr_count
 * @method static Builder|Document whereReverseTax($value)
 */
class Document extends Model implements DocumentElementChangeObeservableInterface, SearchWithRulesInterface,
                                        HasManyMpkModelInterface
{
    use InstanceTrait;
    use UserTrait;
    use RequestTrait;
    use CurrencyTrait;
    use SoftDeletes;
    use FileUriTrait;
    use TransactionTrait;
    use MpkTrait;
    use ProjectTrait;
    use HasManyMpkModelTrait;

    use Searchable;
    use SaveQuietlyTrait;

    public const RELATION_NAME = 'document';
    /** Max file size in kB */
    const MAX_FILE_SIZE = 32768;
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_WAITING_FOR_METADATA = 'wfm';
    const STATUS_PROCESSED = 'processed';
    const STATUS_FAILED = 'failed';
    const STATUS_UNPROCESSABLE = 'unprocessable';
    const TYPE_ACCOUNTING = 'accounting';
    const TYPE_TRAVEL = 'travel';
    const TYPE_CONFIRMATION = 'confirmation';
    const PAYMENT_TYPE_OWN = 'own';
    const PAYMENT_TYPE_SERVICE_CARD = 'service_card';
    const PAYMENT_TYPE_CORPORATE_CARD = 'corporate_card';
    const PAYMENT_TYPE_TRANSFER = 'transfer';
    const ACCOUNTING_STATUS_DONE = 'accounted';
    const ACCOUNTING_STATUS_PENDING = 'pending';
    const ACCOUNTING_TYPE_CORRECTING_INVOICE = 'correcting_invoice'; // środki własne (gotowka)
    const ACCOUNTING_TYPE_INVOICE = 'invoice'; // karta imienna (służbowa)
    const ACCOUNTING_TYPE_RECEIPT = 'receipt';
    const ACCOUNTING_TYPE_EXCLUDED = 'excluded';
    private static ?ExchangeRateFacade $exchangeRateFacade = null;
    protected $indexConfigurator = DocumentIndexConfigurator::class;
    protected $with = ['documentProvider'];
    protected $searchRules = [
        //
    ];
    protected $mapping = [
        'properties' => [

        ]
    ];
    protected $fillable = [
        'request_id',
        'name',
        'file_path',
        'file_name',
        'file_extension',
        'type',
        'provider_id',
        'document_number',
        'corrected_document_number',
        'document_accounting_number',
        'issue_date',
        'received_date',
        'vat_date',
        'annotation',
        'gross',
        'net',
        'currency_id',
        'exchange_rate',
        'payment',
        'accounting_type',
        'debit_account_id',
        'credit_account_id',
        'requested',
        'readonly',
        'is_margin',
        'instance_id',
        'mpk_id',
        'project_id',
        'is_divided_for_mpk',
    ];
    protected $touches = [
        'request',
    ];
    protected $dates = [
        'issue_date',
        'received_date',
        'vat_date',
        'accounting_date',
        'deleted_at',
        'erp_accounted_at',
        'exchange_rate_date'
    ];
    protected $casts = [
        'gross' => 'float',
        'net' => 'float',
        'tax' => 'float',
        'reverse_tax' => 'float',
        'requested' => 'boolean',
        'self_generated' => 'boolean',
        'is_divided_for_mpk' => 'boolean',
    ];

    public static function getTypes()
    {
        return collect([
            static::TYPE_ACCOUNTING,
            static::TYPE_TRAVEL,
        ]);
    }

    public static function getAccountingTypes()
    {
        return collect([
            static::ACCOUNTING_TYPE_CORRECTING_INVOICE,
            static::ACCOUNTING_TYPE_INVOICE,
            static::ACCOUNTING_TYPE_RECEIPT,
        ]);
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public static function getPaymentTypes()
    {
        return collect([
            static::PAYMENT_TYPE_OWN,
            static::PAYMENT_TYPE_SERVICE_CARD,
            static::PAYMENT_TYPE_CORPORATE_CARD,
            static::PAYMENT_TYPE_TRANSFER,
        ]);
    }

    public static function getStatuses()
    {
        return collect([
            static::STATUS_PENDING,
            static::STATUS_PROCESSED,
        ]);
    }

    public static function relationships()
    {
        return collect([
            Accomodation::RELATION_NAME => 'accomodations',
            CompanyCarTrip::RELATION_NAME => 'companyCarTrips',
            ReplacementCarTrip::RELATION_NAME => 'replacementCarTrips',
            PassengerCarTrip::RELATION_NAME => 'passengerCarTrips',
            Cost::RELATION_NAME => 'costs',
            PlaneTrip::RELATION_NAME => 'planeTrips',
            RentedCarTrip::RELATION_NAME => 'rentedCarTrips',
            TrainTrip::RELATION_NAME => 'trainTrips',
            FerryBoatTrip::RELATION_NAME => 'ferryBoatTrips',
            BusTrip::RELATION_NAME => 'busTrips',
            UnrequestedElement::RELATION_NAME => 'unrequestedElements',
        ]);
    }

    public static function searchWithRules(string $query, ?string $searchType = null, ?callable $callback = null)
    {
        return self::search($query, $callback);
    }

    //polymorphic relationships to request elements

    public function toSearchableArray()
    {
        $arr = array_only($this->toArray(), [
            'id',
            'name',
            'document_number',
            'document_accounting_number',
            'issue_date',
            'received_date',
            'accounting_date',
            'annotation',
            'gross',
            'net',
            'tax',
        ]);
        $arr['elements'] = [];
        $arr['user'] = [];

        $this->elements->each(function (DocumentElement $element) use (&$arr) {
            $arr['elements'][] = $element->toSearchableArray();
        });

        if ($this->user) {
            $arr['user'] = $this->user->toSearchableArray();
        }

        return $arr;
    }

    public function accomodations()
    {
        return $this->morphedByMany(Accomodation::class, 'element', 'request_element_document');
    }

    public function companyCarTrips()
    {
        return $this->morphedByMany(CompanyCarTrip::class, 'element', 'request_element_document');
    }

    public function costs()
    {
        return $this->morphedByMany(Cost::class, 'element', 'request_element_document');
    }

    public function planeTrips()
    {
        return $this->morphedByMany(PlaneTrip::class, 'element', 'request_element_document');
    }

    public function rentedCarTrips()
    {
        return $this->morphedByMany(RentedCarTrip::class, 'element', 'request_element_document');
    }

    public function replacementCarTrips()
    {
        return $this->morphedByMany(ReplacementCarTrip::class, 'element', 'request_element_document');
    }

    public function passengerCarTrips()
    {
        return $this->morphedByMany(PassengerCarTrip::class, 'element', 'request_element_document');
    }

    public function trainTrips()
    {
        return $this->morphedByMany(TrainTrip::class, 'element', 'request_element_document');
    }

    public function ferryBoatTrips()
    {
        return $this->morphedByMany(FerryBoatTrip::class, 'element', 'request_element_document');
    }

    public function busTrips()
    {
        return $this->morphedByMany(BusTrip::class, 'element', 'request_element_document');
    }

    public function unrequestedElements()
    {
        return $this->morphedByMany(UnrequestedElement::class, 'element', 'request_element_document');
    }

    public function accountDimensionItems()
    {
        return $this->hasMany(DocumentAccountDimensionItem::class);
    }

    public function requestElements($relationships = [])
    {
        $elements = collect();

        Document::relationships()->each(function ($relation) use (&$elements, $relationships) {
            $elements = $elements->merge($this->{$relation}()->with($relationships)->get());
        });

        return $elements;
    }

    //polymorphic relationships to request elements -end

    public function requestElementsPivot()
    {
        return RequestElementDocument::where('document_id', '=', $this->id)->get();
    }

    public function elements()
    {
        return $this->hasMany(DocumentElement::class);
    }

    public function ocrHints()
    {
        return $this->hasMany(OcrHint::class);
    }

    public function attachments()
    {
        return $this->hasMany(DocumentAttachment::class);
    }

    public function creditAccount()
    {
        return $this->belongsTo(AccountingAccount::class, 'credit_account_id');
    }

    public function debitAccount()
    {
        return $this->belongsTo(AccountingAccount::class, 'debit_account_id');
    }

    public function creditAccountingAccount(): BelongsTo
    {
        return $this->belongsTo(AccountingAccount::class, 'credit_accounting_account_id');
    }

    public function getFilePath()
    {
        return base_path(str_replace('storage/', 'storage/app/public/', $this->file_path)) . '/' . $this->file_name;
    }

    public function getRealFilePath()
    {
        return base_path(str_replace('storage/', 'storage/app/public/', $this->file_path)) . '/' . $this->file_name;
    }

    public function setIssueDateAttribute($issueDate)
    {
        $this->attributes['issue_date'] = is_null($issueDate) === false ? Carbon::createFromTimestamp(
            strtotime($issueDate)
        ) : null;

        if (!$this->vat_date && $this->attributes['issue_date']) {
            $this->vat_date = $this->attributes['issue_date'];
        }
    }

    public function setVatDateAttribute($vatDate)
    {
        $this->attributes['vat_date'] = is_null($vatDate) === false ? Carbon::createFromTimestamp(
            strtotime($vatDate)
        ) : null;
    }

    /**
     * Attribute setter for "received_date"
     *
     * @param mixed $receivedDate the received date value
     */
    public function setReceivedDateAttribute($receivedDate)
    {
        $this->attributes['received_date'] = is_null($receivedDate) === false ? Carbon::createFromTimestamp(
            strtotime($receivedDate)
        ) : null;
    }

    public function chooseReceiveDate(): Carbon
    {
        $documentReceivedDateSettingDto = DocumentReceivedDateSettingDto::createFromSettings($this->instance);

        return $documentReceivedDateSettingDto->isIssueDate() && !empty($this->issue_date) ?
            $this->issue_date : Carbon::now();
    }

    public function getConvertedGross(): ?string
    {
        if (!$this->getExchangeRate() || !$this->gross) {
            $hint = $this->ocrHints->firstWhere('column', 'gross');

            if ($hint) {
                return $hint->label;
            }

            return null;
        }

        return MoneyCalculator::convert((string)$this->gross, (string)$this->getExchangeRate());
    }

    public function getExchangeRateDto(): ?ExchangeRateStrategyResultDto
    {
        try {
            if (!self::$exchangeRateFacade instanceof ExchangeRateFacade) {
                self::$exchangeRateFacade = resolve(ExchangeRateFacade::class);
            }

            return self::$exchangeRateFacade->getForDocumentAndUser($this, \Auth::user() ? \Auth::user() : $this->user);
        } catch (\Throwable $exception) {
            Log::error($exception);

            return null;
        }
    }

    public function getExchangeRate(): ?string
    {
        if ($this->exchange_rate) {
            return $this->exchange_rate;
        }

        $exchangeRateDto = $this->getExchangeRateDto();

        return $exchangeRateDto instanceof ExchangeRateStrategyResultDto ? (string)$exchangeRateDto->getRate() : '1.00';
    }

    public function getLastExchangeRate()
    {
        return resolve(FxRateFacade::class)->findLastRate(
            $this->instance->exchange_rate_provider,
            new CurrencyCode($this->currency->code)
        );
    }

    public function canUserEditExchangeRate(): bool
    {
        $exchangeRateDto = $this->getExchangeRateDto();

        if ($exchangeRateDto instanceof ExchangeRateStrategyResultDto) {
            return $exchangeRateDto->isEditable();
        }

        return false;
    }

    public function getDocumentAccountingNumberAttribute()
    {
        $uid = '';

        if ($this->type === static::TYPE_ACCOUNTING) {
            $uid = 'A';
        } elseif ($this->type === static::TYPE_TRAVEL) {
            $uid = 'T';
        }

        return $uid . str_pad($this->id, 8, 0, STR_PAD_LEFT);
    }

    public function removeTravelElement($element)
    {
        RequestElementDocument::where([
            'document_id' => $this->id,
            'element_id' => $element['id'],
            'element_type' => $element['type']
        ])->delete();
    }

    public function getTitleAttribute(): ?string
    {
        $type = $this->type ?? static::TYPE_ACCOUNTING;
        $fn = 'get' . ucfirst($type) . 'DocumentTitle';

        if (method_exists($this, $fn)) {
            return $this->{$fn}();
        }

        return null;
    }

    public function account(
        ?int $providerId,
        string $providerCountryCode,
        string $providerName,
        string $providerTaxId,
        string $providerErpId,
        string $providerAddress,
        string $providerCity,
        string $providerPostcode
    ) {
        $this->provider_id = $providerId;
        $this->documentProvider()->updateOrCreate([], [
            'country_code' => $providerCountryCode,
            'name' => $providerName,
            'tax_id' => $providerTaxId,
            'erp_id' => $providerErpId,
            'address' => $providerAddress,
            'city' => $providerCity,
            'postcode' => $providerPostcode,
            'provider_id' => $providerId
        ]);
    }

    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class)->withTrashed();
    }

    public function documentProvider(): HasOne
    {
        return $this->hasOne(DocumentProvider::class);
    }

    public function isReadyToAccounting(): bool
    {
        return $this->documentProvider || $this->provider;
    }

    public function hasProvider(): bool
    {
        return $this->documentProvider && $this->documentProvider->erp_id;
    }

    public function netSettledAmount(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => (string)$element->net)
            ->filter()
            ->toArray()
        );
    }

    public function prepareDocumentForAccounting(): void
    {
        $this->splitElementsForMpk();

        $this->elements->each(function (DocumentElement $element) {
            $element->splitDeductibility();
        });
    }

    public function splitElementsForMpk(): void
    {
        if ($this->is_divided_for_mpk) {
            return;
        }

        $mpks = $this->hasMpks;
        if ($mpks->count() <= 1) {
            return;
        }

        $originalElements = $this->elements;
        $this->elements()->delete();

        foreach ($mpks as $mpkIndex => $mpk) {
            $percentage = $mpk->pivot->percentage;
            foreach ($originalElements as $elementIndex => $element) {
                $roundingMode = ($elementIndex + $mpkIndex) % 2 ? PHP_ROUND_HALF_UP : PHP_ROUND_HALF_DOWN;

                $newElement = $element->replicate(['id']);
                $newElement->mpk_id = $mpk->id;
                $newElement->gross = MoneyCalculator::percentageOfAndRound(
                    $element->gross,
                    $percentage,
                    2,
                    $roundingMode
                );

                $newElement->recalculateDeducible();
                $newElement->save();
            }
        }

        $this->recalculate();
        $this->refresh();
        $this->is_divided_for_mpk = true;
        $this->save();
    }

    public function getMpksAggregatedRequestTravelers(): HasManyMpkCollection
    {
        $mpks = HasManyMpkCollection::createFromEntity($this->request);

        if (!$this->isFromBilling()) {
            return $mpks;
        }

        $request = $this->request;
        $requestAuthorId = $request->user->id;

        foreach ($request->travelers as $traveler) {
            if ($requestAuthorId === $traveler->requestTraveler->id) {
                continue;
            }

            $userMpks = HasManyMpkCollection::createFromEntity($traveler->requestTraveler);
            $mpks->merge($userMpks);
        }

        return $mpks;
    }

    public function getBaseNetAmountInDocumentCurrency(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => $element->getOriginalNet())
            ->filter()
            ->toArray()
        );
    }

    public function getBaseNetAmount(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => $element->getBaseNetInDefaultInstanceCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function getBaseTaxAmount(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => $element->getOriginalTax())
            ->filter()
            ->toArray()
        );
    }

    public function getBaseTaxInDefaultInstanceCurrency(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => $element->getBaseTaxInDefaultInstanceCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function getVatInDefaultInstanceCurrency(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => $element->getVatInDefaultInstanceCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function getVatInDocumentCurrency(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => $element->getVatInDocumentCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function getOriginalTaxAmount(): string
    {
        return MoneyCalculator::sub((string)$this->gross, (string)$this->net);
    }

    public function getOriginalConvertedTaxAmount(): string
    {
        return MoneyCalculator::convert(
            $this->getOriginalTaxAmount(),
            (string)$this->getExchangeRate()
        );
    }

    public function getOriginalConvertedNetAmount(): string
    {
        return MoneyCalculator::convert(
            (string)$this->net,
            (string)$this->getExchangeRate()
        );
    }

    public function grossSettledAmount(): string
    {
        if ($this->isTravelDocument()) {
            return (string)$this->gross;
        }

        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => (string)$element->gross)
            ->filter()
            ->toArray()
        );
    }

    public function netConvertedSettledAmount(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => (string)$element->getNetInDefaultInstanceCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function grossConvertedSettledAmount(): string
    {
        if ($this->isTravelDocument()) {
            return MoneyCalculator::convert(
                (string)$this->gross,
                (string)$this->getExchangeRate()
            );
        }

        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $element) => (string)$element->getGrossInDefaultInstanceCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function setAsUnaccounted(): Document
    {
        $this->accounted_at = null;

        return $this;
    }

    public function getExpensesTypes(): ?array
    {
        $expensesTypes = collect();

        $this->elements->each(function ($documentElement) use ($expensesTypes) {
            $expensesTypes->push($documentElement->type->short_name);
        });

        return $expensesTypes->toArray();
    }

    public function getOcrHints(): Collection
    {
        return $this->ocrHints;
    }

    public function getFilteredOcrHints()
    {
        $requestStatus = $this->request->status;
        return $this->ocrHints->filter(function (OcrHint $ocrHint) use ($requestStatus) {
            if ($requestStatus === Request::STATUS_ACCOUNTING) {
                return $ocrHint->column != ColumnNameEnum::PROVIDER_SUGGESTED()->getValue();
            }

            return !in_array(
                $ocrHint->column,
                [ColumnNameEnum::PROVIDER_SUGGESTED()->getValue(), ColumnNameEnum::PROVIDER_ID()->getValue()]
            );
        })->values();
    }

    public function getProvidersSuggested()
    {
        return $this->request->status === Request::STATUS_ACCOUNTING ? $this->ocrHints->filter(
            function (OcrHint $ocrHint) {
                return $ocrHint->column === ColumnNameEnum::PROVIDER_SUGGESTED()->getValue() && $ocrHint->accepted === null;
            }
        )->values() : [];
    }

    public function getPurchaserSuggested(): Collection
    {
        return $this->getOcrHints()->filter(
            function (OcrHint $ocrHint) {
                return $ocrHint->column === ColumnNameEnum::PURCHASER_SUGGESTED()->getValue();
            }
        )->values();
    }

    public function isMargin(): bool
    {
        return (bool)$this->is_margin;
    }

    public function hasNonZeroVatRates(): bool
    {
        /** @var DocumentElement $element */
        foreach ($this->elements as $element) {
            if ($element->vatNumber->value > 0) {
                return true;
            }
        }
        return false;
    }

    public function isInovice(): bool
    {
        return $this->accounting_type === self::ACCOUNTING_TYPE_INVOICE || $this->accounting_type === self::ACCOUNTING_TYPE_CORRECTING_INVOICE;
    }

    public function getERPType(): ERPDocumentType
    {
        if ($this->isMargin()) {
            // margin types
            if ($this->accounting_type === static::ACCOUNTING_TYPE_INVOICE) {
                return ERPDocumentType::INVOICE_MARGIN();
            }

            if ($this->accounting_type === static::ACCOUNTING_TYPE_CORRECTING_INVOICE) {
                return ERPDocumentType::INVOICE_MARGIN_CORRECTION();
            }
        } else {
            if ($this->accounting_type === static::ACCOUNTING_TYPE_RECEIPT) {
                return ERPDocumentType::RECEIPT();
            }

            // non-margin types
            if ($this->accounting_type === static::ACCOUNTING_TYPE_INVOICE) {
                return ERPDocumentType::INVOICE();
            }

            if ($this->accounting_type === static::ACCOUNTING_TYPE_CORRECTING_INVOICE) {
                return ERPDocumentType::INVOICE_CORRECTION();
            }
        }

        throw new \LogicException('Cannot resolve document ERPDocumentType for document id: ' . $this->id);
    }

    public function isFromBilling(): bool
    {
        return $this->self_generated || $this->readonly;
    }

    public function haveDocumentElementsVatTypes(): bool
    {
        return $this->elements->every(function (DocumentElement $documentElement) {
            return is_numeric($documentElement->vat_number_id);
        });
    }

    public function isSettled(): bool
    {
        if (is_null($this->settled_at) === false && is_null($this->document_number) === false
            && is_null($this->payment) === false && $this->type === self::TYPE_ACCOUNTING
            && is_null($this->gross) === false && is_null($this->currency_id) === false
            && $this->elements()->get()->filter(function (DocumentElement $element) {
                return is_null($element->gross) === false
                    && is_null($element->request_element_type) === false
                    && is_null($element->request_element_id) === false;
            })->count() === $this->elements()->count()
        ) {
            return true;
        }

        return false;
    }

    public function isInAccounting(): bool
    {
        return $this->request->isInAccounting();
    }

    public function isAccounted(): bool
    {
        return $this->isSettled() === true && is_null($this->accounted_at) === false;
    }

    /**
     * @return string VAT amount in instance currency
     */
    public function getVatAmount(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $de) => (string)$de->getVatInDefaultInstanceCurrency())
            ->filter()
            ->toArray()
        );
    }

    /**
     * @return string net amount in INSTANCE currency
     */
    public function getNetAmount(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $de) => (string)$de->getNetInDefaultInstanceCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function getGrossAmountInInstanceCurrency(): string
    {
        return MoneyCalculator::convert((string)$this->gross, (string)$this->getExchangeRate());
    }

    public function getNetAmountInDocumentCurrency(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $de) => (string)$de->getNetInDocumentCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function getVATAmountInDocumentCurrency(): string
    {
        return MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(fn(DocumentElement $de) => (string)$de->getVatInDocumentCurrency())
            ->filter()
            ->toArray()
        );
    }

    public function getGrossAmountInDocumentCurrency(): string
    {
        return (string)$this->gross;
    }

    public function ocrProviderNotFound(): bool
    {
        return $this->provider_id === null || (!empty($this->provider->erp_id) && $this->request->user->erp_id === $this->provider->erp_id);
    }

    public function setErpAccounted(ErpAccountedDocumentDto $dto): void
    {
        if (!$this->canBeAccounted()) {
            throw new \DomainException(
                sprintf('Document cannot be accounted in current request\'s status [%s]', $this->request->status)
            );
        }

        $this->erp_id = $dto->getErpId();
        $this->erp_accounted_at = $dto->getErpAccountedAt();
        $this->vat_date = $dto->getErpVatAt();
    }

    public function canBeAccounted(): bool
    {
        return $this->request->canBeAccounted();
    }

    public function getExchangeRateSourceAttribute(): DocumentExchangeRateSourceEnum
    {
        return new DocumentExchangeRateSourceEnum($this->attributes['exchange_rate_source']);
    }

    public function setExchangeRateSourceAttribute(string $documentExchangeRateSourceEnum): Document
    {
        $this->attributes['exchange_rate_source'] = (string)(new DocumentExchangeRateSourceEnum(
            $documentExchangeRateSourceEnum
        ));

        return $this;
    }

    public function setNetAttribute($value): void
    {
        $this->attributes['net'] = $value;
        $this->recalculateTax();
    }

    public function setGrossAttribute($value): void
    {
        $this->attributes['gross'] = $value;
        $this->recalculateNet();
        $this->recalculateTax();
    }

    public function getExchangeRateStrategyAttribute(): DocumentExchangeRateStrategyEnum
    {
        return new DocumentExchangeRateStrategyEnum($this->attributes['exchange_rate_strategy']);
    }

    public function setExchangeRateStrategyAttribute(string $documentExchangeRateStrategyEnum): Document
    {
        $this->attributes['exchange_rate_strategy'] = (string)(new DocumentExchangeRateStrategyEnum(
            $documentExchangeRateStrategyEnum
        ));

        return $this;
    }

    public function getFrontendUrl()
    {
        return sprintf(
            'http://%s/requests/%s/%s/documents/%s/account',
            trim($this->instance->domain, '/'),
            $this->request->type,
            $this->request->slug,
            $this->id
        );
    }

    public function recalculate(
        $withReverseTax = false,
        ?string $reverseGross = null
    ): void {
        $this->recalculateNet();
        $this->recalculateTax($withReverseTax, $reverseGross);
    }

    public function scopeHasProvider(Builder $query, int $providerId): void
    {
        $query->whereProviderId($providerId);
    }

    public function scopeWhereDocumentNumberStrict(Builder $query, string $documentNumber): void
    {
        $query->whereRaw("REPLACE(document_number, ' ', '') = ?", [str_replace(' ', '', $documentNumber)]);
    }

    public function hasMpks(): BelongsToMany
    {
        return $this->belongsToMany(Mpk::class, 'document_has_mpk')
            ->withPivot(['percentage', 'main']);
    }

    public function scopeInvoices(Builder $query): void
    {
        $query->where('type', self::TYPE_ACCOUNTING);
    }

    public function scopeNotFromBilling(Builder $query): void
    {
        $query->where('self_generated', false);
        $query->where('readonly', false);
    }

    public function scopeIssuedBetween(Builder $query, Carbon $from, Carbon $to): void
    {
        $query->whereBetween('issue_date', [$from, $to]);
    }

    protected function getAccountingDocumentTitle(): ?string
    {
        $title = $this->annotation;

        $hints = $this->ocrHints;

        if ($this->provider !== null) {
            $title = $this->provider->name;
        } else {
            $provider = $hints->firstWhere('column', 'provider_id');

            if ($provider) {
                $title = $provider->label;
            }
        }

        return $title;
    }

    protected function getTravelDocumentTitle(): ?string
    {
        return $this->annotation ?: trans('document.travel-document-title');
    }

    private function recalculateTax(
        $withReverseTax = false,
        ?string $reverseGross = null
    ): void {
        $this->attributes['tax'] = null;
        $this->attributes['reverse_tax'] = null;

        if (null !== $this->gross && null !== $this->net) {
            if ($withReverseTax && null !== $reverseGross) {
                $this->attributes['reverse_tax'] = MoneyCalculator::taxFromGrossAndNet(
                    $reverseGross,
                    $this->getGrossAmountInInstanceCurrency()
                );
            } else {
                $this->attributes['tax'] = MoneyCalculator::taxFromGrossAndNet(
                    (string)$this->gross,
                    (string)$this->net
                );
            }
        }
    }

    private function recalculateNet(): void
    {
        if ($this->elements->count() === 0) {
            $this->attributes['net'] = null;
            return;
        }

        $this->attributes['net'] = MoneyCalculator::sumAndRoundHalfUp(
            ...$this->elements
            ->map(function (DocumentElement $element) {
                return $element->getOriginalNet();
            })
            ->filter()
            ->toArray()
        );
    }

    public function shouldBeOcrProcessed(): bool
    {
        return $this->status === self::STATUS_PENDING
            && in_array($this->accounting_type, [null, self::ACCOUNTING_TYPE_INVOICE, self::ACCOUNTING_TYPE_RECEIPT])
            && in_array($this->type, [self::TYPE_ACCOUNTING])
            && false === $this->isFromBilling();
    }

    public function ocrStart(): void
    {
        $this->ocr_started_at = now();
        $this->status = self::STATUS_PENDING;
        $this->accounting_type = $this->accounting_type ?: Document::ACCOUNTING_TYPE_RECEIPT;
    }

    public function ocrSuccess(array $data): void
    {
        $this->ocr_data = json_encode($data);
        $this->ocr_finished_at = now();
        $this->ocr_time = $this->ocr_finished_at->timestamp - $this->ocr_started_at->timestamp;
        $this->status = self::STATUS_PROCESSED;
        $this->ocr_error = '';
    }

    public function ocrFail(string $error): void
    {
        $this->status = self::STATUS_FAILED;
        $this->ocr_error = $error;
        $this->ocr_finished_at = now();
        $this->ocr_time = $this->ocr_finished_at->timestamp - $this->ocr_started_at->timestamp;
    }

    public function instanceOcr(): HasManyThrough
    {
        return $this->hasManyThrough(
            InstanceOcr::class,
            Instance::class,
            'id',
            'instance_id',
            'instance_id'
        );
    }

    public function ocrDriver(): string
    {
        return $this->instanceOcr->first()->driver ?? config('ocr.driver');
    }

    public function useLegacyOcr(): bool
    {
        return $this->ocrDriver() === config('ocr.driver')
            || null === $this->ocrInstanceConfig();
    }

    public function ocrInstanceConfig(): ?InstanceOcr
    {
        return $this->instanceOcr->first();
    }

    public function isTravelDocument(): bool
    {
        return $this->type === self::TYPE_TRAVEL;
    }
}
