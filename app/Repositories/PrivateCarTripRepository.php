<?php

namespace App\Repositories;

use App\Compliance\Compliance;
use App\Document;
use App\Instance;
use App\PrivateCarTrip;
use App\Traits\Repositories\SetRequestElementAcceptanceSource;


class PrivateCarTripRepository extends Repository
{
    use SetRequestElementAcceptanceSource;

    /** @var LocationRepository */
    protected $locationRepo;

    /** @var RequestMileageAllowanceRepository */
    protected $mileageAllowanceRepo;

    public static function model()
    {
        return PrivateCarTrip::class;
    }

    public function __construct()
    {
        parent::__construct();
        $this->locationRepo =resolve(LocationRepository::class);
        $this->mileageAllowanceRepo = resolve(RequestMileageAllowanceRepository::class);
    }

    /**
     * @return array
     */
    protected function createRules()
    {
        return [
            'request_id' => 'exists:requests,id',
            'instance_id' => 'exists:instances,id',
            'departure_at'                           => 'required|carbonDateTime',
            'return_at'                              => 'nullable|required_if:round_trip,true|carbonDateTime',
            'departure_location'                     => 'required|array',
            'destination_location'                   => 'required|array',
            'departure_location.formatted_address'   => 'required|string|max:255',
            'destination_location.formatted_address' => 'required|string|max:255',
            'round_trip'                             => 'required|boolean',
            'distance'                               => 'required|numeric|positive',
            'vehicle_type'                           => 'required|in:' . PrivateCarTrip::vehicleTypes()->implode(','),
            'other_costs_amount'                     => 'nullable|numeric',
            'other_costs_currency_id'                => 'exists:currencies,id',
            'weight'                                 => 'numeric',
            'license_plate'                          => 'string|min:3|max:10',
            'uuid'                                   => 'nullable|string',
        ];
    }

    /**
     * @param array $data
     *
     * @return array
     */
    protected function updateRules($data = [])
    {
        return [
            'request_id'                             => 'exists:requests,id',
            'instance_id'                            => 'exists:instances,id',
            'departure_at'                           => 'required|carbonDateTime',
            'return_at'                              => 'nullable|required_if:round_trip,true|carbonDateTime',
            'departure_location'                     => 'required|array',
            'destination_location'                   => 'required|array',
            'departure_location.formatted_address'   => 'required|string|max:255',
            'destination_location.formatted_address' => 'required|string|max:255',
            'round_trip'                             => 'required|boolean',
            'distance'                               => 'required|numeric|positive',
            'vehicle_type'                           => 'required|in:' . PrivateCarTrip::vehicleTypes()->implode(','),
            'other_costs_amount'                     => 'nullable|numeric',
            'other_costs_currency_id'                => 'exists:currencies,id',
            'weight'                                 => 'numeric',
            'license_plate'                          => 'string|min:3|max:10',
        ];
    }

    /**
     * @return array
     */
    protected function allowedWith()
    {
        return [];
    }

    protected function beforeCreate(PrivateCarTrip $privateCarTrip, $data)
    {
        $this->checkComplianceRules($privateCarTrip);
        $privateCarTrip->return_at = $privateCarTrip->return_at->endOfDay();
        $privateCarTrip->departure_at = $privateCarTrip->departure_at->startOfDay();

        return  $this->setRequestElementAcceptanceSource($privateCarTrip);
    }

    protected function beforeUpdate(PrivateCarTrip $privateCarTrip, $data)
    {
        $this->checkComplianceRules($privateCarTrip);
        return $privateCarTrip;
    }

    protected function afterCreate(PrivateCarTrip $privateCarTrip, $data)
    {
        $departure = $this->locationRepo->createLocationForRequestElement($data['departure_location'], $privateCarTrip->id, static::model()::RELATION_NAME, 'departure_location', $privateCarTrip->instance_id);
        $destination = $this->locationRepo->createLocationForRequestElement($data['destination_location'], $privateCarTrip->id, static::model()::RELATION_NAME, 'destination_location', $privateCarTrip->instance_id);

        $privateCarTrip->departureLocation()->save($departure);
        $privateCarTrip->destinationLocation()->save($destination);

        return $privateCarTrip;
    }

    protected function afterUpdate(PrivateCarTrip $privateCarTrip, $data)
    {
        if(isset($data['departure_location'])) {
            $this->locationRepo->update($privateCarTrip->departureLocation->id ?? null, $data['departure_location']);
        }

        if(isset($data['destination_location'])) {
            $this->locationRepo->update($privateCarTrip->destinationLocation->id ?? null, $data['destination_location']);
        }

        return $privateCarTrip;
    }

    protected function afterDelete(PrivateCarTrip $privateCarTrip)
    {
        $privateCarTrip->departureLocation()->delete();
        $privateCarTrip->destinationLocation()->delete();

        $privateCarTrip->documents->each(function(Document $document) use (&$privateCarTrip) {
            $document->removeTravelElement($privateCarTrip);
        });
    }

    protected function checkComplianceRules(PrivateCarTrip $car)
    {
        Compliance::create($car->request->instance)->privateCarTripChanged($car, $car->request->user);
    }

    /**
     * @param PrivateCarTrip $privateCarTrip
     * @return \Illuminate\Database\Eloquent\Model
     * @throws \Exception
     */
    public function createMileageAllowance(PrivateCarTrip $privateCarTrip) {
        try {
            $data = [
                'instance_id'           => $privateCarTrip->instance_id,
                'request_id'            => $privateCarTrip->request_id,
                'mpk_id'                => $privateCarTrip->request->mpk_id,
                'private_car_trip_id'   => $privateCarTrip->id,
                'departure_date'        => $privateCarTrip->departure_at->toDateTimeString(),
                'arrival_date'          => $privateCarTrip->return_at->toDateTimeString(),
                'round_trip'            => $privateCarTrip->round_trip,
                'distance'              => $privateCarTrip->distance,
                'vehicle_type'          => $privateCarTrip->vehicle_type,
                'departure_from'        => $privateCarTrip->departureLocation->toArray(),
                'arrival_to'            => $privateCarTrip->destinationLocation->toArray(),
                'cost_of_earning'       => false,
                'accounting_account_id' => $privateCarTrip->instance->accounting_account_id,
                'license_plate'         => $privateCarTrip->license_plate,
            ];

            return $this->mileageAllowanceRepo->create($data);
        } catch (\Throwable $exception) {
            return null;
        }
    }
}
