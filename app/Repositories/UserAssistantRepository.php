<?php

namespace App\Repositories;

use App\Permission;
use App\Repositories\Criteria\ActiveAndFutureDelegationUsersCriterion;
use App\Repositories\Criteria\ActiveFromToCriterion;
use App\Repositories\Criteria\AssistsCriterion;
use App\Repositories\Criteria\CurrentUserCriterion;
use App\User;
use App\UserAssistant;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class UserAssistantRepository extends Repository
{
    protected $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        parent::__construct();

        $this->userRepository = $userRepository;
    }

    static function model()
    {
        return UserAssistant::class;
    }

    public function deleteForUser(User $user): void
    {
        $this->prepare();

        $this->builder
            ->where('user_id', '=', $user->id)
            ->delete();
    }

    protected function createRules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'assistant_id' => 'required|exists:users,id',
        ];
    }

    protected function updateRules($data = [])
    {
        return [
        ];
    }

    protected function allowedWith()
    {
        return [
            'user',
            'assistant'
        ];
    }

    public function getUsersThatAreBeingAssistedBy(User $user): Collection
    {
        $assistedUsers = $this->getByCriteria([
            new AssistsCriterion($user),
        ]);

        return User::whereIn('id', $assistedUsers->pluck('user_id')->unique())->get();
    }

    public function getUserAssitants(User $user): Collection
    {
        return $this->with(['assistant'])->getByCriteria([
            new CurrentUserCriterion($user),
        ]);
    }

    public function hasUserAsAssistant(User $user, User $assistant) : bool
    {
        return UserAssistant::query()->where('user_id', '=' ,$user->id)
            ->where('assistant_id', '=', $assistant->id)
            ->exists();
    }

    public function create($data)
    {
        $this->disableAuthorization();

        $model = parent::create($data);

        $this->enableAuthorization();

        return $model;
    }

    public function delete($id)
    {
        $this->disableAuthorization();

        $model = parent::delete($id);

        $this->enableAuthorization();

        return $model;
    }

    public function beforeDelete(UserAssistant $userAssistant)
    {
        $authUser = Auth::user();
        $authUser->can('delete', $userAssistant);

        return $userAssistant;
    }

    public function findUserAssistantById(int $id): ?UserAssistant
    {
        $authUser = Auth::user();
        $userAssistant = UserAssistant::where('id', '=', $id)->first();

        if ($userAssistant instanceof UserAssistant
            && $authUser->can('view', $userAssistant) === true
        ) {
            return $userAssistant;
        }

        return null;
    }
}
