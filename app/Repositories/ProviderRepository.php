<?php

namespace App\Repositories;

use App\Company;
use App\Exceptions\DocumentProviderNotFoundException;
use App\Instance;
use App\Ocr\OcrHint;
use App\Provider;
use App\Repositories\Criteria\CompanyCriterion;
use App\Repositories\Criteria\CompanyOrNullCriterion;
use App\Repositories\Criteria\InstanceV2Criterion;
use App\Repositories\Criteria\NameCriterion;
use App\Repositories\Criteria\OrderByCriterion;
use App\Repositories\Criteria\RegistryNumberCriterion;
use App\Repositories\Criteria\SearchPhraseCriterion;
use App\Repositories\Pagination\Paginator;
use App\Repositories\Pagination\PaginatorInterface;
use App\Rules\ProviderNipUniqueInCurrentInstance;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Modules\Accounting\Pub\Modules\Provider\Enums\SourceEnum;
use Modules\Common\Dtos\PageInterface;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Pub\Facades\FeatureSwitcherFacade;

class ProviderRepository extends Repository
{
    protected FeatureSwitcherFacade $featureSwitcherFacade;

    public function __construct(FeatureSwitcherFacade $featureSwitcherFacade)
    {
        parent::__construct();
        $this->featureSwitcherFacade = $featureSwitcherFacade;
    }

    public static function model()
    {
        return Provider::class;
    }

    protected function createRules(): array
    {
        return array_merge(
            $this->minimalRulesToCreateProvider(),
            [
                'slug' => ['required', 'string', 'max:36'],
                'country_id' => 'nullable|exists:countries,id',
                'address' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'postcode' => 'nullable|string|nullable|max:255',
                'erp_id' => 'nullable|numeric|max:255',
                'source' => 'in:' . implode(',', [SourceEnum::GUS(), SourceEnum::USER()])
            ]
        );
    }

    public function minimalRulesToCreateProvider(?ProviderNipUniqueInCurrentInstance $nipFinder = null): array
    {
        return [
            'instance_id' => 'required|exists:instances,id',
            'name' => 'required|string|min:2|max:255',
            'registry_number' => ['nullable', 'string', 'min:4', 'max:20', $nipFinder == null ? resolve(ProviderNipUniqueInCurrentInstance::class) : $nipFinder],
        ];
    }

    protected function updateRules($data = []): array
    {
        return [
                'instance_id' => 'required|exists:instances,id',
                'name' => 'required|string|min:2|max:255',
                'registry_number' => ['required', 'string', 'min:4', 'max:20'],
                'country_id' => 'nullable|exists:countries,id',
                'address' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'postcode' => 'nullable|string|nullable|max:255',
                'erp_id' => 'nullable|numeric|max:255',
        ];
    }


    /**
     * @return array
     */
    protected function allowedWith()
    {
        return [
//            'country',
        ];
    }

    protected function beforeCreate(Provider $model, $data)
    {
        $registryNumber = $data['registry_number'] ?? $model->registry_number;
        $model->registry_number = $registryNumber !== null ? $this->purifyNip($registryNumber) : null;

        return $model;
    }

    public function findByNameAndNip($name, $nip, Instance $instance): Collection
    {
        $providers = $this->getByCriteria([
            new InstanceV2Criterion($instance),
            new NameCriterion([$name]),
            new RegistryNumberCriterion([$nip]),
        ]);

        if ($providers->isEmpty()) {
            $providers = $this->getByCriteria([
                new InstanceV2Criterion($instance),
                new RegistryNumberCriterion([$nip]),
            ]);
        }

        if ($providers->isEmpty()) {
            $providers = $this->getByCriteria([
                new InstanceV2Criterion($instance),
                new NameCriterion([$name]),
            ]);
        }

        return $providers;
    }

    public function findByNipAndErpId(string $nip, ?string $erpId, int $instanceId): ?Provider
    {
        $nip = $this->purifyNip($nip);

        /** @var Builder $providerQuery */
        $providerQuery = Provider::where([
            ['instance_id', '=', $instanceId],
            ['registry_number', 'like', "%$nip%"]
        ]);

        if($erpId === null) {
            $providerQuery->whereNull('erp_id');
        } else {
            $providerQuery->where('erp_id', '=', $erpId);
        }

        return $providerQuery->first();
    }

    public function findByNipInCurrentInstance(string $nip, int $instanceId): ?Provider
    {
        $nip = $this->purifyNip($nip);

        return Provider::where([
            ['instance_id', '=', $instanceId],
            ['registry_number', 'like', "%$nip%"]
        ])->first();
    }

    public function findAllByNipInCurrentInstance(string $nip, string $instanceSlug): EloquentCollection
    {
        $nip = $this->purifyNip($nip);

        return Provider::select('providers.*')
            ->from('providers')
            ->leftJoin('instances', function ($join) {
                $join->on('instances.id', '=', 'providers.instance_id');
            })
            ->where([
                ['instances.slug', '=', $instanceSlug],
                ['providers.registry_number', 'like', "%$nip%"]
            ])
            ->get();
    }

    public function updateProviderFromOcrHint(OcrHint $ocrHint, Instance $instance): Provider
    {
        $provider = Provider::where([
            ['instance_id', '=', $instance->id],
            ['id', '=', $ocrHint->value]
        ])->first();


        if ($provider instanceof Provider) {
            if (isset($ocrHint->params['name']) === true && $provider->name !== $ocrHint->params['name']) {
                $validator = \Validator::make(
                    [
                        'instance_id'       => $provider->instance_id,
                        'registry_number'   => $provider->registry_number,
                        'name'              => $ocrHint->params['name']
                    ],
                    $this->updateRules()
                );
                $isValid = !$validator->fails();

                if ($isValid === true) {
                    $provider->name = $ocrHint->params['name'];
                    $provider->save();
                }
            }

            return $provider;
        }

        throw DocumentProviderNotFoundException::create();
    }

    protected function purifyNip(string $nip)
    {
        return preg_replace('/[^A-Za-z0-9]/', "", strtoupper($nip));
    }

    public function getProvidersForIndex(PageInterface $page, Company $company): Paginator
    {
        if($this->featureSwitcherFacade->isEnabledForCompany(FeatureEnum::FEATURE_PROVIDER_ERP_ID_REQUIRED(), $company)) {
            $orderByCriterion = new OrderByCriterion('erp_id');
        } else {
            $orderByCriterion = new OrderByCriterion('name');
        }

        return $this->paginateByCriteria(
            [
                new SearchPhraseCriterion(),
                new InstanceV2Criterion($company->instance),
                new CompanyOrNullCriterion([$company->id]),
                $orderByCriterion
            ],
            function () { return $this->providersIndexQuery(); },
            'providers.id',
            $page
        );
    }

    public function providersIndexQuery()
    {
        return $this->builder->distinct()->selectRaw(
            '
                    providers.id,
                    providers.address,
                    providers.name,
                    providers.registry_number,
                    providers.postcode,
                    providers.erp_id,
                    providers.city
               '
        );
    }

    public function findByErpIdAndInstanceId(?string $erpId, int $instanceId): ?Provider
    {
        $this->prepare();

        return $this->builder->where([
            'erp_id' => $erpId,
            'instance_id' => $instanceId,
        ])->withTrashed()->first();
    }

    public function findByEmployeeIdOrByErpId(int $employeeId, int $instanceId, ?string $erpId): ?Provider
    {
        $this->prepare();

        $builder = $this->builder->where([
            'employee_id' => $employeeId,
            'instance_id' => $instanceId,
        ]);

        if(!empty($erpId)) {
            $builder->orWhere(function(Builder  $builder) use ($erpId, $instanceId) {
                $builder->where([
                    'erp_id' => $erpId,
                    'instance_id' => $instanceId,
                ]);
            });
        }

        return $builder->withTrashed()->first();
    }

    public function findByIdAndInstanceId(int $providerId, int $instanceId): Provider
    {
        return Provider::query()
            ->where('id', $providerId)
            ->where('instance_id', $instanceId)
            ->first();
    }

    public function getForCurrentInstance(PageInterface $page, Instance $instance): PaginatorInterface
    {
        return $this->paginateEloquentModelsByCriteria([
            new InstanceV2Criterion($instance),
            new CompanyCriterion()
        ], $page);
    }
}
