<?php

declare(strict_types=1);

namespace App\Repositories\Ocr;

use App\Document;
use App\Ocr\DocumentOcrStatusHistory;

class DocumentOcrStatusHistoryRepository
{
    public function getPreviousStateForDocument(Document $document): ?DocumentOcrStatusHistory
    {
        return  DocumentOcrStatusHistory::where([
            ['document_id', '=', $document->id]
        ])->orderBy('created_at', 'DESC')->first();
    }

    public function saveHistoryForDocument(Document $document): ?DocumentOcrStatusHistory
    {
        return DocumentOcrStatusHistory::createFromDocument($document);
    }
}