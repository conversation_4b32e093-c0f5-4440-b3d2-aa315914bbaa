<?php

declare(strict_types=1);

namespace App\Repositories;

use App\RequestSettlementAcceptorsUser;

class RequestSettlementAcceptorsUserRepository extends Repository
{
    static function model()
    {
        return RequestSettlementAcceptorsUser::class;
    }

    public function findOneByToken(string $token): ?RequestSettlementAcceptorsUser
    {
        return $this->model::query()->where('token', $token)->first();
    }
}
