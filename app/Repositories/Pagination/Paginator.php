<?php

declare(strict_types=1);

namespace App\Repositories\Pagination;

use Illuminate\Support\Collection;
use Modules\Common\Dtos\PageInterface;

class Paginator implements PaginatorInterface
{
    protected $offset;
    protected $limit;
    protected $totalCount;
    protected $items;
    protected array $meta;

    public static function create(Collection $collection, int $totalCount, PageInterface $page, array $meta = [])
    {
        return new self($page->getOffset(), $page->getLimit(), $totalCount, $collection, $meta);
    }

    public function __construct(?int $offset = 0, ?int $limit = 0, ?int $totalCount = 0, ?Collection $items = null, array $meta = [])
    {
        $this->offset = $offset;
        $this->limit = $limit;
        $this->totalCount = $totalCount;
        $this->items = $items instanceof Collection ? $items : collect();
        $this->meta = $meta;
    }

    public function getOffset(): int
    {
        return $this->offset;
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function getTotalCount(): int
    {
        return $this->totalCount;
    }

    public function getItems(): Collection
    {
        return $this->items;
    }

    public function transformItems(callable $callback): self
    {
        $this->items->transform($callback);

        return $this;
    }

    public function getMeta(): array
    {
        return $this->meta;
    }

    public function prepend($item): void
    {
        $this->items->prepend($item);
    }
}