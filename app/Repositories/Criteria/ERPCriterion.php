<?php

namespace App\Repositories\Criteria;

use App\Repositories\DocumentRepository;
use App\Repositories\Repository;
use App\Repositories\RequestRepository;
use Illuminate\Database\Eloquent\Builder;

class ERPCriterion extends Criterion {
    protected $erpId;

    public function __construct(string $erpId) {
        $this->erpId = $erpId;
    }

    public function supportedRepositories() {
        return [
            DocumentRepository::class,
            RequestRepository::class
        ];
    }

    public function make(Builder $builder, Repository $repository)
    {
        return $builder->where('erp_id', '=', $this->erpId);
    }
}
