<?php

namespace App\Repositories\Criteria;

use App\Repositories\DocumentRepository;
use App\Repositories\ProviderRepository;
use App\Repositories\Repository;
use App\Repositories\RequestRepository;
use App\Repositories\TransactionRepository;
use Illuminate\Database\Eloquent\Builder;
use Modules\Accounting\Priv\Repositories\MpkRepository;
use Modules\Accounting\Priv\Repositories\VatNumberRepository;
use Modules\Analytics\Priv\Repositories\ProjectRepository;

class OrderByRawCriterion extends Criterion
{
    protected $rawOrderBy;

    /**
     * OrderByRawCriterion constructor.
     * @param $rawOrderBy
     */
    public function __construct($rawOrderBy)
    {
        $this->rawOrderBy = $rawOrderBy;
    }

    public function make(Builder $builder,  Repository $repository)
    {
        return $builder->orderByRaw( $this->rawOrderBy);
    }

    public function supportedRepositories()
    {
        return [
            MpkRepository::class,
            ProjectRepository::class,
            VatNumberRepository::class,
        ];
    }

}
