<?php

namespace App\Repositories\Criteria;

use App\Repositories\Repository;
use App\Repositories\RequestRepository;
use App\Repositories\TravelExpenseRepository;
use Illuminate\Database\Eloquent\Builder;

class CountryCriterion extends Criterion
{
    protected $countries;

    public function __construct(array $countries = [])
    {
        $this->countries = collect($countries);
    }

    public function supportedRepositories()
    {
        return [
            RequestRepository::class,
            TravelExpenseRepository::class,
        ];
    }

    public function make(Builder $builder, Repository $repository)
    {
        if (request()->has('countries')) {
            $countries = explode(',', request()->get('countries'));

            $this->countries = $this->countries->merge($countries);
        }

        if ($this->countries->isEmpty()) {
            return $builder;
        }

        return $builder->whereIn('country_id', $this->countries);
    }
}
