<?php

namespace App\Repositories\Criteria;

use App\Document;
use App\Repositories\DocumentRepository;
use App\Repositories\Repository;
use App\Repositories\RequestRepository;
use App\Repositories\TransactionRepository;
use App\Request;
use Illuminate\Database\Eloquent\Builder;

class RequestTypeCriterion extends Criterion {
    protected $types;

    public function __construct(array $types = []) {
        $this->types = collect($types);
    }

    public function supportedRepositories() {
        return [
            RequestRepository::class,
            DocumentRepository::class,
            TransactionRepository::class
        ];
    }

    public function make(Builder $builder, Repository $repository)
    {
        $this->builder = $builder;

        switch ((new \ReflectionClass($repository->model()))->getName()) {
            case Request::class:
                $this->makeRequestRepository();
                break;
            case Document::class:
                $this->makeDocumentRepository();
                break;
        }
    }

    protected function makeRequestRepository()
    {
        if(request()->get('types')) {
            $types = explode(',', request()->get('types'));

            $this->types = $this->types->merge($types);
        }

        $allowTypes = $this->types->intersect(Request::getTypes());


        if($allowTypes->isEmpty()) {
            return $this->builder();
        }

        return $this->builder()->whereIn('requests.type', $allowTypes->toArray());
    }

    protected function makeDocumentRepository()
    {
        if(request()->get('request_types')) {
            $types = explode(',', request()->get('request_types'));

            $this->types = $this->types->merge($types);
        }

        $allowTypes = $this->types->intersect(Request::getTypes());

        if($allowTypes->isEmpty()) {
            return $this->builder();
        }

        return $this->builder()->whereHas('request', function($query) use(&$allowTypes) {
            $query->whereIn('type', $allowTypes->toArray());
        });
    }
}
