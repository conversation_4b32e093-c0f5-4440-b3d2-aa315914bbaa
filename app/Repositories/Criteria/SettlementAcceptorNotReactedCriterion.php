<?php

namespace App\Repositories\Criteria;

use App\Repositories\Repository;
use App\Repositories\RequestRepository;
use App\Request;
use Illuminate\Database\Eloquent\Builder;

class SettlementAcceptorNotReactedCriterion extends Criterion
{
    protected $userSlug;

    public function __construct(array $userSlug = null)
    {
        $this->userSlug = collect($userSlug);
    }

    public function supportedRepositories()
    {
        return [
            RequestRepository::class,
        ];
    }

    public function make(Builder $builder, Repository $repository)
    {
        if(request()->get('settlementAcceptors')) {
            $userSlug = explode(',', request()->get('settlementAcceptors'));

            $this->userSlug = $this->userSlug->merge($userSlug);
        }

        if ($this->userSlug->isEmpty()) {
            return $builder;
        }

        return $builder->where(function(Builder $query) {
            $query->whereHas('settlementAcceptors', function (Builder $q) {
                $q->whereIn('slug', $this->userSlug)->where(['accepted' => Request::ACCEPTOR_STATUS_PENDING]);
            });
        });
    }
}
