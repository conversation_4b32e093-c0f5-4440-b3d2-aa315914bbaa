<?php

namespace App\Repositories\Criteria;

use App\Company;
use App\ERP\ERPModule;
use App\Instance;
use App\Repositories\Repository;
use App\Repositories\TransactionRepository;
use Illuminate\Database\Eloquent\Builder;
use Modules\Common\Dtos\PageInterface;
use Modules\Common\ValueObjects\Sorter;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Pub\Facades\FeatureSwitcherFacade;

class OrderByForTransactionCriterion extends Criterion
{
    protected PageInterface $page;

    protected Company $loggedUserCompany;

    public function __construct(PageInterface $page, Company $userCompany)
    {
        $this->page = $page;
        $this->loggedUserCompany = $userCompany;
    }

    public function make(Builder $builder,  Repository $repository)
    {
        $featureSwitcherFacade = resolve(FeatureSwitcherFacade::class);

        $builder
            ->leftJoin('requests', 'requests.id', 'transactions.request_id')
            ->leftJoin('documents', 'documents.id', 'transactions.document_id');

        if ($this->page->getSorter() instanceof Sorter) {
            $builder->orderBy(
                $this->page->getSorter()->getSortBy(),
                $this->page->getSorter()->getSortDirection()
            );

            return $builder;
        }

        if ($featureSwitcherFacade->isEnabledForCompany(FeatureEnum::FEATURE_INTEGRATION_API_ERP_ACCOUNTING_ENABLED(), $this->loggedUserCompany)) {
            $builder->select("transactions.*",
                \DB::raw('(CASE
                WHEN `documents`.`erp_accounted_at` IS NOT null AND `transactions`.`type` = \'document\'  THEN `documents`.`erp_accounted_at`
                WHEN `requests`.`erp_accounted_at` IS NOT null AND `transactions`.`type` = \'cover\' THEN `requests`.`erp_accounted_at`
                ELSE null
                END) AS `transaction_accounted_at` '));
        } else {
            $builder->select("transactions.*",
                \DB::raw('(CASE
                WHEN `documents`.`accounted_at` IS NOT null AND `transactions`.`type` = \'document\'  THEN `documents`.`accounted_at`
                WHEN `requests`.`accounted_at` IS NOT null AND `transactions`.`type` = \'cover\' THEN `requests`.`accounted_at`
                ELSE null
                END) AS `transaction_accounted_at` '));
        }

        $builder->orderBy('transaction_accounted_at', 'desc');
        $builder->orderBy('requests.id', 'desc');

        return $builder;
    }

    public function supportedRepositories()
    {
        return [
            TransactionRepository::class,
        ];
    }

}
