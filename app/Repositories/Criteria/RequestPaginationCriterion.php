<?php

declare(strict_types=1);

namespace App\Repositories\Criteria;

use App\Repositories\CompanyRepository;
use App\Repositories\InstallmentRepository;
use App\Repositories\NotificationRepository;
use App\Repositories\ProviderRepository;
use Modules\Accounting\Priv\Repositories\MpkRepository;
use App\Repositories\Repository;
use App\Repositories\RequestRepository;
use App\Repositories\TransactionRepository;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Builder;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\AccountDimensionRepository;
use Modules\Analytics\Priv\Repositories\KPIRepository;
use Modules\Analytics\Priv\Repositories\ProjectRepository;
use Modules\Common\Dtos\PageInterface;
use Modules\IntegrationFILE\Priv\Repositories\ImportRepository;

class RequestPaginationCriterion extends Criterion
{

    /**
     * @var PageInterface
     */
    protected $page;

    /**
     * RequestPaginationCriterion constructor.
     * @param PageInterface $page
     */
    public function __construct(PageInterface $page)
    {
        $this->page = $page;
    }

    public function supportedRepositories()
    {
        return [
            RequestRepository::class,
            TransactionRepository::class,
            InstallmentRepository::class,
            UserRepository::class,
            MpkRepository::class,
            CompanyRepository::class,
            ProjectRepository::class,
            KPIRepository::class,
            ProviderRepository::class,
            ImportRepository::class,
            NotificationRepository::class,
            AccountDimensionRepository::class,
            AccountDimensionItemRepository::class,
        ];
    }

    public function make(Builder $builder, Repository $repository)
    {
        $this->builder = $builder;

        return $this->makeDefault();
    }

    protected function makeDefault()
    {
        $limit = $this->page->getLimit();
        $offset = $this->page->getOffset();

        if ($limit > 0 && $offset >= 0) {
            return $this->builder()->offset($offset)->limit($limit);
        }

        return $this->builder();
    }
}
