<?php

namespace App\Repositories\Criteria;

use App\Card;
use App\Installment;
use App\Provider;
use App\Repositories\CardRepository;
use App\Repositories\InstallmentRepository;
use App\Repositories\ProviderRepository;
use App\Repositories\Repository;
use App\Repositories\RequestRepository;
use App\Repositories\TransactionRepository;
use App\Repositories\UserRepository;
use App\Request;
use App\Transaction\Transaction;
use App\User;
use Illuminate\Database\Eloquent\Builder;

class CompanyCriterion extends Criterion
{
    protected $companies;

    public function __construct(array $companies = [])
    {
        $this->companies = collect($companies);
    }

    public function supportedRepositories(): array
    {
        return [
            RequestRepository::class,
            UserRepository::class,
            ProviderRepository::class,
            CardRepository::class,
            TransactionRepository::class,
            InstallmentRepository::class
        ];
    }

    public function make(Builder $builder, Repository $repository): Builder
    {
        $this->builder = $builder;

        if (request()->has('companies')) {
            $companies = explode(',', request()->get('companies'));

            $this->companies = $this->companies->merge($companies);
        }

        if (request()->has('company')) {
            $this->companies = $this->companies->merge([request()->get('company')]);
        }

        if (request()->has('company_id')) {
            $this->companies = $this->companies->merge([request()->get('company_id')]);
        }

        if ($this->companies->isEmpty()) {
            return $this->builder();
        }

        switch ((new \ReflectionClass($repository->model()))->getName()) {
            case Request::class : {
                $this->makeForRequest();
                break;
            }
	        case User::class : {
		        $this->makeForUser();
		        break;
	        }
            case Card::class : {
                $this->makeForCard();
                break;
            }
            case Transaction::class : {
                $this->makeForTransaction();
                break;
            }
            case Installment::class: {
                $this->makeForInstallment();
                break;
            }
            case Provider::class:
            {
                $this->makeForProvider();
                break;
            }
        }

        return $this->builder();
    }

    protected function makeForRequest(): Builder
    {
        return $this->builder()->whereHas('user', function (Builder $query) {
            $query->whereIn('company_id', $this->companies->toArray());
        });
    }

    public function makeForUser(): Builder
    {
        return $this->builder()->whereIn('users.company_id', $this->companies);
    }

    public function makeForCard(): Builder
    {
        return $this->builder()->whereIn('company_id', $this->companies);
    }

    protected function makeForTransaction(): Builder
    {
        return $this->builder()->whereHas('request.user', function (Builder $query) {
            $query->whereIn('company_id', $this->companies->toArray());
        });
    }

    protected function makeForInstallment(): Builder
    {
        return $this->builder()->whereHas('request.user.company', function (Builder $query) {
            $query->whereIn('id', $this->companies->toArray());
        });
    }

    public function makeForProvider(): Builder
    {
        return $this->builder()->whereHas('company', function (Builder $query) {
            $query->whereIn('slug', $this->companies->toArray());
        });
    }
}
