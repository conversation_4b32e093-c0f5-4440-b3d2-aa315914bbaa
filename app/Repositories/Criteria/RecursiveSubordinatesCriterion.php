<?php


namespace App\Repositories\Criteria;

use App\Repositories\Repository;
use App\Repositories\UserRepository;
use App\User;
use DB;
use Illuminate\Database\Eloquent\Builder;


class RecursiveSubordinatesCriterion extends Criterion
{
    public function supportedRepositories(): array
    {
        return [
            UserRepository::class,
        ];
    }

    public function make(Builder $builder, Repository $repository): Builder
    {
        $user = \Auth::user();

        if ($user instanceof User === false
            || $user->isController()
            || $user->isAdmin()
            || $user->isSuperAdmin()
        ) {
            return $builder;
        }

        $rawQuery = '
            WITH RECURSIVE subordinates AS (
                SELECT id, user_id
                FROM users
                WHERE user_id = ?
                UNION ALL
                SELECT u.id, u.user_id
                FROM users u
                INNER JOIN subordinates s ON u.user_id = s.id
            )
            SELECT users.* FROM users 
            INNER JOIN subordinates ON users.id = subordinates.id
        ';

        $subordinateIds = collect(DB::select($rawQuery, [$user->id]))->pluck('id')->toArray();

        return $builder->whereIn('users.id', array_merge($subordinateIds, [$user->id]));
    }
}
