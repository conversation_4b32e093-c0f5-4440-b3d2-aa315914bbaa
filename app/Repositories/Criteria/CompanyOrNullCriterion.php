<?php

declare(strict_types=1);

namespace App\Repositories\Criteria;

use App\Provider;
use App\Repositories\ProviderRepository;
use App\Repositories\Repository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;

final class CompanyOrNullCriterion extends Criterion
{
    protected Collection $companies;

    public function __construct(array $companies = [])
    {
        $this->companies = collect($companies);
    }

    public function supportedRepositories()
    {
        return [
            AccountDimensionItemRepository::class,
            ProviderRepository::class
        ];
    }

    public function make(Builder $builder, Repository $repository)
    {
        $this->builder = $builder;

        if($this->companies->isEmpty()) {
            return $this->builder();
        }

        $modelName = get_class($repository)::model();
        switch ($modelName) {
            case AccountDimensionItem::class:
            case Provider::class:
                $this->makeCriteria();
                break;
            default:
                throw new \Exception(sprintf('CompanyOrNullCriterion for given type %s is not implemented', $modelName));
        }
    }

    private function makeCriteria()
    {
        $this->builder()->where(function(Builder $builder) {
            $tableName = $builder->getModel()->getTable();

            $builder->whereIn($tableName . '.company_id', $this->companies);
            $builder->orWhereNull($tableName . '.company_id');
        });
    }
}
