<?php

namespace App\Repositories\Criteria;

use App\Group;
use App\Repositories\GroupRepository;
use App\Repositories\Repository;
use App\Repositories\UserRepository;
use App\User;
use Illuminate\Database\Eloquent\Builder;

class GroupNameCriterion extends Criterion {
    protected $groups;

    public function __construct(array $groups = []) {
        $this->groups = collect($groups);
    }

    public function supportedRepositories() {
        return [
            GroupRepository::class,
            UserRepository::class,
        ];
    }

    public function make(Builder $builder, Repository $repository) {

	    $this->builder = $builder;

	    if(request()->has('groups')) {

		    $groups = explode(',', request()->get('groups'));

		    $this->groups = $this->groups->merge($groups);
	    }

	    if($this->groups->isEmpty()) {
		    return $this->builder();
	    }

	    switch ((new \ReflectionClass($repository->model()))->getName()) {
		    case Group::class : {
			    $this->makeForGroup();
			    break;
		    }
		    case User::class : {
			    $this->makeForUser();
			    break;
		    }

	    }

	    return $this->builder();
    }

	protected function makeForGroup()
	{
		return $this->builder()->whereIn('name', $this->groups);
	}

	public function makeForUser()
	{
		return $this->builder()->whereHas('groups', function (Builder $query) {
			$query->whereIn('id',  $this->groups);
		});
	}



}
