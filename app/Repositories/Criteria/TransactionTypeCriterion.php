<?php

namespace App\Repositories\Criteria;

use App\Repositories\Repository;
use App\Repositories\TransactionRepository;
use Illuminate\Database\Eloquent\Builder;

class TransactionTypeCriterion extends Criterion
{
    protected $transactionType;

    public function __construct(string $transactionType = null) {
        $this->transactionType = $transactionType;
    }

    public function make(Builder $builder, Repository $repository)
    {
        if(request()->has('transaction_type')) {
            $this->transactionType = request()->get('transaction_type');
        }

        if(!$this->transactionType) {
            return $builder;
        }

        return $builder->where('transactions.type', '=', $this->transactionType);
    }

    public function supportedRepositories()
    {
        return [
            TransactionRepository::class
        ];
    }
}
