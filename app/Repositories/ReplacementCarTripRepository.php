<?php

namespace App\Repositories;

use App\ReplacementCarTrip;
use App\Document;
use App\Repositories\Criteria\InstanceV2Criterion;
use App\Repositories\Criteria\RequestElementTypeCriterion;
use App\Traits\Repositories\SetRequestElementAcceptanceSource;
use Modules\Accounting\Priv\Repositories\DocumentElementTypeRepository;

class ReplacementCarTripRepository extends Repository
{
    use SetRequestElementAcceptanceSource;

    protected LocationRepository $locationRepo;
    protected DocumentElementTypeRepository $documentElementTypeRepo;

    public static function model(): string
    {
        return ReplacementCarTrip::class;
    }

    public function __construct(LocationRepository $locationRepository, DocumentElementTypeRepository $documentElementTypeRepo)
    {
        parent::__construct();
        $this->locationRepo = $locationRepository;
        $this->documentElementTypeRepo = $documentElementTypeRepo;
    }

    /**
     * @return array
     */
    protected function createRules()
    {
        return [
            'request_id' => 'exists:requests,id',
            'instance_id' => 'exists:instances,id',
            'departure_at' => 'required|carbonDateTime',
            'return_at' => 'nullable|required_if:round_trip,true|carbonDateTime',
            'departure_location' => 'required|array',
            'destination_location' => 'required|array',
            'departure_location.formatted_address' => 'required|string|max:255',
            'destination_location.formatted_address' => 'required|string|max:255',
            'round_trip' => 'required|boolean',
            'fuel_cost' => 'nullable|numeric',
            'fuel_cost_currency_id' => 'exists:currencies,id',
            'other_costs_amount' => 'nullable|numeric',
            'other_costs_currency_id' => 'exists:currencies,id',
            'weight' => 'numeric',
            'license_plate' => 'string|min:3|max:10',
            'uuid' => 'nullable|string|size:16|alpha_num',
        ];
    }

    protected function updateRules($data = [])
    {
        return [
            'request_id' => 'exists:requests,id',
            'instance_id' => 'exists:instances,id',
            'departure_at' => 'required|carbonDateTime',
            'return_at' => 'nullable|required_if:round_trip,true|carbonDateTime',
            'departure_location' => 'required|array',
            'destination_location' => 'required|array',
            'departure_location.formatted_address' => 'required|string|max:255',
            'destination_location.formatted_address' => 'required|string|max:255',
            'round_trip' => 'required|boolean',
            'fuel_cost' => 'nullable|numeric',
            'fuel_cost_currency_id' => 'exists:currencies,id',
            'other_costs_amount' => 'nullable|numeric',
            'other_costs_currency_id' => 'exists:currencies,id',
            'weight' => 'numeric',
            'license_plate' => 'string|min:3|max:10',
        ];
    }

    /**
     * @return array
     */
    protected function allowedWith()
    {
        return [
            'suggestedElementType',
        ];
    }

    protected function beforeCreate(ReplacementCarTrip $replacementCarTrip, $data)
    {
        $this->setSuggestedType($replacementCarTrip);
        $replacementCarTrip->return_at = $replacementCarTrip->return_at->endOfDay();
        $replacementCarTrip->departure_at = $replacementCarTrip->departure_at->startOfDay();

        return $this->setRequestElementAcceptanceSource($replacementCarTrip);
    }

    protected function afterCreate(ReplacementCarTrip $replacementCarTrip, $data)
    {
        $departure = $this->locationRepo->createLocationForRequestElement($data['departure_location'], $replacementCarTrip->id, static::model()::RELATION_NAME, 'departure_location', $replacementCarTrip->instance->id);
        $destination = $this->locationRepo->createLocationForRequestElement($data['destination_location'], $replacementCarTrip->id, static::model()::RELATION_NAME, 'destination_location', $replacementCarTrip->instance->id);

        $replacementCarTrip->departureLocation()->save($departure);
        $replacementCarTrip->destinationLocation()->save($destination);

        return $replacementCarTrip;
    }

    protected function beforeUpdate(ReplacementCarTrip $replacementCarTrip, $data)
    {
        $this->setSuggestedType($replacementCarTrip);
        return $replacementCarTrip;
    }

    protected function afterUpdate(ReplacementCarTrip $replacementCarTrip, $data)
    {
        if(isset($data['departure_location'])) {
            $this->locationRepo->update($replacementCarTrip->departureLocation->id ?? null, $data['departure_location']);
        }

        if(isset($data['destination_location'])) {
            $this->locationRepo->update($replacementCarTrip->destinationLocation->id ?? null, $data['destination_location']);
        }

        return $replacementCarTrip;
    }

    protected function afterDelete(ReplacementCarTrip $replacementCarTrip)
    {
        $replacementCarTrip->departureLocation()->delete();
        $replacementCarTrip->destinationLocation()->delete();

        $replacementCarTrip->documents->each(function(Document $document) use(&$replacementCarTrip){
            $document->removeTravelElement($replacementCarTrip);
        });
    }

    protected function setSuggestedType(ReplacementCarTrip $car)
    {
        if ($car->fuel_cost > 0) {
            $relation = ReplacementCarTrip::FUEL_DOCUMENT_TYPE;
        } else {
            $relation = ReplacementCarTrip::OTHER_COSTS_DOCUMENT_TYPE;
        }

        $type = $this->documentElementTypeRepo->getByCriteria([
            new InstanceV2Criterion($car->instance),
            new RequestElementTypeCriterion([$relation])
        ])->first();

        abort_unless($type, 400, trans('error.invalid-element-type'));

        $car->type_id = $type->id;
    }
}
