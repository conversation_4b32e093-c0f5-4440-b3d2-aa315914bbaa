<?php

declare(strict_types=1);

namespace App\Nova\Tools;

use Laravel\Nova\Menu\MenuSection;
use Lara<PERSON>\Nova\Nova;
use <PERSON><PERSON>\Nova\Tool;

class DecisionProcessGraphTool extends Tool
{
    /**
     * Perform any tasks that need to happen when the tool is booted.
     *
     * @return void
     */
    public function boot()
    {
        Nova::script('decision-process-graph', __DIR__.'/../../resources/js/tool.js');
        Nova::style('decision-process-graph', __DIR__.'/../../resources/css/tool.css');
    }

    /**
     * Build the menu that renders the navigation links for the tool.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function menu(\Illuminate\Http\Request $request)
    {
        return MenuSection::make('Decision Process Graph')
            ->path('/decision-process-graph')
            ->icon('chart-bar');
    }
}
