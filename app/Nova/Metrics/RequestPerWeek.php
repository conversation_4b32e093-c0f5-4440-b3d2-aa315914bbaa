<?php

namespace App\Nova\Metrics;

use App\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Trend;

class RequestPerWeek extends Trend
{
    public $name = 'Requests per week (not draft)';

    public function calculate(NovaRequest $request)
    {
        $query = Request::notDraft();

        return $this->countByWeeks($request, $query)
            ->showLatestValue();
    }

    public function ranges()
    {
        return [
            4 => '4 Weeks',
            8 => '8 Weeks',
            12 => '12 Weeks',
            24 => '24 Weeks',
        ];
    }

    public function cacheFor()
    {
         return now()->addMinutes(5);
    }

    public function uriKey()
    {
        return 'request-per-week';
    }
}
