<?php

namespace App\Nova\Metrics;

use App\Offer;
use App\Request;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class RequestsPerStatus extends Partition
{
    public function calculate(NovaRequest $request)
    {
        $requests = Request::query()->orderBy('aggregate', 'desc');

        return $this->count($request, $requests, 'status')->label(fn ($value) => trans("global.request-status-{$value}"));
    }

    public function cacheFor()
    {
         return now()->addMinutes(5);
    }

    public function uriKey()
    {
        return 'requests-per-status';
    }
}
