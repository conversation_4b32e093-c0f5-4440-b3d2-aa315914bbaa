<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Instance;
use App\Services\InstanceOAuthService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;

class InstanceApiAccessAction extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'API access';

    public $onlyOnDetail = true;
    public $showOnTableRow = false;

    private InstanceOAuthService $instanceOAuthService;

    public function __construct(InstanceOAuthService $instanceOAuthService)
    {
        $this->instanceOAuthService = $instanceOAuthService;
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        if ($models->isEmpty()) {
            return Action::danger('No instance selected.');
        }

        if ($models->count() > 1) {
            return Action::danger('You can enable API only for one instance at a time.');
        }

        $instanceId = $models->first()->id;
        if ($fields->get('user') === 'new') {
            $user = $fields->get('email');
        } else {
            $user = $fields->get('user');
        }

        return Action::redirect(route('nova.access', [$instanceId, $user]));
    }

    public function fields(): array
    {
        $instance = Instance::find(request()->get('resourceId') ?: request()->get('instance'));

        if (!$instance) {
            return [];
        }

        $apiUsers = $this->instanceOAuthService->findApiUsers($instance)->pluck('email', 'id');
        $apiUsers->put('new', 'Create new API user');

        return [
            Select::make('Instance')->options([$instance->id => $instance->domain])
                ->readonly()
                ->withMeta(['value' => $instance->id])
                ->required(),
            Select::make('User')
                ->options($apiUsers)
                ->rules(['required'])
                ->help('Provide email if you create new API user'),
            Text::make('Email')->rules(['email', 'required_if:user,new'])
                ->withMeta(['value' => $this->instanceOAuthService->defaultEmail($instance)])
                ->help('Required if you create new API user'),
        ];
    }
}
