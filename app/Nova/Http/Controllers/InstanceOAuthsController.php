<?php

declare(strict_types=1);

namespace App\Nova\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Instance;
use App\OAuthClient;
use App\Services\InstanceOAuthService;
use App\User;

class InstanceOAuthsController extends Controller
{
    public function access(
        Instance $instance,
        $userIdentifier = null,
        InstanceOAuthService $instanceOAuthService
    ) {
        $user = $instance->users()->where(function ($query) use ($userIdentifier) {
            $query->where('email', $userIdentifier)
                ->orWhere('id', $userIdentifier);
        })->first();

        $instanceOAuthService->ensureApiEnabled($instance);

        if (!$user) {
            \Validator::make(
                ['user' => $userIdentifier],
                ['user' => 'required|string|email']
            )->validate();

            $user = $instanceOAuthService->createApiUser($instance, $userIdentifier);
        }

        if (OAuthClient::whereUserId($user->id)->exists()) {
            return 'Użytkownik posiada juz klucz API';
        }

        $secret = $instanceOAuthService->generateSecret($user, $instance);

        $test = sprintf(
            'curl -X POST "https://%s/oauth/token" -d "grant_type=client_credentials" -d "client_id=%s" -d "client_secret=%s" -d "scope=*"',
            $instance->domain,
            $secret[0],
            $secret[1]
        );

        return view('auth.oauth', [
            'table' => [
                'Domain' => sprintf('https://%s', $instance->domain),
                'Auth URL' => sprintf('https://%s/oauth/token', $instance->domain),
                'API URL' => sprintf('https://%s/integration-api/v1/', $instance->domain),
                'Client ID' => $secret[0],
                'Client Secret' => $secret[1],
                'Test' => $test
            ]
        ]);
    }

    public function documentation(
        Instance $instance,
        int $userId,
        InstanceOAuthService $instanceOAuthService
    ) {
        /** @var User $user */
        $user = $instance->users()->whereId($userId)->firstOrFail();

        $instanceOAuthService->ensureApiEnabled($instance);

        $password = \Str::random(30);
        $user->password = bcrypt($password);
        $user->save();

        return view('auth.oauth', [
            'table' => [
                'Documentation URL' => sprintf('https://%s/integration-api/documentation', $instance->domain),
                'Login' => $user->email,
                'Password' => $password,
            ]
        ]);
    }
}
