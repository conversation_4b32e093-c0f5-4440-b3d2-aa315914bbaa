<?php

declare(strict_types=1);

namespace App\Nova\Resources\Decision;

use App\Nova\Resources\Instance;
use App\Nova\Resources\Resource;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Mo<PERSON>les\DecisionMaker\Priv\Models\DecisionProcessCondition;

class Condition extends Resource
{
    public static $group = 'Decision';

    public static $model = DecisionProcessCondition::class;

    public static $title = 'decision_code';

    public static $search = [];

    public static $displayInNavigation = false;

    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Code')
                ->readonly(function () {
                    return $this->exists;
                })
                ->required(),

            Boolean::make('Negate'),

//            Json::make("Ddecision Prameters", [
//                Select::make(__("Discount Type"), "type")
//                    ->options([
//                        'percent' => __('Percent'),
//                        'amount' => __('Amount'),
//                    ])->rules('required'),
//            ]),

            Textarea::make("Parameters", "parameters_raw")
                ->withMeta($this->defaultValue('[]'))
                ->rules('json'),

            BelongsTo::make('Node', 'node', Node::class)
                ->readonly(),
        ];
    }

    public function cards(Request $request)
    {
        return [];
    }

    public function filters(Request $request)
    {
        return [];
    }

    public function lenses(Request $request)
    {
        return [];
    }

    public function actions(Request $request)
    {
        return [];
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }
}
