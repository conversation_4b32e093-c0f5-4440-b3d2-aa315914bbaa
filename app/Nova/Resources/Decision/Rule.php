<?php

declare(strict_types=1);

namespace App\Nova\Resources\Decision;

use App\Nova\Filters\InstanceFilter;
use App\Nova\Resources\Resource;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;

class Rule extends Resource
{
    public static $group = 'Decision';

    public static $model = \App\Rule::class;

    public static $title = 'name';

    public static $search = [
        'name'
    ];

    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->readonly($this->readonlyWhenExists())
                ->required(),

            Text::make('Level')->required(),

            $this->belongsToInstance()->readonly($this->readonlyWhenExists()),

            Textarea::make("Parameters", "parameters_raw")
                ->withMeta($this->defaultValue('[]'))
                ->rules('json'),
        ];
    }

    public function cards(Request $request)
    {
        return [];
    }

    public function filters(Request $request)
    {
        return [
            new InstanceFilter(),
        ];
    }

    public function lenses(Request $request)
    {
        return [];
    }

    public function actions(Request $request)
    {
        return [];
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }
}
