<?php

declare(strict_types=1);

namespace App\Nova\Resources\User;

use App\Nova\Filters\InstanceFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\LensRequest;
use <PERSON>vel\Nova\Lenses\Lens;

class InternalUserLens extends Lens
{
    public $name = 'Internal Users';

    public static function query(LensRequest $request, $query): Builder
    {
        return $request->withOrdering($request->withFilters(
            $query->where('internal', true)
        ));
    }

    public function fields(Request $request): array
    {
        return [
            ID::make('Id'),
            Text::make('Email'),
        ];
    }

    public function filters(Request $request): array
    {
        return [
            new InstanceFilter()
        ];
    }

    public function actions(Request $request): array
    {
        return [
            (new LoginAction)->showOnTableRow()
        ];
    }
}
