<?php

declare(strict_types=1);

namespace App\Nova\Resources\Dimension;

use App\Nova\Filters\InstanceFilter;
use App\Nova\Resources\Accounting\ExpenseType;
use App\Nova\Resources\Company;
use App\Nova\Resources\Instance;
use App\Nova\Resources\Resource;
use App\Services\SlugGeneratorService;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Ramsey\Uuid\Uuid;

class DimensionItems extends Resource
{
    public static $perPageViaRelationship = 25;

    public static $with = ['accountDimension'];

    public static $group = 'Dimensions';

    public static $model = AccountDimensionItem::class;

    public static $title = 'name';

    public static $search = [
        'id', 'name',
    ];

    public function title(): string
    {
        return sprintf("%s - %s", $this->name, $this->accountDimension->name);
    }

    /**
     * @param NovaRequest $request
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->sortable(),

            $this->fieldSlug(),

            Text::make('Code')
                ->hideFromIndex()
                ->sortable(),

            ...array_filter([
                ExpenseType::relationshipColumnsForDimensionItem($request, $this)
            ]),

            $this->activeField(),

            Boolean::make('Is Default'),

            BelongsTo::make('Dimension', 'accountDimension', Dimension::class)
                ->searchable(),

            $this->belongsToInstance(),
            $this->belongsToCompany()->nullable()->hideFromIndex(),

            HasOne::make('Dimension', 'accountDimension', Dimension::class),
        ];
    }

    public function cards(Request $request)
    {
        return [];
    }

    public function filters(Request $request)
    {
        return [
            new InstanceFilter
        ];
    }

    public function lenses(Request $request)
    {
        return [];
    }

    public function actions(Request $request)
    {
        return [];
    }
}
