<?php

namespace App\Nova\Resources\Report\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Modules\Report\Priv\Entity\Report;
use Modules\Report\Priv\Job\CreateReportJob;
use Modules\Report\Priv\Job\SwitchReportJob;

class SwitchReportAction extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Switch report';

    public function handle(ActionFields $fields, Collection $models): array
    {
        /** @var Report $model */
        foreach ($models as $model) {
            SwitchReportJob::dispatch($model->name);
        }

        return Action::message('Success');
    }
}
