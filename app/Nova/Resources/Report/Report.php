<?php

namespace App\Nova\Resources\Report;

use App\Nova\Resources\Report\Actions\CreateReportAction;
use App\Nova\Resources\Report\Actions\PopulateReportAction;
use App\Nova\Resources\Report\Actions\SwitchReportAction;
use App\Nova\Resources\Resource;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Modules\Report\Priv\Entity\Report as ReportEntity;
use Modules\Report\Priv\Enum\BaseModelEnum;

class Report extends Resource
{
    public static $model = ReportEntity::class;

    public function title(): string
    {
        return $this->name;
    }

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name'),

            Select::make('base_model')
                ->options(BaseModelEnum::toArray())
                ->displayUsingLabels()
                ->rules('required'),

            Text::make('mapping'),

            Text::make('Filter'),
        ];
    }

    public function actions(Request $request): array
    {
        return [
            new CreateReportAction(),
            new PopulateReportAction(),
            new SwitchReportAction(),
        ];
    }
}