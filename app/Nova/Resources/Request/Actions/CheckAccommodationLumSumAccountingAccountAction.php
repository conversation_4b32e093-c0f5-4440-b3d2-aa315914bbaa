<?php

namespace App\Nova\Resources\Request\Actions;

use Modules\Accounting\Priv\Enum\AccountingAccountKindEnum;
use Modules\DecisionMaker\Pub\Enums\DecisionProcessEnum;

class CheckAccommodationLumSumAccountingAccountAction extends AbstractCheckAccountingAccountAction
{
    public $name = 'Check access lump sum accounting account';

    public $withoutConfirmation = true;

    public function __construct()
    {
        parent::__construct();
        $this->decisionProcess = DecisionProcessEnum::ACCOMMODATION_LUM_SUM_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION();
        $this->accountingAccountKind = AccountingAccountKindEnum::ACCOMMODATION_LUMP_SUM();
    }
}