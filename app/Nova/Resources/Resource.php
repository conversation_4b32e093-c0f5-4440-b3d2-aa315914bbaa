<?php

namespace App\Nova\Resources;

use App\Nova\Resources\Accounting\VatNumber;
use App\Nova\Resources\Dimension\Account;
use App\Nova\Resources\User\User;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\MorphOne;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource as NovaResource;
use Modules\Accounting\Pub\Modules\Provider\Enums\SourceEnum;
use Ramsey\Uuid\Uuid;

abstract class Resource extends NovaResource
{
    public static function perPageOptions()
    {
        return [100, 50, 150];
    }

    protected static function applySearch($query, $search): Builder
    {
        if (static::usesScout()) {
            return parent::applySearch($query, $search);
        }

        $split = explode(' ', $search);

        foreach ($split as $phrase) {
            parent::applySearch($query, $phrase);
        }

        return $query;
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        if ($request->resourceId) {
            $resource = $request->findResourceOrFail();
            Instance::searchThroughInstance($resource, $query);
        }

        return $query;
    }

    public static function findInstance(NovaRequest $request): ?\App\Instance
    {
        if (!$request->viaResource()) {
            return null;
        }

        $parent = $request->findParentModelOrFail();
        if ($parent instanceof \App\Instance) {
            return $parent;
        }

        if (method_exists($parent, 'instance')) {
            return $parent->instance;
        }

        return null;
    }

    public static function scoutQuery(NovaRequest $request, $query)
    {
        return $query;
    }

    /**
     * Build a "detail" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  Builder  $query
     * @return Builder
     */
    public static function detailQuery(NovaRequest $request, $query)
    {
        return parent::detailQuery($request, $query);
    }

    /**
     * Build a "relatable" query for the given resource.
     *
     * This query determines which instances of the model may be attached to other resources.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  Builder  $query
     * @return Builder
     */
    public static function relatableQuery(NovaRequest $request, $query)
    {
        return parent::relatableQuery($request, $query);
    }

    public function sourceField(): Select
    {
        return Select::make('Source')
            ->withMeta($this->defaultValue(SourceEnum::CONSOLE()))
            ->readonly()
            ->options(SourceEnum::options())
            ->required()
            ->hideFromIndex()
            ->fillUsing(fn ($r, $m, $a) => SourceEnum::from($r->{$a}));
    }

    public function activeField()
    {
        return Boolean::make('Is Active', 'is_active_raw')->required()->sortable();
    }

    public function fieldSlug(): Text
    {
        return Text::make('Slug')
            ->withMeta($this->defaultValue(Uuid::uuid4()->toString()))
            ->required()
            ->hideFromIndex()
            ->sortable();
    }

    public function belongsToInstance(): BelongsTo
    {
        return BelongsTo::make('Instance', 'instance', Instance::class)
            ->searchable();
    }

    public function belongsToCompany(): BelongsTo
    {
        return BelongsTo::make('Company', 'company', Company::class)
            ->searchable();
    }

    public function belongsToUser(): BelongsTo
    {
        return BelongsTo::make('User', 'user', User::class)
            ->searchable();
    }

    public function belongsToLocation(): MorphOne
    {
        return MorphOne::make('Location', 'location', Location::class);
    }

    public function belongsToAccountingAccount(): BelongsTo
    {
        return BelongsTo::make('Accounting Account', 'accountingAccount', Account::class)
            ->searchable();
    }

    public function belongsToVatNumber(): BelongsTo
    {
        return BelongsTo::make('Vat number', 'vatNumber', VatNumber::class)->searchable();
    }

    public function orderField(): Text
    {
        return Number::make('Order')
            ->required()
            ->sortable();
    }

    public function defaultValue($value): array
    {
        return $this->exists ? [] : ['value' => $value];
    }

    public function displaySlug(): Text
    {
        return Text::make('Slug')->readonly()->onlyOnDetail();
    }

    public function readonlyWhenExists(): \Closure
    {
        return fn () => $this->exists;
    }

    public function renderJsonParameter(): \Closure
    {
        return function ($value) {
            return is_array($value) || is_object($value)
                ? json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                : $value;
        };
    }

    public function verifySavingJson(): \Closure
    {
        return function () {
            return [
                function ($attribute, $value, $fail) {
                    if (!empty($value) && is_string($value)) {
                        json_decode($value);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            $fail('To nie jest poprawny JSON.');
                        }
                    }
                }
            ];
        };
    }
}
