<?php

declare(strict_types=1);

namespace App\Nova\Resources;

use App\Nova\Filters\InstanceFilter;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Modules\MyCard\Pub\Facades\TenantFacade;

class Company extends Resource
{
    public static $model = \App\Company::class;

    public static $title = 'name';

    public static $search = [
        'id', 'name',
    ];

    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->sortable(),

            Text::make('Code')
                ->hideFromIndex(),

            Text::make('NIP')
                ->hideFromIndex(),

            Select::make('MyCard Tenant', 'mycard_tenant_id')
                ->options($this->getTenantIdOptions())
                ->sortable(),

            $this->fieldSlug(),

            $this->belongsToLocation(),

            $this->belongsToInstance(),
        ];
    }

    private function getTenantIdOptions(): array
    {
        /** @var TenantFacade $tenantFacade */
        $tenantFacade = app(TenantFacade::class);
        $tenants = $tenantFacade->getTenants();

        $tenantIds = [];
        foreach ($tenants as $tenant) {
            $tenantIds[$tenant->getId()] = $tenant->getName();
        }

        return $tenantIds;
    }

    public function cards(Request $request)
    {
        return [];
    }

    public function filters(Request $request)
    {
        return [
            new InstanceFilter(),
        ];
    }

    public function lenses(Request $request)
    {
        return [];
    }

    public function actions(Request $request)
    {
        return [];
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }
}
