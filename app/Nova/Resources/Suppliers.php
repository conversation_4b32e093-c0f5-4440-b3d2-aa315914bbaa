<?php

declare(strict_types=1);

namespace App\Nova\Resources;

use App\Nova\Filters\InstanceFilter;
use App\Provider;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class Suppliers extends Resource
{
    public static $model = Provider::class;

    public static $title = 'name';

    public static $search = [
        'id', 'name',
    ];

    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->displayUsing(fn ($value) => \Str::limit($value, 40))
                ->sortable(),

            $this->fieldSlug(),

            Text::make('Registry Number')
                ->sortable(),

            Text::make('Address')
                ->hideFromIndex()
                ->sortable(),

            Text::make('City')
                ->hideFromIndex()
                ->sortable(),

            Text::make('Postcode')
                ->hideFromIndex()
                ->sortable(),

            Text::make('Erp Id')
                ->sortable(),

            $this->sourceField(),

            $this->belongsToInstance(),

            BelongsTo::make('Company', 'company', Company::class)
                ->nullable()
                ->hideFromIndex(),

            BelongsTo::make('Country', 'country', Country::class)
                ->nullable()
                ->hideFromIndex(),
        ];
    }

    public function cards(Request $request)
    {
        return [];
    }

    public function filters(Request $request)
    {
        return [
            new InstanceFilter
        ];
    }

    public function lenses(Request $request)
    {
        return [];
    }

    public function actions(Request $request)
    {
        return [];
    }
}
