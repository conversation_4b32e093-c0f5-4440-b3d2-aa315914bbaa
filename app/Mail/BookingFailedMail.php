<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Modules\Obt\Pub\Dto\AbstractReservationDetailsDto;

class BookingFailedMail extends Mailable
{
    use Queueable, SerializesModels;

    protected AbstractReservationDetailsDto $dto;

    protected string $errorMessage;

    public function __construct(AbstractReservationDetailsDto $dto, string $errorMessage)
    {
        $this->dto = $dto;
        $this->errorMessage = $errorMessage;
    }

    public static function createFromReservationAndErrorMessage(AbstractReservationDetailsDto $reservationDto, string $errorMessage): BookingFailedMail
    {
        return new BookingFailedMail($reservationDto, $errorMessage);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $type = trans('mail.type_' . $this->dto->getType());
        $subject = sprintf(
            'Reservation failed: %s - %s %s - %s @ %s',
            $type,
            $this->dto->getCustomerUserDto()->getFirstName(),
            $this->dto->getCustomerUserDto()->getLastName(),
            $this->dto->getCustomerUserDto()->getEmail(),
            $this->dto->getCustomerUserDto()->getCustomerDto()->getDomain(),
        );

        return $this->from(config('mail.from.address'))
            ->subject($subject)
            ->view('mails.booking-failed')->with([
                'errorMessage' => $this->errorMessage,
                'type' => $type,
                'dto' => $this->dto,
            ]);
    }
}
