<?php

declare(strict_types=1);

namespace App\Enum;

/**
 * @method static OutboxStatusEnum PENDING
 * @method static OutboxStatusEnum RESERVED
 * @method static OutboxStatusEnum QUEUED
 * @method static OutboxStatusEnum IN_PROGRESS
 * @method static OutboxStatusEnum FINISHED
 */
class OutboxStatusEnum extends AbstractEnum
{
    private const PENDING = 'pending';
    private const RESERVED = 'reserved';
    private const QUEUED = 'queued';
    private const IN_PROGRESS = 'in_progress';
    private const FINISHED = 'finished';
}
