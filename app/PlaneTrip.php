<?php

namespace App;

use App\ElasticSearchConfigurators\PlaneTripIndexConfigurator;
use App\Helpers\NameTranslation;
use App\Interfaces\Models\LocalizableTripElementInterface;
use App\Interfaces\RequestElementAcceptanceSourceInterface;
use App\Interfaces\RequestElementDateableInterface;
use App\Interfaces\RequestElementDocumentableInterface;
use App\Interfaces\RequestElementNameTranslatedInterface;
use App\Interfaces\RequestElementObservableInterface;
use App\Repositories\LocationRepository;
use App\Traits\DestinationNameTrait;
use App\Traits\InstanceTrait;
use App\Traits\Models\AccountedAmount;
use App\Traits\Models\ConvertedAmount;
use App\Traits\Models\OriginalAmountsHelper;
use App\Traits\Models\RequestElementAcceptanceSourceTrait;
use App\Traits\PolymorphicDocumentElementsTrait;
use App\Traits\PolymorphicOfferTrait;
use App\Traits\RequestTrait;
use App\Traits\SuggestedElementTypeTrait;
use App\Vendors\Math;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\TripPlanner\Pub\Enums\RequestElementAcceptanceSourceEnum;
use ScoutElastic\Searchable;


/**
 * App\PlaneTrip
 *
 * @property Currency amountCurrency
 * @property mixed amount
 * @property string search_uuid
 * @property string uuid
 * @property RequestElementAcceptanceSourceEnum request_element_acceptance_source
 * @property int number_of_paxes
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int $instance_id
 * @property int $request_id
 * @property string $uuid
 * @property RequestElementAcceptanceSourceEnum $request_element_acceptance_source
 * @property int $type_id
 * @property string $flight_class
 * @property \Illuminate\Support\Carbon $arrival_at
 * @property int $round_trip
 * @property \Illuminate\Support\Carbon|null $return_at
 * @property string|null $amount
 * @property int|null $amount_currency_id
 * @property int $weight
 * @property int $return_weight
 * @property bool $searcher_disabled
 * @property bool $direct_only
 * @property string $search_uuid
 * @property int $number_of_paxes
 * @property-read \App\Currency|null $amountCurrency
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\DocumentElement[] $documentElements
 * @property-read int|null $document_elements_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Document[] $documents
 * @property-read int|null $documents_count
 * @property-read \App\Location|null $flightFromLocation
 * @property-read \App\Location|null $flightToLocation
 * @property-read \App\Instance $instance
 * @property-read \App\Offer|null $offer
 * @property-read \App\Request $request
 * @property-read \Modules\Accounting\Priv\Entities\DocumentElementType|null $suggestedElementType
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $users
 * @property-read int|null $users_count
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip newQuery()
 * @method static \Illuminate\Database\Query\Builder|PlaneTrip onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip query()
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereAmountCurrencyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereArrivalAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereDirectOnly($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereFlightClass($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereNumberOfPaxes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereRequestElementAcceptanceSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereReturnAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereReturnWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereRoundTrip($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereSearchUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereSearcherDisabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereWeight($value)
 * @method static \Illuminate\Database\Query\Builder|PlaneTrip withTrashed()
 * @method static \Illuminate\Database\Query\Builder|PlaneTrip withoutTrashed()
 * @mixin \Eloquent
 * @property int $is_accepted
 * @method static \Illuminate\Database\Eloquent\Builder|PlaneTrip whereIsAccepted($value)
 */
class PlaneTrip extends AbstractRequestElement implements
    RequestElementObservableInterface,
    RequestElementDateableInterface,
    RequestElementDocumentableInterface,
    RequestElementNameTranslatedInterface,
    LocalizableTripElementInterface,
    RequestElementAcceptanceSourceInterface
{
    use SoftDeletes;
    use RequestTrait;
    use InstanceTrait;
    use ConvertedAmount;
    use OriginalAmountsHelper;
    use AccountedAmount;
    use SuggestedElementTypeTrait;
    use PolymorphicDocumentElementsTrait;
    use DestinationNameTrait;
    use PolymorphicOfferTrait;
    use RequestElementAcceptanceSourceTrait;


    protected $table = 'request_plane_trips';

    protected $touches = ['request'];

    protected $fillable = [
        'uuid',
        'request_id',
        'instance_id',
        'flight_class',
        'arrival_at',
        'round_trip',
        'return_at',
        'amount',
        'amount_currency_id',
	    'weight',
	    'searcher_disabled',
        'direct_only',
        'search_uuid',
        'number_of_paxes',
    ];

    protected $dates = [
        'arrival_at',
        'return_at'
    ];

	protected $casts = [
		'searcher_disabled' => 'boolean',
		'direct_only' => 'boolean',
	];

    const RELATION_NAME = 'plane_trip';

    //flight class
    const CLASS_ECONOMY = 'class_economy';
    const CLASS_ECONOMY_PREMIUM = 'class_economy_premium';
    const CLASS_BUSINESS = 'class_business';
    const CLASS_FIRST = 'class_first';

    public static function isClassLowerOrEqual(string $class1, string $class2): bool
    {
        $hierarchy = [
            self::CLASS_ECONOMY => 1,
            self::CLASS_ECONOMY_PREMIUM => 2,
            self::CLASS_BUSINESS => 3,
            self::CLASS_FIRST => 4,
        ];

        return $hierarchy[$class1] <= $hierarchy[$class2];
    }

    public const RETURN_FLIGHT = 'return';
    public const TARGET_FLIGHT = 'target';

    public static function flightClasses()
    {
        return collect([
           static::CLASS_FIRST,
           static::CLASS_BUSINESS,
           static::CLASS_ECONOMY_PREMIUM,
           static::CLASS_ECONOMY
        ]);
    }

    public function toSearchableArray()
    {
        $arr = array_only($this->toArray(), [
            'id',
            'arrival_at',
            'return_at',
            'amount',
        ]);

        if($this->flightFromLocation) {
            $arr['locations'][] = $this->flightFromLocation->toSearchableArray();
        }
        if($this->flightToLocation) {
            $arr['locations'][] = $this->flightToLocation->toSearchableArray();
        }

        return $arr;
    }

    public function flightFromLocation()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'flight_from']);
    }

    public function flightToLocation()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'flight_to']);
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function documents()
    {
        return $this->morphToMany(Document::class, 'element', 'request_element_document');
    }

    public function amountCurrency()
    {
        return $this->hasOne(Currency::class, 'id', 'amount_currency_id');
    }

    public function getStartDate()
    {
        return $this->arrival_at;
    }

    public function getEndDate()
    {
        return $this->return_at;
    }

    public function getOriginalAmounts(): array
    {

        return $this->addOriginalItem(
            [],
            $this->amountCurrency,
            $this->amount

        );
    }

    public function getAmountAttribute()
    {
        if ($this->request_element_acceptance_source->equals(RequestElementAcceptanceSourceEnum::PRIMARY())) {
            return Math::round($this->attributes['amount'], 2);
        }

        return Math::round(0, 2);
    }

    public function getName($separator = ' → '): ?string
    {
        return $this->getNameTranslation($separator)->translate();
    }

    public function getNameTranslation($separator = ' → '): NameTranslation
    {
        return new NameTranslation('request-summary.plane-trip', ['details' => $this->getShortName($separator)]);
    }

    public function getShortName($separator = ' → '): string
    {
        $from = $this->flightFromLocation->city ?? null;
        $to = $this->flightToLocation->city ?? null;

        return $this->getFromToString($from, $to, $separator);
    }

    public function getType(): ?string
    {
        return static::RELATION_NAME;
    }

    public function isNational(): bool
    {
        $startLocationIsNational = LocationRepository::getCountryFromLocation($this->flightFromLocation)->id === $this->instance->country_id;
        $endLocationIsNational   = LocationRepository::getCountryFromLocation($this->flightToLocation)->id === $this->instance->country_id;

        return $startLocationIsNational && $endLocationIsNational;
    }

    public function isAbroad(): bool
    {
        return !$this->isNational();
    }

    public function isEuropean(): bool
    {
        return !$this->isNational() && !$this->isIntercontinental();
    }

    public function isIntercontinental(): bool
    {
        $startLocationContinent = $this->flightFromLocation->additional_data['continent_code'] ?? null;
        $endLocationContinent  = $this->flightToLocation->additional_data['continent_code'] ?? null;

        return $startLocationContinent !== $endLocationContinent;
    }

	public function setSearcherDisabledAttribute($value)
	{
		$this->attributes['searcher_disabled'] = $value;
	}

	public function getDepartureDateForTarget(): ?Carbon
    {
        try {
            $targetFlightAttributes = $this->getFlightAttributes(self::TARGET_FLIGHT);

            if ($targetFlightAttributes !== null && isset($targetFlightAttributes['departureDateTime']) === true) {
                return Carbon::createFromFormat('Y-m-d H:i:s', $targetFlightAttributes['departureDateTime']);
            }
        } catch (\Throwable $exception) {
            return $this->getStartDate();
        }

        return $this->getStartDate();
    }

    public function getArrivalDateForTarget(): ?Carbon
    {
        try {
            $targetFlightAttributes = $this->getFlightAttributes(self::TARGET_FLIGHT);

            if ($targetFlightAttributes !== null && isset($targetFlightAttributes['arrivalDateTime']) === true) {
                return Carbon::createFromFormat('Y-m-d H:i:s', $targetFlightAttributes['arrivalDateTime']);
            }
        } catch (\Throwable $exception) {
            return $this->getStartDate();
        }

        return $this->getStartDate();
    }

    public function getDepartureDateForReturnIfRoundTrip(): ?Carbon
    {
        if ((bool)$this->round_trip !== true) {
            return $this->getEndDate();
        }

        try {
            $targetFlightAttributes = $this->getFlightAttributes(self::RETURN_FLIGHT);

            if ($targetFlightAttributes !== null && isset($targetFlightAttributes['departureDateTime']) === true) {
                return Carbon::createFromFormat('Y-m-d H:i:s', $targetFlightAttributes['departureDateTime']);
            }
        } catch (\Throwable $exception) {
            return $this->getEndDate();
        }

        return $this->getEndDate();
    }

    public function getArrivalDateForReturnIfRoundTrip(): ?Carbon
    {
        if ((bool)$this->round_trip !== true) {
            return $this->getEndDate();
        }

        try {
            $targetFlightAttributes = $this->getFlightAttributes(self::RETURN_FLIGHT);

            if ($targetFlightAttributes !== null && isset($targetFlightAttributes['arrivalDateTime']) === true) {
                return Carbon::createFromFormat('Y-m-d H:i:s', $targetFlightAttributes['arrivalDateTime']);
            }
        } catch (\Throwable $exception) {
            return $this->getEndDate();
        }

        return $this->getEndDate();
    }

    public function getFlightAttributes(string $type): ?array
    {
        $offer = $this->offer;

        if ($offer instanceof Offer && is_array($offer->full_offer) === true) {
            $target = collect($offer->full_offer)->filter(function ($item) use ($type) {
                return $item['attributes']['flightType'] === $type;
            })->first();

            return $target['attributes'] ?? null;
        }

        return null;
    }

    public function getNameTranslated(?string $lang = null): string
    {
        return trans('request-summary.plane-trip', ['details' => $this->getShortName()], $lang ? $lang : $this->request->user->locale);
    }

    public function getStartDateAttributeName(): string
    {
        return 'arrival_at';
    }

    public function getEndDateAttributeName(): string
    {
        return 'return_at';
    }
}
