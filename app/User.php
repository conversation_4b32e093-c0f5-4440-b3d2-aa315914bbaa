<?php

namespace App;

use App\BillingAPI\DTOs\Notification\EmailNotificationSettingsDto;
use App\Company\Policy;
use App\ElasticSearchConfigurators\Rules\UserCrossFieldsRule;
use App\ElasticSearchConfigurators\Rules\UserMultiMatchRule;
use App\ElasticSearchConfigurators\UserIndexConfigurator;
use App\Enum\EmailNotificationModeEnum;
use App\ERP\Contracts\HasERPCode;
use App\Exceptions\InvalidEmailNotificationSettingsException;
use App\Interfaces\Models\SearchWithRulesInterface;
use App\Interfaces\Monitoring\EmailNotification\EmailNotificationSettingsInterface;
use App\Notifications\Notification;
use App\PersonalDataProviders\Instances\ConnectorResolver;
use App\PersonalDataProviders\PersonalDataService;
use App\PersonalDataProviders\UserPersonalData;
use App\Repositories\UserAssistantRepository;
use App\Repositories\UserDeputyRepository;
use App\Scopes\User\NonInternalUserScope;
use App\Services\Cache;
use App\Services\Country\CountryService;
use App\Traits\CompanyTrait;
use App\Traits\FileUriTrait;
use App\Traits\GroupTrait;
use App\Traits\InstanceTrait;
use App\Traits\LangAttributeTrait;
use Awobaz\Compoships\Compoships;
use Carbon\Carbon;
use Illuminate\Contracts\Notifications\Dispatcher;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\HasDatabaseNotifications;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\RoutesNotifications;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Laravel\Passport\HasApiTokens;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Entities\Traits\MpkTrait;
use Modules\Analytics\Priv\Entities\UserAccountDimensionItem;
use Modules\Common\Entities\Traits\BlameableTrait;
use Modules\Users\Priv\Services\NotificationService;
use Ramsey\Uuid\Uuid;
use ScoutElastic\Builders\FilterBuilder;
use ScoutElastic\Builders\SearchBuilder;
use ScoutElastic\Searchable;

/**
 * App\User
 *
 * @property int id
 * @property string email
 * @property Instance instance
 * @property string name
 * @property int instance_id
 * @property int company_id
 * @property bool is_admin
 * @property int parent_id
 * @property int user_id
 * @property int mpk_id
 * @property string first_name
 * @property string last_name
 * @property string full_name
 * @property Carbon|null blocked_at
 * @property Mpk mpk
 * @property Company company
 * @property string slug
 * @property Collection groups
 * @property string locale
 * @property string avatar
 * @property mixed phone
 * @property mixed phone_ice
 * @property string sex
 * @property Country nationality
 * @property Country citizenship
 * @property Carbon|null birth_date
 * @property Carbon|null passport_issue_date
 * @property Carbon|null passport_valid_date
 * @property string|null passport_number
 * @property string|null identity_card_number
 * @property string|null iban
 * @property string|null license_plate
 * @property string|null company_license_plate
 * @property string|null erp_id
 * @property string|null hr_id
 * @property string level
 * @property string grade
 * @property int|null nationality_id
 * @property int|null citizenship_id
 * @property int|null created_by
 * @property int|null updated_by
 * @property Carbon|null last_synced_at
 * @property null|User supervisor
 * @property string pin_code
 * @property int bad_pin_code_attempts
 * @property boolean internal
 * @property Location|null workLocation
 * @property Provider|null provider
 * @property Collection<UserAccountDimensionItem> accountDimensionItems
 * @property string $identity_provider_id
 * @property string $employee_unique_identifier
 * @property Collection<User> $availableIdentities
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $blocked_at
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $instance_id
 * @property int $company_id
 * @property int|null $mpk_id
 * @property int|null $user_id
 * @property bool $is_admin
 * @property string $locale
 * @property string $slug
 * @property string $email
 * @property string|null $password
 * @property string $grade
 * @property string $level
 * @property string $policy
 * @property string $first_name
 * @property string $last_name
 * @property string $sex
 * @property \Illuminate\Support\Carbon|null $birth_date
 * @property string|null $passport_number
 * @property \Illuminate\Support\Carbon|null $passport_valid_date
 * @property \Illuminate\Support\Carbon|null $passport_issue_date
 * @property string|null $phone
 * @property string|null $phone_ice
 * @property string|null $avatar
 * @property string|null $identity_card_number
 * @property string|null $license_plate
 * @property string|null $company_license_plate
 * @property string|null $erp_id
 * @property string|null $hr_id
 * @property int|null $nationality_id
 * @property int $citizenship_id
 * @property int $is_new
 * @property string|null $iban
 * @property string|null $pin_code
 * @property int $bad_pin_code_attempts
 * @property bool $internal
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $last_synced_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Request[] $acceptorRequests
 * @property-read int|null $acceptor_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection|UserAccountDimensionItem[] $accountDimensionItems
 * @property-read int|null $account_dimension_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Request[] $accountedRequests
 * @property-read int|null $accounted_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\UserAssistant[] $assistants
 * @property-read int|null $assistants_count
 * @property-read int|null $available_identities_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Card[] $cards
 * @property-read int|null $cards_count
 * @property-read \App\Country $citizenship
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\OAuthClient[] $clients
 * @property-read int|null $clients_count
 * @property-read \App\Company $company
 * @property-read User|null $createdBy
 * @property-read mixed $full_name
 * @property \ScoutElastic\Highlight|null $highlight
 * @property-read mixed $lang
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Group[] $groups
 * @property-read int|null $groups_count
 * @property-read \App\Instance $instance
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\LoyaltyCard[] $loyaltyCards
 * @property-read int|null $loyalty_cards_count
 * @property-read Mpk|null $mpk
 * @property-read \Illuminate\Database\Eloquent\Collection|Mpk[] $mpks
 * @property-read int|null $mpks_count
 * @property-read \App\Country|null $nationality
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection|\Illuminate\Notifications\DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read \App\Provider|null $provider
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Request[] $requests
 * @property-read int|null $requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Request[] $settlementAcceptorRequests
 * @property-read int|null $settlement_acceptor_requests_count
 * @property-read User|null $supervisor
 * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Passport\Token[] $tokens
 * @property-read int|null $tokens_count
 * @property-read User|null $updatedBy
 * @property-read \Illuminate\Database\Eloquent\Collection|User[] $users
 * @property-read int|null $users_count
 * @property-read \App\Location|null $workLocation
 * @property-read Mpk[]|Collection $hasMpks
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Query\Builder|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereBadPinCodeAttempts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereBirthDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereBlockedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCitizenshipId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCompanyLicensePlate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmployeeUniqueIdentifier($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereErpId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereGrade($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereHrId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIban($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIdentityCardNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIdentityProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereInternal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIsAdmin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIsNew($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLastSyncedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLicensePlate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereMpkId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereNationalityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassportIssueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassportNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassportValidDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePhoneIce($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePinCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePolicy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereSex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|User withTrashed()
 * @method static \Illuminate\Database\Query\Builder|User withoutTrashed()
 * @mixin \Eloquent
 * @property-read int|null $has_mpks_count
 * @property-read \Illuminate\Database\Eloquent\Collection|User[] $subordinates
 * @property-read int|null $subordinates_count
 * @method static Builder|User inCompany(\App\Company $company)
 */
class User extends Authenticatable implements HasERPCode, SearchWithRulesInterface, HasManyMpkModelInterface
{
    use HasDatabaseNotifications;
    use RoutesNotifications;
    use SoftDeletes;
    use HasApiTokens;
    use InstanceTrait;
    use MpkTrait;
    use CompanyTrait;
    use GroupTrait;
    use LangAttributeTrait;
    use FileUriTrait;
    use Searchable;
    use BlameableTrait;
    use Compoships;
    use HasManyMpkModelTrait;
    use Notifiable {
        notifyNow as public laravelNotify;
   }

    const ACTIVE_AS_FINANCE = 'Finance';
    const ACTIVE_AS_MANAGER = 'Manager';
    const ACTIVE_AS_REGULAR = 'Regular';
    const ACTIVE_AS_TRAVELER = 'Traveler';

    public const SORTABLE_COLUMNS = [
        'full_name',
        'email',
        'supervisor_full_name',
        'blocked_at',
        'company_code'
    ];

    protected $indexConfigurator = UserIndexConfigurator::class;

    // For elastic searches only.
    protected $searchRules = [];

    protected $personalData = null;

    protected $lazyIsAccountant = null;

    const USER_SEX_MALE = 'm';
    const USER_SEX_FEMALE = 'f';
    const RELATION_NAME = 'user';
    const GROUP_NAME_ADMINISTRATOR = 'Administrator';
    const GROUP_NAME_CONTROL = 'Control';
    const GROUP_NAME_AGENT = 'Agent';
    const GROUP_NAME_REGULAR = 'Regular';
    const GROUP_NAME_APPROVER = 'Approver';
    const GROUP_NAME_FINANCE = 'Finance';
    const GROUP_NAME_BILLING = 'Billing';
    const GROUP_NAME_INTEGRATION_API = 'IntegrationAPI';
    const GROUP_NAME_HR = 'HR';
    const GROUP_NAME_COORDINATOR = 'Coordinator';
    public const GROUP_NAME_REGIONAL_POSITION = 'RegionalPosition';
    public const GROUP_NAME_CEO = 'CEO';
    public const GROUP_NAME_CFO = 'CFO';
    public const GROUP_NAME_BM = 'BoardMember';
    public const GROUP_NAME_TAXI = 'TAXI';

    public const GROUP_MYCARD_EMPLOYEE = 'MyCard Employee';

    public const GROUP_MYCARD_ACCOUNTANT = 'MyCard Accountant';

    public const GROUP_MYCARD_MANAGER = 'MyCard Manager';

    public const GROUP_B2B = 'B2B';

    public const GROUP_FREEMIUM = 'Freemium';

    public const GROUP_PURCHASE = 'Purchase';

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'phone',
        'phone_ice',
        'instance_id',
        'company_id',
        'user_id',
        'grade',
        'level',
        'mpk_id',
        'blocked_at',
        'sex',
        'nationality_id',
        'citizenship_id',
        'birth_date',
        'passport_number',
        'passport_issue_date',
        'passport_valid_date',
        'identity_card_number',
        'policy',
        'locale',
        'license_plate',
        'company_license_plate',
        'erp_id',
        'hr_id',
        'locale',
        'iban',
        'remember_token',
        'employee_unique_identifier',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $dates = [
        'deleted_at',
        'blocked_at',
        'birth_date',
        'passport_issue_date',
        'passport_valid_date'
    ];

    protected $casts = [
        'is_admin' => 'boolean',
        'internal' => 'boolean',
    ];

    protected $mapping = [
        'properties' => [
            'first_name' => ['type' => 'keyword', 'normalizer' => 'useLowercase'],
            'last_name' => ['type' => 'keyword', 'normalizer' => 'useLowercase'],
            'email' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'company_name' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'group_names' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'mpk_name' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'status_name_pl' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'status_name_en' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'company_code' => ['type' => 'keyword'],
            'level' => ['type' => 'keyword'],
            'erp_id' => ['type' => 'keyword'],
            'hr_id' => ['type' => 'keyword'],
            'cross_first_name' => [
                'type' => 'text',
                'analyzer' => 'nGram_analyzer',
                'search_analyzer' => 'whitespace_analyzer'
            ],
            'cross_last_name' => [
                'type' => 'text',
                'analyzer' => 'nGram_analyzer',
                'search_analyzer' => 'whitespace_analyzer'
            ],
            'cross_email' => [
                'type' => 'text',
                'analyzer' => 'nGram_analyzer',
                'search_analyzer' => 'whitespace_analyzer'
            ],
            'cross_company_code' => [
                'type' => 'text',
                'analyzer' => 'nGram_analyzer',
                'search_analyzer' => 'whitespace_analyzer'
            ],
        ]
    ];

    public const MULTI_MATCH_USER_SEARCH = 'multi_match_user_search';
    public const CROSS_FIELDS_USER_SEARCH = 'cross_fields_user_search';

    public static function generateSlug(): string
    {
        return Uuid::uuid4()->toString();
    }

    protected function setSearchRule(?string $searchType = null): void
    {
        switch ($searchType) {
            case self::CROSS_FIELDS_USER_SEARCH:
                $this->searchRules = [UserCrossFieldsRule::class];
                break;
            default:
                $this->searchRules = [UserMultiMatchRule::class];
                break;
        }
    }

    public static function withoutInternals()
    {
        return static::addGlobalScope(new NonInternalUserScope());
    }

    public function availableIdentities(): HasMany
    {
        return $this->hasMany(
            User::class,
            ['employee_unique_identifier', 'instance_id'],
            ['employee_unique_identifier', 'instance_id']
        );
    }

    public static function searchWithRules(string $query, ?string $searchType = null, ?callable $callback = null)
    {
        $model = new static;
        $model->setSearchRule($searchType);
        $softDelete = static::usesSoftDelete() && config('scout.soft_delete', false);

        if ($query == '*') {
            return new FilterBuilder($model, $callback, $softDelete);
        } else {
            return new SearchBuilder($model, $query, $callback, $softDelete);
        }
    }

    public static function getAvailableSexes()
    {
        return collect([
            static::USER_SEX_MALE,
            static::USER_SEX_FEMALE,
        ]);
    }

    public function toSearchableArray()
    {
        $searchableArray = [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'company_name' => $this->company instanceof Company ? $this->company->name : null,
            'group_names' => $this->getGroupNames(),
            'mpk_name' => $this->mpk instanceof Mpk ? $this->mpk->name : null,
            'status_name_pl' => empty($this->blocked_at) === false
                ? trans(
                    'instance-users.status-deactivate',
                    ['date' => $this->blocked_at->toDateTimeString()],
                    'pl_PL.utf8'
                )
                : trans('instance-users.status-active', [], 'pl_PL.utf8'),
            'status_name_en' => empty($this->blocked_at) === false
                ? trans('instance-users.status-deactivate', ['date' => $this->blocked_at->toDateTimeString()], 'en')
                : trans('instance-users.status-active', [], 'en'),
            'company_code' => $this->company instanceof Company ? $this->company->code : null,
            'level' => $this->level,
            'cross_first_name' => $this->first_name,
            'cross_last_name' => $this->last_name,
            'cross_email' => $this->email,
            'cross_company_code' => $this->company instanceof Company ? $this->company->code : null,
            'erp_id' => $this->erp_id,
            'hr_id' => $this->hr_id,
        ];

        foreach ($searchableArray as $index => $value) {
            $searchableArray[$index] = strtolower($value);
        }

        return $searchableArray;
    }

    protected function getGroupNames(): string
    {
        $groupNames = '';

        $this->groups->each(function (Group $group) use (&$groupNames) {
            $groupNames = $groupNames . ' ' . trans('role-group.' . strtolower($group->name));
        });

        return $groupNames;
    }

    //todo dodać obsługę aktywnego usera, np. czy nie jest zablokowany blocked_at
    public function isActive(): bool
    {
        return null === $this->blocked_at && null === $this->deleted_at;
    }

    public function isSuperAdmin()
    {
        return $this->is_admin || in_array($this->id, config('vaterval.super_admin_ids'));
    }

    public function isAdmin(): bool
    {
        return $this->is_admin || $this->hasGroup(static::GROUP_NAME_ADMINISTRATOR);
    }

    public function isManager(): bool
    {
        return $this->hasGroup(static::GROUP_NAME_APPROVER);
    }

    public function hasGroup(string $group): bool
    {
        return $this->groups->where('name', '=', $group)->isNotEmpty();
    }

    public function getLocale()
    {
        return $this->locale;
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function requests()
    {
        return $this->hasMany(Request::class);
    }

    public function accountDimensionItems(): HasMany
    {
        return $this->hasMany(UserAccountDimensionItem::class);
    }

    public function accountedRequests()
    {
        return $this->hasMany(Request::class, 'accounting_user_id');
    }

    public function acceptorRequests()
    {
        return $this->belongsToMany(Request::class, 'request_acceptors_user')
            ->withPivot(['accepted', 'default', 'added_by', 'acceptance_date', 'token', 'comment_id']);
    }

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class);
    }

    public function instance(): BelongsTo
    {
        return $this->belongsTo(Instance::class);
    }

    public function settlementAcceptorRequests()
    {
        return $this->belongsToMany(Request::class, 'request_settlement_acceptors_user')
            ->withPivot(['accepted', 'default', 'added_by', 'acceptance_date', 'token', 'comment_id']);
    }

    public function supervisor()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function nationality()
    {
        return $this->belongsTo(Country::class, 'nationality_id');
    }

    public function citizenship()
    {
        return $this->belongsTo(Country::class, 'citizenship_id');
    }

    public function loyaltyCards()
    {
        return $this->hasMany(LoyaltyCard::class);
    }

    public function assistants()
    {
        return $this->hasMany(UserAssistant::class);
    }

    public function cards()
    {
        return $this->belongsToMany(Card::class, 'card_users')->withPivot(['default']);
    }

    public function hasSupervisor()
    {
        return $this->user_id !== null;
    }

    public function workLocation()
    {
        return $this->morphOne(Location::class, 'localizable');
    }

    public function routeNotificationForSlack($driver, $notification = null)
    {
        return 'https://slack.com/api/chat.postMessage';
    }

    /**
     * @param $instance Notification
     * not sure 100% so I don't type-hint
     * @param array $channels
     */
    public function notify($instance, array $channels = null)
    {
        $notificationService = resolve(NotificationService::class);

        $notificationService->notify($this, $instance);
    }

    /**
     * @param $instance Notification
     * not sure 100% so I don't type-hint
     */
    public function notifyNow($instance, array $channels = null)
    {
        $notificationService = resolve(NotificationService::class);

        $notificationService->notify($this, $instance, false, $channels);
    }

    public function routeNotificationForMail()
    {
        try {
            $emailNotificationSettingsDto = resolve(EmailNotificationSettingsInterface::class)->getSettings(
                $this->instance->id,
                $this->instance->domain
            );

            if ($emailNotificationSettingsDto instanceof EmailNotificationSettingsDto) {
                switch ((string)$emailNotificationSettingsDto->getMode()) {
                    case (string)EmailNotificationModeEnum::ENABLED():
                        return $this->email;
                    case (string)EmailNotificationModeEnum::FORWARDING():
                        return (string)$emailNotificationSettingsDto->getEmail();
                    case (string)EmailNotificationModeEnum::DISABLED();
                        return null;
                }
            }
        } catch (\Throwable $exception) {
            InvalidEmailNotificationSettingsException::create($this->instance->domain);
            return $this->email;
        }
    }

    public function haveDeputies(): bool
    {
        return $this->getActiveUserDeputies()->isNotEmpty();
    }

    public function haveAssistants(): bool
    {
        return $this->getActiveUserAssistants()->isNotEmpty();
    }

    /**
     * @return Collection UserAssistant[]
     */
    public function getActiveUserAssistants(): Collection
    {
        return resolve(UserAssistantRepository::class)->getUserAssitants($this);
    }

    /**
     * @return Collection UserDeputy[]
     */
    public function getActiveUserDeputies(): Collection
    {
        return resolve(UserDeputyRepository::class)->getActiveUserDeputies($this);
    }

    /**
     * Returns list of enabled notification channels.
     *
     * Returns:
     * [
     *   "ui" => true,
     *   "sms" => false,
     *   "mail" => false,
     * ]
     *
     * @param $notificationClassName
     *
     * @return array
     */
    public function getNotificationSettings($notificationClassName): array
    {
        //todo If exists notification settings for user, load it instead of default settings

        //default settings
        return $notificationClassName::getDefaultChannelsSettings();
    }

    /**
     * It will be used to generate notification user settings view
     *
     * Returns:
     * [
     *   "App\Notifications\ExampleNotification" => [
     *      "ui" => true,
     *      "sms" => false,
     *      "mail" => false,
     *   ], [...]
     * ]
     *
     * @return array
     */
    public function getListNotificationSettings(): array
    {
        $listNotificationSettings = [];

        foreach (config('vaterval.notifications') as $className) {
            $listNotificationSettings[$className] = $this->getNotificationSettings($className);
        }

        return $listNotificationSettings;
    }

    /**
     * Returns list of all subordinates on all levels of the depth.
     *
     * @return Collection
     */
    public function getSubordinates(): Collection
    {
        $usersCollection = collect();
        $directSubordinates = $this->getDirectSubordinates();

        $this->fetchSubordinatesRecursive($directSubordinates, $usersCollection);

        return $usersCollection->pluck('id');
    }

    public function scopeInCompany(Builder $query, Company $company): void
    {
        $query->where('company_id', $company->id);
    }

    public function subordinates(): HasMany
    {
        return $this->hasMany(User::class, 'user_id');
    }

    public function getDirectSubordinates(): Collection
    {
        return User::where(['instance_id' => $this->instance->id])
            ->where(['user_id' => $this->id])
            ->get();
    }

    protected function fetchSubordinatesRecursive(Collection $subordinates, Collection &$usersCollection): void
    {
        $subordinates->each(function (User $user) use (&$usersCollection) {
            if ($usersCollection->has($user->id) === false) {
                $usersCollection->put(
                    $user->id,
                    (object)[
                        'id' => $user->id,
                        'parent_id' => $user->user_id
                    ]
                );

                $directSubordinates = $user->getDirectSubordinates();

                if ($directSubordinates->isNotEmpty()) {
                    $this->fetchSubordinatesRecursive($directSubordinates, $usersCollection);
                }
            }
        });
    }

    public function hasAbility(string $ability): bool
    {
        return $this->hasAnyAbility([$ability]);
    }

    public function userAbilities(): Collection
    {
        return Cache::get('user-' . $this->id . '-abilities', function () {
            return $this->loadMissing('groups.permissions')->groups
                ->map(fn(Group $group) => $group->permissions)
                ->flatten()
                ->filter(fn($permission) => $permission->can)
                ->pluck('ability')
                ->unique();
        }, 3600, ['permissions', $this->getCacheTag()]);
    }

    public function hasAnyAbility(array $abilities): bool
    {
        return $this->userAbilities()->intersect($abilities)->isNotEmpty();
    }

    public static function clearAbilityCache(): void
    {
        \Cache::tags('permissions')->clear();
    }

    public function getAvatarPublicPath()
    {
        if (empty($this->attributes['avatar'])) {
            return asset('images/avatar.png');
        }

        // hack for jarek and lukasz avatars. VTL-3235
        if (str_contains($this->attributes['avatar'], '//')) {
            return $this->attributes['avatar'];
        }

        if ($this->email === $this->instance->getModuleSettings('technical_user')['email']) {
            return asset($this->avatar);
        }

        return asset($this->getThumbUriTemplate($this->avatar, $this->avatarPath()));
    }

    public function getAvatar(): string
    {
        return $this->attributes['avatar'] ?? (string)config('vaterval.default_avatar');
    }

    protected function avatarPath()
    {
        return "storage/avatars/{$this->slug}";
    }

    /** On demand attributes */

    public function getBirthDateAttribute()
    {
        if ($this->attributes['birth_date']) {
            return new Carbon($this->attributes['birth_date']);
        }

        if ($this->instance->moduleEnabled(PersonalDataService::MODULE_NAME) && $this->isFromErp()) {
            return optional($this->getPersonalDataFromProvider())->birthDate;
        }

        return null;
    }

    public function getCitizenshipAttribute()
    {
        if ($this->instance->moduleEnabled(PersonalDataService::MODULE_NAME) && $this->isFromErp()) {
            $nationalityCode = optional($this->getPersonalDataFromProvider())->nationality;

            return $nationalityCode
                ? resolve(CountryService::class)->getCountryByCode($nationalityCode)
                : $this->instance->country;
        }

        return $this->citizenship()->first();
    }

    public function getIdentityCardNumberAttribute()
    {
        if ($this->attributes['identity_card_number']) {
            return $this->attributes['identity_card_number'];
        }

        if ($this->instance->moduleEnabled(PersonalDataService::MODULE_NAME) && $this->isFromErp()) {
            return optional($this->getPersonalDataFromProvider())->identityCardNumber;
        }

        return null;
    }

    /** On demand attributes end*/

    public function getFullNameAttribute()
    {
        return $this->first_name . " " . $this->last_name;
    }

    public function getUserNameSuffix(): string
    {
        $suffixes = [];

        $this->groups->each(function ($group) use (&$suffixes) {
            if (!empty($group->user_name_suffix)) {
                $suffixes[] = $group->user_name_suffix;
            }
        });

        return $suffixes ? implode(', ', $suffixes) : '';
    }

    /**
     * @return bool
     */
    public function isAccountant()
    {
        if ($this->lazyIsAccountant === null) {
            $this->lazyIsAccountant = $this->can('accounting', $this);
        }

        return $this->lazyIsAccountant;
    }

    /**
     * @return bool
     */
    public function isController()
    {
        $userGroups = $this->groups->all();

        foreach ($userGroups as $checkGroup) {
            if ($checkGroup instanceof Group) {
                $checkGroupName = $checkGroup->name;
            } else {
                $checkGroupName = $checkGroup['name'];
            }

            if (strtolower($checkGroupName) === strtolower(self::GROUP_NAME_CONTROL)) {
                return true;
            }
        }

        return false;
    }

    public function isAgent()
    {
        return $this->groups->where('name', '=', static::GROUP_NAME_AGENT)->isNotEmpty();
    }

    public function isRegular()
    {
        return $this->groups->whereIn('name', [
            static::GROUP_NAME_REGULAR,
            static::GROUP_B2B
        ])->isNotEmpty();
    }

    protected function getPersonalDataFromProvider(): ?UserPersonalData
    {
        return null;
        if ($this->isBlocked()) {
            return null;
        }

        if ($this->personalData === null) {
            $this->personalData = (new PersonalDataService(
                ConnectorResolver::resolveForInstance($this->instance)
            ))->getByErpID($this->erp_id);
        }

        return $this->personalData;
    }

    public function isBlocked(): bool
    {
        if (is_null($this->blocked_at)) {
            return false;
        }

        return $this->blocked_at->lte(Carbon::now());
    }

    public function isFromErp(): bool
    {
        return (bool)$this->erp_id;
    }

    public function mpks()
    {
        return $this->belongsToMany(Mpk::class, 'user_mpks')->withTrashed();
    }

    public function getCode(): string
    {
        if ($this->erp_id === null) {
            throw new \LogicException('User does not have ERP ID');
        }

        return (string)$this->erp_id;
    }

    /** @var string */
    protected $start;

    /** @var string */
    protected $end;

    public function getActiveInRequestsForPeriod(Carbon $start, Carbon $end): Collection
    {
        $this->start = $start;
        $this->end = $end;

        $asOwner = Request::addSelect(
            'requests.number',
            'requests.id',
            'requests.type',
            'mpks.code as mpk_code',
            DB::raw('\'' . self::ACTIVE_AS_REGULAR . '\' as activity_type')
        )
            ->leftJoin('mpks', function ($join) {
                $join->on('requests.mpk_id', '=', 'mpks.id');
            })
            ->where('user_id', '=', $this->id)
            ->where('sent_at', '>=', $this->start)
            ->where('sent_at', '<=', $this->end);

        $asTraveler = Request::addSelect(
            'requests.number',
            'requests.id',
            'requests.type',
            'mpks.code as mpk_code',
            DB::raw('\'' . self::ACTIVE_AS_TRAVELER . '\' as activity_type')
        )
            ->leftJoin('request_travelers', function ($join) {
                $join->on('request_travelers.request_id', '=', 'requests.id');
            })
            ->leftJoin('mpks', function ($join) {
                $join->on('requests.mpk_id', '=', 'mpks.id');
            })
            ->where('request_travelers.request_traveler_id', '=', $this->id)
            ->where('user_id', '!=', $this->id)
            ->where('sent_at', '>=', $this->start)
            ->where('sent_at', '<=', $this->end);

        $asAcceptor = Request::addSelect(
            'requests.number',
            'requests.id',
            'requests.type',
            'mpks.code as mpk_code',
            DB::raw('\'' . self::ACTIVE_AS_MANAGER . '\' as activity_type')
        )
            ->leftJoin('mpks', function ($join) {
                $join->on('requests.mpk_id', '=', 'mpks.id');
            })
            ->leftJoin('request_acceptors_user', function ($join) {
                $join->on('request_acceptors_user.request_id', '=', 'requests.id');
            })
//            ->where('status', '=', Request::STATUS_WAITING_FOR_ACCEPTANCE)
//            ->where('request_acceptors_user.accepted', '=', Request::ACCEPTOR_STATUS_PENDING)
            ->where('sent_at', '>=', $this->start)
            ->where('sent_at', '<=', $this->end)
            ->where('request_acceptors_user.user_id', '=', $this->id);;

        $asSettlementAcceptor = Request::addSelect(
            'requests.number',
            'requests.id',
            'requests.type',
            'mpks.code as mpk_code',
            DB::raw('\'' . self::ACTIVE_AS_MANAGER . '\' as activity_type')
        )
            ->leftJoin('mpks', function ($join) {
                $join->on('requests.mpk_id', '=', 'mpks.id');
            })
            ->leftJoin('request_settlement_acceptors_user', function ($join) {
                $join->on('request_settlement_acceptors_user.request_id', '=', 'requests.id');
            })
//            ->where('status', '=', Request::STATUS_WAITING_FOR_ACCEPTANCE)
//            ->where('request_settlement_acceptors_user.accepted', '=', Request::ACCEPTOR_STATUS_PENDING)
            ->where('settled_at', '>=', $this->start)
            ->where('settled_at', '<=', $this->end)
            ->where('request_settlement_acceptors_user.user_id', '=', $this->id);

        $asAccountant = Request::addSelect(
            'requests.number',
            'requests.id',
            'requests.type',
            'mpks.code as mpk_code',
            DB::raw('\'' . self::ACTIVE_AS_FINANCE . '\' as activity_type')
        )
            ->leftJoin('mpks', function ($join) {
                $join->on('requests.mpk_id', '=', 'mpks.id');
            })
            ->where(function ($q) {
                $q->where([
                    ['accounted_at', '>=', $this->start],
                    ['accounted_at', '<=', $this->end]
                ])
                    ->orWhere([
                        ['finished_at', '>=', $this->start],
                        ['finished_at', '<=', $this->end]
                    ])
                    ->orWhere([
                        ['erp_accounted_at', '>=', $this->start],
                        ['erp_accounted_at', '<=', $this->end]
                    ])
                    ->orWhere([
                        ['erp_vat_at', '>=', $this->start],
                        ['erp_vat_at', '<=', $this->end]
                    ]);
            })
            ->where('requests.accounting_user_id', '=', $this->id);

        return $asOwner
            ->union($asTraveler)
            ->union($asAcceptor)
            ->union($asSettlementAcceptor)
            ->union($asAccountant)
            ->get();
    }

    public function canUsePeriodicRequestExpense(): bool
    {
        return $this->instance->isPeriodicRequestExpensesEnabled()
            && $this->hasAbility(Permission::PERIODIC_REQUEST_EXPENSE);
    }

    public function notifyInternal($instance, ?array $channels, bool $queued): void
    {
        $dispatcher = app(Dispatcher::class);

        if ($queued) {
            $dispatcher->send($this, $instance);
        } else {
            $dispatcher->sendNow($this, $instance, $channels);
        }
    }

    public function isPinCodeBlocked(): bool
    {
        return $this->bad_pin_code_attempts >= config('vaterval.max_bad_pin_code_attempts');
    }

    public function isIntegrationAPIUser(): bool
    {
        return $this->hasAbility(Permission::INTEGRATION_API_ACCESS);
    }

    public function provider(): HasOne
    {
        return $this->hasOne(Provider::class, 'employee_id');
    }

    public function isBillingUser(): bool
    {
        return $this->groups->where('name', '=', static::GROUP_NAME_BILLING)->isNotEmpty();
    }

    public function isB2BUser(): bool
    {
        return $this->groups->where('name', '=', static::GROUP_B2B)->isNotEmpty();
    }

    public function getPolicyFileName(): ?string
    {
        return $this->company->policy_file_token && $this->company->policy_file
            ? Policy::BASE_URL . $this->company->policy_file . '/token/' . $this->company->policy_file_token
            : null;
    }

    public function saveWithoutEvents(array $options = [])
    {
        return static::withoutEvents(function () use ($options) {
            return $this->save($options);
        });
    }

    public function getCacheTag(): string
    {
        return 'user-' . $this->id;
    }

    public function hasRegionalPosition(): bool
    {
        return $this->hasAbility(Permission::REGIONAL_POSITION);
    }

    public function hasMpks(): BelongsToMany
    {
        return $this->belongsToMany(Mpk::class, 'user_has_mpk')
            ->withPivot(['percentage', 'main'])
            ->orderByDesc('id');
    }

    public function getSupervisor(int $level = 1): ?User
    {
        $currentUser = $this;
        $lastSupervisor = null;
        $supervisor = $currentUser->supervisor;

        while ($level > 0 && $supervisor) {
            $lastSupervisor = $supervisor;
            $currentUser = $supervisor;
            $supervisor = $currentUser->supervisor;
            $level--;
        }

        return $lastSupervisor;
    }
}
