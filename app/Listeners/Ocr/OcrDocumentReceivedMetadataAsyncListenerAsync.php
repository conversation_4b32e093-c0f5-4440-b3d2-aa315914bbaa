<?php

declare(strict_types=1);

namespace App\Listeners\Ocr;

use App\Document;
use App\Events\Ocr\OcrDocumentReceivedMetadata;
use App\Events\Ocr\OcrProcessFinished;
use App\Exceptions\Ocr\OcrDocumentNotFoundException;
use App\Instance;
use App\Repositories\DocumentRepository;
use App\Repositories\OcrHintRepository;
use Carbon\Carbon;
use Illuminate\Log\Logger;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class OcrDocumentReceivedMetadataAsyncListenerAsync extends OcrProcessListenerAsyncAbstract implements ShouldQueue
{
    use InteractsWithQueue;

    public $queue = 'ocr';

    protected $logger;

    public function __construct(
        DocumentRepository $documentRepository,
        OcrHintRepository $ocrHintRepository,
        Logger $logger
    ) {
        parent::__construct($documentRepository, $ocrHintRepository);

        $this->logger = $logger;
    }

    public function handle(OcrDocumentReceivedMetadata $event): void
    {
        $document = $this->documentRepository->getByIdAndOcrFileMd5($event->getDocumentId(), $event->getOcrFileMd5());

        try {
            if ($document instanceof Document) {
                $document->ocr_data = json_encode($event->getMetadata());
                $event->getProvider()->markDocumentAsExported($document->ocr_provider_id);
                $document->status = Document::STATUS_PROCESSED;
                $document->ocr_finished_at = Carbon::now();
                $document->save();
                $document->refresh();
                $this->saveOcrHints($document);
                event(new OcrProcessFinished($document));
            } else {
                throw OcrDocumentNotFoundException::create(
                    sprintf(
                        'Ocr document %s with md5 %s not found',
                        $event->getDocumentId(),
                        $event->getOcrFileMd5())
                );
            }
        } catch (OcrDocumentNotFoundException $exception) {
            $this->logger->warning((string)$exception);
        } catch (\Throwable $exception) {
            $document->ocr_error = $exception->getMessage() . ' ' . $exception->getFile() . '::' .$exception->getLine();
            $document->status = Document::STATUS_FAILED;
            $document->save();
            $document->refresh();

            event(new OcrProcessFinished($document));

            $this->fail($exception);
        }
    }
}
