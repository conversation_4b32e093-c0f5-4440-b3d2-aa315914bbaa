<?php

namespace App\Listeners\Request;

use App\Events\Request\StatusChangeEvent;
use App\Repositories\RequestRepository;
use App\Services\Request\RequestAutoChangeStatusService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Mindento\Tracer\Traits\Traceable;

class StatusChange<PERSON>istener implements ShouldQueue
{
    use Traceable;

    public $queue = 'status_changes';

    private RequestAutoChangeStatusService $requestAutoChangeStatusService;

    private RequestRepository $requestRepository;

    public function __construct(
        RequestAutoChangeStatusService $requestAutoChangeStatusService,
        RequestRepository $requestRepository
    ) {
        $this->requestAutoChangeStatusService = $requestAutoChangeStatusService;
        $this->requestRepository = $requestRepository;
    }

    public function handle(StatusChangeEvent $event): void
    {
        $requestSlug = $event->requestSlug();
        $request = $this->requestRepository->findBySlug($requestSlug);

        if (null === $request) {
            return;
        }

        $this->requestAutoChangeStatusService->changeStatus($request);
    }

    public function tags(): array
    {
        return ['status_change'];
    }
}
