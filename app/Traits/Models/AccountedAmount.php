<?php

namespace App\Traits\Models;


use App\Document;
use App\DocumentElement;
use App\Vendors\Math;
use Illuminate\Support\Collection;

trait AccountedAmount
{
    /**
     * @return string
     */
	public function getAccountedAmount(): string
    {
        return Math::round($this->documents->reduce(function ($carry, Document $document) {
            if($document->settled_at) {
                $document->elements->each(function (DocumentElement $element) use (&$document, &$carry) {
                    if ($document->isTravelDocument()) {
                        return;
                    }

                    try {
                        $carry += Math::multiply($element->gross, $element->getExchangeRate(), 4);
                    } catch (\Throwable $exception) { }
                });
            }

            return $carry;

        }, 0), 2);
    }

    public function getAccountedComponentAmounts(): array
    {
        $amounts = collect();

        $this->documents->each(function (Document $document) use (&$amounts) {
            if ($document->settled_at) {
                $document->elements->each(function (DocumentElement $element) use (&$document, &$amounts) {
                    try {
                        if (!$amounts->has($document->currency->code)) {
                            $amounts->put($document->currency->code, $element->gross);
                        } else {
                            $this->updateCurrencyAmount($amounts, $document->currency->code, $element->gross);
                        }
                    } catch (\Throwable $exception) {
                    }
                });
            }
        });

        return $this->getComponentAmounts($amounts)->toArray();
    }

    protected function updateCurrencyAmount(Collection &$amounts, $currency, $amount)
    {
        $element           = $amounts->get($currency);
        $element += $amount;
        $amounts->put($currency, $element);
    }

    protected function getComponentAmounts(Collection $amounts)
    {
        $noForeigns = $amounts->keys()->every(function($key) {
            return $key === $this->instance->currency->code;
        });
        if($noForeigns) {
            return collect();
        }

        return $amounts->map(function ($amount, $currency) {
            return [
                'amount'   => $amount,
                'currency' => $currency
            ];
        });
    }
}
