<?php

namespace App\Traits;

use App\Request;
use App\User;
use Illuminate\Support\Collection;

trait AcceptorsManagementTrait {

    protected function logAcceptorChange(Request $request, \App\User $acceptor, $isSettlementAcceptor, $type)
    {
        $action = $type == 'detach' ? 'detached' : 'attached';

        \App\RequestChange::create([
            'instance_id' => $request->instance_id,
            'request_id'  => $request->id,
            'user_id'     => \Auth::user()->id ?? null,
            'status'      => $request->status,
            'changes'     => [
                'note'        => $isSettlementAcceptor ? "settlement acceptor $action" : "acceptor $action",
                'acceptor_id' => $acceptor->id,
            ],
        ]);

//        $request->touch();
    }

    protected function allAcceptorsAccepted(Collection $acceptors)
    {
        try {
            return $acceptors->filter(function(User $acceptor) {
                return in_array($acceptor->pivot->accepted, [
                    Request::ACCEPTOR_STATUS_NEED_CORRECT,
                    Request::ACCEPTOR_STATUS_REJECTED,
                    Request::ACCEPTOR_STATUS_PENDING,
                ]);
            })->isEmpty();
        } catch (\Throwable $exception) {
            return false;
        }
    }
}
