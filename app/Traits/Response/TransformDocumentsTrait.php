<?php

declare(strict_types=1);

namespace App\Traits\Response;

use App\Http\Responses\DocumentResponse;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Entities\DocumentElementType;
use Modules\Accounting\Priv\Http\Responses\DocumentElementTypeResponse;

trait TransformDocumentsTrait
{
    protected function transformDocuments(Collection $documents)
    {
        return DocumentResponse::collection($documents)->getData();
    }

    protected function transformSuggestedElementType(DocumentElementType $type)
    {
        return DocumentElementTypeResponse::item($type)->getData();
    }
}
