<?php

namespace App;

use App\Helpers\Amount;
use App\Traits\HasAmounts;
use Carbon\Carbon;
use Domain\Invoice\AccountingNote\Services\StartAccountingNotePaymentService;
use Domain\Invoice\Jobs\IssueQueuedInvoicesJob;
use Domain\Invoice\Services\StartInvoicePaymentService;
use Domain\Obt\Offer\Option\OfferOption;
use Domain\Obt\Repositories\OfferRepository;
use Domain\Obt\Traits\CallLocationTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Modules\Obt\Pub\Enums\ObtTypeEnum;
use Modules\Obt\Pub\Enums\ReservationStatusEnum;
use Modules\Obt\Pub\Facades\ReservationCancelAccountingFacade;

/**
 * App\Reservation
 *
 * @property Amount fee
 * @property Amount markup
 * @property Amount original_price
 * @property Amount calculated_markup
 * @property Amount calculated_price
 * @property string fee_currency
 * @property string markup_currency
 * @property string original_price_currency
 * @property string calculated_markup_currency
 * @property string calculated_price_currency
 * @property string mindento_request_slug
 * @property string mindento_element_type
 * @property integer mindento_element_id
 * @property string uuid
 * @property string type
 * @property string option_uuid
 * @property array reservation
 * @property int|null attempted_at
 * @property int process_attempts
 * @property string status
 * @property Offer $offer
 * @property ?Offer second_offer
 * @property int ticket_id
 * @property int cancellation_ticket_id
 * @property File|null ticket
 * @property File|null ticket_original
 * @property Collection jobs
 * @property Collection correctedAccountingDocument
 * @property Collection incorrectAccountingDocument
 * @property Collection correctAccountingDocument
 * @property string result_message
 * @property string|null error_type
 * @property Amount calculated_net_price
 * @property string calculated_net_price_currency
 * @property string obt_reservation_id *
 * @property string|null ticket_number
 * @property bool refundable
 * @property Carbon|null cancel_date
 * @property Payment payment
 * @property int id
 * @property string|null mindento_request_number
 * @property string|null mindento_request_mpk_code
 * @property string|null mindento_request_mpk_name
 * @property Carbon|null date_of_entry
 * @property Carbon|null date_of_exit
 * @property int customer_user_id
 * @property CustomerUser customerUser
 * @property bool ticket_invalidated
 * @property File|null cancellationTicket
 * @property string|null refunded_gross_amount
 * @property string|null reference_numbers
 * @property int $id
 * @property string $uuid
 * @property int $offer_id
 * @property int $customer_user_id
 * @property string $option_uuid
 * @property string $type
 * @property string|null $status
 * @property string|null $result_message
 * @property string|null $error_type
 * @property int|null $refundable
 * @property \Illuminate\Support\Carbon|null $date_of_exit
 * @property \Illuminate\Support\Carbon|null $date_of_entry
 * @property \Illuminate\Support\Carbon|null $cancel_date
 * @property string|null $obt_reservation_id
 * @property string $mindento_request_slug
 * @property string|null $mindento_request_number
 * @property string|null $mindento_request_mpk_code
 * @property string|null $mindento_request_mpk_name
 * @property int $mindento_element_id
 * @property string $mindento_element_type
 * @property int|null $ticket_id
 * @property bool $ticket_invalidated
 * @property int|null $cancellation_ticket_id
 * @property string|null $refunded_gross_amount
 * @property string|null $ticket_number
 * @property string|null $reference_numbers
 * @property array $reservation
 * @property string $fee
 * @property string $fee_currency
 * @property array|null $fee_exchange_rate
 * @property string $markup
 * @property string $original_price
 * @property string $original_price_currency
 * @property array|null $original_price_exchange_rate
 * @property string $calculated_net_price
 * @property string $calculated_net_price_currency
 * @property string|null $calculated_net_price_exchange_rate
 * @property string $calculated_price
 * @property string $calculated_price_currency
 * @property array|null $calculated_price_exchange_rate
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\AccountingNoteDraft[] $accountingNoteDrafts
 * @property-read int|null $accounting_note_drafts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\AccountingNote[] $accountingNotes
 * @property-read int|null $accounting_notes_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Invoice[] $alesInvoices
 * @property-read int|null $ales_invoices_count
 * @property-read \App\File|null $cancellationTicket
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Invoice[] $correctAccountingDocument
 * @property-read int|null $correct_accounting_document_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Invoice[] $correctedAccountingDocument
 * @property-read int|null $corrected_accounting_document_count
 * @property-read \App\CustomerUser $customerUser
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\ReservationDependencyJob[] $dependencyJobs
 * @property-read int|null $dependency_jobs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\InvoiceDraft[] $draftInvoices
 * @property-read int|null $draft_invoices_count
 * @property-read Amount $amount_for
 * @property-read mixed $calculated_markup
 * @property-read \App\Offer|null $second_offer
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Invoice[] $incorrectAccountingDocument
 * @property-read int|null $incorrect_accounting_document_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Invoice[] $invoices
 * @property-read int|null $invoices_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\ReservationJob[] $jobs
 * @property-read int|null $jobs_count
 * @property-read \App\Payment|null $payment
 * @property-read \App\ReservationPendingAck|null $pendingAck
 * @property-read \App\File|null $ticket
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation query()
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCalculatedNetPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCalculatedNetPriceCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCalculatedNetPriceExchangeRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCalculatedPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCalculatedPriceCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCalculatedPriceExchangeRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCancelDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCancellationTicketId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereCustomerUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereDateOfEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereDateOfExit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereErrorType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereFeeCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereFeeExchangeRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereMarkup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereMindentoElementId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereMindentoElementType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereMindentoRequestMpkCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereMindentoRequestMpkName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereMindentoRequestNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereMindentoRequestSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereObtReservationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereOfferId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereOptionUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereOriginalPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereOriginalPriceCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereOriginalPriceExchangeRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereReferenceNumbers($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereRefundable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereRefundedGrossAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereReservation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereResultMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereTicketId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereTicketInvalidated($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereTicketNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Reservation whereUuid($value)
 * @mixin \Eloquent
 * @method static Builder|Reservation status($statuses)
 */
class Reservation extends Model
{
    use HasAmounts;
    use CallLocationTrait;

    const STATUS_CANCELED = 'canceled';
    const STATUS_FAILED = 'failed';
    const STATUS_EXPIRED = 'expired';
    const STATUS_WAITING_FOR_VERIFY = 'waiting_for_verify';
    const STATUS_NEW = 'new';
    const STATUS_WAITING_FOR_ACCEPTATION = 'waiting_for_acceptation';
    /** @var string yet another request enabled check */
    const STATUS_YA_RE_CHECK = 'ya_re_check';
    const STATUS_WAITING_FOR_CONFIRM = 'waiting_for_confirm';
    const STATUS_WAITING_FOR_TICKET = 'waiting_for_ticket';
    const STATUS_BOOKED = 'booked';

    const STATUS_PENDING_CANCELLATION = 'pending_cancellation';
    const STATUS_CANCELLATION_FAILED = 'cancellation_failed';
    const STATUS_RESEARCH = 'research';

    // amounts properties
    const FEE_AMOUNT_ATTRIBUTE = 'fee';
    const ORIGINAL_PRICE_AMOUNT_ATTRIBUTE = 'original_price';
    const CALCULATED_MARKUP_AMOUNT_ATTRIBUTE = 'calculated_markup';
    const CALCULATED_PRICE_AMOUNT_ATTRIBUTE = 'calculated_price';

    // used for invoice's additional data
    const TYPE_OFFLINE = 'offline';
    const TYPE_ONLINE = 'online';

    protected $dates = [
        'cancel_date',
        'date_of_entry',
        'date_of_exit'
    ];

    protected $casts = [
        'reservation' => 'array',
        'fee_exchange_rate' => 'array',
        'original_price_exchange_rate' => 'array',
        'calculated_price_exchange_rate' => 'array',
        'ticket_invalidated' => 'boolean'
    ];

    public static function getStatuses(): array
    {
        return [
            static::STATUS_WAITING_FOR_VERIFY,
            static::STATUS_WAITING_FOR_ACCEPTATION,
            static::STATUS_NEW,
            static::STATUS_CANCELED,
            static::STATUS_PENDING_CANCELLATION,
            static::STATUS_CANCELLATION_FAILED,
            static::STATUS_EXPIRED,
            static::STATUS_RESEARCH,
            static::STATUS_WAITING_FOR_CONFIRM,
            static::STATUS_YA_RE_CHECK,
            static::STATUS_WAITING_FOR_TICKET,
            static::STATUS_BOOKED,
            static::STATUS_FAILED,
        ];
    }

    public static function getProcessableStatuses(): array
    {
        return [
            static::STATUS_WAITING_FOR_VERIFY,
            static::STATUS_WAITING_FOR_ACCEPTATION,
            static::STATUS_NEW,
            static::STATUS_WAITING_FOR_CONFIRM,
            static::STATUS_YA_RE_CHECK,
            static::STATUS_WAITING_FOR_TICKET,
            static::STATUS_PENDING_CANCELLATION,
        ];
    }

    public function hasProcessableStatus(): bool
    {
        return in_array($this->status, self::getProcessableStatuses());
    }

    public function getFeeAttribute($fee)
    {
        return $this->getAmountForAttribute(static::FEE_AMOUNT_ATTRIBUTE, $fee);
    }

    public function getOriginalPriceAttribute($originalPrice)
    {
        return $this->getAmountForAttribute(static::ORIGINAL_PRICE_AMOUNT_ATTRIBUTE, $originalPrice);
    }

    public function getCalculatedMarkupAttribute($calculatedMarkup)
    {
        return $this->getAmountForAttribute(static::CALCULATED_MARKUP_AMOUNT_ATTRIBUTE, $calculatedMarkup);
    }

    // @todo krystian refactor
    public function getCalculatedNetPriceAttribute($calculatedMarkup)
    {
        return $this->getAmountForAttribute('calculated_net_price', $calculatedMarkup);
    }

    public function getCalculatedPriceAttribute($calculatedPrice)
    {
        return $this->getAmountForAttribute(static::CALCULATED_PRICE_AMOUNT_ATTRIBUTE, $calculatedPrice);
    }

    public function pendingAck(): HasOne
    {
        return $this->hasOne(ReservationPendingAck::class);
    }

    public function invoices()
    {
        return $this->hasMany(\App\Invoice::class);
    }

    public function accountingNotes()
    {
        return $this->hasMany(AccountingNote::class);
    }

    public function accountingNoteDrafts()
    {
        return $this->hasMany(AccountingNoteDraft::class);
    }

    public function draftInvoices()
    {
        return $this->hasMany(\App\InvoiceDraft::class);
    }

    public function offer()
    {
        return $this->belongsTo(\App\Offer::class);
    }

    public function ticket()
    {
        return $this->belongsTo(File::class);
    }

    public function cancellationTicket()
    {
        return $this->hasOne(File::class, 'id', 'cancellation_ticket_id');
    }

    public function jobs()
    {
        return $this->hasMany(ReservationJob::class);
    }

    public function payment()
    {
        return $this->hasOne(Payment::class);
    }

    public function dependencyJobs(): HasMany
    {
        return $this->hasMany(ReservationDependencyJob::class);
    }

    public function saveTicket(File $file, ?string $number = null, ?string $referenceNumbers = null): void
    {
        $this->ticket_id = $file->id;
        $this->ticket_number = $number;
        $this->reference_numbers = $referenceNumbers;
    }

    public function saveInvalidatedTicket(File $file): void
    {
        if ($this->status === (string)ReservationStatusEnum::STATUS_CANCELED()) {
            $this->cancellation_ticket_id = $file->id;
            $this->ticket_invalidated = true;
        }
    }

    public function isBooked(): bool
    {
        return $this->status === static::STATUS_BOOKED;
    }

    public function isConfirmed()
    {
        return $this->isBooked();
    }

    public function isBookedWithTicket()
    {
        return $this->isBooked();
    }

    public function isCanceled(): bool
    {
        return in_array($this->status, [static::STATUS_CANCELED, static::STATUS_FAILED, static::STATUS_EXPIRED]);
    }

    public function isWaitingForTicket()
    {
        return $this->status === static::STATUS_WAITING_FOR_TICKET;
    }

    public function getOffer(): ?Offer
    {
        return $this->offer;
    }

    public function getReservedOption(): ?OfferOption
    {
        return $this->offer->getOption($this->option_uuid);
    }

    public function failed()
    {
        return $this->status === static::STATUS_FAILED;
    }

    public function getSalesInvoice()
    {
        return $this->invoices()->where('type', Invoice::SALE_INVOICE)->get()->first();
    }

    public function alesInvoices(): HasMany
    {
        return $this->invoices()->where('type', Invoice::SALE_INVOICE);
    }

    public function getPurchaseInvoice()
    {
        return $this->invoices()->where('type', Invoice::PURCHASE_INVOICE)->get()->first();
    }

    public function setNewStatus(): void
    {
        if ($this->hasStatus(Reservation::STATUS_NEW)) {
            return;
        }

        $this->status = Reservation::STATUS_NEW;
        $this->save();
    }

    public function setBookedStatus(): bool
    {
        $this->status = Reservation::STATUS_BOOKED;

        return $this->save();
    }

    public function setPendingCancellation(): bool
    {
        $this->status = Reservation::STATUS_PENDING_CANCELLATION;

        return $this->save();
    }

    public function setErrorType(?string $errorType): Reservation
    {
        if (empty($this->getAttributeValue('error_type')) === true) {
            $this->error_type = $errorType;
        }

        return $this;
    }

    public function getRequestedAttributes(): array
    {
        return $this->reservation['requestedAttributes'] ?? [];
    }

    public function getSecondOfferAttribute(): ?Offer
    {
        $requestedAttrs = $this->reservation['requestedAttributes'] ?? [];
        if ($this->mindento_element_type === 'plane_trip' && !empty($requestedAttrs['targetFlightUuid'])) {
            return resolve(OfferRepository::class)->getByUuid($requestedAttrs['targetFlightUuid']);
        }

        return null;
    }

    public function customerUser()
    {
        return $this->belongsTo(CustomerUser::class);
    }

    public function getStatus(): ReservationStatusEnum
    {
        return new ReservationStatusEnum($this->status);
    }

    public function getInvoiceDateStrategy(): string
    {
        return $this->offer->obtType->invoice_data_strategy;
    }

    public function shouldHasCorrectAccountingDocument(): bool
    {
        if ($this->correctAccountingDocument->count() > 0) {
            return false;
        }

        if ($this->payment && $this->payment->succeeded()) {
            return true;
        }

        if ($this->waitForInvoice()) {
            return false;
        }

        return $this->ticket_number || $this->isBooked();
    }

    public function correctAccountingDocument(): HasMany
    {
        return $this->alesInvoices()->whereIn('internal_kind', $this->getCorrectInternalKinds());
    }

    public function getCorrectInternalKinds(): array
    {
        return $this->payment ? $this->payment->getCorrectInternalKinds() : [];
    }

    public function incorrectAccountingDocument(): HasMany
    {
        return $this->alesInvoices()->whereNotIn('internal_kind', $this->getCorrectInternalKinds());
    }

    public function numberOfDocuments(): int
    {
        return array_sum(array_filter([
            $this->payment && $this->payment->succeeded() ? 1 : 0,
            $this->payment && $this->payment->refundSucceeded() ? 1 : 0,
            $this->correctedAccountingDocument->count(),
        ]));
    }

    public function accountingDocumentStatus(): int
    {
        if ($this->incorrectAccountingDocument->count()) {
            return -1;
        }

        if ($this->numberOfDocuments() === 0 && $this->correctAccountingDocument->count() === 0) {
            return 0;
        }

        if ($this->correctAccountingDocument->count() === $this->numberOfDocuments()) {
            return 1;
        } else {
            return -1;
        }
    }

    public function waitForInvoice(): bool
    {
        return $this->payment
            && $this->payment->waitingFor()
            && $this->isBooked()
            && $this->payment->hasAccountingDocument();
    }

    public function correctedAccountingDocument(): HasMany
    {
        return $this->alesInvoices()->has('correctionOf');
    }

    public function isReadyToIssueInvoice(): bool
    {
        if (!$this->payment) {
            return false;
        }

        return $this->payment->succeeded();
    }

    public function isParkingRequested(): bool
    {
        return stripos((string)$this->getRequestedAttributes()['remark'], 'parking') !== false;
    }

    public function requestUrl(): string
    {
        return sprintf(
            'https://%s/requests/trip/%s',
            $this->offer->customer->domain,
            $this->mindento_request_slug
        );
    }

    public function scopeStatus(Builder $q, string ...$statuses): void
    {
        $q->whereIn('status', $statuses);
    }

    public function clearJobs(): void
    {
        $this->jobs()->delete();
    }

    public function shouldRefundMoney(): bool
    {
        return $this->refundable
            && $this->payment
            && $this->payment->succeeded()
            && $this->hasStatus(
                self::STATUS_EXPIRED,
                self::STATUS_FAILED,
            );
    }

    public function hasStatus(...$statuses): bool
    {
        return in_array($this->status, $statuses);
    }

    public function postCancel(): void
    {
        if (false === $this->isCanceled()) {
            return;
        }

        $this->getConnection()->transaction(function () {
            $this->postCancelPayment();
            $this->postCancelInvoice();
        });
    }

    private function postCancelPayment(): void
    {
        if ($this->isCanceled()) {
            $this->payment()
                ->whereIn('status', [
                    Payment::STATUS_PENDING,
                    Payment::STATUS_PENDING_RETRY,
                    Payment::STATUS_QUEUE_FOR_RETRY,
                    Payment::STATUS_RETRY,
                ])
                ->update([
                    'status' => Payment::STATUS_CANCELED,
                    'should_start_at' => null,
                ]);
        }
    }

    public function isReadyToPay(): bool
    {
        return $this->isBooked() && is_null($this->payment);
    }

    public function hasPaymentAccountingDocument(): bool
    {
        return $this->payment->hasAccountingDocument();
    }

    public function canPaymentStartNow(): bool
    {
        return $this->payment
            && $this->payment->canStartNow();
    }

    private function postCancelInvoice(): void
    {
        if ($this->isCanceled()) {
            $this->dependencyJobs()->invoice()->processable()->update([
                'status' => ReservationDependencyJob::STATUS_DONE,
            ]);
        }
    }

    public function obtTypeIs(ObtTypeEnum $obtTypeEnum): bool
    {
        return $this->offer
            && $this->offer->obtType
            && $this->offer->obtType->type_class === $obtTypeEnum->getValue();
    }

    public function createCorrection(
        StartInvoicePaymentService $invoicePaymentService,
        ReservationCancelAccountingFacade $reservationCancelAccountingFacade,
        StartAccountingNotePaymentService $accountingNotePaymentService
    ): void {
        if (false === $this->isCanceled()) {
            return;
        }

        if (!$this->payment || !$this->payment->invoice_id) {
            return;
        }

        if ($this->payment->hasCorrectionDraftInvoice()) {
            return;
        }

        $this->getConnection()->transaction(function () use (
            $reservationCancelAccountingFacade,
            $accountingNotePaymentService,
            $invoicePaymentService
        ) {
            $job = ReservationDependencyJob::firstOrCreate([
                'reservation_id' => $this->id,
                'type' => ReservationDependencyJob::TYPE_INVOICE,
                'status' => ReservationDependencyJob::STATUS_NEW,
            ], [
                'process' => null,
            ]);

            $invoiceCorrectionDraft = $reservationCancelAccountingFacade->createInvoiceCorrectionDraft($this);

            if ($this->payment->isCardPayment()) {
                $invoicePaymentService->startForInvoiceDraftCorrection($invoiceCorrectionDraft);
            }

            if ($this->obtTypeIs(ObtTypeEnum::TYPE_TRAIN())) {
                $accountingNoteDraft = $reservationCancelAccountingFacade->createAccountingNoteDraft($this);

                if ($this->payment->isCardPayment()) {
                    $accountingNotePaymentService->startForAccountingNoteDraft($accountingNoteDraft);
                }
            }

            IssueQueuedInvoicesJob::dispatch($job);
        });
    }

    public function hasCorrectionInvoice(): bool
    {
        return $this->payment
            && $this->payment->hasCorrectionInvoice();
    }

    public function hasInvoice(): bool
    {
        return $this->payment
            && $this->payment->hasInvoice();
    }
}
