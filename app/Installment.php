<?php

namespace App;

use App\ElasticSearchConfigurators\InstallmentIndexConfigurator;
use App\ElasticSearchConfigurators\Rules\InstallmentMultiMatchRule;
use App\Interfaces\Models\SearchWithRulesInterface;
use App\Interfaces\RequestElementObservableInterface;
use App\Repositories\ExchangeRateRepository;
use App\Traits\CurrencyTrait;
use App\Traits\InstanceTrait;
use App\Traits\RequestTrait;
use App\Vendors\Math;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use ScoutElastic\Searchable;

/**
 * Class Installment
 *
 * @package App
 * @property integer id
 * @property float amount
 * @property string|null exchange_rate
 * @property Currency currency
 * @property integer request_id
 * @property mixed date
 * @property Carbon|null accounting_date
 * @property string status
 * @property string erp_id
 * @property int $id
 * @property int $request_id
 * @property int $instance_id
 * @property string $amount
 * @property string $exchange_rate
 * @property \Illuminate\Support\Carbon|null $date
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $accounting_date
 * @property int $currency_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $erp_id
 * @property-read \App\Currency $currency
 * @property \ScoutElastic\Highlight|null $highlight
 * @property-read \App\Instance $instance
 * @property-read \App\Request $request
 * @method static \Illuminate\Database\Eloquent\Builder|Installment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Installment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Installment query()
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereAccountingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereCurrencyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereErpId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereExchangeRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Installment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Installment extends Model implements RequestElementObservableInterface, SearchWithRulesInterface
{
    use RequestTrait;
    use CurrencyTrait;
    use InstanceTrait;
    use Searchable;

    public const SORTABLE_COLUMNS = [
        'request_sent_at',
        'request_number',
        'request_status_translated',
        'request_user_last_name',
        'amount',
        'exchange_rate',
        'currency_code',
        'payout_date',
        'accounting_date',
        'erp_id'
    ];

    protected $indexConfigurator = InstallmentIndexConfigurator::class;

    // For elastic searches only.
    protected $searchRules = [
        InstallmentMultiMatchRule::class
    ];

    // For elastic searches only.
    protected $mapping = [
        'properties' => [
            'installment_request_number' => ['type' => 'keyword'],
            'installment_amount' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'installment_exchange_rate' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'installment_currency_code' => ['type' => 'keyword'],
            'installment_erp_id' => ['type' => 'keyword'],
            'installment_user_full_name' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'installment_company_code' => ['type' => 'keyword'],
        ]
    ];

    public static function searchWithRules(string $query, ?string $searchType = null, ?callable $callback = null)
    {
        return self::search($query, $callback);
    }

    public static function getInstallmentRequestNonEditableStatuses(): array
    {
        return [
            Request::STATUS_ACCEPTANCE_OF_SETTLEMENT,
            Request::STATUS_ACCOUNTING,
            Request::STATUS_TRANSFER_ERROR,
            Request::STATUS_TRANSFERRED,
            Request::STATUS_FINISH,
            Request::STATUS_DELETED
        ];
    }

    public function toSearchableArray()
    {
        // Make searchable only things visible by user on front.
        $searchableArray = [
            'installment_request_number' => $this->request instanceof Request ? $this->request->number: null,
            'installment_amount' => $this->amount,
            'installment_exchange_rate' => $this->exchange_rate,
            'installment_currency_code' => $this->currency->code,
            'installment_erp_id' => $this->erp_id,
            'installment_user_full_name' =>
                $this->request instanceof Request && $this->request->user instanceof User ? $this->request->user->full_name : null,
            'installment_company_code' =>
                $this->request instanceof Request && $this->request->user instanceof User && $this->request->user->company instanceof Company
                            ? $this->request->user->company->code
                            : null
        ];

        foreach ($searchableArray as $index => $value) {
            $searchableArray[$index] = strtolower($value);
        }

        return $searchableArray;
    }

    const RELATION_NAME = 'installment';

    const STATUS_UNPAID = 'unpaid';
    const STATUS_PAID = 'paid';

    protected $fillable = [
        'currency_id',
        'amount',
        'exchange_rate',
        'request_id',
        'instance_id',
        'date',
        'status',
        'erp_id'
    ];

    protected $dates = [
        'date',
        'accounting_date'
    ];

    public static function getStatuses(): Collection
    {
        return collect([
            static::STATUS_UNPAID,
            static::STATUS_PAID,
        ]);
    }

    public function isPaid(): bool
    {
        return is_null($this->getAttributeValue('date')) === false;
    }

    public function getConvertedAmount(): string
    {
        if (is_numeric($this->exchange_rate) === true
            && Math::isGreaterThanZero((string)$this->exchange_rate)
            && $this->currency instanceof Currency
            && $this->currency->code !== $this->instance->currency->code
        ) {
            return Math::multiply($this->amount, $this->exchange_rate, 2);
        }

        return resolve(ExchangeRateRepository::class)->convertAmountToInstanceCurrency(
            $this->currency,
            $this->amount,
            $this->request instanceof Request ? $this->request->getDateForRequestedRequestCalculation() : Carbon::now(),
            $this->instance
        );
    }

    public function getConvertedAmountCurrency(): Currency
    {
        return $this->instance->currency;
    }

    public function installmentRequestHasEditableStatus(): bool
    {
        return $this->request instanceof Request
            && in_array($this->request->status, self::getInstallmentRequestNonEditableStatuses()) === false;
    }

    public function getExchangeRateAttribute(): string
    {
        if (empty($this->attributes['exchange_rate']) === false) {
            return (string)$this->attributes['exchange_rate'];
        }

        return '0.00';
    }
}
