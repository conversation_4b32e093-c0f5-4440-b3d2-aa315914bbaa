<?php

declare(strict_types=1);

namespace App\Events\Ocr;

use App\Vendors\OCR\AuthConfig;
use App\Vendors\OCR\Contracts\Async\OcrServiceInterface;
use App\Vendors\OCR\Services\Async\OcrServiceFactory;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Mindento\Tracer\Traits\Traceable;

class OcrDocumentReceivedMetadata implements ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels, Traceable;

    /** @var int */
    protected $documentId;

    /** @var string */
    protected $ocrFileMd5;

    /** @var array */
    protected $metadata;

    /** @var OcrServiceInterface  */
    protected $provider;

    public function __construct(int $documentId, string $ocrFileMd5, array $metadata)
    {
        $this->documentId = $documentId;
        $this->ocrFileMd5 = $ocrFileMd5;
        $this->metadata = $metadata;
        $this->provider = OcrServiceFactory::create(
            new AuthConfig(config('ocr.driver')),
            (string)config('ocr.driver')
        );
    }

    public function tags()
    {
        return ['ocr-document-received-metadata'];
    }

    public function getDocumentId(): int
    {
        return $this->documentId;
    }

    public function getOcrFileMd5(): string
    {
        return $this->ocrFileMd5;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function getProvider(): OcrServiceInterface
    {
        return $this->provider;
    }
}
