<?php

declare(strict_types=1);

namespace App\Events\Document;

use App\DTO\Document\TicketInvalidatedDto;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Mindento\Tracer\Traits\Traceable;

class TicketInvalidatedEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels, Traceable;

    public TicketInvalidatedDto $ticketInvalidatedDto;

    public function __construct(TicketInvalidatedDto $ticketInvalidatedDto)
    {
        $this->ticketInvalidatedDto = $ticketInvalidatedDto;
    }

    public function broadcastOn(): PrivateChannel
    {
        return new PrivateChannel(sprintf('App.Request.%s',  (string)$this->ticketInvalidatedDto->getRequestSlug()));
    }

    public function broadcastWith(): array
    {
        return $this->ticketInvalidatedDto->getPayload();
    }

    public function tags(): array
    {
        return [
            sprintf('document:%s', (string)$this->ticketInvalidatedDto->getDocumentSlug()),
            sprintf('request:%s', (string)$this->ticketInvalidatedDto->getRequestSlug())
        ];
    }
}
