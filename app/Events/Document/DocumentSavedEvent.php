<?php

declare(strict_types=1);

namespace App\Events\Document;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Mindento\Tracer\Traits\Traceable;

final class DocumentSavedEvent implements ShouldQueue
{
    use Dispatchable, Traceable;

    protected int $documentId;
    private array $oldData;

    public function __construct(int $documentId, array $oldData = [])
    {
        $this->documentId = $documentId;
        $this->oldData = $oldData;
    }

    public function getDocumentId(): int
    {
        return $this->documentId;
    }

    public function getOldData(): array
    {
        return $this->oldData;
    }
}
