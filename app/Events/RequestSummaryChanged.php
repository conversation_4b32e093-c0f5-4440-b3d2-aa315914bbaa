<?php

namespace App\Events;

use App\Request;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Mindento\Tracer\Traits\Traceable;

class RequestSummaryChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels, Traceable;

    /**
     * @var Request $summary
     */
    public $summary;


    public $slug;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($summary, $slug)
    {
        $this->summary = $summary;
        $this->slug = $slug;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('App.Request.' . $this->slug . '.User.' . $this->summary['user_slug']);
    }

    public function broadcastWith()
    {
        return $this->summary;
    }

    public function tags()
    {
        return ['summary', 'request:'.$this->slug];
    }
}
