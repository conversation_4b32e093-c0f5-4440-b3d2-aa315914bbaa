<?php

declare(strict_types=1);

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Mindento\Tracer\Traits\Traceable;

class SearchOffersResultsChanged implements ShouldBroadcast, ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels, Traceable, Queueable;

    /**
     * @var array $offers
     */
    public $offers;

    /**
     * @var string $uuid
     */
    public $uuid;

    public function __construct($offers, $uuid)
    {
        $this->onQueue('search-result');
        $this->offers = $offers;
        $this->uuid = $uuid;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('App.Offer.' . $this->uuid);
    }

    public function broadcastWith()
    {
        return $this->offers;
    }

    public function tags(): array
    {
        return [
            'search',
            'search:' . $this->uuid,
        ];
    }
}
