<?php

declare(strict_types=1);

namespace App\Events\Request;

use App\Installment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Mindento\Tracer\Traits\Traceable;

class RequestInstallmentHasBeenPaidEvent implements ShouldQueue
{
    use Dispatchable, SerializesModels, Traceable;

    public $installment;

    public function __construct(Installment $installment)
    {
        $this->installment = $installment;
    }
}