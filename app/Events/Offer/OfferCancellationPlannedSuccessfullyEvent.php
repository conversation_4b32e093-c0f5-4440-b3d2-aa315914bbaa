<?php

declare(strict_types=1);

namespace App\Events\Offer;

use App\DTO\Offer\OfferCancellationPlannedSuccessfullyDto;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Mindento\Tracer\Traits\Traceable;

class OfferCancellationPlannedSuccessfullyEvent
{
    protected OfferCancellationPlannedSuccessfullyDto $cancelOfferDto;

    public function __construct(OfferCancellationPlannedSuccessfullyDto $cancelOfferDto)
    {
        $this->cancelOfferDto = $cancelOfferDto;
    }

    public function getCancelOfferDto(): OfferCancellationPlannedSuccessfullyDto
    {
        return $this->cancelOfferDto;
    }
}