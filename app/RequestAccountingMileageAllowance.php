<?php

declare(strict_types=1);

namespace App;

use App\Interfaces\RequestElementObservableInterface;
use App\Traits\InstanceTrait;
use App\Traits\RequestTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Entities\Traits\AccountingAccountTrait;
use Modules\Accounting\Priv\Entities\Traits\MpkTrait;
use Modules\Accounting\Priv\Entities\VatNumber;
use Modules\Accounting\Priv\Enum\DeductibilityEnum;
use Modules\Analytics\Priv\Entities\RequestAccountingMileageAllowanceAccountDimensionItem;
use Modules\Analytics\Priv\Entities\Traits\ProjectTrait;

/**
 * App\RequestAccountingMileageAllowance
 *
 * @property integer amount
 * @property integer accounting_account_id
 * @property integer mpk_id
 * @property boolean cost_of_earning
 * @property mixed id
 * @property AccountingAccount|null accountingAccount
 * @property Mpk mpk
 * @property VatNumber vatNumber
 * @property Collection accountDimensionItems
 * @property DeductibilityEnum deductibility
 * @property Currency currency
 * @property int currency_id
 * @property string exchange_rate
 * @property int $id
 * @property int $instance_id
 * @property int $request_id
 * @property int|null $accounting_account_id
 * @property int $mpk_id
 * @property string $amount
 * @property bool $cost_of_earning
 * @property DeductibilityEnum $deductibility
 * @property int|null $project_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|RequestAccountingMileageAllowanceAccountDimensionItem[] $accountDimensionItems
 * @property-read int|null $account_dimension_items_count
 * @property-read AccountingAccount|null $accountingAccount
 * @property-read \App\Currency $currency
 * @property-read string $exchange_rate
 * @property-read \App\Instance $instance
 * @property-read Mpk $mpk
 * @property-read \Modules\Analytics\Priv\Entities\Project|null $project
 * @property-read \App\Request $request
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance query()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereAccountingAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereCostOfEarning($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereDeductibility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereMpkId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereProjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestAccountingMileageAllowance whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class RequestAccountingMileageAllowance extends Model implements RequestElementObservableInterface
{
    use InstanceTrait;
    use RequestTrait;
    use AccountingAccountTrait;
    use MpkTrait;
    use ProjectTrait;

    protected $casts = [
        'cost_of_earning' => 'boolean',
    ];

    protected $fillable = [
        'instance_id',
        'request_id',
        'accounting_account_id',
        'mpk_id',
        'amount',
        'cost_of_earning',
        'project_id',
        'deductibility',
    ];

    public function accountDimensionItems()
    {
        return $this->hasMany(RequestAccountingMileageAllowanceAccountDimensionItem::class, 'request_accounting_mileage_allowance_id');
    }

    public function getDeductibilityAttribute(): DeductibilityEnum
    {
        return new DeductibilityEnum($this->attributes['deductibility']);
    }

    public function getAmount(): string
    {
        return (string) $this->amount;
    }

    public function getExchangeRateAttribute(): string
    {
        return Currency::DEFAULT_CURRENCY_EXCHANGE_RATE;
    }

    public function getCurrencyAttribute(): Currency
    {
        return $this->request->instance->currency;
    }

    public function getCurrencyId(): int
    {
        return $this->request->instance->currency_id;
    }
}