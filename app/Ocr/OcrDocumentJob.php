<?php

declare(strict_types=1);

namespace App\Ocr;

use App\Document;
use App\Events\Ocr\OcrProcessAsReceipt;
use App\Events\Ocr\OcrProcessAsync;
use App\Events\Ocr\OcrProcessFinished;
use App\Repositories\OcrHintRepository;
use App\Vendors\OCR\AuthConfig;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Mindento\Shared\InvoiceOcr\Port\File;

class OcrDocumentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Document
     */
    private $document;

    public function __construct(
        Document $document
    ) {
        $this->onQueue('ocr');
        $this->document = $document;
    }

    /**
     * @throws \Throwable
     */
    public function handle(
        OcrFactory $ocrFactory,
        OcrHintRepository $ocrHintRepository
    ) {
        $document = $this->document;

        if ($document->useLegacyOcr()) {
            $this->legacyOcr($document);
            return;
        }

        $this->newOcr($document, $ocrFactory, $ocrHintRepository);
    }

    private function newOcr(
        Document $document,
        OcrFactory $ocrFactory,
        OcrHintRepository $ocrHintRepository
    ): void
    {
        if (false === $document->shouldBeOcrProcessed()) {
            return;
        }

        $instanceOcr = $document->ocrInstanceConfig();
        if ($instanceOcr === null) {
            throw new \RuntimeException('Instance OCR config not found');
        }

        $ocr = $ocrFactory->make($instanceOcr);

        try {
            $document->ocrStart();
            $document->save();
            $result = $ocr->recognize(new File($document->getRealFilePath()));
        } catch (\Throwable $e) {
            $document->ocrFail($e->getMessage());
            event(new OcrProcessFinished($document));
            $document->save();

            throw $e;
        }

        $document->ocrSuccess($result->toArray());
        $document->save();

        //TODO - below legacy part - refactor $ocrHintRepository
        $ocrHintRepository
            ->fieldsFor($document->ocrDriver())
            ->parseOcrResponse($document, $document->ocr_data)
            ->saveHints();

        event(new OcrProcessFinished($document));
    }

    private function legacyOcr(Document $document): void
    {
        $document->status = Document::STATUS_PROCESSING;
        $document->save();

        if (!$document->ocr_retry || config('ocr.driver') === 'skanujto-async') {
            if (config('ocr.driver') === 'skanujto-async') {
                event(
                    new OcrProcessAsync(config('ocr.driver'), $document, new AuthConfig(config('ocr.driver')))
                );
            } else {
                event(
                    new \App\Events\Ocr\OcrProcess(
                        config('ocr.driver'),
                        $document,
                        new AuthConfig(config('ocr.driver'))
                    )
                );
            }
        } else {
            event(
                new OcrProcessAsReceipt(config('ocr.driver'), $document, new AuthConfig(config('ocr.driver')))
            );
        }
    }
}
