<?php

namespace App;

use App\Traits\LoggedAsTrait;
use App\Traits\UserTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

/**
 * App\UserLoggedAs
 *
 * @property int $id
 * @property int $user_id
 * @property int $logged_as
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $from
 * @property string|null $to
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\User $loggedAs
 * @property-read \App\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs whereFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs whereLoggedAs($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs whereTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserLoggedAs whereUserId($value)
 * @mixin \Eloquent
 */
class UserLoggedAs extends Model
{
    use UserTrait;
    use LoggedAsTrait;

    const LOG_TYPE_ASSISTANT = 'assistant';
    const LOG_TYPE_DEPUTY = 'deputy';
    const LOG_TYPE_ADMIN = 'admin';
    const LOG_TYPE_ORIGINAL_USER = 'original_user';


    public static function logTypes(): Collection
    {
        return collect([
            static::LOG_TYPE_ASSISTANT,
            static::LOG_TYPE_DEPUTY,
            static::LOG_TYPE_ADMIN,
        ]);
    }

    protected $fillable = [
        'user_id',
        'logged_as',
        'from',
        'to',
        'type'
    ];

    protected $dates = [
      'from'
    ];

    public function loggedAs()
    {
        return $this->belongsTo(User::class, 'logged_as');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
