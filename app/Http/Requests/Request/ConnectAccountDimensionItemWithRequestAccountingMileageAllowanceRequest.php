<?php

declare(strict_types=1);

namespace App\Http\Requests\Request;

use App\Http\Requests\GetRequest;
use App\Repositories\RequestAccountingMileageAllowanceRepository;
use App\Repositories\RequestRepository;
use App\Request;
use App\RequestAccountingMileageAllowance;
use Illuminate\Http\Response;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionItemViewObject;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;

class ConnectAccountDimensionItemWithRequestAccountingMileageAllowanceRequest extends GetRequest
{
    protected const REQUEST_SLUG = 'request_slug';

    protected const ACCOUNTING_MILEAGE_ALLOWANCE = 'accounting_mileage_allowance';

    protected const ACCOUNT_DIMENSION_ID = 'account_dimension_id';

    protected const ACCOUNT_DIMENSION_ITEM_ID = 'account_dimension_item_id';

    protected RequestRepository $requestRepository;

    protected RequestAccountingMileageAllowanceRepository $requestAccountingMileageAllowanceRepository;

    protected AccountDimensionItemFacade $facade;

    public function __construct(
        RequestRepository $requestRepository,
        RequestAccountingMileageAllowanceRepository $requestAccountingMileageAllowanceRepository,
        AccountDimensionItemFacade $facade
    ) {
        $this->requestRepository = $requestRepository;
        $this->requestAccountingMileageAllowanceRepository = $requestAccountingMileageAllowanceRepository;
        $this->facade = $facade;
    }

    public function authorize()
    {
        $request = $this->getRequest();

        if($request === null || $request->instance_id !== $this->user()->instance_id) {
            return false;
        }

        return true;
    }

    public function rules()
    {
        return [
            self::REQUEST_SLUG => ['required', 'string'],
            self::ACCOUNTING_MILEAGE_ALLOWANCE => ['required', 'string'],
            self::ACCOUNT_DIMENSION_ID => ['required', 'numeric'],
            self::ACCOUNT_DIMENSION_ITEM_ID => ['required', 'numeric'],

        ];
    }

    public function getRequest(): ?Request
    {
        if (!empty($this->route()->parameter(self::REQUEST_SLUG))) {
            return $this->requestRepository->findById($this->route()->parameter(self::REQUEST_SLUG));
        }

        return null;
    }

    public function getRequestAccountingMileageAllowance(): ?RequestAccountingMileageAllowance
    {
        if (!empty($this->route()->parameter(self::ACCOUNTING_MILEAGE_ALLOWANCE))) {
            return $this->requestAccountingMileageAllowanceRepository->findById($this->route()->parameter(self::ACCOUNTING_MILEAGE_ALLOWANCE));
        }

        return null;

    }

    public function getAccountingDimensionItem(): AccountDimensionItemViewObject
    {
        $request = $this->getRequest();
        $accountDimensionItemViewObject = $this->facade->findAccountDimensionItem(
            (int) $this->route()->parameter(self::ACCOUNT_DIMENSION_ID),
            (int) $this->route()->parameter(self::ACCOUNT_DIMENSION_ITEM_ID),
            $this->getRequest()->instance_id,
            $request->company_id
        );

        abort_if($accountDimensionItemViewObject === null, Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));

        return $accountDimensionItemViewObject;
    }
}
