<?php

declare(strict_types=1);

namespace App\Http\Requests\Request;

use App\Exceptions\ValidationException;
use Illuminate\Foundation\Http\FormRequest;

class SendToAcceptanceRequest extends FormRequest
{
    public function rules() {
        return [
            'chronologyConfirmed'    => 'required|boolean'
        ];
    }

    public function withValidator($validator)
    {
        if($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    public function getSlug(): ?string
    {
        return $this->route()->parameter('slug');
    }
}