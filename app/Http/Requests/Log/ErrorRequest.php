<?php

declare(strict_types=1);

namespace App\Http\Requests\Log;

use Modules\Common\Http\Requests\BaseFormRequest;

class ErrorRequest extends BaseFormRequest
{
    private const DEBUG_SESSION_ID = 'debug_session_id';
    private const STACK_TRACE = 'stack_trace';
    private const MESSAGE = 'message';
    private const URL = 'url';
    private const BROWSER = 'browser';
    private const OS = 'os';
    private const RESOLUTION = 'resolution';

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            self::DEBUG_SESSION_ID => ['required', 'string'],
            self::STACK_TRACE => ['required', 'string'],
            self::MESSAGE => ['required', 'string'],
            self::URL => ['required', 'string'],
            self::BROWSER => ['required', 'string'],
            self::OS => ['required', 'string'],
            self::RESOLUTION => ['required', 'string'],
        ];
    }

    public function getDebugSessionId(): string
    {
        return $this->get(self::DEBUG_SESSION_ID);
    }

    public function getStackTrace(): string
    {
        return $this->get(self::STACK_TRACE);
    }

    public function getMessage(): string
    {
        return $this->get(self::MESSAGE);
    }

    public function getUrl(): string
    {
        return $this->get(self::URL);
    }

    public function getBrowser(): string
    {
        return $this->get(self::BROWSER);
    }

    public function getOS(): string
    {
        return $this->get(self::OS);
    }

    public function getResolution(): string
    {
        return $this->get(self::RESOLUTION);
    }
}
