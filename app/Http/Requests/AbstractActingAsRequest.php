<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Exceptions\GenericRequestException;
use App\Permission;
use App\Repositories\UserRepository;
use App\User;
use Illuminate\Foundation\Http\FormRequest;

abstract class AbstractActingAsRequest extends FormRequest
{
    protected const USER_SLUG_FIELD = 'slug';
    protected $slugField;

    protected $userRepository;

    public function __construct(
        array $query = [],
        array $request = [],
        array $attributes = [],
        array $cookies = [],
        array $files = [],
        array $server = [],
        $content = null,
        UserRepository $userRepository = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);

        $this->userRepository = $userRepository;
        $this->slugField = self::USER_SLUG_FIELD;
    }

    public function getSubjectUser(): User
    {
        if ($this->isRequestInBehalfOfUser() === true) {
            $user = $this->userRepository->findUserBySlug($this->get($this->slugField));

            if (($user instanceof User) === false) {
                throw GenericRequestException::create(trans('error.server-error'));
            }

            return $user;
        }

        /** @var User $authUser */
        $authUser = \Auth::user();

        return $authUser;
    }

    protected function isRequestInBehalfOfUser(): bool
    {
        /**
         * @var User $authUser
         */
        $authUser = \Auth::user();

        if (
            empty($this->get($this->slugField)) === false &&
            (
                $authUser->hasAbility(Permission::MULTI_INSTANCE_VIEW) ||
                $authUser->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_USERS)
            )

        ) {
            return true;
        }

        return false;
    }
}
