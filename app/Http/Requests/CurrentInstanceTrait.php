<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Instance;
use Symfony\Component\HttpFoundation\ServerBag;

/**
 * @property ServerBag server
 *
 * Trait CurrentInstanceTrait
 * @package App\Http\Requests
 */
trait CurrentInstanceTrait
{
    private static ?Instance $_currentInstance = null;

    public function getCurrentInstance(): Instance
    {
        if (self::$_currentInstance === null) {
            $domain = request()->server('HTTP_HOST');

            self::$_currentInstance = Instance::where('domain', $domain)->first();

            if (self::$_currentInstance === null) {
                abort(404, trans('error.instance-not-found'));
            }
        }

        return self::$_currentInstance;
    }
}
