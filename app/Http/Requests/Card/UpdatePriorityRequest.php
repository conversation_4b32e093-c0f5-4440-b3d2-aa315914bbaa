<?php

declare(strict_types=1);

namespace App\Http\Requests\Card;

use App\Exceptions\ValidationException;
use App\Http\Requests\AbstractActingAsRequest;
use App\Repositories\UserRepository;
use Illuminate\Contracts\Validation\Validator;

class UpdatePriorityRequest extends AbstractActingAsRequest
{
    protected const USER_SLUG_FIELD = 'userSlug';

    public function __construct(
        array $query = [],
        array $request = [],
        array $attributes = [],
        array $cookies = [],
        array $files = [],
        array $server = [],
        $content = null,
        UserRepository $userRepository = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content, $userRepository);

        $this->slugField = self::USER_SLUG_FIELD;
    }

    public function authorize() {
        return true;
    }

    public function rules() {
        return [
            'userSlug' => 'nullable|string'
        ];
    }

    /**
     * @inheritdoc
     */
    protected function failedValidation(Validator $validator)
    {
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
