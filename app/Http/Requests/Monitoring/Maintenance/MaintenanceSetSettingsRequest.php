<?php

declare(strict_types=1);

namespace App\Http\Requests\Monitoring\Maintenance;

use App\Exceptions\ValidationException;
use App\Repositories\InstanceRepository;
use App\Services\Monitoring\Maintenance\MaintenanceDto;
use Illuminate\Foundation\Http\FormRequest;

class MaintenanceSetSettingsRequest extends FormRequest
{
    protected $instanceRepository;

    public function __construct(
        array $query = [],
        array $request = [],
        array $attributes = [],
        array $cookies = [],
        array $files = [],
        array $server = [],
        $content = null,
        InstanceRepository $instanceRepository = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);

        $this->instanceRepository = $instanceRepository;
    }

    public function rules() {
        return [
            'mindentoInstanceId'    => 'required|numeric',
            'mindentoDomain'        => 'required|string|min:2|max:255',
            'enabled'               => 'required|boolean',
            'title'                 => 'nullable|string',
            'message'               => 'nullable|string',
            'allowedIps'            => 'nullable|array',
        ];
    }

    public function withValidator($validator)
    {
        if($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    public function resolveMaintenanceDtoAndInstance(): array
    {
        $instance = $this->instanceRepository->findByIdAndDomain(
            (int)$this->get('mindentoInstanceId'),
            (string)$this->get('mindentoDomain')
        );

        $maintenanceDto = MaintenanceDto::createFromCurrentSettingAndRequest($instance->getMaintenanceModeSettings(), $this);

        return [$maintenanceDto, $instance];
    }

    public function getMindentoInstanceId(): int
    {
        return (int)$this->get('mindentoInstanceId');
    }

    public function getMindentoDomain(): string
    {
        return (string)$this->get('mindentoDomain');
    }

    public function getIsEnabled(): bool
    {
        return (bool)$this->get('enabled');
    }

    public function getTitle(): ?string
    {
        return $this->has('title') ? (string)$this->get('title') : null;
    }

    public function getMessage(): ?string
    {
        return $this->has('message') ? (string)$this->get('message') : null;
    }

    public function getAllowedIps(): ?array
    {
        return $this->has('allowedIps') ? (array)$this->get('allowedIps') : null;
    }
}