<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\CurrentInstanceRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\DecisionMaker\Priv\Models\DecisionProcess;
use Mo<PERSON>les\DecisionMaker\Priv\Models\DecisionProcessCondition;
use Modules\DecisionMaker\Priv\Models\DecisionProcessNode;

class DecisionProcessGraphController extends Controller
{
    /**
     * Simple test endpoint to verify controller is working
     */
    public function test(): JsonResponse
    {
        return response()->json(['message' => 'DecisionProcessGraphController is working!']);
    }

    /**
     * Debug endpoint to check raw data
     */
    public function debug(CurrentInstanceRequest $request): JsonResponse
    {
        $currentInstance = $request->getCurrentInstance();

        // Get raw decision processes
        $processes = DecisionProcess::with(['nodes.conditions'])->get();

        $debugData = [
            'currentInstance' => [
                'id' => $currentInstance->id,
                'name' => $currentInstance->name,
            ],
            'processCount' => $processes->count(),
            'processes' => $processes->map(function($process) {
                return [
                    'id' => $process->id,
                    'slug' => $process->slug,
                    'nodeCount' => $process->nodes->count(),
                    'firstNode' => $process->nodes->first() ? [
                        'id' => $process->nodes->first()->id,
                        'decision_code' => $process->nodes->first()->decision_code,
                        'operator_code' => $process->nodes->first()->operator_code,
                        'instance_id' => $process->nodes->first()->instance_id,
                        'conditionCount' => $process->nodes->first()->conditions->count(),
                    ] : null
                ];
            })
        ];

        return response()->json($debugData);
    }
    /**
     * Get decision process graph data for visualization
     *
     * @param Request $request
     * @param string|null $slug
     * @return JsonResponse
     */
    public function getGraphData(CurrentInstanceRequest $request, ?string $slug = null): JsonResponse
    {
        try {
            $currentInstance = $request->getCurrentInstance();

            if ($slug) {
                // Get specific decision process with relationships
                $process = DecisionProcess::with(['nodes.conditions'])->where('slug', $slug)->first();
                if (!$process) {
                    return response()->json(['error' => 'Decision process not found'], 404);
                }
                $processes = collect([$process]);
            } else {
                // Get all decision processes
                $processes = DecisionProcess::with(['nodes.conditions'])->get();
            }

            $graphData = $this->transformToGraphData($processes, $currentInstance);

            // Debug logging
            \Log::info('Decision Process Graph Data', [
                'processCount' => $processes->count(),
                'nodeCount' => count($graphData['nodes']),
                'firstNode' => $graphData['nodes'][0] ?? null,
                'currentInstance' => $currentInstance->name ?? 'Unknown'
            ]);

            return response()->json($graphData);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch graph data: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Transform decision processes to graph-friendly format
     *
     * @param \Illuminate\Support\Collection $processes
     * @param \App\Instance $currentInstance
     * @return array
     */
    private function transformToGraphData($processes, $currentInstance): array
    {
        $nodes = [];
        $edges = [];
        $processGroups = [];

        foreach ($processes as $process) {
            $processGroups[] = [
                'id' => $process->id,
                'slug' => $process->slug,
                'name' => $this->formatProcessName($process->slug),
            ];

            // Filter nodes by current instance - include nodes with no instance_id (default) or matching instance
            $processNodes = $process->nodes()
                ->with('conditions') // Ensure conditions are loaded
                ->where(function ($query) use ($currentInstance) {
                    $query->whereNull('instance_id')
                          ->orWhere('instance_id', $currentInstance->id);
                })
                ->orderBy('order')
                ->get();

            foreach ($processNodes as $index => $node) {
                $nodeId = "process_{$process->id}_node_{$node->id}";
                
                // Create node data
                $nodes[] = [
                    'id' => $nodeId,
                    'label' => $this->formatNodeLabel($node),
                    'type' => 'decision_node',
                    'processId' => $process->id,
                    'processSlug' => $process->slug,
                    'nodeId' => $node->id,
                    'operatorCode' => $node->operator_code,
                    'decisionCode' => $node->decision_code,
                    'decisionParameters' => $node->decision_parameters,
                    'order' => $node->order ?? $index,
                    'description' => $node->description,
                    'conditions' => $this->getNodeConditions($node),
                    'group' => $process->id,
                    'instanceId' => $node->instance_id,
                    'isDefault' => $node->instance_id === null,
                ];

                // Create edges between consecutive nodes in the same process
                if ($index > 0) {
                    $previousNodeId = "process_{$process->id}_node_{$processNodes[$index - 1]->id}";
                    $edges[] = [
                        'id' => "edge_{$previousNodeId}_to_{$nodeId}",
                        'source' => $previousNodeId,
                        'target' => $nodeId,
                        'type' => 'flow',
                        'label' => 'Next if conditions fail',
                    ];
                }
            }
        }

        return [
            'nodes' => $nodes,
            'edges' => $edges,
            'processGroups' => $processGroups,
            'metadata' => [
                'totalProcesses' => $processes->count(),
                'totalNodes' => count($nodes),
                'totalEdges' => count($edges),
                'currentInstanceId' => $currentInstance->id,
                'currentInstanceName' => $currentInstance->name,
                'generatedAt' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Get conditions for a node
     *
     * @param DecisionProcessNode $node
     * @return array
     */
    private function getNodeConditions(DecisionProcessNode $node): array
    {
        if (!$node->conditions) {
            return [];
        }

        return $node->conditions->map(function (DecisionProcessCondition $condition) {
            return [
                'id' => $condition->id,
                'code' => $condition->code,
                'parameters' => $condition->parameters,
                'negate' => $condition->negate,
                'description' => $this->formatConditionDescription($condition),
            ];
        })->toArray();
    }

    /**
     * Format process name for display
     *
     * @param string $slug
     * @return string
     */
    private function formatProcessName(string $slug): string
    {
        return str_replace(['_', '-'], ' ', ucwords($slug, '_-'));
    }

    /**
     * Format node label for display
     *
     * @param DecisionProcessNode $node
     * @return string
     */
    private function formatNodeLabel(DecisionProcessNode $node): string
    {
        $label = str_replace(['_', '-'], ' ', ucwords($node->decision_code, '_-'));
        
        if ($node->description) {
            $label .= "\n" . substr($node->description, 0, 50);
            if (strlen($node->description) > 50) {
                $label .= '...';
            }
        }

        return $label;
    }

    /**
     * Format condition description for display
     *
     * @param DecisionProcessCondition $condition
     * @return string
     */
    private function formatConditionDescription(DecisionProcessCondition $condition): string
    {
        $description = str_replace(['_', '-'], ' ', ucwords($condition->code, '_-'));
        
        if ($condition->negate) {
            $description = 'NOT ' . $description;
        }

        if (!empty($condition->parameters)) {
            $paramStr = is_array($condition->parameters) 
                ? json_encode($condition->parameters) 
                : $condition->parameters;
            $description .= " ({$paramStr})";
        }

        return $description;
    }
}
