<?php

declare(strict_types=1);

namespace App\Nova\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\DecisionMaker\Priv\Models\DecisionProcess;
use Mo<PERSON>les\DecisionMaker\Priv\Models\DecisionProcessCondition;
use Modules\DecisionMaker\Priv\Models\DecisionProcessNode;

class DecisionProcessGraphController extends Controller
{
    /**
     * Get decision process graph data for visualization
     *
     * @param Request $request
     * @param string|null $slug
     * @return JsonResponse
     */
    public function getGraphData(Request $request, ?string $slug = null): JsonResponse
    {
        try {
            if ($slug) {
                // Get specific decision process
                $process = DecisionProcess::where('slug', $slug)->first();
                if (!$process) {
                    return response()->json(['error' => 'Decision process not found'], 404);
                }
                $processes = collect([$process]);
            } else {
                // Get all decision processes
                $processes = DecisionProcess::with(['nodes.conditions'])->get();
            }

            $graphData = $this->transformToGraphData($processes);

            return response()->json($graphData);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch graph data: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Transform decision processes to graph-friendly format
     *
     * @param \Illuminate\Support\Collection $processes
     * @return array
     */
    private function transformToGraphData($processes): array
    {
        $nodes = [];
        $edges = [];
        $processGroups = [];

        foreach ($processes as $process) {
            $processGroups[] = [
                'id' => $process->id,
                'slug' => $process->slug,
                'name' => $this->formatProcessName($process->slug),
            ];

            $processNodes = $process->nodes()->orderBy('order')->get();

            foreach ($processNodes as $index => $node) {
                $nodeId = "process_{$process->id}_node_{$node->id}";
                
                // Create node data
                $nodes[] = [
                    'id' => $nodeId,
                    'label' => $this->formatNodeLabel($node),
                    'type' => 'decision_node',
                    'processId' => $process->id,
                    'processSlug' => $process->slug,
                    'nodeId' => $node->id,
                    'operatorCode' => $node->operator_code,
                    'decisionCode' => $node->decision_code,
                    'decisionParameters' => $node->decision_parameters,
                    'order' => $node->order ?? $index,
                    'description' => $node->description,
                    'conditions' => $this->getNodeConditions($node),
                    'group' => $process->id,
                ];

                // Create edges between consecutive nodes in the same process
                if ($index > 0) {
                    $previousNodeId = "process_{$process->id}_node_{$processNodes[$index - 1]->id}";
                    $edges[] = [
                        'id' => "edge_{$previousNodeId}_to_{$nodeId}",
                        'source' => $previousNodeId,
                        'target' => $nodeId,
                        'type' => 'flow',
                        'label' => 'Next if conditions fail',
                    ];
                }
            }
        }

        return [
            'nodes' => $nodes,
            'edges' => $edges,
            'processGroups' => $processGroups,
            'metadata' => [
                'totalProcesses' => $processes->count(),
                'totalNodes' => count($nodes),
                'totalEdges' => count($edges),
                'generatedAt' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Get conditions for a node
     *
     * @param DecisionProcessNode $node
     * @return array
     */
    private function getNodeConditions(DecisionProcessNode $node): array
    {
        return $node->conditions->map(function (DecisionProcessCondition $condition) {
            return [
                'id' => $condition->id,
                'code' => $condition->code,
                'parameters' => $condition->parameters,
                'negate' => $condition->negate,
                'description' => $this->formatConditionDescription($condition),
            ];
        })->toArray();
    }

    /**
     * Format process name for display
     *
     * @param string $slug
     * @return string
     */
    private function formatProcessName(string $slug): string
    {
        return str_replace(['_', '-'], ' ', ucwords($slug, '_-'));
    }

    /**
     * Format node label for display
     *
     * @param DecisionProcessNode $node
     * @return string
     */
    private function formatNodeLabel(DecisionProcessNode $node): string
    {
        $label = str_replace(['_', '-'], ' ', ucwords($node->decision_code, '_-'));
        
        if ($node->description) {
            $label .= "\n" . substr($node->description, 0, 50);
            if (strlen($node->description) > 50) {
                $label .= '...';
            }
        }

        return $label;
    }

    /**
     * Format condition description for display
     *
     * @param DecisionProcessCondition $condition
     * @return string
     */
    private function formatConditionDescription(DecisionProcessCondition $condition): string
    {
        $description = str_replace(['_', '-'], ' ', ucwords($condition->code, '_-'));
        
        if ($condition->negate) {
            $description = 'NOT ' . $description;
        }

        if (!empty($condition->parameters)) {
            $paramStr = is_array($condition->parameters) 
                ? json_encode($condition->parameters) 
                : $condition->parameters;
            $description .= " ({$paramStr})";
        }

        return $description;
    }
}
