<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\Deputy\DeputyRequest;
use App\Http\Responses\UserDeputyResponse;
use App\Services\Deputy\DeputyService;

class UserDeputyController extends Controller
{
    /** @var DeputyService */
    protected $deputyService;

    public function __construct(
        DeputyService $deputyService
    ) {
        $this->deputyService = $deputyService;
    }

    public function index(DeputyRequest $request)
    {
        return UserDeputyResponse::collection(
            $this->deputyService->getUserDeputies($request->getSubjectUser())
        );
    }

    public function store(DeputyRequest $request)
    {
        return UserDeputyResponse::item(
            $this->deputyService->addUserDeputyToUser(
                $request->getDeputyUser(),
                $request->getSubjectUser(),
                $request->getDeputyPeriodFrom(),
                $request->getDeputyPeriodTo()
            )
        )->addInfo(trans('info.user-deputy-has-been-added-to-user'));
    }

    public function destroy(DeputyRequest $request)
    {
        return UserDeputyResponse::item(
            $this->deputyService->deleteDeputyFromUser($request->getDeputy(), $request->getSubjectUser())
        )->addInfo(trans('info.user-deputy-has-been-deleted'));
    }
}
