<?php

namespace App\Http\Controllers;

use App\Card;
use App\CardManager\DTOs\AddCardDTO;
use App\CardManager\Integration\Client;
use App\CardManager\Services\AddCardService;
use App\CardManager\Services\CardInfoBroadcaster;
use App\CardManager\Services\DeleteCardService;
use App\CardManager\Services\UpdateUserCardsPriorityService;
use App\Http\Requests\Card\DestroyRequest;
use App\Http\Requests\Card\IndexRequest;
use App\Http\Requests\Card\StoreRequest;
use App\Http\Requests\Card\UpdatePriorityRequest;
use App\Http\Responses\CardResponse;
use App\Http\Responses\Response2;
use App\Repositories\CardRepository;
use App\User;
use Illuminate\Http\Request;

class CardController extends Controller
{
    protected CardInfoBroadcaster $cardInfoBroadcaster;
    private AddCardService $addCardService;

    public function __construct(
        CardInfoBroadcaster $cardInfoBroadcaster,
        AddCardService $addCardService
    ) {
        $this->cardInfoBroadcaster = $cardInfoBroadcaster;
        $this->addCardService = $addCardService;
    }

    public function store(StoreRequest $request)
    {
        /** @var User $user */
        $user = $request->getSubjectUser();

        $companies = $request->companies;

        if(!$companies){
            $companies = $user->company()->pluck('id')->toArray();
        }

        $DTO = new AddCardDTO(
            $request->type,
            route('cardAdded', ['url' => $request->successUrl]),
            route('cardAdded', ['url' => $request->errorUrl]),
            $request->ip(),
            $request->get('screenHeight', ''),
            $request->get('screenWidth', ''),
            $user->email,
            $user->phone,
            $request->get('cardholderName', ''),
            $request->header('User-Agent'),
            '',
            true,
            $request->header('Accept')
        );

        $data = $this->addCardService->add($user->instance, $user, $DTO, $companies);

        return Response2::item($data->formData());
    }

    public function index(IndexRequest $indexRequest)
    {
        $user = $indexRequest->getSubjectUser();

        if(!$user) {
            abort(400, trans('error.user-not-found'));
        }

        $cards = (new \App\CardManager\Card\CardRepository(resolve(CardRepository::class), new Client()))
            ->getAvailableCardsByUser($user);

        return CardResponse::collection($cards);
    }

    public function corporate(Request $request)
    {
        $cards = (new \App\CardManager\Card\CardRepository(resolve(CardRepository::class), new Client()))
            ->getAvailableInstanceCorporateCards($request->user()->instance);

        return CardResponse::collection($cards);
    }

    public function updateCardsPriority(CardRepository $repository, UpdatePriorityRequest $request)
    {
        $service = new UpdateUserCardsPriorityService(
            collect(request()->get('cards', [])),
            (new \App\CardManager\Card\CardRepository($repository, new Client())),
            $request->getSubjectUser()
        );

        /** @var Card $card */
        $cards = $service->updatePriority();

        return CardResponse::collection($cards);
    }

    public function destroy($slug, CardRepository $cardRepository, DestroyRequest $request)
    {
        /** @var Card $dbCard */
        $dbCard = $cardRepository->getBySlug($slug);

        if(!$dbCard) {
            abort(404, trans('global.item-does-not-exists'));
        }

        $client = new Client();
        $service = (new DeleteCardService($cardRepository, $client, $dbCard));
        $card = $service->delete();

        $cmRepository = new \App\CardManager\Card\CardRepository($cardRepository, $client);

        if($card->isDeleted()) {
            $this->cardInfoBroadcaster->broadcast($dbCard->slug);
        }

        if($dbCard->isCorporate()) {
            $availableCards = $cmRepository->getAvailableInstanceCorporateCards($request->user()->instance);
        } else {
            $availableCards = $cmRepository->getAvailableCardsByUser($request->getSubjectUser());
        }

        return CardResponse::collection($availableCards);
    }
}
