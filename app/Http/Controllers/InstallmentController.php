<?php


namespace App\Http\Controllers;


use App\Http\Responses\InstallmentIndexResponse;
use App\Http\Responses\InstallmentResponse;
use App\Installment;
use App\Permission;
use App\Repositories\Criteria\CompanyCriterion;
use App\Repositories\Criteria\InstallmentAccountingPeriodCriterion;
use App\Repositories\Criteria\InstallmentAccountingStatusCriterion;
use App\Repositories\Criteria\InstallmentStatusCriterion;
use App\Repositories\Criteria\InstanceV2Criterion;
use App\Repositories\Criteria\PermissionScopeCriterion;
use App\Repositories\Criteria\RequestStatusCriterion;
use App\Repositories\Criteria\SearchPhraseCriterion;
use App\Repositories\Criteria\UserCriterion;
use App\Repositories\RequestRepository;
use App\Repositories\InstallmentRepository;
use App\Request;
use Modules\Common\Dtos\OffsetPageDto;

class InstallmentController extends Controller
{
    public function index(InstallmentRepository $installmentRepository, \Illuminate\Http\Request $request)
    {
        $pagination = request()->get('pagination') === 'true' || request()->get('pagination') === true;

        if ($pagination === true) {
            return InstallmentIndexResponse::collection($installmentRepository->getInstallmentsIndex(
                OffsetPageDto::fromRequestAndModelClass(Installment::class),
                $request->user()->instance
            ));
        } else {
            $installments = $installmentRepository->getByCriteria([
                new InstanceV2Criterion($request->user()->instance),
                new SearchPhraseCriterion(),
                new UserCriterion(),
                new CompanyCriterion(),
                new InstallmentAccountingStatusCriterion(),
                new InstallmentAccountingPeriodCriterion(),
                (new InstallmentStatusCriterion())->setStatusesFromRequest(),
                new RequestStatusCriterion([
//                    Request::STATUS_DRAFT, // MIN-656
//                    Request::STATUS_WAITING_FOR_ACCEPTANCE, // MIN-656
                    Request::STATUS_UPCOMING_TRIP,
                    Request::STATUS_TRIP,
                    Request::STATUS_SETTLEMENT,
                    Request::STATUS_ACCEPTANCE_OF_SETTLEMENT,
                    Request::STATUS_ACCOUNTING,

                    Request::STATUS_FINISH,
                    Request::STATUS_TRANSFER_ERROR,
                    Request::STATUS_TRANSFERRED
                ]),
                new PermissionScopeCriterion(Permission::REQUEST_ACCOUNTING),
            ]);

            return InstallmentResponse::collection($installments);
        }
    }

    public function store($request, RequestRepository $requestRepository)
    {
        $installment = $requestRepository->createInstallment($request, request()->all());

        return InstallmentResponse::item($installment)->addInfo(trans('info.installment-has-been-saved'));
    }

    public function update($request, $id, RequestRepository $requestRepository)
    {
        $data = request()->all();
        $installment = $requestRepository->updateInstallment($request, $id, $data);

        return InstallmentResponse::item($installment)->addInfo(trans('info.installment-has-been-saved'));
    }

    public function setPaidDate($id, InstallmentRepository $installmentRepository)
    {
        /** @var Installment $installment */
        $installment = $installmentRepository->findById($id);

        return InstallmentResponse::item($installmentRepository->setPaidDate($installment, request('date')));
    }

    public function setAccountingDate($id, InstallmentRepository $installmentRepository)
    {
        /** @var Installment $installment */
        $installment = $installmentRepository->findById($id);

        return InstallmentResponse::item($installmentRepository->setAccountingDate($installment, request('date')));
    }

    public function setExchangeRate($id, InstallmentRepository $installmentRepository)
    {
        /** @var Installment $installment */
        $installment = $installmentRepository->findById($id);

        return InstallmentResponse::item($installmentRepository->setExchangeRate($installment, request('exchangeRate')));
    }

    public function setPaidAmount($id, InstallmentRepository $installmentRepository)
    {
        /** @var Installment $installment */
        $installment = $installmentRepository->findById($id);

        $amount = request('amount');
        $currency = request('currency');

        return InstallmentResponse::item($installmentRepository->setPaidAmount($installment, $amount, $currency ));
    }

    public function setStatus($id, InstallmentRepository $installmentRepository)
    {
        /** @var Installment $installment */
        $installment = $installmentRepository->findById($id);

        return InstallmentResponse::item($installmentRepository->setStatus($installment, request('status')));
    }

    public function setAccountingStatus($id, InstallmentRepository $installmentRepository)
    {
        /** @var Installment $installment */
        $installment = $installmentRepository->findById($id);

        return InstallmentResponse::item($installmentRepository->setAccountingStatus($installment, request('accounted')));
    }

    public function setErpId($id, InstallmentRepository $installmentRepository, Request $request)
    {
        /** @var Installment $installment */
        $installment = $installmentRepository->findById($id);

        return InstallmentResponse::item($installmentRepository->setErpId($installment, request('erpId')));
    }

    public function destroy($request, $id, RequestRepository $requestRepository)
    {
        $requestRepository->deleteInstallment($request, $id);

        return InstallmentResponse::item([])->addInfo(trans('info.installment-has-been-deleted'));
    }
}
