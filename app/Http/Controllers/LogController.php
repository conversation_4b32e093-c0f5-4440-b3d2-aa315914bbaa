<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\Log\ErrorRequest;
use Illuminate\Http\Response;
use Illuminate\Log\Logger;
use Illuminate\Support\Facades\Log;

class LogController extends Controller
{
    public function error(ErrorRequest $request): Response
    {
        /** @var Logger $logger */
        $logger = Log::channel('frontend-stack');

        $logger->error($request->getMessage(), [
            'browser' => $request->getBrowser(),
            'os' => $request->getOS(),
            'resolution' => $request->getResolution(),
            'url' => $request->getUrl(),
            'debug_session_id' => $request->getDebugSessionId(),
            'exception' => [
                'message' => $request->getMessage(),
                'code' => null,
                'trace' => $request->getStackTrace(),
                'file' => null
            ]
        ]);

        return response([], Response::HTTP_NO_CONTENT);
    }
}
