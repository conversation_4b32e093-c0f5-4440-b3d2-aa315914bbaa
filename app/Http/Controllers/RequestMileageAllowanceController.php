<?php

namespace App\Http\Controllers;

use App\Helpers\NameTranslation;
use App\Http\Responses\RequestMileageAllowanceAccountDimensionItemResponse;
use App\Http\Responses\RequestMileageAllowanceResponse;
use App\Repositories\Criteria\InstanceV2Criterion;
use App\Repositories\Criteria\RequestSlugCriterion;
use App\Repositories\RequestMileageAllowanceAccountDimensionItemRepository;
use App\Repositories\RequestMileageAllowanceRepository;
use App\Repositories\RequestRepository;
use App\RequestMileageAllowance;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;

class RequestMileageAllowanceController extends Controller
{
    public function index($request_slug, RequestMileageAllowanceRepository $allowanceRepository, Request $request)
    {
        $allowances = $allowanceRepository->getByCriteria([
            new InstanceV2Criterion($request->user()->instance),
            new RequestSlugCriterion([$request_slug]),
        ]);

        return RequestMileageAllowanceResponse::collection($allowances);
    }

    public function update($request, $id, RequestRepository $requestRepository)
    {
        $data = request()->all();
        $allowance = $requestRepository->updateMileageAllowance($request, $id, $data);
        $allowance->refresh();

        return RequestMileageAllowanceResponse::item($allowance)->addInfo(trans('info.request-mileage-has-been-saved'));
    }

    public function addAccountDimensionItem(
        string $slug,
        int $milleageAllowanceId,
        int $accountDimensionItem,
        int $accountDimensionItemId,
        RequestMileageAllowanceRepository $mileageAllowanceRepository,
        AccountDimensionItemFacade $facade,
        RequestMileageAllowanceAccountDimensionItemRepository $accountDimensionItemRepository
    )
    {
        /** @var RequestMileageAllowance $requestMilleageAllowance */
        $requestMilleageAllowance = $mileageAllowanceRepository->findById($milleageAllowanceId);

        $accountDimensionItemViewObject = $facade->findAccountDimensionItem(
            $accountDimensionItem,
            $accountDimensionItemId,
            $requestMilleageAllowance->request->instance_id,
            $requestMilleageAllowance->request->company_id,
        );

        abort_if($accountDimensionItemViewObject === null, Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));

        $requestAccountDimensionItem = $accountDimensionItemRepository->connect($requestMilleageAllowance, $accountDimensionItemViewObject);

        return RequestMileageAllowanceAccountDimensionItemResponse::item($requestAccountDimensionItem)
            ->addSuccess((new NameTranslation('success.request-status-has-been-changed'))->toArray());
    }

    public function removeAccountDimensionItem(
        string $slug,
        int $rmadiId,
        RequestRepository $requestRepository,
        RequestMileageAllowanceAccountDimensionItemRepository $requestMileageAllowanceItemRepository
    ) {
        /** @var \App\Request $request */
        $request = $requestRepository->findBySlug($slug);

        $requestMillageAllowanceAccountDimensionItem = $requestMileageAllowanceItemRepository->findByRequestAndId($request, $rmadiId);

        abort_if($requestMillageAllowanceAccountDimensionItem === null, Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));
        abort_if($requestMillageAllowanceAccountDimensionItem->accountDimension->is_required === true, Response::HTTP_UNPROCESSABLE_ENTITY, trans('validation.filled'));

        return response()->json(
            [],
            $requestMileageAllowanceItemRepository->disconnect($requestMillageAllowanceAccountDimensionItem)
                ? Response::HTTP_OK
                : Response::HTTP_UNPROCESSABLE_ENTITY
        );
    }
}
