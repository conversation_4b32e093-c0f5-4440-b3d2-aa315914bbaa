<?php

namespace App\Http\Controllers;

use App\Exceptions\ClearingInstanceNotPossibleException;
use App\Http\Requests\CurrentInstanceRequest;
use App\Http\Requests\Instance\OrganizationalStructure\IndexRequest as OrganizationalStructureIndexRequest;
use App\Http\Responses\InstanceResponse;
use App\Http\Responses\Response2;
use App\Instance;
use App\Repositories\InstanceRepository;
use App\Services\ClearInstance\ClearInstanceService;
use App\Services\Language\LanguageService;
use App\Services\OrganizationalStructure\OrganizationalStructureService;

/**
 * Class InstanceController
 * @package App\Http\Controllers
 */
class InstanceController extends Controller
{
    protected $organizationalStructureService;

    public function __construct(OrganizationalStructureService $organizationalStructureService)
    {
        $this->organizationalStructureService = $organizationalStructureService;
    }

    public function index()
    {
        $instances = Instance::all();
        return InstanceResponse::collection($instances);
    }
    /**
     * @param InstanceRepository $instanceRepository
     * @return $this
     * @throws \Exception
     */
    public function store(InstanceRepository $instanceRepository)
    {
        $instance = $instanceRepository->create(request()->all());

        return InstanceResponse::item($instance)->addInfo(trans('info.instance-has-been-saved'));
    }

    /**
     * @param $instance_id
     * @param InstanceRepository $instanceRepository
     * @return $this
     * @throws \Exception
     */
    public function update($instance_id, InstanceRepository $instanceRepository)
    {
        $instance = $instanceRepository->update($instance_id, request()->all());
        $instance->refresh();

        return InstanceResponse::item($instance)->addInfo(trans('info.instance-has-been-saved'));
    }

    /**
     * @param $instance_id
     * @param InstanceRepository $instanceRepository
     * @return $this
     * @throws \Exception
     */
    public function destroy($instance_id, InstanceRepository $instanceRepository)
    {
        $instance = $instanceRepository->delete($instance_id);

        return InstanceResponse::item($instance)->addInfo(trans('info.instance-has-been-deleted'));
    }

    public function ping(CurrentInstanceRequest $request)
    {
        return InstancePingResponse::item($request->getCurrentInstance());
    }

    public function availableLanguages()
    {
        return Response2::collection((new LanguageService())->getAvailableLanguages());
    }

    public function clear(CurrentInstanceRequest $request, ClearInstanceService $clearInstanceService)
    {
        $instance = $request->getCurrentInstance();

        try {
            $clearInstanceService->clearInstance($instance, $request->user());
        } catch (ClearingInstanceNotPossibleException $e) {
            abort(403, trans('global.access-denied'));
        }

        return Response2::item()->addSuccess(trans('success.instance-has-been-cleared'));
    }

    public function organizationalStructure(OrganizationalStructureIndexRequest $request)
    {
        return Response2::item($this->organizationalStructureService->generate($request->getSubjectUser()));
    }
}
