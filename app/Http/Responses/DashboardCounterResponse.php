<?php

declare(strict_types=1);

namespace App\Http\Responses;

use App\Instance;
use App\Repositories\Pagination\PaginatorInterface;
use App\Repositories\RequestRepository;
use App\Request;
use Illuminate\Support\Facades\Auth;

class DashboardCounterResponse extends Response2Paginated
{
    protected function transform(\stdClass $requestCounter)
    {
        return [
            'currentTrips'          => $requestCounter->currentTrips,
            'requestToAccept'       => $requestCounter->requestToAccept,
            'settlementToAccept'    => $requestCounter->settlementToAccept,
            'waitingToSettlement'   => $requestCounter->waitingToSettlement
        ];
    }
}
