<?php

declare(strict_types=1);

namespace App\Http\Responses;

use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Http\Responses\MpkResponse;

class HasManyMpkResponse extends MpkResponse
{
    protected function transform(Mpk $mpk): array
    {
        return parent::transform($mpk) + [
            'main' => $mpk->pivot->main,
            'percentage' => $mpk->pivot->percentage,
        ];
    }
}
