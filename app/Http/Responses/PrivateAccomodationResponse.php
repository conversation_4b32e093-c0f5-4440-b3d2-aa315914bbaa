<?php

namespace App\Http\Responses;

use App\PrivateAccomodation;
use Illuminate\Support\Collection;

class PrivateAccomodationResponse extends Response2
{
    protected function transform(PrivateAccomodation $accomodation)
    {
        return [
            'id'               => $accomodation->id,
            'uuid'             => $accomodation->uuid,
            'type'             => PrivateAccomodation::RELATION_NAME,
            'name'             => $accomodation->getName(),
            'location'         => LocationResponse::item($accomodation->location)->getData(),
            'amount'           => $accomodation->amount,
            'amount_currency'  => $accomodation->amountCurrency ? $accomodation->amountCurrency->code : null,
            'converted_amount_currency' => $accomodation->getConvertedAmountCurrency()->code,
            'converted_amount' => $accomodation->getConvertedAmount(),
            'arrival_at'       => $this->dateTimeFormat($accomodation->arrival_at),
            'departure_at'     => $this->dateTimeFormat($accomodation->departure_at),
            'weight'           => $accomodation->weight,
            'return_weight'    => $accomodation->return_weight,
            'accounted_amount' => $accomodation->getAccountedAmount(),
            'accounted_component_amounts' => $accomodation->getAccountedComponentAmounts(),
//            'round_trip'       => true, //same hack as in rented car
            'icon_type' => $accomodation->getType(),
            'search_uuid' => null,
            'acceptance_source' => (string)$accomodation->request_element_acceptance_source,
        ];
    }

    protected function transformDocuments(Collection $documents)
    {
        return DocumentResponse::collection($documents)->getData();
    }
}
