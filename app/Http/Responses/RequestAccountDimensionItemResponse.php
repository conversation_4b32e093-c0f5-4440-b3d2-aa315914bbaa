<?php

declare(strict_types=1);

namespace App\Http\Responses;

use Modules\Accounting\Pub\ViewObjects\RequestAccountDimensionItemViewObject;

class RequestAccountDimensionItemResponse extends Response2
{
    protected function transform(RequestAccountDimensionItemViewObject $accountDimensionItem)
    {
        return [
            'id' => $accountDimensionItem->getId(),
            'slug' => $accountDimensionItem->getSlug(),
            'request_id' => $accountDimensionItem->getRequestId(),
            'account_dimension_id' => $accountDimensionItem->getAccountDimensionId(),
            'account_dimension_item_id' => $accountDimensionItem->getAccountDimensionItemId(),
            'accountDimensionItem' => AccountDimensionItemResponse::item($accountDimensionItem->getAccountDimensionItem())->getData()
        ];
    }
}