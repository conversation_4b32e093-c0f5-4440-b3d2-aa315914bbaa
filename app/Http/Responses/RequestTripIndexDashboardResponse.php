<?php

declare(strict_types=1);

namespace App\Http\Responses;

use App\Helpers\NameTranslation;
use App\Instance;
use App\Repositories\RequestRepository;
use App\Request;
use App\Vendors\Math;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

// TODO: refactor when we will get time for this.
class RequestTripIndexDashboardResponse extends Response2Paginated
{
    /**
     * @var Request
     */
    private $request;

    /** @var RequestRepository */
    protected $repo;

    protected function init()
    {
        parent::init();

        $this->repo = resolve(RequestRepository::class);
    }

    protected function transform(\stdClass $request)
    {
        $this->request = $request;
        $currencyCode =  $request->instance_currency_code;
        $authUserId = Auth::user()->id;

        $request->documents = $this->prepareDocuments($request);
        $request->costs = $this->prepareCosts($request);
        $request->combinedTravelElements = $this->prepareRequestElements($request);

        return [
            'instance_id'               => $request->instance_id,
            'user'                      => [
                'full_name'                         => $request->user_full_name,
                'first_name'                        => $request->user_first_name,
                'last_name'                         => $request->user_last_name
            ],
            'request_acceptors_users_full_names'    => $request->request_acceptors_users_full_names,
            'settlement_acceptors_users_full_names'    => $request->settlement_acceptors_users_full_names,
            'uid'                                   => $request->number,
            'slug'                                  => $request->slug,
            'type'                                  => $request->type,
            'status'                                => $request->status,
            'name'                                  => $this->getName($request->name),
            'sent_at'                               => $this->dateTimeFormat($request->sent_at),
            'delegation'                            => (bool)$request->delegation,
            'unrealized'                            => $request->unrealized_at !== null,

            // TODO: remove camel case after Piotr changes.
            'requestElementsSumAmount'              => $request->request_element_sum_amount,
            'requestElementsAccountedSumAmount'     => $request->request_element_accounted_sum_amount,
            'requestElementsSumCurrency'            => $currencyCode,
            'requestElementsAccountedSumCurrency'   => $currencyCode,
            'travelExpensesSumAmountCurrency'       => $currencyCode,
            'travelExpensesSumAmount'               => $request->travel_expense_cache,

            'request_elements_sum_amount'               => $request->request_element_sum_amount,
            'request_elements_accounted_sum_amount'     => $request->request_element_accounted_sum_amount,
            'request_elements_sum_currency'             => $currencyCode,
            'request_elements_accounted_sum_currency'   => $currencyCode,
            'travel_expenses_sum_amount_currency'       => $currencyCode,
            'travel_expenses_sum_amount'                => $request->travel_expense_cache,

            'documents' => $request->documents ?? [],

            'combinedTravelElements' => $request->combinedTravelElements,
            'unrequestedElement'=> [
                'id'        => $request->request_unrequested_element_id,
                'type'      => $request->request_unrequested_element_type,
                'name'      => $request->request_unrequested_element_name,
                'icon_type' => $request->request_unrequested_element_icon_type,
            ],
            'costs' => $request->costs,
            'basicSummary' => is_string($request->basicSummary) === true
                ? json_decode($request->basicSummary)
                : ['requestedConvertedAmount' => ['amount' => 0], 'settledConvertedAmount' => ['amount' => 0]],
            'abilities' => [
                'settle'                        => $request->user_id === $authUserId && $request->is_after_trip == 1
            ],

            'lumpSumsDeclaration' => is_string($request->lumpSumsDeclaration) === true
                ? json_decode($request->lumpSumsDeclaration)
                : [
                    'lump_sum_settled'            => 0,
                    'lump_sum_accounted'          => 0,
                    'requested_lump_sums'         => 0,
                    'settled_lump_sums'           => 0,
                    'has_accommodation_lump_sums' => false,
                    'has_mileage_allowances'      => false,
                    'has_access_lump_sums'        => false,
                    'has_drive_lump_sums'         => false,
                ],
            'days_in_settlement'                => is_string($request->trip_ends) === true && is_string($request->status_settlement_at) === true
                                                        ? Request::daysInSettlementInternal(
                                                            $request->type,
                                                            Carbon::createFromFormat('Y-m-d H:i:s', $request->trip_ends),
                                                            Carbon::createFromFormat('Y-m-d H:i:s', $request->status_settlement_at)
                                                        )
                                                        : 0,
        ];
    }

    public function getName(?string $name = null): ?string
    {
        try {
            $decodedData = is_string($name) === true ? json_decode($name): null;
            $lang = Auth::user()->locale;

            if ($decodedData instanceof \stdClass && property_exists($decodedData, $lang)) {
                return $decodedData->{$lang};
            }
        } catch (\Throwable $exception) {
            \Log::error($exception);
        }

        return null;
    }

    protected function prepareDocuments(\stdClass $request)
    {
        $documents = is_string($request->documents) ? json_decode($request->documents) : [];

        if (is_array($documents) === true && empty($documents) === false) {
            foreach ($documents as $documentIndex => $document) {
                if (property_exists($document, 'currency') === true && is_string($document->currency)) {
                    $document->currency = json_decode($document->currency);
                }

                if (property_exists($document, 'request_elements') === true && is_string($document->request_elements)) {
                    $document->request_elements = json_decode($document->request_elements);
                }

                if (property_exists($document, 'elements') === true && is_string($document->elements)) {
                    $document->elements = json_decode($document->elements);
                    $document->converted_gross_sum_of_elements = 0;
                    $document->gross_sum_of_elements = 0;

                    foreach ($document->elements as $index => $element) {
                        if (property_exists($element, 'gross') === true && is_numeric($element->gross)) {
                            $document->converted_gross_sum_of_elements = Math::add(
                                (string)$document->converted_gross_sum_of_elements,
                                Math::multiply(
                                    (string)$element->gross,
                                    is_numeric($document->exchange_rate) ? (string)$document->exchange_rate : (string)1
                                )
                            );

                            $document->gross_sum_of_elements = Math::add(
                                (string)$document->gross_sum_of_elements,
                                (string)$element->gross
                            );
                        }
                        if (property_exists($element, 'type') === true && is_string($element->type)) {
                            $element->type = json_decode($element->type);
                            $document->elements[$index] = $element;
                            $documents[$documentIndex] = $document;
                        }
                    }
                }
            }
        }
        return $documents;
    }

    protected function prepareCosts(\stdClass $request)
    {
        $costs = is_string($request->costs) ? json_decode($request->costs) : [];

        if (is_array($costs) === true && empty($costs) === false) {
            foreach ($costs as $costIndex => $cost) {
                $cost->name = (new NameTranslation('expense-type.' . $cost->name, ['description' => $cost->description ? $cost->description : '']))->translate();
                $costs[$costIndex] = get_object_vars($cost);
            }
        }

        return $costs;
    }

    protected function prepareRequestElements(\stdClass $request)
    {
        return is_string($request->combinedTravelElements) ? json_decode($request->combinedTravelElements): [];
    }
}
