<?php

declare(strict_types=1);

namespace App\Http\Responses\Offer;

use App\Http\Responses\Response2;
use App\Offer;
use Carbon\Carbon;

class OfferResponse extends Response2
{
    protected function transform(Offer $offer): array
    {
        return [
            'offer_uuid' => $offer->offer_uuid,
            'search_uuid' => $offer->search_uuid,
            'cancel_date' => $offer->isCancelable()
                ? $offer->getCancelDate()->toDateTimeString()
                : null,
            'cancellable' => $offer->isCancelable(), // cancel_date > now + refundable
            'status' => $offer->status
        ];
    }
}
