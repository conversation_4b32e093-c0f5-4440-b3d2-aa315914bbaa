<?php

namespace App\Http\Responses;

use App\Currency;
use App\Document;
use App\Interfaces\RequestElementInterface;
use App\Offer;
use App\Provider;
use App\Repositories\DocumentRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Http\Responses\MpkResponse;
use Modules\Accounting\Pub\ViewObjects\DocumentAccountDimensionItemViewObject;
use Modules\Analytics\Priv\Entities\DocumentAccountDimensionItem;
use Modules\Analytics\Priv\Entities\Project;
use Modules\Analytics\Priv\Http\Responses\ProjectResponse;
use Modules\Common\ValueObjects\Rate;
use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateStrategyResultDto;
use Modules\ExchangeRate\Pub\Enums\DocumentExchangeRateSourceEnum;
use Modules\ExchangeRate\Pub\Enums\DocumentExchangeRateStrategyEnum;
use Modules\MyCard\Pub\Facades\MyCardDocumentResponseFacade;

class DocumentResponse extends DocumentMoneyResponse
{
    /** @var Document $document */
    protected $document;

    protected MyCardDocumentResponseFacade $myCardDocumentResponseFacade;

    protected function init()
    {
        $this->myCardDocumentResponseFacade = resolve(MyCardDocumentResponseFacade::class);
    }

    protected function transform(Document $document): array
    {
        $this->document = $document;

        $exchangeRateDto = $document->getExchangeRateDto();
        $providersSuggested = $document->getProvidersSuggested();

        $lastExchangeRate = $document->getLastExchangeRate();

        $defaultExchangeRateAccepted = $document->exchange_rate_source->equals(
            DocumentExchangeRateSourceEnum::STRATEGY()
        );

        $needToFlushCurrency = $this->needToFlushCurrency(
                $document,
                $exchangeRateDto
            ) && $document->exchange_rate_source->equals(DocumentExchangeRateSourceEnum::STRATEGY());

        $response = [
                'id' => $document->id,
                'instance_id' => $document->instance_id,
                'mpk_id' => $document->mpk_id,
                'project_id' => $document->project_id,
                'name' => $document->name,
                'type' => $document->type,
                'file_type' => $document->getFileType($document->file_name),
                'document_number' => $document->document_number,
                'corrected_document_number' => $document->corrected_document_number,
                'document_accounting_number' => $document->document_accounting_number,
                'erp_accounting_number' => $document->erp_id,
                'issue_date' => $document->issue_date ? $document->issue_date->toDateString() : null,
                'received_date' => $document->received_date ? $document->received_date->toDateString() : null,
                'accounting_date' => $document->accounting_date ? $document->accounting_date->toDateString() : null,
                'annotation' => $document->annotation,
                'ocr_status' => $document->status,
                'ocr_provider_not_found' => $document->ocrProviderNotFound(),
                'currency' => $document->currency->code ?? null,
                'converted_gross' => (float)$document->getConvertedGross(),

                'exchange_rate' => $needToFlushCurrency ? null : $document->exchange_rate,
                'exchange_rate_date' => $needToFlushCurrency ? null : ($document->exchange_rate_date instanceof Carbon ? $document->exchange_rate_date->toDateString(
                ) : null),
                'default_exchange_rate_accepted' => $needToFlushCurrency ? false : $defaultExchangeRateAccepted,
                'exchange_rate_source' => $needToFlushCurrency ? null : ($document->exchange_rate_source->equals(
                    DocumentExchangeRateSourceEnum::EMPTY()
                ) ? null : (string)$document->exchange_rate_source),
                'exchange_rate_strategy' => $needToFlushCurrency ? null : ($document->exchange_rate_strategy->equals(
                    DocumentExchangeRateStrategyEnum::EMPTY()
                ) ? null : (string)$document->exchange_rate_strategy),
                'default_exchange_rate' => $exchangeRateDto instanceof ExchangeRateStrategyResultDto ? (string)$exchangeRateDto->getRate(
                ) : '0.00',
                'default_exchange_rate_date' => $exchangeRateDto instanceof ExchangeRateStrategyResultDto ? $exchangeRateDto->getEffectiveDate(
                )->toDateString() : null,
                'default_exchange_rate_strategy' => $exchangeRateDto instanceof ExchangeRateStrategyResultDto ? ((string)$exchangeRateDto->getStrategy(
                ))::CODE : null,
                'can_edit_exchange_rate' => $exchangeRateDto instanceof ExchangeRateStrategyResultDto ? $exchangeRateDto->isEditable(
                ) : false,
                'last_exchange_rate' => [
                    'effective_date' => (string)$lastExchangeRate->getEffectiveDate(),
                    'rate' => $lastExchangeRate->getRate(),
                    'currency' => $document->currency
                ],
                'payment' => $document->payment,
                'accounting_type' => $document->accounting_type,
                'is_margin' => $document->is_margin,
                'debit_account_id' => $document->debit_account_id,
                'credit_account_id' => $document->credit_account_id,
                'accounted_at' => $document->accounted_at,
                'settled_at' => $document->settled_at,
                'title' => $document->title,
                'self_generated' => $document->self_generated,
                'readonly' => (bool)$document->readonly,
                'from_billing' => $document->isFromBilling(),
                'abilities' => [
                    'view' => $this->can('view', $document),
                    'edit' => $this->can('edit', $document),
                    'edit_basic' => $this->can('editBasic', $document),
                    'account' => $this->can('account', $document),
                    'delete' => $this->can('delete', $document),
                    'add_custom_provider' => $this->can('canAddCustomProvider', $document),
                    'sendToSettlement' => $this->can('sendToSettlement', $document),
                    'canUserEditElementExchangeRate' => $this->can('canUserEditElementExchangeRate', $document),
                ],
                'request_slug' => $document->request->slug,
                'request_elements' => $this->requestElements($document),
                'expenses_types' => $document->getExpensesTypes(),
                'ocrSuggestedProvider' => $providersSuggested,
                'erp_accounted_at' => $this->dateTimeFormat($document->erp_accounted_at),
                'vat_date' => $this->dateFormat($document->vat_date),
                'status' => $document->status,
                'slug' => $document->slug,
                'accountDimensionItems' => $document->accountDimensionItems,
            ] + parent::transform($document);

        $response = $this->withRequest($document, $response);
        $response = $this->withExpenseTypes($document, $response);
        $response = $this->rules($document, $response);
        $response = $this->withOfferUuid($document, $response);
        $response = $this->withMpks($document, $response);

        $response['_links'] = $this->myCardDocumentResponseFacade->prepareLinks($document) ?? [];

        return $response;
    }

    private function needToFlushCurrency(
        Document $document,
        ExchangeRateStrategyResultDto $exchangeRateDto
    ): bool {
        if (is_null($document->exchange_rate)) {
            return false;
        }

        return !$exchangeRateDto->getRate()->isEqual(new Rate($document->exchange_rate));
    }

    protected function requestElements(Document $document)
    {
        return $document->requestElementsPivot()->transform(function ($pivot) {
            return [
                'id' => $pivot->element_id,
                'type' => $pivot->element_type
            ];
        });
    }

    protected function withRequest(Document $document, array $response): array
    {
        if (!$this->isParamInWithParams('request')) {
            return $response;
        }

        $response['request'] = RequestResponse::item($document->request)->getData();

        return $response;
    }

    protected function withExpenseTypes(Document $document, array $response): array
    {
        if (!$this->isParamInWithParams('expenseTypes')) {
            return $response;
        }

        $response['expenseTypes'] = resolve(DocumentRepository::class)->getExpenseTypesResponse($document);

        return $response;
    }

    protected function transformElements(Collection $collection)
    {
        return DocumentElementResponse::collection($collection)->getData();
    }

    protected function transformCurrency(Currency $currency = null)
    {
        return CurrencyResponse::item($currency)->getData();
    }

    protected function transformProvider(Provider $provider = null)
    {
        if ($this->document->documentProvider) {
            $provider = (object)$this->document->documentProvider->toArray();
            $provider->id = $this->document->documentProvider->getProviderId();

            return ProviderResponse::item($provider)->getData();
        }

        if ($this->document->provider) {
            return ProviderResponse::item((object)$this->document->provider->toArray())->getData();
        }

        return null;
    }


    protected function transformAccountDimensionItems(Collection $collection)
    {
        return DocumentAccountDimensionItemResponse::collection(
            $collection->map(function (DocumentAccountDimensionItem $item) {
                return DocumentAccountDimensionItemViewObject::createFromDocumentAccountDimensionItem($item);
            })
        )->getData();
    }

    protected function transformOcrHints(Collection $collection)
    {
        return OcrHintResponse::collection($this->document->getFilteredOcrHints())->getData();
    }

    protected function transformAttachments(Collection $collection)
    {
        return DocumentAttachmentResponse::collection($collection)->getData();
    }

    protected function withOfferUuid(Document $document, array $response): array
    {
        if ($document->type === Document::TYPE_TRAVEL) {
            $requestElement = $document->requestElements()->first();

            if ($requestElement instanceof RequestElementInterface && $requestElement->offer instanceof Offer) {
                $response['offer_uuid'] = $requestElement->offer->offer_uuid;
                $response['ticket_invalidated'] = $requestElement->offer->ticket_invalidated;
            }
        }

        return $response;
    }

    protected function withMpks(Document $document, array $response): array
    {
        $response['mpks'] = HasManyMpkResponse::collection($document->hasMpks)->getData();

        return $response;
    }

    protected function transformMpk(Mpk $mpk = null)
    {
        return MpkResponse::item($mpk)->getData();
    }

    protected function transformProject(Project $project = null)
    {
        return ProjectResponse::item($project)->getData();
    }
}
