<?php

namespace App\Http\Responses;

use App\Compliance\Compliance;
use App\Offer;
use App\Repositories\OfferRepository;
use Modules\Accounting\Priv\Entities\DocumentElementType;
use App\TrainTrip;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Http\Responses\DocumentElementTypeResponse;
use Modules\TripPlanner\Pub\Facades\OfferTravelerFacade;

class TrainTripsResponse extends Response2
{
    /**
     * @var OfferRepository
     */
    protected $offerRepository;

    /**
     * @var OfferTravelerFacade
     */
    protected $offerTravelerFacade;

    public function __construct()
    {
        $this->offerTravelerFacade = resolve(OfferTravelerFacade::class);
        $this->offerRepository = resolve(OfferRepository::class);
        parent::__construct();
    }

    protected function transform(TrainTrip $trainTrip)
    {
        $response = [
            'id' => $trainTrip->id,
            'uuid' => $trainTrip->uuid,
            'type' => TrainTrip::RELATION_NAME,
            'name' => $trainTrip->getName(),
            'departure_at' => $this->dateTimeFormat($trainTrip->departure_at),
            'arrival_at' => $this->dateTimeFormat($trainTrip->arrival_at),
            'return_at' => $this->dateTimeFormat($trainTrip->return_at),
            'round_trip' => (bool)$trainTrip->round_trip,
            'destination_location' => LocationResponse::item($trainTrip->destinationLocation)->getData(),
            'departure_location' => LocationResponse::item($trainTrip->departureLocation)->getData(),
            'amount' => $trainTrip->amount,
            'amount_currency' => $trainTrip->amountCurrency ? $trainTrip->amountCurrency->code : null,
            'calculated_amount_currency' => $trainTrip->getConvertedAmountCurrency()->code,
            'converted_amount' => $trainTrip->getConvertedAmount(),
            'weight' => $trainTrip->weight,
            'return_weight' => $trainTrip->return_weight,
            'accounted_amount' => $trainTrip->getAccountedAmount(),
            'accounted_component_amounts' => $trainTrip->getAccountedComponentAmounts(),
            'icon_type' => $trainTrip->getType(),
            'searcher_disabled' => (bool)$trainTrip->searcher_disabled,
            'search_uuid' => $trainTrip->offer !== null ? $trainTrip->offer->search_uuid : null,
            'travelers' => [],
            'offer_uuid' => $trainTrip->offer !== null && $trainTrip->offer->offer_uuid ? $trainTrip->offer->offer_uuid : null,
            'target_real_departure_at' => $trainTrip->offer !== null && empty($trainTrip->offer->full_offer) === false && isset($trainTrip->offer->full_offer[0]) === true ? optional((object)$trainTrip->offer->full_offer[0])->attributes['departureDate'] : null,
            'target_real_arrival_at' => $trainTrip->offer !== null && empty($trainTrip->offer->full_offer) === false && isset($trainTrip->offer->full_offer[0]) === true ? optional((object)$trainTrip->offer->full_offer[0])->attributes['arrivalDate'] : null,
            'acceptance_source' => (string)$trainTrip->request_element_acceptance_source
        ];



        if(!empty($trainTrip->search_uuid)) {
            /** @var Offer $offer */
            $offer = $this->offerRepository->findBySearchUuIdAndRequestId($trainTrip->search_uuid, $trainTrip->request_id);

            $response['travelers'] = [];
            if($offer !== null) {
                $response['travelers'] = $this->offerTravelerFacade->getRequestTravelersForOffer($offer)->getData();
            }
        }

        $this->withComplianceRules($trainTrip);
        $response = $this->rules($trainTrip, $response);

        return $response;
    }

    protected function withComplianceRules(&$trainTrip)
    {
        $rules =  Compliance::create($trainTrip->request->instance)->trainTripRetrieved($trainTrip, $trainTrip->request->user);
        $trainTrip->rules = $rules->toArray();
    }

    protected function transformDocuments(Collection $documents)
    {
        return DocumentResponse::collection($documents)->getData();
    }

    protected function transformSuggestedElementType(DocumentElementType $type)
    {
        return DocumentElementTypeResponse::item($type)->getData();
    }
}
