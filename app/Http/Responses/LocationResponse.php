<?php


namespace App\Http\Responses;


use App\Country;
use App\Location;

class LocationResponse extends Response2
{
    protected function transform(Location $location)
    {
        return [
          'country_id' => $location->country instanceof Country ? $location->country->id : null,
          'country' => $location->country,
          'country_code' => $location->country_code,
          'city' => $location->city,
          'province' => $location->province,
          'address' => $location->address,
          'long' => $location->long,
          'lat' => $location->lat,
          'name' => $location->name,
          'formatted_address' => $location->formatted_address,
          'additional_data' => $location->additional_data,
        ];
    }
}