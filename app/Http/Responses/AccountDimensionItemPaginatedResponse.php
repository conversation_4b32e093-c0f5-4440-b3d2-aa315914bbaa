<?php

declare(strict_types=1);

namespace App\Http\Responses;

use App\Enum\ActiveEnum;

class AccountDimensionItemPaginatedResponse extends Response2Paginated
{
    protected function transform(\stdClass $accountDimensionItem): array
    {
        return [
            'id' => $accountDimensionItem->id,
            'code' => $accountDimensionItem->code,
            'name' => $accountDimensionItem->name,
            'is_active' => new ActiveEnum($accountDimensionItem->is_active),
            'message' => $accountDimensionItem->message
        ];
    }
}
