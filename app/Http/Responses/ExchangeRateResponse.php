<?php

declare(strict_types=1);

namespace App\Http\Responses;

use Modules\ExchangeRate\Pub\Dtos\Output\ExchangeRateStrategyResultDto;

class ExchangeRateResponse extends Response2
{

    protected function transform(ExchangeRateStrategyResultDto $exchangeRateDto)
    {
        return [
            'effective_date'    => (string)$exchangeRateDto->getEffectiveDate(),
            'rate'              => (string)$exchangeRateDto->getRate(),
            'currency'          => ['code' => (string)$exchangeRateDto->getCurrencyCode()]
        ];
    }
}
