<?php

declare(strict_types=1);

namespace App\Http\Responses;

use App\User;

class UserForSelectResponse extends Response2Paginated
{
    protected function transform(User $user)
    {
        return [
            'id'          => $user->id,
            'company_id' => $user->company_id,
            'first_name'  => $user->first_name,
            'last_name'   => $user->last_name,
            'full_name'   => $user->full_name,
            'slug'        => $user->slug,
            'email'       => $user->email,
            'avatar'      => $user->avatar ? $user->avatar : (string)config('vaterval.default_avatar'),
            'company'     => $user->company
        ];
    }
}
