<?php

declare(strict_types=1);

namespace App\Http\Responses;

use App\Instance;
use App\Repositories\RequestRepository;
use App\Request;
use Illuminate\Support\Facades\Auth;

class RequestTripIndexResponse extends Response2Paginated
{
    /**
     * @var Request
     */
    private $request;

    /** @var RequestRepository */
    protected $repo;

    protected function init()
    {
        parent::init();

        $this->repo = resolve(RequestRepository::class);
    }

    protected function transform(\stdClass $request)
    {
        $this->request = $request;
        $currencyCode =  $request->instance_currency_code;

        return [
            'instance_id'               => $request->instance_id,
            'user'                      => [
                'full_name'                         => $request->user_full_name,
                'first_name'                        => $request->user_first_name,
                'last_name'                         => $request->user_last_name
            ],
            'request_acceptors_users_full_names'    => $request->request_acceptors_users_full_names,
            'settlement_acceptors_users_full_names'    => $request->settlement_acceptors_users_full_names,
            'uid'                                   => $request->number,
            'slug'                                  => $request->slug,
            'type'                                  => $request->type,
            'status'                                => $request->status,
            'name'                                  => $this->getName($request->name),
            'sent_at'                               => $this->dateTimeFormat($request->sent_at),

            // TODO: remove camel case after Piotr changes.
            'requestElementsSumAmount'              => $request->request_element_sum_amount,
            'requestElementsAccountedSumAmount'     => $request->request_element_accounted_sum_amount,
            'requestElementsSumCurrency'            => $currencyCode,
            'requestElementsAccountedSumCurrency'   => $currencyCode,
            'travelExpensesSumAmountCurrency'       => $currencyCode,
            'travelExpensesSumAmount'               => $request->travel_expense_cache,

            'request_elements_sum_amount'               => $request->request_element_sum_amount,
            'request_elements_accounted_sum_amount'     => $request->request_element_accounted_sum_amount,
            'request_elements_sum_currency'             => $currencyCode,
            'request_elements_accounted_sum_currency'   => $currencyCode,
            'travel_expenses_sum_amount_currency'       => $currencyCode,
            'travel_expenses_sum_amount'                => $request->travel_expense_cache,
        ];
    }

    public function getName(?string $name = null): ?string
    {
        try {
            $decodedData = is_string($name) === true ? json_decode($name): null;
            $lang = Auth::user()->locale;

            if ($decodedData instanceof \stdClass && property_exists($decodedData, $lang)) {
                return $decodedData->{$lang};
            }
        } catch (\Throwable $exception) {
            \Log::error($exception);
        }

        return null;
    }
}
