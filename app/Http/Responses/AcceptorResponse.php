<?php

namespace App\Http\Responses;

use App\Comment;
use App\Policies\UserPolicy;
use App\Request;
use App\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class AcceptorResponse extends UserBasicResponse
{
    /**
     * @var Request|null
     */
    private $parentRequest;

    private $isSettlementAcceptor;

    static function collection($elements = null, Request $parentRequest = null, bool $isSettlementAcceptor = false)
    {
        $response = static::create();

        $response->isSettlementAcceptor = $isSettlementAcceptor;

        $response->parentRequest = $parentRequest;

        if($elements instanceof LengthAwarePaginator) {
            $response->data = $elements->items();

            // Usuwamy ifnormacjeo danych
            $additional = $elements->toArray();
            unset($additional['data']);

            $response->additional([
                'paginate' => $additional,
            ]);
        } else {
            $response->data = $elements;
        }

        $response->process();

        return $response;
    }

    protected function transform(User $user)
    {
        $response = parent::transform($user);

        $response['abilities'] = [
            'detachAcceptor' => $this->checkDetachAcceptor($user),
            'detachSettlementAcceptor' => $this->checkDetachSettlementAcceptor($user),
        ];

        $response['accepted'] = $user->pivot->accepted;
        $response['default'] = $user->pivot->default;
        $response['acceptance_date'] = $user->pivot->acceptance_date;
        $response['added_by'] = $user->pivot->added_by;
        $response['comment'] = $this->getAcceptorComment($user);

        return $response;
    }

    protected function checkDetachAcceptor(User $user): bool
    {
        return $this->parentRequest instanceof Request ? UserPolicy::detachAcceptor(\Auth::user(), $user, $this->parentRequest, $user->pivot->default) : false;
    }

    protected function checkDetachSettlementAcceptor(User $user): bool
    {
        return $this->parentRequest instanceof Request ? UserPolicy::detachSettlementAcceptor(\Auth::user(), $user, $this->parentRequest, $user->pivot->default) : false;
    }

    protected function getAcceptorComment(User $user)
    {
        $comment = Comment::find($user->pivot->comment_id);

        $responseComment = $comment ? CommentResponse::create()->prepareContent($comment) : null;

        if(!$responseComment && !$this->isSettlementAcceptor && !$this->checkDetachAcceptor($user)){
            $responseComment = trans('request-comment.you-cannot-delete-approver');
        }

        if(!$responseComment && $this->isSettlementAcceptor && !$this->checkDetachSettlementAcceptor($user)){
            $responseComment = trans('request-comment.you-cannot-delete-settlement-approver');
        }

        return $responseComment;
    }
}
