<?php

namespace App\Http\Responses;

use App\Compliance\Compliance;
use App\Cost;
use Modules\Accounting\Priv\Entities\DocumentElementType;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Http\Responses\DocumentElementTypeResponse;

class CostResponse extends Response2
{
    protected function transform(Cost $cost)
    {
        $response =  [
            'id' => $cost->id,
            'type' => Cost::RELATION_NAME,
            'name' => $cost->getName(),
            'cost_type' => DocumentElementTypeResponse::item($cost->elementType)->getData(),
            'description' => $cost->description,
            'amount' => $cost->amount,
            'amount_currency' => $cost->amountCurrency ? $cost->amountCurrency->code : null,
            'accounted_amount' => $cost->getAccountedAmount(),
            'converted_amount' => $cost->getConvertedAmount(),
            'accounted_component_amounts' => $cost->getAccountedComponentAmounts(),
            'icon_type' => $cost->getType(),
        ];

        $this->withComplianceRules($cost);
        $response = $this->rules($cost, $response);

        return $response;
    }

    protected function transformDocuments(Collection $documents)
    {
        return DocumentResponse::collection($documents)->getData();
    }

    protected function transformSuggestedElementType(DocumentElementType $type)
    {
        return DocumentElementTypeResponse::item($type)->getData();
    }

    protected function withComplianceRules(&$cost)
    {
        $rules =  Compliance::create($cost->request->instance)->costRetrieved($cost, $cost->request->user);
        $cost->rules = $rules->toArray();
    }
}
