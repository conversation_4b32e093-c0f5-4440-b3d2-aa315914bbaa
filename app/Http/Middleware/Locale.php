<?php

namespace App\Http\Middleware;


use App\Http\Requests\CurrentInstanceRequest;
use App\Translation;

class Locale
{
    public function handle(CurrentInstanceRequest $request, \Closure $next)
    {
        $trans = (new Translation());

        $locale = $request->has('lang')
            ? $trans->getLocaleFromShortName($request->get('lang'))
            : $trans->getLocaleFromDB($request->getCurrentInstance());

        \App::setLocale($locale);

        return $next($request);
    }

}
