<?php

declare(strict_types=1);

namespace App\Http\Middleware\Visibility;

use App\Http\Responses\Auth\ForbiddenResponse;
use App\User;

class ReportsVisibility
{
    const FINANCIAL_DASHBOARD_DATA = 'financialDashboardData';

    public function handle($request, \Closure $next) {
        /** @var User $user */
        $user = \Auth::user();
        
        if ($request->route()->getActionMethod() === self::FINANCIAL_DASHBOARD_DATA && $user->isRegular() === true) {
            return ForbiddenResponse::get()->toResponse();
        }

        return $next($request);
    }
}
