<?php


namespace App\Http\Middleware;


use App\Http\Responses\Auth\UnauthorizedResponse;
use App\User;
use Illuminate\Http\Request;
use Lcobucci\JWT\Parser;

class AuthUserByClient
{
    public function handle(Request $request, \Closure $next) {

        $bearerToken=$request->bearerToken();
        $tokenId= (new Parser())->parse($bearerToken)->getHeader('jti');
        $client = \Laravel\Passport\Token::find($tokenId)->client;
        $user = User::find($client->user_id ?? null);
//dd($user);
        if(!$user || $user->isBillingUser() === false) {
            return new UnauthorizedResponse();
        }

        \Auth::setUser($user);

        return $next($request);
    }
}