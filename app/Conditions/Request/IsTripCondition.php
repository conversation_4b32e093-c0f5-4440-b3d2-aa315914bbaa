<?php

declare(strict_types=1);

namespace App\Conditions\Request;

use App\Request;
use Modules\DecisionMaker\Pub\DataSets\DataSetInterface;

class IsTripCondition extends AbstractRequestCondition
{
    public const CODE = 'is_trip';

    public function check(DataSetInterface $data): bool
    {
        $request = $this->extractRequest($data);

        return $request->type === Request::TYPE_TRIP;
    }
}