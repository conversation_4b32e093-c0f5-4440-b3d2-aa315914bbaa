<?php

declare(strict_types=1);

namespace App\Conditions\Request;

use App\Request;
use App\Vendors\Math;
use Modules\DecisionMaker\Pub\DataSets\DataSetInterface;
use Modules\DecisionMaker\Pub\Interfaces\ConditionInterface;

class AccommodationTotalAmountGreaterThanCondition implements ConditionInterface
{
    public const CODE = 'accommodation_total_amount_greater_than';

    protected string $limit;
    private bool $limitPerNight;

    public function __construct(string $limit, bool $limitPerNight = true)
    {
        $this->limit = $limit;
        $this->limitPerNight = $limitPerNight;
    }

    public function check(DataSetInterface $data): bool
    {
        /** @var Request $request */
        $request = $data->get('request');

        $accommodations = $request->accomodations;

        if ($accommodations->count() <= 0) {
            return false;
        }

        $amount = Math::add('0', '0');
        foreach ($accommodations as $accommodation) {
            $accommodationAmount = $this->limitPerNight ? $accommodation->getConvertedAmountPerNight(
            ) : $accommodation->amount;

            $amount = Math::add((string)$accommodationAmount, $amount);
        }

        if (Math::leftIsGreeterThanRight($amount, $this->limit)) {
            return true;
        }

        return false;
    }
}
