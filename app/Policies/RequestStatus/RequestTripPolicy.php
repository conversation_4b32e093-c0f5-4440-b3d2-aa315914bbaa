<?php


namespace App\Policies\RequestStatus;


use App\Interfaces\RequestStatusPolicyInterface;
use App\Permission;
use App\Request;
use App\Traits\Policies\CanShowUnrequestedDocumentsTrait;
use App\Traits\Policies\DirectSubordinateRequestTrait;
use App\Traits\Policies\RequestDocumentPolicyTrait;
use App\Traits\Policies\RequestInstallmentsEditableTrait;
use App\Traits\Policies\RequestPolicyTrait;
use App\User;
use Carbon\Carbon;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Pub\Facades\FeatureSwitcherFacade;

class RequestTripPolicy implements RequestStatusPolicyInterface
{
    use RequestPolicyTrait;
    use RequestInstallmentsEditableTrait;
    use CanShowUnrequestedDocumentsTrait;
    use DirectSubordinateRequestTrait;
    use RequestDocumentPolicyTrait;

    /**
     * @inheritdoc
     */
    public function view(User $user, Request $request): bool
    {
        if ($this->isOwner($user, $request)) {
            return true;
        }

        if ($this->isAcceptor($user, $request)) {
            return true;
        }

        if ($this->isAccountant($user, $request)) {
            return true;
        }

        if($user->hasAbility(Permission::MULTI_INSTANCE_VIEW)) {
            return true;
        }

        if($user->isAgent()) {
            return true;
        }
        if ($this->isDirectSubordinateRequest($user, $request) === true) {
            return true;
        }


        return false;
    }

    /**
     * @inheritdoc
     */
    public function edit(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function delete(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function attachAcceptor(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function detachAcceptor(User $user, User $acceptor, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function cancel(User $user, Request $request): bool
    {
        if($this->requestHasAccountingDocuments($request) || $this->requestHasOfferBooked($request)) {
            return false;
        }

        if ($this->isOwner($user, $request) && $request->countableDocuments->isEmpty()) {
            return true;
        }

        if ($user->isAgent() && $this->isCreator($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function sendToAcceptance(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function accept(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function returnToImprovement(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function reject(User $user, Request $request): bool
    {
        return false;
    }


    /**
     * @inheritdoc
     */
    public function settle(User $user, Request $request): bool
    {
        if ($this->isOwner($user, $request)) {
            return true;
        }
        return false;
    }

    public function update(User $user, Request $request): bool
    {
        if ($this->isOwner($user, $request)) {
            return true;
        }

        if ($user->isAgent() === true
            && $this->isCreator($user, $request) === true
            && $this->isRequestFromSameInstanceAsUser($user, $request) === true
        ) {
            return true;
        }

        return false;

    }

    /**
     * @inheritdoc
     */
    public function editDocuments(User $user, Request $request): bool
    {
        if ($this->isOwner($user, $request)) {
            return true;
        }

	    if ($user->isAgent()) {
		    return true;
	    }

        if($this->isBillingUser($user)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function accountDocuments(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function sendToSettlement(User $user, Request $request): bool
    {
        if($this->isOwner($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function sendToAcceptanceOfSettlement(User $user, Request $request): bool
    {
        return false;
    }


    /**
     * @inheritdoc
     */
    public function attachSettlementAcceptor(User $user, Request $request): bool
    {
        if ($this->isOwner($user, $request)) {
            return true;
        }

        if ($this->isAcceptor($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function detachSettlementAcceptor(User $user, User $acceptor, Request $request): bool
    {
        if($this->isOwner($user, $request)) {
            return true;
        }

        return $request->settlementAcceptors()
            ->wherePivot('accepted', Request::ACCEPTOR_STATUS_PENDING)
            ->wherePivot('user_id', $acceptor->id)
            ->wherePivot('added_by', $user->id)
            ->get()
            ->isNotEmpty();

    }

    /**
     * @inheritdoc
     */
    public function acceptSettlement(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function returnToSettlementImprovement(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function cancelAssignmentToAccountant(User $user, Request $request): bool
    {
        return self::returnToSettlementImprovement($user, $request);
    }

    /**
     * @inheritdoc
     */
    public function assignAccountant(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function account(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function sendToERP(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function viewSettlementSummary(User $user, Request $request): bool
    {
        return $this->sendToAcceptanceOfSettlement($user, $request);
    }

    /**
     * Set installment paid date nad installment status
     * @param User $user
     * @param Request $request
     * @return bool
     */
    public function accountantEditInstallments(User $user, Request $request): bool
    {
        if($this->isAccountant($user, $request)) {
            return true;
        }

        return false;
    }


    /**
     * @inheritdoc
     */
    public function searchOffers(User $user, Request $request): bool
    {
        if($this->isOwner($user, $request)) {
            return true;
        }

        if($user->isAgent()) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function bookOffers(User $user, Request $request): bool
    {
        if($this->isOwner($user, $request)) {
            return true;
        }

        if($user->isAgent()) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function setAsUnrealized(User $user, Request $request): bool
    {
        if($this->isOwner($user, $request)) {
            return true;
        }

        return false;
    }

    public function tripDidNotStarted(User $user, Request $request): bool
    {
        if($this->isOwner($user, $request) && $request->isTripStartLessThanNow() === true) {
            return true;
        }

        return false;
    }

    public function canEditPurpose(User $user, Request $request): bool
    {
        if($this->isOwner($user, $request)) {
            return true;
        }

        if ($user->isAgent() === true
            && $this->isCreator($user, $request) === true
            && $this->isRequestFromSameInstanceAsUser($user, $request) === true
        ) {
            return true;
        }

        return false;
    }

    public function addDocuments(User $user, Request $request): bool
    {
        return $this->editDocuments($user, $request);
    }

    public function canEditBasicSection(User $user, Request $request): bool
    {
        if($this->isOwner($user, $request)) {
            return true;
        }

        return false;
    }

    public function addUnrequestedReservation(User $user, Request $request): bool
    {
        $isEnabledForCompany = resolve(FeatureSwitcherFacade::class)->isEnabledForCompany(
            FeatureEnum::FEATURE_ADDITIONAL_RESERVATION(),
            $request->company
        );
        $isBeforeTripEnds = !Carbon::now()->isAfter($request->trip_ends->endOfDay());
        $isOwnerOrCreator = $this->isOwner($user, $request) === true || $this->isCreator($user, $request);
        $isTripRequest = $request->isTrip();

        return $isEnabledForCompany && $isOwnerOrCreator && $isBeforeTripEnds && $isTripRequest;
    }

    public function returnToDecreeWhenTransferError(User $user, Request $request): bool
    {
        return false;
    }
}
