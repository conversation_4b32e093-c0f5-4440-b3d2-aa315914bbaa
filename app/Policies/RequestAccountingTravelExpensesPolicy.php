<?php

namespace App\Policies;

use App\RequestAccountingTravelExpenses;
use App\Traits\Policies\SuperAdminCanDoAnything;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RequestAccountingTravelExpensesPolicy
{
    use HandlesAuthorization;
    use SuperAdminCanDoAnything;

    public function view(User $user, $travelElement)
    {
        return true;
    }

    public function create(User $user)
    {
        return true;
    }

    public function createReal(User $user, RequestAccountingTravelExpenses $expenses)
    {
        if(!$expenses->request->isDelegation()) {
            return false;
        }

        return true;
    }

    public function update(User $user, RequestAccountingTravelExpenses $expenses)
    {
        if(!$expenses->request->isDelegation()) {
            return false;
        }
        return true;
    }

    public function edit(User $user, RequestAccountingTravelExpenses $expenses)
    {
        return $this->update($user, $expenses);
    }

    public function delete(User $user, RequestAccountingTravelExpenses $expenses)
    {
        return true;
    }
}
