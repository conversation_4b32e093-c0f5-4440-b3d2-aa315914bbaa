<?php

namespace App\Policies;

use App\Group;
use App\Permission;
use App\Traits\Policies\SuperAdminCanDoAnything;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class GroupPolicy
{
    use HandlesAuthorization;
    use SuperAdminCanDoAnything;

    public function view(User $user, Group $group)
    {
        return
            $this->isTheGroupMember($user, $group) ||
            $user->hasAbility(Permission::MULTI_INSTANCE_VIEW) ||
            $user->hasAbility(Permission::INTEGRATION_API_ACCESS) ||
            $user->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_USERS);
    }

    public function create(User $user)
    {
        return $user->isSuperAdmin();
    }

    public function update(User $user, Group $group)
    {
        return $user->isSuperAdmin();
    }

    public function delete(User $user, Group $group)
    {
        return $user->isSuperAdmin();
    }

    //helpers
    private function isTheGroupMember(User $user, Group $group)
    {
        return $group->users()->where('users.id', $user->id)->exists();
    }
}
