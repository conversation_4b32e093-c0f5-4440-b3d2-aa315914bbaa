<?php

namespace App\Policies\Csp;

use App\Http\Requests\CurrentInstanceRequest;
use Spatie\Csp\Directive;
use Spatie\Csp\Keyword;
use Spatie\Csp\Policies\Policy;

class CustomCspPolicy extends Policy
{
    public function configure()
    {
        /** @var CurrentInstanceRequest $request */
        $request = request();

        $host = $request->getHost();

        $this->addDirective(Directive::SCRIPT, Keyword::SELF);
        $this->addDirective(Directive::SCRIPT, 'cdnjs.cloudflare.com');
        $this->addDirective(Directive::SCRIPT, 'documentcloud.adobe.com');
        // LogRocket
        $this->addDirective(Directive::SCRIPT, 'cdn.logrocket.io');
        $this->addDirective(Directive::SCRIPT, 'cdn.lr-in.com');
        $this->addDirective(Directive::SCRIPT, 'cdn.lr-ingest.io');
        $this->addDirective(Directive::SCRIPT, 'cdn.lr-in.com');
        $this->addDirective(Directive::CONNECT, '*.logrocket.io');
        $this->addDirective(Directive::CONNECT, '*.lr-ingest.io');
        $this->addDirective(Directive::CONNECT, '*.logrocket.com');
        $this->addDirective(Directive::CONNECT, '*.lr-in.com');
        $this->addDirective(Directive::CONNECT, 'sentry.mindento.com');
        $this->addDirective(Directive::CONNECT, 'viewlicense.adobe.io');
        $this->addDirective(Directive::CONNECT, 'maps.googleapis.com');

        if (env('APP_ENV') == 'local') {
            $this->addDirective(Directive::SCRIPT,  "*.localhost:3000");
            $this->addDirective(Directive::FONT, "*.localhost:3000");
        }
        $this->addDirective(Directive::SCRIPT, 'maps.googleapis.com');
        $this->addDirective(Directive::SCRIPT, Keyword::UNSAFE_EVAL);
        $this->addDirective(Directive::SCRIPT, Keyword::UNSAFE_INLINE);

        $this->addDirective(Directive::CONNECT, Keyword::SELF);
        $this->addDirective(Directive::CONNECT, "$host:*");
        $this->addDirective(Directive::CONNECT, 'ws:');

        try {
            $instance = $request->getCurrentInstance()->getSocketURL();
        } catch (\Exception $e) {
            $instance = null;
        }

        if ($instance) {
            $this->addDirective(Directive::CONNECT, $instance);
        }

        $this->addDirective(Directive::WORKER, Keyword::SELF);
        $this->addDirective(Directive::WORKER, 'blob:');

        $this->addDirective(Directive::STYLE, Keyword::SELF);
        $this->addDirective(Directive::STYLE, Keyword::SELF);
        $this->addDirective(Directive::STYLE, Keyword::UNSAFE_INLINE);
        $this->addDirective(Directive::STYLE, 'fonts.googleapis.com');

        $this->addDirective(Directive::BASE, Keyword::SELF);
        $this->addDirective(Directive::FORM_ACTION, Keyword::SELF);
        $this->addDirective(Directive::MEDIA, Keyword::SELF);

        $this->addDirective(Directive::FONT, Keyword::SELF);
        $this->addDirective(Directive::FONT, 'data:');
        $this->addDirective(Directive::FONT, 'fonts.gstatic.com');
    }
}
