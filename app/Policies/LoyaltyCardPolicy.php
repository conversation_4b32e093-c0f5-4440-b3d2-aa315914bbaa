<?php

namespace App\Policies;

use App\LoyaltyCard;
use App\Traits\Policies\SuperAdminCanDoAnything;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LoyaltyCardPolicy
{
    use HandlesAuthorization;
    use SuperAdminCanDoAnything;

    public function view(User $user, LoyaltyCard $loyaltyCard)
    {
        return $user->can('view', $loyaltyCard->user);
    }

    public function create(User $user)
    {
        return true;
    }

    public function createReal(User $user, LoyaltyCard $loyaltyCard)
    {
        return $user->can('update', $loyaltyCard->user);
    }

	public function update(User $user, LoyaltyCard $loyaltyCard)
	{
		return $user->can('update', $loyaltyCard->user);
	}

    public function edit(User $user, LoyaltyCard $loyaltyCard)
    {
	    return $user->can('update', $loyaltyCard->user);
    }

    public function delete(User $user, LoyaltyCard $loyaltyCard)
    {
       return $user->can('update', $loyaltyCard->user);
    }
}
