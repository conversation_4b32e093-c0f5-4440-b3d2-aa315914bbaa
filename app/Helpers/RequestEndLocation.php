<?php


namespace App\Helpers;


use App\Country;
use App\Location;
use App\Repositories\LocationRepository;
use Carbon\Carbon;

class RequestEndLocation
{
    /** @var Carbon */
    public $date;

    /** @var Location */
    public $location;

    /** @var Country */
    public $country;

    public function __construct(Carbon $date, Location $location)
    {
        $this->date = $date;
        $this->location = $location;
        $this->country = LocationRepository::getCountryFromLocation($location);
    }
}