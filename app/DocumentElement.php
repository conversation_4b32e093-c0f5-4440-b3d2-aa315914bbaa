<?php

namespace App;

use App\ERP\Contracts\ERPElement;
use App\Interfaces\DocumentElementChangeObeservableInterface;
use App\Traits\DocumentTrait;
use App\Traits\InstanceTrait;
use App\Traits\SaveQuietlyTrait;
use App\Vendors\MoneyCalculator;
use Chelout\RelationshipEvents\Concerns\HasBelongsToEvents;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Entities\DocumentElementType;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Entities\Traits\AccountingAccountTrait;
use Modules\Accounting\Priv\Entities\Traits\MpkTrait;
use Modules\Accounting\Priv\Entities\Traits\VatNumberTrait;
use Modules\Accounting\Priv\Entities\VatNumber;
use Modules\Accounting\Priv\Enum\DeductibilityEnum;
use Modules\Accounting\Priv\Services\AccountingAccountSelection\AccountingAccountSelectionService;
use Modules\Analytics\Priv\Entities\DocumentElementAccountDimensionItem;
use Modules\Analytics\Priv\Entities\Project;
use Modules\Analytics\Priv\Entities\Traits\ProjectTrait;

/**
 * App\DocumentElement
 *
 * @property int $id
 * @property int $instance_id
 * @property int|null $accounting_account_id
 * @property int|null $vat_number_id
 * @property int|null $mpk_id
 * @property int $type_id
 * @property int $document_id
 * @property int|null $request_element_id
 * @property string|null $request_element_type
 * @property string|null $gross
 * @property string|null $net
 * @property string|null $exchange_rate
 * @property bool $cost_of_earning
 * @property DeductibilityEnum $deductibility
 * @property bool $asset
 * @property string|null $description
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $project_id
 * @property-read Collection|DocumentElementAccountDimensionItem[] $accountDimensionItems
 * @property-read int|null $account_dimension_items_count
 * @property-read AccountingAccount|null $accountingAccount
 * @property-read \App\Document $document
 * @property-read \App\Instance $instance
 * @property-read Mpk|null $mpk
 * @property-read Project|null $project
 * @property-read Model|\Eloquent $requestElement
 * @property-read DocumentElementType $type
 * @property-read VatNumber|null $vatNumber
 * @method static Builder|DocumentElement newModelQuery()
 * @method static Builder|DocumentElement newQuery()
 * @method static \Illuminate\Database\Query\Builder|DocumentElement onlyTrashed()
 * @method static Builder|DocumentElement query()
 * @method static Builder|DocumentElement whereAccountingAccountId($value)
 * @method static Builder|DocumentElement whereAsset($value)
 * @method static Builder|DocumentElement whereCostOfEarning($value)
 * @method static Builder|DocumentElement whereCreatedAt($value)
 * @method static Builder|DocumentElement whereDeductibility($value)
 * @method static Builder|DocumentElement whereDeletedAt($value)
 * @method static Builder|DocumentElement whereDescription($value)
 * @method static Builder|DocumentElement whereDocumentId($value)
 * @method static Builder|DocumentElement whereExchangeRate($value)
 * @method static Builder|DocumentElement whereGross($value)
 * @method static Builder|DocumentElement whereId($value)
 * @method static Builder|DocumentElement whereInstanceId($value)
 * @method static Builder|DocumentElement whereMpkId($value)
 * @method static Builder|DocumentElement whereNet($value)
 * @method static Builder|DocumentElement whereProjectId($value)
 * @method static Builder|DocumentElement whereRequestElementId($value)
 * @method static Builder|DocumentElement whereRequestElementType($value)
 * @method static Builder|DocumentElement whereTypeId($value)
 * @method static Builder|DocumentElement whereUpdatedAt($value)
 * @method static Builder|DocumentElement whereVatNumberId($value)
 * @method static \Illuminate\Database\Query\Builder|DocumentElement withTrashed()
 * @method static \Illuminate\Database\Query\Builder|DocumentElement withoutTrashed()
 * @mixin \Eloquent
 * @property float|null $tax
 * @property float|null $reverse_tax
 * @property int|null $sibling_id
 * @property-read DocumentElement|null $deductibleSibling
 * @property-read DocumentElement|null $nondeductibleSibling
 * @method static Builder|DocumentElement whereSiblingId($value)
 * @method static Builder|DocumentElement whereTax($value)
 * @method static Builder|DocumentElement whereReverseTax($value)
 */
class DocumentElement extends Model implements DocumentElementChangeObeservableInterface, ERPElement
{
    use InstanceTrait;
    use DocumentTrait;
    use AccountingAccountTrait;
    use VatNumberTrait;
    use ProjectTrait;
    use MpkTrait;
    use SoftDeletes;
    use HasBelongsToEvents;
    use SaveQuietlyTrait;

    const REQUEST_ELEMENT_PLANE_TRIP = 'plane_trip';
    const REQUEST_ELEMENT_COMPANY_CAR_TRIP = 'company_car_trip';
    const REQUEST_ELEMENT_PRIVATE_CAR_TRIP = 'private_car_trip';
    const REQUEST_ELEMENT_RENTED_CAR_TRIP = 'rented_car_trip';
    const REQUEST_ELEMENT_TRAIN_TRIP = 'train_trip';
    const REQUEST_ELEMENT_TYPE_ACCOMODATION = 'accomodation';
    protected $fillable = [
        'type_id',
        'document_id',
        'gross',
        'net',
        'vat_number_id',
        'accounting_account_id',
        'cost_of_earning',
        'description',
        'asset',
        'mpk_id',
        'exchange_rate',
        'project_id',
        'deductibility',
    ];
    protected $touches = [
        'document',
    ];
    protected $casts = [
        'net' => 'float',
        'tax' => 'float',
        'reverse_tax' => 'float',
        'cost_of_earning' => 'boolean',
        'asset' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::belongsToAssociated(
        /**
         * @param string $relation
         * @param DocumentElement $documentElement
         * @param Document|array $document
         * @return void
         */ function (string $relation, DocumentElement $documentElement, $document) {
            if ($relation === Document::RELATION_NAME) {
                $document->recalculate();
            }
        }
        );
    }

    public function toSearchableArray()
    {
        $arr = array_only($this->toArray(), [
            'id',
            'gross',
            'net',
        ]);

        $arr['type'] = $this->type->toSearchableArray();

        return $arr;
    }

    public function accountDimensionItems()
    {
        return $this->hasMany(DocumentElementAccountDimensionItem::class);
    }

    public function type()
    {
        return $this->belongsTo(DocumentElementType::class);
    }

    public function deductibleSibling(): BelongsTo
    {
        return $this->belongsTo(DocumentElement::class, 'sibling_id')
            ->where('deductibility', DeductibilityEnum::DEDUCTIBLE()->getValue());
    }

    public function nondeductibleSibling(): HasOne
    {
        return $this->hasOne(DocumentElement::class, 'sibling_id')
            ->where('deductibility', DeductibilityEnum::NONDEDUCTIBLE()->getValue());
    }

    public function getExchangeRate()
    {
        return $this->document->getExchangeRate();
    }

    public function getGrossInDefaultInstanceCurrency(): string
    {
        if ($this->document && $this->getExchangeRate() && $this->gross) {
            return MoneyCalculator::convert(
                (string)$this->gross,
                (string)$this->getExchangeRate()
            );
        }

        return '0';
    }

    public function getNetInDefaultInstanceCurrency(): string
    {
        if ($this->document && $this->net && $this->getExchangeRate()) {
            return MoneyCalculator::convert(
                (string)$this->net,
                (string)$this->getExchangeRate()
            );
        }

        return '0';
    }

    public function getBaseNetInDefaultInstanceCurrency(): string
    {
        if ($this->document && $this->net && $this->getExchangeRate()) {
            return MoneyCalculator::convert(
                (string)$this->getOriginalNet(),
                (string)$this->getExchangeRate()
            );
        }

        return '0';
    }

    public function getBaseTaxInDefaultInstanceCurrency(): string
    {
        if ($this->document && $this->net && $this->getExchangeRate()) {
            return MoneyCalculator::taxFromGross(
                (string)$this->getGrossInDefaultInstanceCurrency(),
                (string)$this->getVatRate()
            );
        }

        return '0';
    }

    public function getNetInDocumentCurrency(): string
    {
        return $this->net ?: '0';
    }

    public function getOriginalNet(): string
    {
        if ($this->shouldApplyReverseTax()) {
            return $this->gross;
        }

        return MoneyCalculator::grossToNet(
            $this->gross ?: '0',
            $this->getVatRate() ?: '0'
        );
    }

    public function getOriginalTax(): string
    {
        return MoneyCalculator::taxFromGross(
            $this->gross ?: '0',
            $this->getVatRate() ?: '0'
        );
    }

    public function getVatInDocumentCurrency(): string
    {
        return $this->tax ?? "0";
    }

    public function getGrossInDocumentCurrency(): string
    {
        return $this->gross ?? "0";
    }

    public function getVatInDefaultInstanceCurrency(): string
    {
        return MoneyCalculator::taxFromGrossAndNet(
            $this->getGrossInDefaultInstanceCurrency(),
            $this->getNetInDefaultInstanceCurrency(),
        );
    }

    public function requestElement()
    {
        return $this->morphTo();
    }

    public function isAccountTravelExpense(): bool
    {
        return false;
    }

    public function isOwnPaidDocumentElement(): bool
    {
        return $this->document->payment === Document::PAYMENT_TYPE_OWN;
    }

    public function isServiceCardPaidDocumentElement(): bool
    {
        return $this->document->payment === Document::PAYMENT_TYPE_SERVICE_CARD;
    }

    public function getAccount(): ?AccountingAccount
    {
        // @todo check if account = null
        return $this->accountingAccount;
    }

    public function getMPK(): string
    {
        // @todo check if mpk null
        return $this->mpk->getCode();
    }

    public function getVatNumber(): ?VatNumber
    {
        return $this->vatNumber;
    }

    public function getDeductibilityAttribute(): DeductibilityEnum
    {
        return new DeductibilityEnum($this->attributes['deductibility']);
    }

    public function getAmount(): string
    {
        return $this->getGrossInDefaultInstanceCurrency();
    }

    public function isPrivateCarLumpSumElement(): bool
    {
        return false;
    }

    public function setNetAttribute($value): void
    {
        $this->attributes['net'] = $value;
        $this->recalculateTax();
    }

    public function setGrossAttribute($value): void
    {
        $this->attributes['gross'] = $value;
        $this->recalculate();
        $this->recalculateDocument();
    }

    public function setTypeId($value): void
    {
        $this->attributes['type_id'] = $value;
        $this->recalculate();
        $this->recalculateDocument();
    }

    public function setVatNumberIdAttribute($value): void
    {
        $previousVatNumberId = $this->attributes['vat_number_id'] ?? null;
        $wasChanged = $value && $previousVatNumberId && $previousVatNumberId != $value;

        if ($value == $previousVatNumberId) {
            return;
        }

        if ($wasChanged && $this->isDeductibilitySplitAlready()) {
            ($this->nondeductibleSibling ?: $this)->deductibleSibling()->dissociate();
            $this->deductibility = DeductibilityEnum::DEDUCTIBLE();
        }

        $this->attributes['vat_number_id'] = $value;
        $this->load('vatNumber');
        $this->recalculate();
        $this->recalculateDocument();
    }

    public function recalculateNet(): void
    {
        $vatRate = $this->getVatRate();

        if (null !== $this->gross && null !== $vatRate) {
            $this->attributes['net'] = MoneyCalculator::grossToNet(
                $this->getOriginalGross(),
                $vatRate,
                $this->getDeducibilityRate(),
                2,
                $this->getPercentageRate(),
                $this->getRoundMode()
            );
            if ($this->vatNumber->reverseAccountingAccount) {
                $this->attributes['net'] = $this->getOriginalGross();
            }
        } else {
            $this->attributes['net'] = null;
        }
    }

    public function getDeducibilityRate(): string
    {
        if (null === $this->vatNumber) {
            return '100';
        }

        return $this->vatNumber->getDeductibilityRate();
    }

    public function recalculateTax(): void
    {
        $this->attributes['tax'] = null;
        $this->attributes['reverse_tax'] = null;

        if (null !== $this->gross && $this->isNetDefinedAlready()) {
            if ($this->shouldApplyReverseTax()) {
                $net = $this->getGrossInDefaultInstanceCurrency();
                $gross = MoneyCalculator::netToGross(
                    $net,
                    $this->getVatRate(),
                    2,
                    $this->getRoundMode()
                );
                $this->attributes['reverse_tax'] = MoneyCalculator::taxFromGrossAndNet($gross, $net);
            } else {
                $this->attributes['tax'] = MoneyCalculator::taxFromGrossAndNet(
                    (string)$this->gross,
                    (string)$this->attributes['net']
                );
            }
        }
    }

    public function recalculate(): void
    {
        $this->recalculateNet();
        $this->recalculateTax();
    }

    public function splitDeductibility(): void
    {
        if (false === $this->shouldBeDeductibilitySplit() || $this->isDeductibilitySplitAlready()) {
            return;
        }

        $this->createNondeductibleLine();
        $this->recalculateDeducible();

        $this->recalculateDocument();
    }

    public function recalculateDeducible(): void
    {
        $this->deductibility = DeductibilityEnum::DEDUCTIBLE()->getValue();

        $this->attributes['net'] = MoneyCalculator::grossToNet(
            $this->gross,
            $this->getVatRate(),
            $this->getDeducibilityRate(),
            2,
            $this->getPercentageRate(),
            $this->getRoundMode()
        );

        $this->attributes['gross'] = MoneyCalculator::grossDeductibility(
            $this->gross,
            $this->getVatRate(),
            $this->getDeducibilityRate(),
            $this->getPercentageRate(),
            2,
            $this->getRoundMode()
        );

        $this->recalculateTax();

        $this->save();
    }

    public function isSplitLine(): bool
    {
        return !!$this->sibling_id;
    }

    public function resolveAccountingAccount(): ?AccountingAccount
    {
        $this->load('type');

        return $this->isSplitLine()
            ? $this->type->siblingAccountingAccount
            : $this->type->accountingAccount;
    }

    private function isNetDefinedAlready(): bool
    {
        return null !== ($this->attributes['net'] ?? null);
    }

    private function getVatRate(): ?string
    {
        $vatRate = (string)data_get($this, 'vatNumber.value');

        return is_numeric($vatRate) ? $vatRate : null;
    }

    private function recalculateDocument(): void
    {
        if ($this->document) {
            $reverseGross = null;
            if ($withReverseTax = isset($this->vatNumber->reverseAccountingAccount)){
                $reverseGross = MoneyCalculator::netToGross($this->document->getGrossAmountInInstanceCurrency(), $this->getVatRate(), 2);
            }
            $this->document->recalculate($withReverseTax, $reverseGross);
        }
    }

    private function createNondeductibleLine(): void
    {
        $nonDeductibleElement = $this->replicate();
        $nonDeductibleElement->unsetRelation('accountDimensionItems');
        $nonDeductibleElement->deductibleSibling()->associate($this);
        $nonDeductibleElement->deductibility = DeductibilityEnum::NONDEDUCTIBLE()->getValue();
        $nonDeductibleElement->accounting_account_id = null;

        $nonDeductibleElement->attributes['net'] = MoneyCalculator::grossToNet(
            $nonDeductibleElement->gross,
            $nonDeductibleElement->getVatRate(),
            $nonDeductibleElement->getDeducibilityRate(),
            2,
            $nonDeductibleElement->getPercentageRate(),
            $nonDeductibleElement->getRoundMode()
        );

        $nonDeductibleElement->attributes['gross'] = MoneyCalculator::grossNonDeductibility(
            $nonDeductibleElement->gross,
            $nonDeductibleElement->getVatRate(),
            $nonDeductibleElement->getDeducibilityRate(),
            $nonDeductibleElement->getPercentageRate(),
            2,
            $nonDeductibleElement->getRoundMode()
        );

        $nonDeductibleElement->recalculateTax();

        $accountingAccountSelectionService = resolve(AccountingAccountSelectionService::class);
        if (null !== $account = $accountingAccountSelectionService->forDocumentElement($nonDeductibleElement)) {
            $nonDeductibleElement->accounting_account_id = $account->id;
        }

        $this->nondeductibleSibling()->save($nonDeductibleElement);
        $this->load('nondeductibleSibling');
    }

    private function getOriginalGross(): string
    {
        if ($this->isDeductibilitySplitAlready()) {
            return MoneyCalculator::sumAndRoundHalfUp(
                $this->gross ?: '0',
                $this->nondeductibleSibling->gross ?? $this->deductibleSibling->gross ?? '0'
            );
        }

        return $this->gross ?: '0';
    }

    private function shouldBeDeductibilitySplit(): bool
    {
        return $this->vatNumber->isFullyDeductible() && $this->vatNumber->split_journal_entries_line;
    }

    private function isDeductibilitySplitAlready(): bool
    {
        return $this->deductibleSibling || $this->nondeductibleSibling;
    }

    private function isNotDeductible(): bool
    {
        return $this->deductibility->equals(DeductibilityEnum::NONDEDUCTIBLE());
    }

    private function isDeductible(): bool
    {
        return $this->deductibility->equals(DeductibilityEnum::DEDUCTIBLE());
    }

    private function getPercentageRate(): string
    {
        if (false === $this->isDeductibilitySplitAlready()) {
            return '100';
        }

        return $this->isNotDeductible() ? '25' : '75';
    }

    private function getRoundMode(): int
    {
        if (false === $this->isDeductibilitySplitAlready()) {
            return PHP_ROUND_HALF_UP;
        }

        return $this->isNotDeductible() ? PHP_ROUND_HALF_UP : PHP_ROUND_HALF_DOWN;
    }

    private function shouldApplyReverseTax(): bool
    {
        if (!$this->vatNumber){
            return false;
        }

        return null !== $this->vatNumber->reverseAccountingAccount;
    }

    public function getReverseTaxInDocumentCurrency(): string
    {
        if (!$this->reverse_tax) {
            return '0';
        }

        $exchangeRate = $this->getExchangeRate();
        if (!$this->document || !$exchangeRate) {
            return '0';
        }

        if ($exchangeRate === '1' || $this->document->currency_id === $this->document->instance->currency->id) {
            return (string)$this->reverse_tax;
        }

        $result = MoneyCalculator::divide(
            (string)$this->reverse_tax,
            $exchangeRate
        );
        
        return MoneyCalculator::round($result);
    }
}
