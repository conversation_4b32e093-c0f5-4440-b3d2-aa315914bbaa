<?php

namespace App\Providers;

use App\AccessLumpSum;
use App\Accomodation;
use App\BillingAPI\Client\Services\TravelDocumentAnnotationResolver;
use App\BillingAPI\Interfaces\UserSyncServiceInterface;
use App\BillingAPI\Services\UserSyncService;
use App\BillingAPI\Services\UserSyncServiceStub;
use App\BusTrip;
use App\CardManager\Integration\AddCardInterface;
use App\CardManager\Integration\CardmanagerAddCard;
use App\CardManager\Integration\Client;
use App\Comment;
use App\Company;
use App\CompanyCarTrip;
use App\Cost;
use App\Database\TransactionManager;
use App\Database\TransactionManagerInterface;
use App\Document;
use App\DocumentElement;
use App\ERP\Contracts\ERPNotificationService as ERPNotificationServiceInterface;
use App\ERP\Instances\Amrest\Fakes\FakeWSDLClient;
use App\ERP\Instances\Amrest\WSDLClient;
use App\ERP\Services\ERPNotificationService;
use App\FerryBoatTrip;
use App\Group;
use App\Helpers\Client\ClientInterface;
use App\IdentityProviders\Clients\IdentityProviderServiceInterface;
use App\IdentityProviders\Clients\Keycloak\Clients\KeycloakAdminApiClient;
use App\IdentityProviders\Clients\Keycloak\Services\KeycloakService;
use App\Installment;
use App\Instance;
use App\Interfaces\ClearNotificationServiceInterface;
use App\Interfaces\InternalNotificationServiceInterface;
use App\Interfaces\Monitoring\EmailNotification\EmailNotificationSettingsInterface;
use App\Interfaces\Services\Notification\NotificationService as VoucherIssuedServiceInterface;
use App\Interfaces\Services\Offers\CancelOfferService as CancelOfferServiceInterface;
use App\Interfaces\Services\Reservation\RentedCarTripReservationServiceInterface;
use App\Observers\CommentObserver;
use App\Observers\DocumentElementObserver;
use App\Observers\GroupObserver;
use App\Observers\InstallmentObserver;
use App\Observers\PermissionObserver;
use App\PassengerCarTrip;
use App\Permission;
use App\ReplacementCarTrip;
use App\Repositories\OAuth2ClientRepository;
use App\Rules\DocumentUniqueRule;
use App\Services\EmailNotification\EmailNotificationSettingsService;
use App\Interfaces\Services\PeriodicExpense\PeriodicRequestServiceInterface;
use App\Services\Notification\NotificationService;
use App\Services\Offers\CancelOfferService;
use App\Services\PeriodicExpense\PeriodicRequestService;
use App\Services\RegistryService;
use App\Services\RequestAcceptance\AcceptByTokenEmailAcceptanceStrategy;
use App\Services\RequestAcceptance\EmailAcceptanceStrategyInterface;
use App\Services\LockedRequestUuidGenerator;
use App\Services\Reservation\RentedCarTripReservationService;
use App\Services\RulesService\Rules\RuleDTOFactory;
use App\Services\RulesService\RulesServiceInterface;
use GuzzleHttp\HandlerStack;
use GuzzleLogMiddleware\LogMiddleware;
use Illuminate\Support\Collection;
use Laravel\Passport\Bridge\ClientRepository;
use Modules\Common\Entities\BlameableInterface;
use App\Interfaces\Monitoring\InstanceMonitoringCheckServiceInterface;
use App\Interfaces\Monitoring\Maintenance\MaintenanceModeServiceInterface;
use App\Interfaces\Monitoring\MonitoringServiceInterface;
use App\Interfaces\Repositories\NotificationRepositoryInterface;
use App\Interfaces\Services\AssistantService as AssistantServiceInterface;
use App\Interfaces\Services\Auth\ChangePasswordService as ChangePasswordServiceInterface;
use App\Interfaces\Services\BillingDocument\DocumentElementsResolverServiceInterface;
use App\Interfaces\Services\DeputyService as DeputyServiceInterface;
use App\Interfaces\Services\Subsummary\AllowanceSeparatorServiceInterface;
use App\Notifications\ExtendedDatabaseNotificationChannel;
use App\Observers\DocumentObserver;
use App\Interfaces\StorageServiceInterface;
use App\Observers\DocumentElementChangeObserver;
use App\Observers\RequestElementsObserver;
use App\Observers\RequestObserver;
use App\Observers\UserObserver;
use App\PlaneTrip;
use App\PrivateAccomodation;
use App\PrivateCarTrip;
use App\RentedCarTrip;
use App\Repositories\AccessLumpSumRepository;
use App\Repositories\AccomodationRepository;
use App\Repositories\AirportRepository;
use App\Repositories\BusTripRepository;
use App\Repositories\CardRepository;
use App\Repositories\CommentRepository;
use App\Repositories\CompanyCarTripRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\CostRepository;
use App\Repositories\CountryRepository;
use App\Repositories\DocumentAttachmentRepository;
use App\Repositories\DocumentElementRepository;
use App\Repositories\DocumentRepository;
use App\Repositories\ExchangeRateRepository;
use App\Repositories\FerryBoatTripRepository;
use App\Repositories\GroupRepository;
use App\Repositories\InstallmentRepository;
use App\Repositories\InstanceRepository;
use App\Repositories\LocationRepository;
use App\Repositories\LoyaltyCardRepository;
use Illuminate\Support\Facades\Event;
use App\Repositories\NotificationRepository;
use App\Repositories\OcrHintRepository;
use App\Repositories\OfferRepository;
use App\Repositories\PlaneTripRepository;
use App\Repositories\PrivateAccomodationRepository;
use App\Repositories\PrivateCarTripRepository;
use App\Repositories\RentedCarTripRepository;
use App\Repositories\RequestAccommodationDriveLumpSumRepository;
use App\Repositories\RequestBorderCrossingRepository;
use App\Repositories\RequestMileageAllowanceRepository;
use App\Repositories\RequestRepository;
use App\Repositories\RequestMealDeductionRepository;
use App\Repositories\TargetPointRepository;
use App\Repositories\TrainTripRepository;
use App\Repositories\TravelExpenseRepository;
use App\Repositories\UnrequestedElementRepository;
use App\Repositories\UserAssistantRepository;
use App\Repositories\UserLoggedAsRepository;
use App\Repositories\UserRepository;
use App\Request;
use App\RequestAccommodationDriveLumpSum;
use App\RequestBorderCrossing;
use App\RequestMileageAllowance;
use App\RequestMealDeduction;
use App\Services\Assistant\AssistantService;
use App\Services\AuditLog\CreateAuditLogService;
use App\Services\Auth\ChangePasswordService;
use App\Services\BillingDocument\DocumentElementsResolverService;
use App\Services\Deputy\DeputyService;
use App\Services\InternalNotificationService;
use App\Services\LoginAs\LogAsAssistantService;
use App\Services\LoginAs\OriginallyLoggedAsService;
use App\Services\BorderCrossing\BorderCrossingService;
use App\Services\ClearInstance\ClearInstanceService;
use App\Services\Country\CountryService;
use App\Services\Currency\CurrencyService;
use Modules\Analytics\Priv\Services\FinancialReports\FinancialDataServices\FinancialDataServiceManager;
use App\Services\Monitoring\InstanceMonitoringChecksService;
use App\Services\Monitoring\Maintenance\MaintenanceModeService;
use App\Services\Monitoring\MonitoringService;
use App\Services\Notification\ClearNotificationService;
use App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestAccommodationDriveLumpSum\AccommodationDriveLumpSumDAO;
use App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestAccommodationDriveLumpSum\AccommodationDriveLumpSumService;
use App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestMealDeductions\RequestMealDeductionsDAO;
use App\Services\RequestMealDeductionsAndLumpSumsWidget\RequestMealDeductions\RequestMealDeductionsService;
use App\Services\RulesService\RulesService;
use App\Services\Search\SearchService;
use App\Services\Storage\StorageService;
use App\Services\Subsummary\AllowanceSeparatorService;
use App\Services\Summary\SummaryService;
use App\Services\TravelExpenses\TravelExpensesService;
use App\Services\TravelExpenseSingleton\TravelExpenseSingletonService;
use App\Services\User\UserWorkLocationService;
use App\Services\UserImport\ImportService;
use App\TargetPoint;
use App\TrainTrip;
use App\UnrequestedElement;
use App\User;
use App\Validators\CarbonTimeGreaterThanField;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Notifications\Channels\DatabaseChannel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;
use Modules\Common\Http\Client\BaseHttpClient;
use Modules\Common\Http\Client\Handlers\ApplicationGuzzleLoggerHandler;
use Modules\Common\Http\Client\HttpClientInterface;
use Modules\Users\Priv\Services\NotificationService as UserNotificationService;
use Modules\Users\Priv\Strategy\Notification\NotificationOwnerAndAssistantsStrategy;
use Modules\Users\Priv\Strategy\Notification\NotificationDeputiesStrategy;
use Modules\Users\Priv\Strategy\Notification\NotificationOwnerStrategy;

class AppServiceProvider extends ServiceProvider
{
    public function boot()
    {
        setlocale(LC_TIME, config('app.locale'));
        Carbon::setLocale(config('app.locale'));

        Validator::extend('carbonDate', function ($attribute, $value, $parameters, $validator) {
            return $validator->validateDateFormat($attribute, $value, ['Y-m-d']);
        });
        Validator::extend('carbonDateTime', function ($attribute, $value, $parameters, $validator) {
            return $validator->validateDateFormat($attribute, $value, ['Y-m-d H:i:s']);
        });

        Validator::extend('carbonTime', function ($attribute, $value, $parameters, $validator) {
            return $validator->validateDateFormat($attribute, $value, ['H:i']);
        });

        Validator::extend('carbonTimeGreaterThanField', function ($attribute, $value, $parameters, $validator) {
            $rule = (new CarbonTimeGreaterThanField($attribute, $value, $parameters[0], $validator));

            return $rule->passes();
        });

        Validator::extend(
            'DocumentUniqueRule',
            function ($attribute, $value, $parameters, $validator) {
                $rule = $this->app->get(DocumentUniqueRule::class);

                return $rule->passes($value, ...$parameters);
            }
        );

        Validator::extend('positive', function ($attribute, $value, $parameters, $validator) {
            return $value > 0;
        });

        \Blade::directive('amount_format', function ($expr) {

            return "<?php echo number_format((float) $expr, 2, ',', ' ');?>";
        });

        \Blade::directive('format_img_url_size', function ($imgUrl, $width = 300, $height = 300) {
            return "<?php echo str_replace(['<width>', '<height>'], [$width, $height], $imgUrl); ?>";
        });


        \Blade::directive('exchange_rate_format', function ($expr) {

            return "<?php echo number_format((float) $expr, 4, ',', ' ');?>";
        });

        \Blade::component('components.error-placeholder', 'error');

        Request::observe(RequestObserver::class);
        Document::observe(DocumentObserver::class);
        DocumentElement::observe(DocumentElementObserver::class);
        Permission::observe(PermissionObserver::class);
        Group::observe(GroupObserver::class);
        DocumentElement::observe(DocumentElementChangeObserver::class);

        Installment::observe(RequestElementsObserver::class);
        Installment::observe(InstallmentObserver::class);
        Accomodation::observe(RequestElementsObserver::class);
        PrivateAccomodation::observe(RequestElementsObserver::class);
        CompanyCarTrip::observe(RequestElementsObserver::class);
        ReplacementCarTrip::observe(RequestElementsObserver::class);
        Cost::observe(RequestElementsObserver::class);
        PlaneTrip::observe(RequestElementsObserver::class);
        PrivateCarTrip::observe(RequestElementsObserver::class);
        RentedCarTrip::observe(RequestElementsObserver::class);
        TrainTrip::observe(RequestElementsObserver::class);
        RequestBorderCrossing::observe(RequestElementsObserver::class);
        RequestMealDeduction::observe(RequestElementsObserver::class);
        RequestAccommodationDriveLumpSum::observe(RequestElementsObserver::class);
        RequestMileageAllowance::observe(RequestElementsObserver::class);
        User::observe(UserObserver::class);
        Comment::observe(CommentObserver::class);

        Relation::morphMap([
            Accomodation::RELATION_NAME            => Accomodation::class,
            TrainTrip::RELATION_NAME               => TrainTrip::class,
            FerryBoatTrip::RELATION_NAME           => FerryBoatTrip::class,
            BusTrip::RELATION_NAME                 => BusTrip::class,
            CompanyCarTrip::RELATION_NAME          => CompanyCarTrip::class,
            Cost::RELATION_NAME                    => Cost::class,
            PlaneTrip::RELATION_NAME               => PlaneTrip::class,
            PrivateCarTrip::RELATION_NAME          => PrivateCarTrip::class,
            RentedCarTrip::RELATION_NAME           => RentedCarTrip::class,
            ReplacementCarTrip::RELATION_NAME      => ReplacementCarTrip::class,
            PassengerCarTrip::RELATION_NAME        => PassengerCarTrip::class,
            Instance::RELATION_NAME                => Instance::class,
            RequestMileageAllowance::RELATION_NAME => RequestMileageAllowance::class,
            Request::RELATION_NAME                 => Request::class,
            TargetPoint::RELATION_NAME             => TargetPoint::class,
            PrivateAccomodation::RELATION_NAME     => PrivateAccomodation::class,
            AccessLumpSum::RELATION_NAME           => AccessLumpSum::class,
            UnrequestedElement::RELATION_NAME      => UnrequestedElement::class,
            User::RELATION_NAME                    => User::class,
            Company::RELATION_NAME => Company::class,
        ]);

        Event::listen('eloquent.creating: *', function ($event, $models) {
            $authenticatedUser = \Auth::user();
            if ($authenticatedUser !== null) {
                foreach($models as $model) {
                    if($model instanceof BlameableInterface) {
                        $model->created_by = $authenticatedUser->id;
                        $model->updated_by = $authenticatedUser->id;
                    }
                }
            }
        });

        Event::listen('eloquent.updating: *', function ($event, $models) {
            $authenticatedUser = \Auth::user();
            if ($authenticatedUser !== null) {
                foreach($models as $model) {
                    if($model instanceof BlameableInterface) {
                        $model->updated_by = $authenticatedUser->id;
                    }
                }
            }
        });
    }

    public function register()
    {
        $isLocalEnvironment = ($this->app->environment() == 'local');
        if ($isLocalEnvironment) {
            $this->app->register(\Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
        }

        $this->app->bind(RequestMealDeductionsService::class, function ($app, $params) {
            $borderCrossingService = new BorderCrossingService($params[0]);
            $requestRepository     = resolve(RequestRepository::class);
            $dao                   = new RequestMealDeductionsDAO($requestRepository, $params[0]);
            $travelExpensesService = new TravelExpensesService();

            return new RequestMealDeductionsService($borderCrossingService, $requestRepository, $dao, $travelExpensesService, $params[0]);
        });

        $this->app->bind(AccommodationDriveLumpSumService::class, function ($app, $params) {
            $borderCrossingService = new BorderCrossingService($params[0]);
            $requestRepository     = resolve(RequestRepository::class);
            $dao                   = new AccommodationDriveLumpSumDAO($requestRepository, $params[0]);

            return new AccommodationDriveLumpSumService($borderCrossingService, $requestRepository, $dao, $params[0]);
        });

        $this->app->singleton(SearchService::class);
        $this->app->singleton(CountryService::class);
        $this->app->singleton(CurrencyService::class);
        $this->app->singleton(TravelExpenseSingletonService::class);
        $this->app->singleton(SummaryService::class);
        $this->app->singleton(TravelExpensesService::class);
        $this->app->singleton(ClearInstanceService::class);
        $this->app->singleton(RuleDTOFactory::class);

        $this->app->singleton(FinancialDataServiceManager::class, function ($app) {
            return new FinancialDataServiceManager($app);
        });

        $this->app->bind(UserAssistantRepository::class, function () {
            return new UserAssistantRepository(
                resolve(UserRepository::class)
            );
        });

        $this->app->singleton(OriginallyLoggedAsService::class);

        $this->app->singleton(CreateAuditLogService::class);

        // For slack notifications.
        $this->app
            ->when(InternalNotificationService::class)
            ->needs('$logger')
            ->give(Log::channel('internal_notifications'));
        $this->app
            ->when(InternalNotificationService::class)
            ->needs('$channel')
            ->give('internal_notifications');

        $this->app->bind(InternalNotificationServiceInterface::class, InternalNotificationService::class);
        $this->app->bind(MonitoringServiceInterface::class, MonitoringService::class);
        $this->app->bind(MaintenanceModeServiceInterface::class, MaintenanceModeService::class);
        $this->app->bind(InstanceMonitoringCheckServiceInterface::class, InstanceMonitoringChecksService::class);

        $this->app->bind(DatabaseChannel::class, ExtendedDatabaseNotificationChannel::class);
        $this->app->bind(ClearNotificationServiceInterface::class, ClearNotificationService::class);
        $this->app->bind(NotificationRepositoryInterface::class, NotificationRepository::class);

        $this->app->bind(DocumentElementsResolverServiceInterface::class, DocumentElementsResolverService::class);
        $this->app->bind(AllowanceSeparatorServiceInterface::class, AllowanceSeparatorService::class);

        $this->app->bind(ERPNotificationServiceInterface::class, ERPNotificationService::class);

        $this->app->bind(WSDLClient::class, function() {
            if (config('erp.amrest_sap_fake_soap_client') === 'enabled') {
                return new FakeWSDLClient(
                    (string)config('erp.amrest_sap_wsdl_path')
                );
            } else {
                return new WSDLClient(
                    (string)config('erp.amrest_sap_wsdl_path'),
                    (string)config('erp.amrest_sap_login'),
                    (string)config('erp.amrest_sap_password'),
                    (bool)config('erp.amrest_sap_log_requests')
                );
            }
        });

        // Assistants.
        $this->app->bind(AssistantServiceInterface::class, AssistantService::class);
        //Deputies.
        $this->app->bind(DeputyServiceInterface::class, DeputyService::class);
        // Auth.
        $this->app->bind(ChangePasswordServiceInterface::class, ChangePasswordService::class);
        // File storage service.
        $this->app->bind(StorageServiceInterface::class, StorageService::class);
        $this->app->bind(Guard::class, function() {
            return auth('api');
        });

        $this->app->singleton(TransactionManagerInterface::class, TransactionManager::class);

        $this->app->bind(EmailNotificationSettingsInterface::class, EmailNotificationSettingsService::class);
        $this->app->bind(PeriodicRequestServiceInterface::class, PeriodicRequestService::class);
        $this->app->bind(VoucherIssuedServiceInterface::class, NotificationService::class);
        $this->app->bind(EmailAcceptanceStrategyInterface::class, AcceptByTokenEmailAcceptanceStrategy::class);

        $this->app->when(\App\CardManager\Card\CardRepository::class)->needs(ClientInterface::class)->give(function () {
            return new Client();
        });
        $this->app->bind(EmailAcceptanceStrategyInterface::class, AcceptByTokenEmailAcceptanceStrategy::class);

        $this->app->bind(UserSyncServiceInterface::class, \App::runningUnitTests() ? UserSyncServiceStub::class : UserSyncService::class);
        $this->app
            ->when(UserSyncService::class)
            ->needs(ClientInterface::class)
            ->give(function() {
                // TODO: maybe it will needs refactor later.
                $user = \App\Group::where(['name' => 'Billing', 'visible' => false])->first()->users()->first();

                return new \App\BillingAPI\Client\Client($user);
            });

        $this->app
            ->when(UserSyncService::class)
            ->needs('$debug')
            ->give(function() {
                $debug = env('APP_DEBUG', false);

                return $debug === true || $debug === 'true';
            });

        $this->app->singleton(HttpClientInterface::class, BaseHttpClient::class);
        $this->app->when(BaseHttpClient::class)->needs(\GuzzleHttp\Client::class)->give(function() {
            $middleware = new LogMiddleware(
                Log::channel('external_systems_notifications'),
                new ApplicationGuzzleLoggerHandler(),
                false,
                true
            );

            $handlerStack = HandlerStack::create();
            $handlerStack->push($middleware);

            return new \GuzzleHttp\Client(['handler' => $handlerStack]);
        });

        $this->app
            ->when(UserSyncService::class)
            ->needs(ClientInterface::class)
            ->give(function() {
                // TODO: maybe it will needs refactor later.
                $billingGroup = \App\Group::where(['name' => 'Billing', 'visible' => false])->first();
                $users = $billingGroup !== null ? $billingGroup->users() : null;

                return new \App\BillingAPI\Client\Client($users !== null ? $users->first() : null);
            });

        $this->app->bind(ClientRepository::class, OAuth2ClientRepository::class);

        $this->app->singleton(LockedRequestUuidGenerator::class);
        $this->app->singleton(RegistryService::class);
        $this->app->bind(RulesServiceInterface::class, RulesService::class);
        $this->app->bind(CancelOfferServiceInterface::class, CancelOfferService::class);
        $this->app->bind(RentedCarTripReservationServiceInterface::class, RentedCarTripReservationService::class);
        $this->app->bind(IdentityProviderServiceInterface::class, KeycloakService::class);
        $this->app->when(KeycloakAdminApiClient::class)->needs('$isLocalEnvironment')->give($isLocalEnvironment);
        $this->app->bind(AddCardInterface::class, CardmanagerAddCard::class);

        $notificationStrategiesList = [
            NotificationDeputiesStrategy::class,
            NotificationOwnerAndAssistantsStrategy::class,
            NotificationOwnerStrategy::class
        ];

        $this->app->when($notificationStrategiesList)->needs('$config')->give(
            config('vaterval.user_notifications_settings.excluded_from_redirect')
        );

        $this->app->tag($notificationStrategiesList, 'notify_strategies');

        $this->app->bind(UserNotificationService::class, function() {
            return new UserNotificationService(...$this->app->tagged('notify_strategies'));
        });

        Collection::macro('add', function($value) {
            $this->items[] = $value;

            return $this;
        });

        if(config('app.env') !== 'local') {
            $this->app['request']->server->set('HTTPS', true);
        }
    }
}
