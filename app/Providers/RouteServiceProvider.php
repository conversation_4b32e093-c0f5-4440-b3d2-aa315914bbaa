<?php

namespace App\Providers;

use App\Http\Controllers\HomeController;
use App\Http\Middleware\InstanceMaintenanceMode;
use App\Nova\Resources\Nova;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    protected $billingNamespace = 'App\BillingAPI\Controllers';

    protected $internalNamespace = 'App\InternalAPI\Controllers';

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        Route::prefix('nova-api')
            ->middleware(config('nova.middleware', []))
            ->name('nova.')
            ->namespace('App\Nova\Http\Controllers')
            ->group(base_path('routes/nova.php'));

        $this->mapApiRoutes();

        $this->mapWebRoutes();

        if($this->app->get(Nova::class)->isNova()) {
            return;
        }

        $this->mapBillingApiRoutes();
        $this->mapInternalApiRoutes();

        $this->mapRedirectRoutes();

        $this->mapMonitoringApiRoutes();

        $placeholder = 'fallbackPlaceholder';
        Route::addRoute(
            'GET', "{{$placeholder}}", function ($route = '/') {
            if (starts_with($route, 'storage/')) {
                abort(404);
            }
            return \App::call(HomeController::class . '@index');
        })
            ->middleware([InstanceMaintenanceMode::class])
            ->where($placeholder, '.*')
            ->fallback();
        //
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware('web')
             ->namespace($this->namespace)
             ->group(base_path('routes/web.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
             ->middleware('api')
             ->namespace($this->namespace)
             ->group(base_path('routes/api.php'));
    }

    protected function mapBillingApiRoutes()
    {
        Route::prefix('billing-api')
            ->middleware('billing')
            ->namespace($this->billingNamespace)
            ->group(base_path('routes/billing.php'));
    }

    protected function mapInternalApiRoutes()
    {
        Route::prefix('internal-api')
            ->middleware('internal-api')
            ->namespace($this->internalNamespace)
            ->group(base_path('routes/internal-api.php'));
    }

    protected function mapRedirectRoutes()
    {
        Route::middleware('redirect')
            ->namespace($this->namespace)
            ->group(base_path('routes/redirect.php'));
    }

    protected function mapMonitoringApiRoutes()
    {
        Route::prefix('ops')
            ->namespace($this->billingNamespace)
            ->group(base_path('routes/monitoring.php'));
    }
}
