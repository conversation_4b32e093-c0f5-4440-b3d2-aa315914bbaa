<?php


namespace App\Exceptions;


class LocationValidationException extends ValidationException
{
    public function __construct($validator, $response = null, $errorBag = 'default')
    {
        parent::__construct($validator, $response, $errorBag);
    }

    public function errors()
    {
        $errors = array();
        collect($this->validator->errors()->messages())->each(function ($messages, $key) use (&$errors) {
            $errors['location.'.$key] = $messages;
        });

        return $errors;
    }
}