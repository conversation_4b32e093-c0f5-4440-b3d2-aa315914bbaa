<?php


namespace App\Exports;


use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;

class TaxationData implements Arrayable
{
    /** @var Carbon */
    protected $settlementDate;

    /** @var string */
    protected $requestName;

    /** @var string */
    protected $userName;

    /** @var string */
    protected $mpk;

    /** @var mixed */
    protected $documentNumber;

    /** @var mixed */
    protected $documentGross;

    /** @var mixed */
    protected $currencyCode;

    /** @var mixed */
    protected $accommodationAmount;

    /** @var mixed */
    protected $representationAmount;

    /**
     * @return Carbon
     */
    public function getSettlementDate(): Carbon
    {
        return $this->settlementDate;
    }

    /**
     * @return string
     */
    public function getRequestName(): string
    {
        return $this->requestName;
    }

    /**
     * @return string
     */
    public function getUserName(): string
    {
        return $this->userName;
    }

    /**
     * @return string
     */
    public function getMpk(): string
    {
        return $this->mpk;
    }

    /**
     * @return mixed
     */
    public function getDocumentGross()
    {
        return $this->documentGross;
    }

    /**
     * @return mixed
     */
    public function getCurrencyCode()
    {
        return $this->currencyCode;
    }

    /**
     * @return mixed
     */
    public function getAccommodationAmount()
    {
        return $this->accommodationAmount;
    }

    /**
     * @return mixed
     */
    public function getRepresentationAmount()
    {
        return $this->representationAmount;
    }

    /**
     * TaxationData constructor.
     * @param Carbon $settlementDate
     * @param string $requestName
     * @param string $userName
     * @param string $mpk
     * @param $documentNumber
     * @param $documentGross
     * @param $currencyCode
     * @param $accommodationAmount
     * @param $representationAmount
     */
    public function __construct(
        Carbon $settlementDate,
        string $requestName,
        string $userName,
        string $mpk,
        $documentNumber,
        $documentGross,
        $currencyCode,
        $accommodationAmount,
        $representationAmount)
    {
        $this->settlementDate = $settlementDate;
        $this->requestName = $requestName;
        $this->userName = $userName;
        $this->mpk = $mpk;
        $this->documentNumber = $documentNumber;
        $this->documentGross = $documentGross;
        $this->currencyCode = $currencyCode;
        $this->accommodationAmount = $accommodationAmount;
        $this->representationAmount = $representationAmount;
    }

    /**
     * @return mixed
     */
    public function getDocumentNumber()
    {
        return $this->documentNumber;
    }


    /**
     * @param array $array
     * @return TaxationData
     */
    public static function fromArray(array $array)
    {
        return new static(
            $array['settlement_date'],
            $array['request_name'],
            $array['user_name'],
            $array['mpk'],
            $array['document_number'],
            $array['document_gross'],
            $array['currency_code'],
            $array['accommodation_amount'],
            $array['representation_amount']
        );
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return [
            'settlement_date' => $this->getSettlementDate()->format('Y-m-d'),
            'request_name' => $this->getRequestName(),
            'user_name' => $this->getUserName(),
            'mpk' => $this->getMpk(),
            'document_number' => $this->getDocumentNumber(),
            'document_gross' => $this->getDocumentGross(),
            'currency_code' => $this->getCurrencyCode(),
            'accommodation_amount' => $this->getAccommodationAmount(),
            'representation_amount' => $this->getRepresentationAmount()
        ];
    }
}
