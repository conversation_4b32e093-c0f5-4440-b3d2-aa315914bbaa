<?php

namespace App\Cover;

use App\Company;
use App\Provider;
use Carbon\Carbon;

class Cover
{
    /** @var Carbon|null */
    protected $accountingDate;

    /** @var string */
    protected $documentNumber;

    /** @var Carbon */
    protected $issueDate;

    /** @var string */
    protected $documentAccountingNumber;

    /** @var string */
    protected $gross;

    /** @var string|null */
    protected $erpAccountingNumber;

    /** @var Carbon|null */
    protected $vatDate;

    /** @var Carbon|null */
    protected $erpAccountedAt;

    /** @var Carbon|null */
    protected $accountedAt;

    /**
     * Cover constructor.
     *
     * @param  string       $documentNumber
     * @param  Carbon       $issueDate
     * @param  string       $documentAccountingNumber
     * @param  string       $gross
     * @param  Carbon|null  $accountingDate
     * @param  string|null  $erpAccountingNumber
     * @param  Carbon|null  $vatDate
     * @param  Carbon|null  $erpAccountedAt
     * @param  Carbon|null  $accountedAt
     */
    public function __construct(
        string $documentNumber,
        Carbon $issueDate,
        string $documentAccountingNumber,
        string $gross,
        ?Carbon $accountingDate,
        ?string $erpAccountingNumber,
        ?Carbon $vatDate,
        ?Carbon $erpAccountedAt,
        ?Carbon $accountedAt
    ) {
        $this->accountingDate           = $accountingDate;
        $this->documentNumber           = $documentNumber;
        $this->issueDate                = $issueDate;
        $this->documentAccountingNumber = $documentAccountingNumber;
        $this->gross                    = $gross;
        $this->erpAccountingNumber      = $erpAccountingNumber;
        $this->vatDate                  = $vatDate;
        $this->erpAccountedAt           = $erpAccountedAt;
        $this->accountedAt              = $accountedAt;
    }

    public function toArray()
    {
        return [
            'accounting_date'            => $this->accountingDate,
            'document_number'            => $this->documentNumber,
            'issue_date'                 => $this->issueDate->toDateString(),
            'document_accounting_number' => $this->documentAccountingNumber,
            'gross'                      => $this->gross,
            'erp_accounting_number'      => $this->erpAccountingNumber,
            'vat_date'                   => $this->vatDate ? $this->vatDate->toDateString() : null,
            'erp_accounted_at'           => $this->erpAccountedAt ? $this->erpAccountedAt->toDateString() : null,
            'accounted_at'               => $this->accountedAt ? $this->accountedAt->toDateString() : null,
        ];
    }

}
