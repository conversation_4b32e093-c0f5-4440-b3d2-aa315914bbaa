<?php


namespace App\Jobs;


use App\Repositories\TargetPointRepository;
use App\Request;
use App\Services\RequestCourse\Location\LocationElement;
use App\Services\RequestCourse\Location\LocationElementsCollection;
use App\Services\RequestCourse\Location\PlaneTripLocationElement;
use App\Services\RequestCourse\Location\TrainTripLocationElement;
use App\Services\RequestCourse\RequestCourseService;
use App\TargetPoint;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class SuggestTripAttributesJob
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var Request */
    protected $request;

    /** @var RequestCourseService */
    protected $requestCourseService;

    /** @var TargetPointRepository */
    protected $targetPointRepository;

    /**
     * SuggestTripStartAndEndDatesJob constructor.
     * @param Request $request
     * @param RequestCourseService $requestCourseService
     */
    public function __construct(Request $request, RequestCourseService $requestCourseService)
    {
        $this->request              = $request;
        $this->requestCourseService = $requestCourseService;
        $this->targetPointRepository = resolve(TargetPointRepository::class);
    }

    public function handle()
    {
        $locations = $this->requestCourseService->setRequest($this->request)->getLocations(true)->sortAsc();
        $this->setStartAndEndDates($locations);
        SuggestBorderCrossings::dispatchNow($this->request);

        $this->request->resolveNational();
        $this->setFirstTargetPointDateForJourney($locations, $this->request);
        $this->setNextTargetPointsAccordingToAvailableLocations($locations, $this->request);


        $this->request->save();

        SuggestAccessLumpSums::dispatchNow($this->request->refresh());
        CreateMileageAllowances::dispatchNow($this->request->refresh());
    }

    protected function setFirstTargetPointDateForJourney($locations, Request $request): void
    {
        try {
            /** @var Collection $locations */
            $locations = $locations
                ->getLocations()
                ->sortBy(function (LocationElement $locationElement) {
                    return $locationElement->getWeight();
                })
                ->values();
            // It will correct first target point date only for trains.
            $firstProposition = $locations->filter(function (LocationElement $locationElement) {
                return $locationElement instanceof LocationElement && $locationElement->getLocation()['localizable_type'] !== 'target_point';
            })->first();

            $firstTargetPoint = $locations->filter(function (LocationElement $locationElement) use ($locations, $firstProposition) {
                if ($locationElement->isTarget() === true
                    && $locationElement->getLocation()['localizable_type'] === 'target_point'
                    && (
                        $firstProposition instanceof TrainTripLocationElement
                        || $firstProposition instanceof PlaneTripLocationElement
                    )
                ) {
                    return true;
                }

                return false;
            })->first();

            $arrivalInTargetPoint = collect($locations->filter(function (LocationElement $locationElement) {
                if ($locationElement instanceof TrainTripLocationElement || $locationElement instanceof PlaneTripLocationElement) {
                    return true;
                }

                return false;
            })->values())->first(); // Get FIRST - it will be arrival on first target place.

            if ($firstTargetPoint instanceof LocationElement) {
                $targetPoint = $this->targetPointRepository->findFirstTargetPointByRequestCountryAndDate(
                    $this->request->id,
                    $firstTargetPoint->getLocation()['country_code'],
                    $firstTargetPoint->getDate()
                );

                if ($targetPoint instanceof TargetPoint
                    && $request->isNationalTrip() === true
                    && (
                        $arrivalInTargetPoint instanceof TrainTripLocationElement
                        || $arrivalInTargetPoint instanceof PlaneTripLocationElement
                    )
                ) {
                    $targetPoint->date = $arrivalInTargetPoint->getArrivalDate()->toDateTimeString();
                    $targetPoint->save();
                }
            }
        } catch (\Throwable $exception) {
            Log::error($exception);
        }
    }

    protected function setNextTargetPointsAccordingToAvailableLocations($locations, Request $request): void
    {
        try {
            /** @var Collection $locations */
            $locations = $locations
                ->getLocations()
                ->sortBy(function (LocationElement $locationElement) {
                    return $locationElement->getWeight();
                })
                ->values();
            $targetPointPointer = 0;
            $locations->each(function (LocationElement $locationElement, $key) use (&$locations, &$targetPointPointer, $request) {
                $previous = $locations->get($key - 1);

                // Only for "tickets"m target point is destination and we need to set arrival date
                // on target point from previous "ticket" if available.
                if (($previous instanceof TrainTripLocationElement
                        || $previous instanceof PlaneTripLocationElement
                    ) && $locationElement->getLocation()['localizable_type'] === 'target_point'
                ) {
                    if ($targetPointPointer > 0) {
                        $targetPoint = TargetPoint::select('target_points.*')
                            ->from('target_points')
                            ->leftJoin('countries', function ($join) {
                                $join->on('countries.id', '=', 'target_points.country_id');
                            })
                            ->where([
                                ['target_points.request_id', '=', $this->request->id],
                                ['countries.country_code', '=', $locationElement->getLocation()['country_code']],
                            ])
                            ->orderBy('id', 'asc')
                            ->offset($targetPointPointer)
                            ->first();

                        if ($targetPoint instanceof TargetPoint && $request->isNationalTrip() === true) {
                            $targetPoint->date = $previous->getArrivalDate()->toDateTimeString();
                            $targetPoint->save();
                        }
                    }

                    $targetPointPointer++;
                }
            });
        } catch (\Throwable $exception) {
            Log::error($exception);
        }
    }

    protected function setStartAndEndDates(LocationElementsCollection $locations)
    {
        $locations = $locations->getLocations();
        if($locations->count() > 0) {
            $tripEndDate = $locations->last();
            $tripStartDate = $this->findFirstTransportationInLocations($locations);

            $this->setTripEnd($tripEndDate);
            $this->setTripStart($tripStartDate);
        }
    }

    protected function findFirstTransportationInLocations(Collection $locations): LocationElement
    {
        $availableTransportationLocations = $locations->filter(function (LocationElement $locationElement) {
            return $locationElement instanceof TrainTripLocationElement
                || $locationElement instanceof PlaneTripLocationElement;
        });

        if ($availableTransportationLocations->count() > 0 ) {
            /** @var LocationElement $firstTransportation */
            $firstTransportation = $availableTransportationLocations->first();

            if ($firstTransportation->getDate()->day === $locations->first()->getDate()->day) {
                return $firstTransportation;
            }
        }

        return $locations->first();
    }

    protected function setTripStart(LocationElement $firstTripLocation): void
    {
        if($firstTripLocation
            && (
                $this->request->getOriginal('trip_starts') === null
                || $firstTripLocation instanceof TrainTripLocationElement
                || $firstTripLocation instanceof PlaneTripLocationElement
            )
        ) {
            if ($firstTripLocation instanceof PlaneTripLocationElement) {
                $this->request->trip_starts = $firstTripLocation->getDepartureDate()->copy();
                $this->request->report_trip_starts = $this->request->trip_starts;
            } else {
                $this->request->trip_starts = $firstTripLocation->getDate()->copy();
                $this->request->report_trip_starts = $this->request->trip_starts;
            }
        }
    }

    protected function setTripEnd(LocationElement $lastTripLocation): void
    {
        if($lastTripLocation
            && (
                $this->request->getOriginal('trip_ends') === null
                || $lastTripLocation instanceof TrainTripLocationElement
                || $lastTripLocation instanceof PlaneTripLocationElement
            )
        ) {
            if ($lastTripLocation instanceof PlaneTripLocationElement
                && $lastTripLocation->getArrivalDate() instanceof Carbon
                && $lastTripLocation->getArrivalDate()->greaterThan($lastTripLocation->getDate())
            ) {
                $this->request->trip_ends = $lastTripLocation->getArrivalDate()->copy();
                $this->request->report_trip_ends = $this->request->trip_ends;
            } else {
                $this->request->trip_ends = $lastTripLocation->getDate()->gt($this->request->trip_starts)
                    ? $lastTripLocation->getDate()->copy()
                    : $lastTripLocation->getDate()->copy()->endOfDay();
                $this->request->report_trip_ends = $this->request->trip_ends;
            }
        }
    }
}
