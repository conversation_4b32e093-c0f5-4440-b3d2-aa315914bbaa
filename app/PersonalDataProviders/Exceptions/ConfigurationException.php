<?php declare(strict_types=1);

namespace App\PersonalDataProviders\Exceptions;

use App\PersonalDataProviders\Contracts\PersonalDataProvidersExceptionInterface;

class ConfigurationException extends \Exception implements PersonalDataProvidersExceptionInterface
{
    /** @var string */
    protected $instance;

    /** @var string */
    protected $configKey;

    public function __construct(string $instance, $configKey)
    {
        $this->instance = $instance;
        $this->configKey = $configKey;

        parent::__construct("Personal data provider configuration for instance $instance is invalid, $configKey is missing");
    }

    public function toNotification(): string
    {
        return "Konfiguracja dostawcy danych osobowych dla instancji $this->instance jest niepoprawna. Brakuje klucza: $this->configKey";
    }
}
