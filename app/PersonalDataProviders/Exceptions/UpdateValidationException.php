<?php
/**
 * Created by PhpStorm.
 * User: patry<PERSON>
 * Date: 31.07.19
 * Time: 18:35
 */

namespace App\PersonalDataProviders\Exceptions;


use App\PersonalDataProviders\Contracts\PersonalDataProvidersExceptionInterface;
use Exception;

class UpdateValidationException extends Exception implements PersonalDataProvidersExceptionInterface
{
    protected $userEmail;

    protected $errors;


    public function __construct(string $userEmail, string $errors)
    {
        $this->userEmail = $userEmail;
        $this->errors = $errors;

        parent::__construct(sprintf('Validation errors occurred while updating user with email %s. Detailed errors: %s',
            $this->userEmail, $errors));
    }


    public function toNotification(): string
    {
        return sprintf('Podczas aktualizacji użytkownika o emailu: %s wystąpiły błędy w walidacji danych. Szczegółowe dane o błędach: %s',
            $this->userEmail, $this->errors);
    }

}
