<?php

namespace App\PersonalDataProviders\Instances\Amrest\CEDB\Services;

use App\Group;
use App\Instance;
use App\PersonalDataProviders\Contracts\GroupAttacherInterface;
use App\User;

class AttachGroupToUserService implements GroupAttacherInterface
{
    /** @var Group */
    protected $regularGroup;

    /** @var Group */
    protected $execGroup;

    public function __construct(Instance $instance)
    {
        $this->regularGroup = Group::where(['name' => 'Regular', 'instance_id' => $instance->id])->first();
        $this->execGroup    = Group::where(['name' => 'Exec', 'instance_id' => $instance->id])->first();
    }

    public function attachGroupToUser(User $user): void
    {
        if ($user->level >= 8) {
            $this->execGroup->users()->attach($user->id);
        } else {
            $this->regularGroup->users()->attach($user->id);
        }

    }
}
