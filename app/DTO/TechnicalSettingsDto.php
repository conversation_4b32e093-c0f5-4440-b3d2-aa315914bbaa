<?php

declare(strict_types=1);

namespace App\DTO;

use Illuminate\Contracts\Support\Jsonable;

final class TechnicalSettingsDto implements Jsonable
{
    private const LOG_ROCKET = 'logRocket';

    private const SENTRY = 'sentry';
    private const ENVIRONMENT = 'environment';
    private const RELEASE = 'release';

    private array $settings;
    private string $environment;
    private string $release;

    public function __construct(array $settings, string $environment, string $release)
    {
        $this->settings = $settings;
        $this->environment = $environment;
        $this->release = $release;
    }

    public static function createFromArray(array $settings, string $environment, string $release): TechnicalSettingsDto
    {
        return new TechnicalSettingsDto($settings, $environment, $release);
    }

    public function toJson($options = 0)
    {
        return json_encode([
            self::LOG_ROCKET => $this->boolVal(self::LOG_ROCKET),
            self::SENTRY => $this->boolVal(self::SENTRY),
            self::ENVIRONMENT => $this->environment,
            self::RELEASE => $this->release,
        ], JSON_FORCE_OBJECT);
    }

    private function boolVal(string $key): bool
    {
        return isset($this->settings[$key]) ? boolval($this->settings[$key]) : false;
    }
}
