<?php

declare(strict_types=1);

namespace App\DTO\Notification;

use App\Instance;
use App\Notifications\DTO\InstanceDto;
use App\Notifications\DTO\RequestDto;
use App\Request;
use App\ValueObjects\Notifications\NotificationType;

abstract class NotificationDto
{
    /** @var NotificationType */
    protected $notificationType;

    protected ?RequestDto $requestDto;

    protected ?InstanceDto $instanceDto;
    private ?Request $request;

    public function __construct(string $notificationType, ?Request $request, ?Instance $instance = null)
    {
        $this->notificationType = new NotificationType($notificationType);
        $this->requestDto = $request !== null ? new RequestDto($request) : null;
        $this->instanceDto = $instance !== null ? InstanceDto::fromInstance($instance) : ($request !== null ? InstanceDto::fromInstance($request->instance) : null);
        $this->request = $request;
    }

    public function getNotificationType(): NotificationType
    {
        return $this->notificationType;
    }

    public function getRequestDto(): ?RequestDto
    {
        return $this->requestDto;
    }

    public function getInstanceDto(): ?InstanceDto
    {
        return $this->instanceDto;
    }

    public function getRequest(): ?Request
    {
        return $this->request;
    }
}