<?php

declare(strict_types=1);

namespace App\DTO\Notification;

use App\Document;
use App\Notifications\DTO\DocumentDto;
use App\Request;

class VoucherIssuedDto extends NotificationDto
{
    protected $documentDto;

    protected ?string $offerErrorMessageSlug;

    protected array $fullOffer;

    public function __construct(
        string $notificationType,
        Request $request,
        Document $document,
        ?string $offerErrorMessageSlug,
        array $fullOffer,
        ?string $ticketNumber,
        ?string $referenceNumbers = null
    ) {
        parent::__construct($notificationType, $request);

        $this->documentDto = DocumentDto::createFromDocumentModelAndFullOffer($document, $fullOffer, $ticketNumber, $referenceNumbers);
        $this->offerErrorMessageSlug = $offerErrorMessageSlug;
    }

    public function getDocumentDto(): DocumentDto
    {
        return $this->documentDto;
    }

    public function getOfferErrorMessageSlug(): ?string
    {
        return $this->offerErrorMessageSlug;
    }
}