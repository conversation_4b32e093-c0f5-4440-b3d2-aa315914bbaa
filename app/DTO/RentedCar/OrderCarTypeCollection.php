<?php

declare(strict_types=1);

namespace App\DTO\RentedCar;

use App\Enum\RentedCar\OrderCarTypeEnum;
use App\Vendors\TypedCollection;
use Illuminate\Support\Collection;

class OrderCarTypeCollection extends TypedCollection
{
    public function getTypeClassName(): string
    {
        return OrderCarTypeEnum::class;
    }

    public static function createFromArray(?array $values = []): OrderCarTypeCollection
    {
        $collection = collect($values);
        $transformed = $collection->transform(function ($item) {
            return new OrderCarTypeEnum($item);
        });

        return new static($transformed->toArray());
    }

    public function toFlatArray(): array
    {
        return $this
            ->values()
            ->toArray();
    }

    public function toFlatCollection(): Collection
    {
        $collection = collect();

        $this
            ->each(function (OrderCarTypeEnum $orderCarTypeEnum) use (&$collection) {
                $collection->push((string)$orderCarTypeEnum);
            });

        return $collection;
    }
}