<?php

declare(strict_types=1);

namespace TestsCodeception\integration\Domain\Obt\Integrations\TraveltechObt\Responses;

use Codeception\Test\Unit;
use Domain\Obt\Integrations\TraveltechObt\Responses\BuyTicketsResponse;

class BuyTicketsResponseTest extends Unit
{
    private const TRAVEL_TECH_FAILED_BUY_TICKETS_RESPONSE_XML = '/TravelTech/buy_tickets_failed_response.xml';

    public function testBuyingTicketsFailed()
    {
        $xml = simplexml_load_string(file_get_contents(resource_path('mockups') . self::TRAVEL_TECH_FAILED_BUY_TICKETS_RESPONSE_XML));

        $buyTicketsResponse = BuyTicketsResponse::createFromSimpleXMLElement($xml, []);
        $this->assertInstanceOf(BuyTicketsResponse::class, $buyTicketsResponse);
    }

    public function testBuyingTicketsFailedErrorTypeWillBeAppended(): void
    {
        $xml = simplexml_load_string(file_get_contents(resource_path('mockups') . self::TRAVEL_TECH_FAILED_BUY_TICKETS_RESPONSE_XML));

        $buyTicketsResponse = BuyTicketsResponse::createFromSimpleXMLElement($xml, []);
        $this->assertInstanceOf(BuyTicketsResponse::class, $buyTicketsResponse);
        $this->assertEquals('UNSELABLE_TRAIN_OR_SERVICE_IN_DATE_MISHMASH', $buyTicketsResponse->getErrorType());
    }
}
