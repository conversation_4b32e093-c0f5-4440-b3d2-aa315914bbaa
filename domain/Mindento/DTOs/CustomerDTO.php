<?php

namespace Domain\Mindento\DTOs;


class CustomerDTO
{
    private $id;

    private $mindento_id;

    private $name;

    private $active_users;

    private $fixed_fee_users;

    private $free_users;

    private $company_name;

    private $street;

    private $building_number;

    private $flat_number;

    private $postcode;

    private $city;

    private $country_code;

    private $nip;

    private $payer_details_active;

    private $payer_company_name;

    private $payer_street;

    private $payer_building_number;

    private $payer_flat_number;

    private $payer_postcode;

    private $payer_city;

    private $payer_nip;

    private $payer_nip_prefix;

    private $currency;

    private $active;

    private $balance;

    private $mindento_created_at;

    private $invoice_language_setting;

    private $invoice_addresses_setting;

    private $service_status_setting;

    private $default_payment_period_days_setting;

    /**
     * CustomerDTO constructor.
     *
     * @param $id
     * @param $mindento_id
     * @param $name
     * @param $active_users
     * @param $fixed_fee_users
     * @param $free_users
     * @param $company_name
     * @param $street,
     * @param $building_number,
     * @param $flat_number,
     * @param $postcode
     * @param $city
     * @param $country_code
     * @param $nip
     * @param $payer_details_active
     * @param $payer_company_name
     * @param $payer_street,
     * @param $payer_building_number,
     * @param $payer_flat_number,
     * @param $payer_postcode
     * @param $payer_city
     * @param $payer_nip
     * @param $payer_nip_prefix
     * @param $currency
     * @param $active
     * @param $balance
     * @param $mindento_created_at
     * @param $invoice_language_setting
     * @param $invoice_addresses_setting
     * @param $service_status_setting
     * @param $default_payment_period_days_setting
     */
    public function __construct(
        $id,
        $mindento_id,
        $name,
        $active_users,
        $fixed_fee_users,
        $free_users,
        $company_name,
        $street,
        $building_number,
        $flat_number,
        $postcode,
        $city,
        $country_code,
        $nip,
        $payer_details_active,
        $payer_company_name,
        $payer_street,
        $payer_building_number,
        $payer_flat_number,
        $payer_postcode,
        $payer_city,
        $payer_nip,
        $payer_nip_prefix,
        $currency,
        $active,
        $balance,
        $mindento_created_at,
        $invoice_language_setting,
        $invoice_addresses_setting,
        $service_status_setting,
        $default_payment_period_days_setting
    ) {
        $this->id                                  = $id;
        $this->mindento_id                         = $mindento_id;
        $this->name                                = $name;
        $this->active_users                        = $active_users;
        $this->fixed_fee_users                     = $fixed_fee_users;
        $this->free_users                          = $free_users;
        $this->company_name                        = $company_name;
        $this->street                              = $street;
        $this->building_number                     = $building_number;
        $this->flat_number                         = $flat_number;
        $this->postcode                            = $postcode;
        $this->city                                = $city;
        $this->country_code                        = $country_code;
        $this->nip                                 = $nip;
        $this->payer_details_active                = $payer_details_active;
        $this->payer_company_name                  = $payer_company_name;
        $this->payer_street                        = $payer_street;
        $this->payer_building_number               = $payer_building_number;
        $this->payer_flat_number                   = $payer_flat_number;
        $this->payer_postcode                      = $payer_postcode;
        $this->payer_city                          = $payer_city;
        $this->payer_nip                           = $payer_nip;
        $this->payer_nip_prefix                    = $payer_nip_prefix;
        $this->currency                            = $currency;
        $this->active                              = $active;
        $this->balance                             = $balance;
        $this->mindento_created_at                 = $mindento_created_at;
        $this->invoice_language_setting            = $invoice_language_setting;
        $this->invoice_addresses_setting           = $invoice_addresses_setting;
        $this->service_status_setting              = $service_status_setting;
        $this->default_payment_period_days_setting = $default_payment_period_days_setting;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return mixed
     */
    public function getVtlId()
    {
        return $this->mindento_id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @return mixed
     */
    public function getActiveUsers()
    {
        return $this->active_users;
    }

    /**
     * @return mixed
     */
    public function getFixedFeeUsers()
    {
        return $this->fixed_fee_users;
    }

    /**
     * @return mixed
     */
    public function getFreeUsers()
    {
        return $this->free_users;
    }

    /**
     * @return mixed
     */
    public function getCompanyName()
    {
        return $this->company_name;
    }

    /**
     * @return mixed
     */
    public function getStreet()
    {
        return $this->street;
    }

    /**
     * @return mixed
     */
    public function getBuildingNumber()
    {
        return $this->building_number;
    }

    /**
     * @return mixed
     */
    public function getFlatNumber()
    {
        return $this->flat_number;
    }

    /**
     * @return mixed
     */
    public function getPayerStreet()
    {
        return $this->payer_street;
    }

    /**
     * @return mixed
     */
    public function getPayerBuildingNumber()
    {
        return $this->payer_building_number;
    }

    /**
     * @return mixed
     */
    public function getPayerFlatNumber()
    {
        return $this->payer_flat_number;
    }

    /**
     * @return mixed
     */
    public function getPostcode()
    {
        return $this->postcode;
    }

    /**
     * @return mixed
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @return mixed
     */
    public function getNip()
    {
        return $this->nip;
    }

    /**
     * @return mixed
     */
    public function getPayerDetailsActive()
    {
        return $this->payer_details_active;
    }

    /**
     * @return mixed
     */
    public function getPayerCompanyName()
    {
        return $this->payer_company_name;
    }

    /**
     * @return mixed
     */
    public function getPayerPostcode()
    {
        return $this->payer_postcode;
    }

    /**
     * @return mixed
     */
    public function getPayerCity()
    {
        return $this->payer_city;
    }

    /**
     * @return mixed
     */
    public function getPayerNip()
    {
        return $this->payer_nip;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @return mixed
     */
    public function getActive()
    {
        return $this->active;
    }

    /**
     * @return mixed
     */
    public function getBalance()
    {
        return $this->balance;
    }

    /**
     * @return mixed
     */
    public function getMindentoCreatedAt()
    {
        return $this->mindento_created_at;
    }

    /**
     * @return mixed
     */
    public function getInvoiceLanguageSetting()
    {
        return $this->invoice_language_setting;
    }

    /**
     * @return mixed
     */
    public function getInvoiceAddressesSetting()
    {
        return $this->invoice_addresses_setting;
    }

    /**
     * @return mixed
     */
    public function getServiceStatusSetting()
    {
        return $this->service_status_setting;
    }

    /**
     * @return mixed
     */
    public function getDefaultPaymentPeriodDaysSetting()
    {
        return $this->default_payment_period_days_setting;
    }

    /**
     * @return mixed
     */
    public function getCountryCode()
    {
        return $this->country_code;
    }

    /**
     * @return mixed
     */
    public function getPayerNipPrefix()
    {
        return $this->payer_nip_prefix;
    }

}
