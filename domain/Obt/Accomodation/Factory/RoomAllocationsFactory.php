<?php

declare(strict_types=1);

namespace Domain\Obt\Accomodation\Factory;

use App\Helpers\CurrentCustomer;
use App\SearchQueryPax;
use Domain\Obt\Accomodation\Dto\RoomAllocations;
use Domain\Obt\Accomodation\Dto\RoomConfig;
use Domain\Obt\DTOs\ReservationSpecificFields\HotelReservationSpecificFields;
use Illuminate\Support\Collection;

class RoomAllocationsFactory
{
    /**
     * @var HotelGuestFactory
     */
    protected $hotelGuestFactory;

    /**
     * RoomAllocationsFactory constructor.
     * @param HotelGuestFactory $hotelGuestFactory
     */
    public function __construct(HotelGuestFactory $hotelGuestFactory)
    {
        $this->hotelGuestFactory = $hotelGuestFactory;
    }

    public function createFromArray(
        array $source,
        string $leaderMindentoId
    ): RoomAllocations {
        $roomAllocations = new RoomAllocations();

        /** @var array $room */
        foreach ($source as $room) {
            $roomAllocation = new RoomConfig();
            foreach ($room as $userData) {
                $roomAllocation->addGuest(
                    $this->hotelGuestFactory->createHotelGuestByMindentoId(
                        $userData['user_slug'],
                        HotelReservationSpecificFields::createFromArray($userData),
                        $leaderMindentoId,
                        $userData['traveler_slug']
                    )
                );
            }
            $roomAllocations->push($roomAllocation);
        }

        return $roomAllocations;
    }

    public function createFromPaxes(Collection $paxes): RoomAllocations
    {
        $roomAllocations = new RoomAllocations();
        $paxesGroupedByRooms = $paxes->groupBy(function (SearchQueryPax $pax) {
            return $pax->getAdditionalAttribute(SearchQueryPax::ROOM_NUMBER_KEY);
        })->sortKeys();

        foreach ($paxesGroupedByRooms as $roomAllocation) {
            $room = new RoomConfig();
            /** @var SearchQueryPax $pax */
            foreach ($roomAllocation as $pax) {
                $room->addGuest(
                    $this->hotelGuestFactory->createFromPax($pax)
                );
            }
            $roomAllocations->push($room);
        }

        return $roomAllocations;
    }
}
