<?php

declare(strict_types=1);

namespace Domain\Obt\Invoice;

use App\Invoice;
use App\OutboxItem;
use App\Reservation;
use App\ReservationDependencyJob;
use Domain\AuditLog\Facades\ReservationLogContext;
use Domain\Invoice\Factories\InvoiceDatesFactory;
use Domain\Invoice\Jobs\IssueQueuedInvoicesJob;
use Domain\Invoice\Jobs\TransformAccountingNoteToInvoiceJob;
use Domain\Invoice\Services\Cancellation\ReservationDraftInvoiceService;
use Domain\Invoice\Services\CreateInvoiceService;
use Domain\Invoice\Services\StartInvoicePaymentService;
use Domain\Obt\DependencyJob\DependencyJobService;
use Domain\Obt\Jobs\OutboxSupervisorJob;
use Domain\Obt\Repositories\ReservationDependencyJobRepository;
use Illuminate\Contracts\Bus\Dispatcher;
use Modules\Obt\Pub\Enums\ReservationStatusEnum;

final class InvoiceManager
{
    private ReservationDraftInvoiceService $reservationDraftInvoiceService;
    private CreateInvoiceService $createInvoiceService;
    private StartInvoicePaymentService $startInvoicePaymentService;
    private Dispatcher $dispatcher;
    private DependencyJobService $dependencyJobService;
    private InvoiceDatesFactory $invoiceSaleDateFactory;

    public function __construct(
        ReservationDraftInvoiceService $reservationDraftInvoiceService,
        CreateInvoiceService $createInvoiceService,
        StartInvoicePaymentService $startInvoicePaymentService,
        DependencyJobService $dependencyJobService,
        InvoiceDatesFactory $invoiceSaleDateFactory,
        Dispatcher $dispatcher
    ) {
        $this->reservationDraftInvoiceService = $reservationDraftInvoiceService;
        $this->createInvoiceService = $createInvoiceService;
        $this->startInvoicePaymentService = $startInvoicePaymentService;
        $this->dispatcher = $dispatcher;
        $this->dependencyJobService = $dependencyJobService;
        $this->invoiceSaleDateFactory = $invoiceSaleDateFactory;
    }

    public function process(ReservationDependencyJob $reservationDependencyJob): void
    {
        if ($reservationDependencyJob->type === ReservationDependencyJob::TYPE_OFFLINE_RESERVATION_INVOICE) {
            switch ($reservationDependencyJob->status) {
                case ReservationDependencyJob::STATUS_NEW:
                    $invoiceDraft = $this->createInvoiceService->createAdditionalInvoiceDraftForOfflineReservation(
                        $reservationDependencyJob->reservation
                    );

                    if ($invoiceDraft !== null) {
                        if ($invoiceDraft->customer->isRecurringInvoiceEnabled()) {
                            $payment = $this->startInvoicePaymentService->startForInvoiceDraftWithRecurringInvoicing(
                                $invoiceDraft,
                                $invoiceDraft->getTotalGrossAmountAttribute()
                            );
                        } else {
                            $payment = $this->startInvoicePaymentService->startForInvoiceDraft(
                                $invoiceDraft,
                                $invoiceDraft->getTotalGrossAmountAttribute()
                            );
                        }
                        $reservationDependencyJob->payment()->associate($payment);
                    }

                    break;
                case ReservationDependencyJob::STATUS_WAITING:
                    if ($reservationDependencyJob->payment !== null
                        && $reservationDependencyJob->payment->invoice !== null
                    ) {
                        ReservationLogContext::setReservation($reservationDependencyJob->reservation);
                        $this->sendInvoice(
                            $reservationDependencyJob->payment->invoice,
                            $reservationDependencyJob->reservation
                        );
                    }
                    break;
            }
        }
    }

    public function queueInvoiceIssuing(Reservation $reservation): void
    {
        /** @var ReservationDependencyJob $reservationDependencyJob */
        $reservationDependencyJob = resolve(ReservationDependencyJobRepository::class)->getByReservationIdAndType(
            $reservation->id,
            ReservationDependencyJob::TYPE_INVOICE
        );

        if (!$reservationDependencyJob) {
            $issueDate = $this->invoiceSaleDateFactory->issueDate($reservation);
            $reservationDependencyJob = $this->dependencyJobService->createForInvoice($reservation->id, $issueDate);

            if ($issueDate->isToday()) {
                IssueQueuedInvoicesJob::dispatch($reservationDependencyJob);
            }
        }
    }

    public function issueNowQueuedInvoice(Reservation $reservation): void
    { // todo  - change date od issuing invoice when cancelling reservation
        $reservation->dependencyJobs()
            ->where('type', ReservationDependencyJob::TYPE_INVOICE)
            ->where('status', ReservationDependencyJob::STATUS_NEW)
            ->get()
            ->each(function (ReservationDependencyJob $job) {
                $job->executeNow();
            });
    }

    public function issueInvoice(Reservation $reservation): void
    {
        \DB::transaction(function () {
            $invoices = $this->createInvoiceService->createInvoicesForReservation($reservation);

            foreach ($invoices as $invoice) {
                $this->sendInvoice($invoice, $reservation);
            }
        });
    }

    public function generateCorrectionDocuments(Reservation $reservation): void
    {
        if (ReservationStatusEnum::STATUS_CANCELED()->equals($reservation->getStatus())) {
            $invoice = $this->reservationDraftInvoiceService->transformCorrectionDraftFromReservationToInvoice($reservation);
            $this->sendInvoice($invoice, $reservation);

            if (
                (
                    $reservation->payment->isFake() &&
                    $reservation->payment->fakeShouldSucceed() ||
                    $reservation->payment->isTransferPayment()
                ) &&
                ($accountingNoteDraft = $reservation->payment->invoice->accountingNoteDraft) !== null
            ) {
                $this->dispatcher->dispatch(new TransformAccountingNoteToInvoiceJob(
                    $accountingNoteDraft->id
                ));
            }
        }
    }

    protected function sendInvoice(Invoice $invoice, Reservation $reservation): void
    {
        $outboxItem = OutboxItem::sendAccountingDocument($reservation);
        $invoice->outbox()->save($outboxItem);
        OutboxSupervisorJob::dispatch();
    }
}
