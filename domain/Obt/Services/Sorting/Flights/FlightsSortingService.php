<?php


namespace Domain\Obt\Services\Sorting\Flights;


use App\Helpers\OffersCollection;
use Domain\Obt\Interfaces\SortingServiceInterface;
use Domain\Obt\Services\Sorting\ParametersBags\FlightsSortingParametersBag;
use Illuminate\Support\Collection;

class FlightsSortingService implements SortingServiceInterface
{
    const OFFERS_SORT_BY_DEPARTURE_TIME = 'departure-time';
    const OFFERS_SORT_BY_CHEAPEST_FIRST = 'cheapest-first';
    const OFFERS_SORT_BY_TRAVEL_TIME = 'travel-time';
    const OFFERS_SORT_BY_BEST_OFFER = 'best-offer';

    /** @var OffersCollection  */
    protected $offersCollection;

    /** @var FlightsSortingParametersBag */
    protected $parametersBag;

    /**
     * FilteringService constructor.
     * @param $offersCollection
     */
    public function __construct(OffersCollection $offersCollection, FlightsSortingParametersBag $parametersBag)
    {
        $this->setOffersCollection($offersCollection)
            ->setParametersBag($parametersBag);
    }

    /**
     * @return Collection
     */
    public static function sortTypes(): Collection
    {
        return collect([
            static::OFFERS_SORT_BY_BEST_OFFER,
            static::OFFERS_SORT_BY_CHEAPEST_FIRST,
            static::OFFERS_SORT_BY_DEPARTURE_TIME,
            static::OFFERS_SORT_BY_TRAVEL_TIME,
        ]);
    }

    public function getOffersCollection(): OffersCollection
    {
        return $this->offersCollection;
    }

    public function setOffersCollection(OffersCollection $collection): FlightsSortingService
    {
        $this->offersCollection = $collection;

        return $this;
    }

    /**
     * @return FlightsSortingParametersBag
     */
    public function getParametersBag(): FlightsSortingParametersBag
    {
        return $this->parametersBag;
    }

    /**
     * @param FlightsSortingParametersBag $parametersBag
     * @return FlightsSortingService
     */
    public function setParametersBag(FlightsSortingParametersBag $parametersBag): FlightsSortingService
    {
        $this->parametersBag = $parametersBag;
        return $this;
    }

    public function sort(): OffersCollection
    {
    	if($this->getParametersBag()->getSortType() === null)   {
    		return $this->offersCollection;
	    }

        return SorterFactory::create($this->getParametersBag()->getSortType())->sort($this->offersCollection);
    }
}
