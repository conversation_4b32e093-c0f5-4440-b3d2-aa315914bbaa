<?php

namespace Domain\Obt\Integrations\YpsilonObt\Responses\PricingResponse;


use App\Helpers\TypedCollection;
use App\Helpers\XMLItem;
use Domain\Obt\Integrations\YpsilonObt\CreatableFromXMLInterface;

class Rules extends TypedCollection implements CreatableFromXMLInterface
{
    public function getTypeClassName(): string
    {
        return Rule::class;
    }

    public static function createFromXML(XMLItem $XMLItem): Rules
    {
        $rules = new static;

        collect($XMLItem->getIterable('rule'))->each(function (XMLItem $XMLItem) use ($rules) {
            $rules->push(Rule::createFromXML($XMLItem));
        });

        return $rules;
    }
}