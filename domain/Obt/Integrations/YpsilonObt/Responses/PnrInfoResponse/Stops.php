<?php


namespace Domain\Obt\Integrations\YpsilonObt\Responses\PnrInfoResponse;


use Domain\Obt\Integrations\YpsilonObt\TransformToFlights\ConnectionFlight;
use Illuminate\Support\Collection;

class Stops
{
    /** @var Collection */
    protected $stops;

    /**
     * Stops constructor.
     * @param Collection $stops
     */
    public function __construct(Collection $stops)
    {
        $this->stops = $stops;
    }

    /**
     * @return Collection
     */
    public function getStops(): Collection
    {
        return $this->stops;
    }

    public function getStopsAmount()
    {
        return $this->stops->count();
    }

    public function getStopsStringLabel()
    {
        return $this->stops->count() > 1 ? 'stops' : 'stop';
    }

    public function __toString()
    {
        $result = "{$this->getStopsAmount()} {$this->getStopsStringLabel()}, ";

        $this->stops->each(function(ConnectionFlight $stop) use (&$result) {
            $result .= "{$stop->getTravelTime()} {$stop->getAirport()}";
        });

        return $result;
    }
}
