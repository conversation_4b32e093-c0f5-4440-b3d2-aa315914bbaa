<?php


namespace Domain\Obt\Integrations\MerigoObt\Interfaces;


use Carbon\Carbon;
use Illuminate\Support\Collection;

//todo delete!
interface VoucherResponseInterface extends ResponseInterface
{

    /**
     * @return Carbon
     */
    public function getArrivalDate(): Carbon;

    /**
     * @return string
     */
    public function getPhone(): string;

    /**
     * @return string
     */
    public function getRoomDetails(): string;


    /**
     * @return string
     */
    public function getRoomTypes(): Collection;

    /**
     * @return int
     */
    public function getNights(): int;

    /**
     * @return string
     */
    public function getRoomBasis(): string;

    /**
     * @return string
     */
    public function getSupplierReferenceNumber(): string;

    /**
     * @return Collection
     */
    public function getRemarks(): Collection;

    /**
     * @return string
     */
    public function getHotelName(): string;

    /**
     * @return string
     */
    public function getBookingCode(): string;

    /**
     * @return string
     */
    public function getHotelAddress(): string;

    /**
     * @return string
     */
    public function getBookedAndPayableBy(): string;

}
