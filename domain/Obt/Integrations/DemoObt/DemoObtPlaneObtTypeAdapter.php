<?php

namespace Domain\Obt\Integrations\DemoObt;

use App\CustomerUser;
use App\Reservation;
use Carbon\Carbon;
use Domain\Obt\DTOs\ReservationSpecificFields\PlaneReservationSpecificFields;
use Domain\Obt\Integrations\StartBookingResult;
use Domain\Obt\Integrations\YpsilonObt\Factories\PaxFactory;
use Domain\Obt\Integrations\YpsilonObt\ProcessYpsilonRequestService;
use Domain\Obt\Integrations\YpsilonObt\Requests\BookingRequest\BookingRequest;
use Domain\Obt\Integrations\YpsilonObt\Requests\FareRequest\Pax;
use Domain\Obt\Integrations\YpsilonObt\Requests\PnrInfoRequest\PnrInfoRequest;
use Domain\Obt\Integrations\YpsilonObt\Responses\BookingResponse\BookingResponse;
use Domain\Obt\Integrations\YpsilonObt\Responses\PnrInfoResponse\PnrInfoResponse;
use Domain\Obt\Integrations\YpsilonObt\YpsilonDate;
use Domain\Obt\Integrations\YpsilonObt\YpsilonPlaneObtTypeAdapter;
use Domain\Obt\Integrations\YpsilonObt\YpsilonRequestInterface;
use Domain\Obt\Integrations\YpsilonObt\YpsilonResponseInterface;
use Domain\Obt\Results\TicketGenerationResultDto;
use Domain\Obt\Services\Logger\ObtLogContext;
use Domain\Obt\Services\Logger\YpsilonRequestLogger;
use GuzzleHttp\RequestOptions;

class DemoObtPlaneObtTypeAdapter extends YpsilonPlaneObtTypeAdapter
{
    private $departureDate;

    public function getFareRequestPax(CustomerUser $customerUser, PlaneReservationSpecificFields $reservationSpecificFields, int $paxNumber, ?string $mindentoRequestTravelerSlug): Pax
    {
        return PaxFactory::createFromFake($customerUser, $reservationSpecificFields, $paxNumber, $mindentoRequestTravelerSlug)->getFareRequestPax();
    }

    public function getBookingRequestPax(CustomerUser $customerUser, PlaneReservationSpecificFields $reservationSpecificFields, int $paxNumber, Carbon $lastDepartureAt, ?string $mindentoRequestTravelerSlug): \Domain\Obt\Integrations\YpsilonObt\Requests\BookingRequest\Pax
    {
        return PaxFactory::createFromFake($customerUser, $reservationSpecificFields, $paxNumber, $mindentoRequestTravelerSlug)->getBookingRequestPax($lastDepartureAt);
    }

    public function processRequest(YpsilonRequestInterface $request, $sessionId, ObtLogContext $obtLogContext): YpsilonResponseInterface
    {
        $logger = new YpsilonRequestLogger($obtLogContext);
        $paramsForLogger = $this->request2array($request, $sessionId);

        if ($request instanceof BookingRequest) {
            $ypsilonResponse = BookingResponse::createFromXML(simplexml_load_string(file_get_contents(resource_path('mockups/bookingResponse.xml'))));
            $logger->logDemoRequest($request, $paramsForLogger);

            return $ypsilonResponse;
        }

        if ($request instanceof PnrInfoRequest) {
            $ypsilonResponse = PnrInfoResponse::createFromXML(simplexml_load_string(file_get_contents(resource_path('mockups/pnrinforequest.xml'))));
            $logger->logDemoRequest($request, $paramsForLogger);

            return $ypsilonResponse;
        }

        return resolve(ProcessYpsilonRequestService::class)->process($request, $sessionId, $obtLogContext, $this->obtServiceDTO);
    }

    private function request2array(YpsilonRequestInterface $request, $sessionId): array
    {
        // requires clone because request is incorrect after second saveXML() invoke
        $cloned = clone $request;

        $params = [
            RequestOptions::HEADERS => $cloned->getHeaders($this->obtServiceDTO),
            RequestOptions::BODY => (string)$cloned->getDOMElement()->ownerDocument->saveXML()
        ];

        if ($sessionId) {
            $params[RequestOptions::HEADERS]['Session'] = $sessionId;
        }

        return $params;
    }

    public function startBooking(Reservation $reservation): StartBookingResult
    {
        $this->departureDate = isset($reservation->offer->searchQuery->query['departure_at']) ? $reservation->offer->searchQuery->query['departure_at'] : null;

        return parent::startBooking($reservation);
    }

    public function confirmBooking(Reservation $reservation): bool
    {
        $reservation->status = Reservation::STATUS_WAITING_FOR_TICKET;
        $reservation->save();

        return true;
    }

    /**
     * @param string $fileKey
     * @param YpsilonDate $date
     * @param string|null $sessionId
     * @param Reservation $reservation
     * @return PnrInfoResponse|null
     */
    protected function processPnrInfoRequest(string $fileKey, YpsilonDate $date, ?string $sessionId, Reservation $reservation)
    {
        return PnrInfoResponse::createFromXML(simplexml_load_string(file_get_contents(resource_path('mockups/pnr_info_request_has_ticket_number.xml'))));
//        return PnrInfoResponse::createFromXML(simplexml_load_string(file_get_contents(resource_path('mockups/pnr_info_request_without_ticket_number.xml'))));
    }

    /**
     * @param Reservation $reservation
     * @param PnrInfoResponse $response
     * @return bool
     */
    private function generateTicket(Reservation $reservation, PnrInfoResponse $response): TicketGenerationResultDto
    {
        if (isset($reservation->offer->offer['tariffSlug'])) {
            $ticketDTO = $response->getTicketDTO();

            $ticket = $this->getTicketFileService()->fromFlightTicketDTO($ticketDTO, $reservation->offer);
            $reservation->saveTicket($ticket, $ticketDTO->getPnrNumber(), $ticketDTO->getReferenceNumbers());
        }
        $reservation->save();

        return TicketGenerationResultDto::ready();
    }
}
