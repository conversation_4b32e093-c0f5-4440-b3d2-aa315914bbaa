<?php

namespace Domain\Marketplace\Hotel\Request;

use Domain\Marketplace\Hotel\DTO\IdentifierDTO;

abstract class IdentifierRequest<PERSON>bs<PERSON> implements RequestInterface, RequestIdentifiableInterface
{
    private IdentifierDTO $requestIdentifier;

    private IdentifierDTO $identifierDTO;

    private function __construct(IdentifierDTO $requestIdentifier, IdentifierDTO $identifierDTO)
    {
        $this->requestIdentifier = $requestIdentifier;
        $this->identifierDTO = $identifierDTO;
    }

    public static function fromIdentifiers(IdentifierDTO $requestIdentifier, IdentifierDTO $identifierDTO): RequestInterface
    {
        return new static($requestIdentifier, $identifierDTO);
    }

    public function getIdentifier(): string
    {
        return $this->requestIdentifier->getId();
    }

    public function toArray(): array
    {
        return [
            $this->getIdentifierKey() => $this->identifierDTO->getId(),
        ];
    }

    abstract protected function getIdentifierKey(): string;
}
