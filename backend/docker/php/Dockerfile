FROM php:8.3-alpine

RUN apk update && apk add --no-cache nano curl git unzip acl bash

RUN curl -sSLf \
    -o /usr/local/bin/install-php-extensions \
    https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions && \
    chmod +x /usr/local/bin/install-php-extensions

COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
RUN composer global require laravel/installer

# local user in container
ARG CURRENT_USER=devuser
ARG USERID=1000
ARG GROUPID=1000
RUN echo "CURRENT_USER: ${CURRENT_USER}" && \
    echo "GROUPID: ${GROUPID}" && \
    echo "USERID: ${USERID}" && \
    EXISTING_GROUP_NAME=$(getent group ${GROUPID} | cut -d: -f1) && \
    if [ -z "$EXISTING_GROUP_NAME" ]; then \
      addgroup -g ${GROUPID} ${CURRENT_USER} && \
      GROUP_TO_USE=${CURRENT_USER}; \
    else \
      GROUP_TO_USE=${EXISTING_GROUP_NAME}; \
    fi && \
    EXISTING_USER_NAME=$(getent passwd ${USERID} | cut -d: -f1) && \
    echo "EXISTING_USER_NAME: ${EXISTING_USER_NAME}" && \
    echo "GROUP_TO_USE: ${GROUP_TO_USE}" && \
    if [ -n "${EXISTING_USER_NAME}" ]; then deluser ${EXISTING_USER_NAME}; fi && \
    adduser -D -u ${USERID} -G ${GROUP_TO_USE} ${CURRENT_USER};
#/ local user in container
