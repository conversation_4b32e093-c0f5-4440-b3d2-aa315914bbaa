version: '3'

dotenv: [.env, .env.github, .env.devops]

includes:
    github: taskfile.github.yaml
    prod: taskfile.prod.yaml
    docker: taskfile.docker.yaml
    release: taskfile.release.yaml
    tests: taskfile.tests.yaml
    db: taskfile.db.yaml

vars:
    CURRENT_DIR:
        sh: pwd | grep -o '[^/]*$'
    DOCKER_COMPOSE_FILES: |
        -f docker-compose.yaml \
    BUILD_ARGS: --build-arg USERID=$(id -u) --build-arg GROUPID=$(id -g) --build-arg CURRENT_USER=${LOCAL_USER}
    DOCKER_BUILDKIT: 'docker build ${BUILD_ARGS} -t buildkit --build-arg USERID=$(id -u) -q -f ./docker/php/Dockerfile .'

tasks:
    exec:
        desc: 'Run a command in the container'
        cmds:
            - docker compose exec php sh -c "{{.CLI_ARGS}}"

    artisan:
        silent: true
        cmds:
            - task exec -- php artisan {{.CLI_ARGS}}

    composer:
        silent: true
        cmds:
            - task exec -- composer {{.CLI_ARGS}}

    composer:dump:
        silent: true
        cmds:
            - task exec -- composer dumpautoload

    ide-helper:
        cmds:
            - task artisan -- ide-helper:eloquent
            - task artisan -- ide-helper:generate
            - task artisan -- ide-helper:meta
            - task artisan -- ide-helper:models -W

    build:
        cmds:
            - DOCKER_BUILDKIT=1 docker compose build

    build:php:
        cmds:
            - DOCKER_BUILDKIT=1 docker compose build php

    up:
        cmds:
            - docker compose up -d

    stop:
        cmds:
            - docker compose stop

    down:
        cmds:
            - docker compose down {{.CLI_ARGS}}

    ps:
        cmds:
            - docker compose ps

    log:
        cmds:
            - docker compose up

    sh:
        cmds:
            - docker compose exec -it php sh

    http:sh:
        cmds:
            - docker compose exec -it http sh

    sh:run:
        cmds:
            - docker compose run -it php sh

    sail:
        desc: 'Run Laravel Sail commands'
        cmds:
            - vendor/laravel/sail/bin/sail {{.ARGS}}

    run:
        desc: 'Run development environment locally in detached mode'
        cmds:
            - docker stop $(docker ps -a -q)
            - task:sail -- up -d

    xdebug:on:
        desc: 'Enable Xdebug'
        cmds:
            - docker compose exec php sh -c "export XDEBUG_MODE=debug,develop && echo 'Xdebug enabled'"

    xdebug:off:
        desc: 'Disable Xdebug'
        cmds:
            - docker compose exec php sh -c "export XDEBUG_MODE=off && echo 'Xdebug disabled'"

    xdebug:toggle:
        desc: 'Toggle Xdebug on/off'
        cmds:
            - |
                if docker compose exec php sh -c "echo \$XDEBUG_MODE" | grep -q "debug"; then
                  task xdebug:off
                else
                  task xdebug:on
                fi

    php:run:
        desc: 'Run a PHP script'
        cmds:
            - task exec -- php {{.CLI_ARGS}}

    php:debug:
        desc: 'Run a PHP script with Xdebug enabled'
        cmds:
            - docker compose exec -e XDEBUG_MODE=debug -e XDEBUG_SESSION=1 php php {{.CLI_ARGS}}
